server {
    listen 80;
    server_tokens off;

    client_max_body_size 200M;
    client_body_timeout 300s;
    client_header_timeout 300s;
    keepalive_timeout 300s;
    send_timeout 300s;

    client_body_buffer_size 128k;
    client_header_buffer_size 1k;

    server_name localhost;
    root   /var/www/html/sismonp3tgai;


    sendfile on;
    tcp_nopush on;
    output_buffers 2 512k;
    postpone_output 1460;

    autoindex off;

    # Block potentially dangerous files except PHP
    location ~* \.(sh|py|pl|cgi)$ {
        deny all;
    }

    index index.php index.html index.htm;

    location = / {
        if ($args ~* "(union.*select|<script>|benchmark\(|sleep\(|base64_decode|etc/passwd)") {
            return 301 /trap.html;
        }
        try_files $uri $uri/ /index.php?$args;
    }

    location = /trap.html {
        return 200 "<html><body>Nothing here</body></html>";
        add_header Content-Type text/html;
    }

    location ~* /(gbo303|judi|shell|wp-class|moon|install)\.php$ {
        deny all;
    }

    location ~* \.(git|env|svn|htaccess|bash_history|DS_Store) {
        deny all;
    }

    location ~* /(uploads|berita)/judi/ {
        deny all;
    }

    location ~* ^/(wp-admin|wp-login\.php|wp-includes|wlwmanifest\.xml|xmlrpc\.php|readme\.html) {
        return 444;
    }

    location ~* "^/(wp-content|wp-json|wp1|wp2|blog|site|cms|test|old|202[0-5])/" {
        return 444;
    }

    if ($http_user_agent ~* "(sqlmap|nikto|acunetix|nmap|curl|wget|python-requests|httpclient)") {
        return 444;
    }

    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    error_page 503 @temporarily_unavailable;

    location @temporarily_unavailable {
        return 503 "Service temporarily unavailable. Please try again in a few moments.";
        add_header Content-Type text/plain;
        add_header Retry-After 30;
    }

    location = /50x.html {
        root /var/www/html;
    }

    location ~ /\. {
        deny all;
    }

    location ~* /uploads/.*\.php$ {
        deny all;
    }

    location /uploads {
        if ($args != "") {
            return 301 /trap.html;
        }

        location ~* \.(jpg|jpeg|png|pdf|ico|woff|woff2|ttf|eot|svg)$ {
            expires max;
            log_not_found off;
        }

        location ~* \.php$ {
            deny all;
        }
    }

    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass app:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_read_timeout 3600;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
        fastcgi_param CI_ENV development;
        fastcgi_param REDIRECT_STATUS 200;
    }

    location ~ /\.git {
        deny all;
    }

    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "upgrade-insecure-requests";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload";
    add_header Permissions-Policy "geolocation=(),midi=(),sync-xhr=(),microphone=(),camera=(),magnetometer=(),gyroscope=(),fullscreen=(self),payment=()";
    proxy_hide_header X-Runtime;
    proxy_hide_header X-Powered-By;
}
