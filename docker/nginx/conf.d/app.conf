# HTTP server - redirect to HTTPS
server {
    listen 80;
    server_name localhost;
    server_tokens off;

    # Redirect all HTTP requests to HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS server with HTTP/2
server {
    listen 443 ssl http2;
    server_name localhost;
    server_tokens off;

    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/nginx.crt;
    ssl_certificate_key /etc/nginx/ssl/nginx.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # HTTP/2 and performance optimizations
    client_max_body_size 200M;
    client_body_timeout 300s;
    client_header_timeout 300s;
    keepalive_timeout 300s;
    send_timeout 300s;

    # Buffer sizes optimized for HTTP/2
    client_body_buffer_size 128k;
    client_header_buffer_size 4k;
    large_client_header_buffers 4 16k;

    # HTTP/2 specific settings
    http2_max_field_size 16k;
    http2_max_header_size 32k;
    http2_max_requests 1000;
    http2_recv_timeout 30s;

    server_name localhost;
    root   /var/www/html/sismonp3tgai;


    # File serving optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    output_buffers 2 512k;
    postpone_output 1460;

    # Gzip compression for HTTP/2
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    autoindex off;

    # Block potentially dangerous files except PHP
    location ~* \.(sh|py|pl|cgi)$ {
        deny all;
    }

    index index.php index.html index.htm;

    # Rate limiting for login attempts
    location ~* ^/(login|auth) {
        limit_req zone=login burst=3 nodelay;
        try_files $uri $uri/ /index.php$is_args$args;
    }

    # Rate limiting for API endpoints
    location ~* ^/api/ {
        limit_req zone=api burst=20 nodelay;
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location = / {
        if ($args ~* "(union.*select|<script>|benchmark\(|sleep\(|base64_decode|etc/passwd)") {
            return 301 /trap.html;
        }
        try_files $uri $uri/ /index.php?$args;
    }

    location = /trap.html {
        return 200 "<html><body>Nothing here</body></html>";
        add_header Content-Type text/html;
    }

    location ~* /(gbo303|judi|shell|wp-class|moon|install)\.php$ {
        deny all;
    }

    location ~* \.(git|env|svn|htaccess|bash_history|DS_Store) {
        deny all;
    }

    location ~* /(uploads|berita)/judi/ {
        deny all;
    }

    location ~* ^/(wp-admin|wp-login\.php|wp-includes|wlwmanifest\.xml|xmlrpc\.php|readme\.html) {
        return 444;
    }

    location ~* "^/(wp-content|wp-json|wp1|wp2|blog|site|cms|test|old|202[0-5])/" {
        return 444;
    }

    if ($http_user_agent ~* "(sqlmap|nikto|acunetix|nmap|curl|wget|python-requests|httpclient)") {
        return 444;
    }

    # Static files caching and optimization
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        access_log off;

        # HTTP/2 Server Push for critical resources
        location ~* \.(css)$ {
            add_header Link "</assets/vendor/bootstrap/css/bootstrap.min.css>; rel=preload; as=style" always;
        }

        location ~* \.(js)$ {
            add_header Link "</assets/vendor/jquery/jquery.min.js>; rel=preload; as=script" always;
        }
    }

    # Main location block
    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    error_page 503 @temporarily_unavailable;

    location @temporarily_unavailable {
        return 503 "Service temporarily unavailable. Please try again in a few moments.";
        add_header Content-Type text/plain;
        add_header Retry-After 30;
    }

    location = /50x.html {
        root /var/www/html;
    }

    location ~ /\. {
        deny all;
    }

    location ~* /uploads/.*\.php$ {
        deny all;
    }

    location /uploads {
        if ($args != "") {
            return 301 /trap.html;
        }

        location ~* \.(jpg|jpeg|png|pdf|ico|woff|woff2|ttf|eot|svg)$ {
            expires max;
            log_not_found off;
        }

        location ~* \.php$ {
            deny all;
        }
    }

    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass app:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;

        # Optimized for HTTP/2
        fastcgi_read_timeout 3600;
        fastcgi_buffer_size 256k;
        fastcgi_buffers 8 256k;
        fastcgi_busy_buffers_size 512k;
        fastcgi_temp_file_write_size 512k;
        fastcgi_max_temp_file_size 0;

        # Environment and status
        fastcgi_param CI_ENV development;
        fastcgi_param REDIRECT_STATUS 200;
        fastcgi_param HTTP_SCHEME $scheme;
        fastcgi_param HTTP_HOST $host;

        # HTTP/2 optimizations
        fastcgi_keep_conn on;
    }

    location ~ /\.git {
        deny all;
    }

    # Security headers optimized for HTTP/2
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "upgrade-insecure-requests" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header Permissions-Policy "geolocation=(),midi=(),sync-xhr=(),microphone=(),camera=(),magnetometer=(),gyroscope=(),fullscreen=(self),payment=()" always;

    # HTTP/2 specific headers
    add_header Alt-Svc 'h2=":80"' always;
    add_header Vary "Accept-Encoding" always;

    # Performance headers
    add_header X-Cache-Status $upstream_cache_status always;

    # Hide server information
    proxy_hide_header X-Runtime;
    proxy_hide_header X-Powered-By;
}
