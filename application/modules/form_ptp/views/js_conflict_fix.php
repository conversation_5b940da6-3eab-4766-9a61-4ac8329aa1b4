<script type="text/javascript">
/**
 * JavaScript Conflict Resolution for Form_PTP Module
 * Fixes modal and library conflicts when navigating between modules
 */

// Namespace for form_ptp to prevent conflicts
var FormPTP = FormPTP || {};

// Store original jQuery if it exists
FormPTP.originalJQuery = window.jQuery;

// Conflict resolution function
FormPTP.resolveConflicts = function() {
    // Clear any existing modal backdrops
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    
    // Reset modal z-index issues
    $('.modal').css('z-index', '');
    
    // Clear any hanging event handlers
    $(document).off('.modal');
    
    // Reinitialize Bootstrap modals with unique namespace
    $('.modal').each(function() {
        var $modal = $(this);
        var modalId = $modal.attr('id');
        
        // Add module prefix to prevent ID conflicts
        if (modalId && !modalId.startsWith('formptp_')) {
            $modal.attr('id', 'formptp_' + modalId);
        }
        
        // Reinitialize modal
        $modal.modal('dispose').modal({
            backdrop: 'static',
            keyboard: false
        });
    });
    
    // Fix DataTables conflicts
    if ($.fn.DataTable) {
        // Clear existing DataTables
        $('.dataTable').each(function() {
            if ($.fn.DataTable.isDataTable(this)) {
                $(this).DataTable().destroy();
            }
        });
    }
    
    // Clear any existing tooltips/popovers
    $('[data-toggle="tooltip"]').tooltip('dispose');
    $('[data-toggle="popover"]').popover('dispose');
    
    // Clear any existing select2 instances
    if ($.fn.select2) {
        $('.select2-hidden-accessible').select2('destroy');
    }
};

// Auto-resolve conflicts when page loads
$(document).ready(function() {
    FormPTP.resolveConflicts();
    
    // Monitor for modal events and resolve conflicts
    $(document).on('show.bs.modal', '.modal', function() {
        // Ensure only one modal is open at a time
        $('.modal').not(this).modal('hide');
        
        // Fix backdrop issues
        setTimeout(function() {
            if ($('.modal-backdrop').length > 1) {
                $('.modal-backdrop').not(':last').remove();
            }
        }, 100);
    });
    
    // Clean up when modal is hidden
    $(document).on('hidden.bs.modal', '.modal', function() {
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();
    });
});

// Function to safely initialize form elements
FormPTP.initializeFormElements = function() {
    // Initialize select2 dropdowns
    if ($.fn.select2) {
        $('.select2').each(function() {
            if (!$(this).hasClass('select2-hidden-accessible')) {
                $(this).select2({
                    theme: 'bootstrap4',
                    width: '100%'
                });
            }
        });
    }
    
    // Initialize date pickers
    if ($.fn.datepicker) {
        $('.datepicker').each(function() {
            if (!$(this).hasClass('hasDatepicker')) {
                $(this).datepicker({
                    format: 'yyyy-mm-dd',
                    autoclose: true,
                    todayHighlight: true
                });
            }
        });
    }
    
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip({
        container: 'body'
    });
};

// Function to safely load province/kabupaten data
FormPTP.loadLocationData = function(provinceCode, year, targetElement) {
    if (!provinceCode || !year) {
        console.error('Province code and year are required');
        return;
    }
    
    // Validate inputs
    if (!/^[0-9]{2}$/.test(provinceCode)) {
        console.error('Invalid province code format');
        return;
    }
    
    if (!/^[0-9]{4}$/.test(year)) {
        console.error('Invalid year format');
        return;
    }
    
    var url = '<?php echo base_url(); ?>form_ptp/getKab/' + provinceCode + '/' + year;
    
    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'json',
        beforeSend: function() {
            $(targetElement).html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</div>');
        },
        success: function(response) {
            if (response.error) {
                $(targetElement).html('<div class="alert alert-danger">' + response.error + '</div>');
            } else if (response.html) {
                $(targetElement).html(response.html);
            } else {
                $(targetElement).html('<div class="alert alert-warning">No data available</div>');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading location data:', error);
            $(targetElement).html('<div class="alert alert-danger">Error loading data</div>');
        }
    });
};

// Function to safely submit forms
FormPTP.submitForm = function(formId, callback) {
    var $form = $('#' + formId);
    
    if ($form.length === 0) {
        console.error('Form not found: ' + formId);
        return;
    }
    
    // Prevent double submission
    if ($form.data('submitting')) {
        return false;
    }
    
    // Validate required fields
    var isValid = true;
    $form.find('[required]').each(function() {
        if (!$(this).val()) {
            $(this).addClass('is-invalid');
            isValid = false;
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    if (!isValid) {
        alert('Please fill in all required fields');
        return false;
    }
    
    $form.data('submitting', true);
    var $submitBtn = $form.find('button[type="submit"]');
    var originalText = $submitBtn.text();
    
    $submitBtn.prop('disabled', true).text('Processing...');
    
    $.ajax({
        url: $form.attr('action'),
        type: $form.attr('method') || 'POST',
        data: $form.serialize(),
        dataType: 'json',
        success: function(response) {
            if (typeof callback === 'function') {
                callback(response);
            }
        },
        error: function(xhr, status, error) {
            console.error('Form submission error:', error);
            alert('Error submitting form: ' + error);
        },
        complete: function() {
            $form.data('submitting', false);
            $submitBtn.prop('disabled', false).text(originalText);
        }
    });
};

// Error handler for AJAX requests
FormPTP.handleAjaxError = function(xhr, status, error) {
    console.error('AJAX Error:', status, error);
    
    if (xhr.status === 403) {
        alert('Access denied. Please check your permissions.');
    } else if (xhr.status === 404) {
        alert('Resource not found.');
    } else if (xhr.status === 500) {
        alert('Server error. Please try again later.');
    } else {
        alert('An error occurred: ' + error);
    }
};

// Setup global AJAX error handler
$(document).ajaxError(function(event, xhr, settings, error) {
    FormPTP.handleAjaxError(xhr, 'error', error);
});

// Setup CSRF token for all AJAX requests
$(document).ajaxSend(function(event, xhr, settings) {
    if (settings.type === 'POST') {
        var csrfName = '<?php echo $this->security->get_csrf_token_name(); ?>';
        var csrfHash = '<?php echo $this->security->get_csrf_hash(); ?>';
        
        if (settings.data) {
            settings.data += '&' + csrfName + '=' + csrfHash;
        } else {
            settings.data = csrfName + '=' + csrfHash;
        }
    }
});

// Initialize form elements when document is ready
$(document).ready(function() {
    FormPTP.initializeFormElements();
});

// Export to global scope for compatibility
window.FormPTP = FormPTP;

</script>
