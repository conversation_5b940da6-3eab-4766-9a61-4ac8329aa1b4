<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<style>
   /* body {
      font-family: 'Montserrat', sans-serif;
      margin: 0;
   } */
   table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
      font-size: 14px;
      min-width: 400px;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
   }
   thead {
      background-color: #353c4e; /* Navy color */
      color: #ffffff;
      text-align: center;
      font-weight: bold;
   }
   th, td {
      padding: 12px 15px;
      border: 1px solid #dddddd;
   }
   tbody tr {
      border-bottom: 1px solid #dddddd;
   }
   tbody tr:nth-of-type(even) {
      background-color: #f3f3f3;
   }
   tbody tr:last-of-type {
      border-bottom: 2px solid #353c4e;
   }
   tbody tr:hover {
      background-color: #f1f1f1;
   }
   .btn {
      font-family: 'Montserrat', sans-serif;
      padding: 10px 20px;
      margin: 10px 0;
      border: none;
      background-color: #009879;
      color: white;
      cursor: pointer;
      transition: background-color 0.3s ease;
   }
   .btn:hover {
      background-color: #007f67;
   }
</style>
<div class="page-body">
   <!-- Server Side Processing table start -->
   <div class="card">
      <div class="card-header">
         <h5><?=$title;?></h5>
      </div>
      <div class="card-block">
         <div class="row">
            <div class="col-sm-3">
               <div class="form-group">
                  <label class="control-label">BBWS</label>
                  <select class="form-control" id="bbws_filter" name="bbws_filter" size="1" onchange="change_filter('bbws_filter',this);">
                  </select>
               </div>
            </div>
            <div class="col-sm-3">
               <div class="form-group">
                  <label class="control-label">Provinsi</label>
                  <select class="form-control" id="prov_filter" name="prov_filter" size="1" onchange="change_filter('prov_filter',this);">
                  </select>
               </div>
            </div>
            <div class="col-sm-12">
               <button onclick="download_report()" class="btn btn-success">Download Excel</button>
            </div>
         </div>
         <br/>
         <div class="alert alert-success col-sm-6" style="display:none;">
            <div id="text_i"></div>
         </div>
         <div class="dt-responsive table-responsive">
            <table>
               <thead>
               <thead>
    <tr height="16" class="xl68" style='height:12.00pt;background:#353c4e;'>
    <td height="80" width="64" rowspan="4" style='height:60.00pt;border-bottom:.5pt solid windowtext;' x:str>No</td>
    <td width="300" rowspan="4" style='border-bottom:.5pt solid windowtext;' x:str>Nama BBWS/BWS</td>
    <td width="300" rowspan="4" style='border-bottom:.5pt solid windowtext;' x:str>Nama Provinsi</td>
    <td width="222" rowspan="4" style='border-bottom:.5pt solid windowtext;' x:str>Penerima P3-TGAI</td>
    <td width="500" colspan="3" rowspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Alokasi Anggaran (Rp.)</td>
    <td width="400" rowspan="4" style='border-bottom:.5pt solid windowtext;' x:str>Sosialisasi Tingkat Penerima P3-TGAI (Lokasi)</td>
    <td width="275" rowspan="4" style='border-bottom:.5pt solid windowtext;' x:str>Musyawarah Desa I (Lokasi)</td>
    <td width="450" rowspan="4" style='border-bottom:.5pt solid windowtext;' x:str>Penetapan P3A/GP3A/IP3A penerima P3-TGAI (Lokasi)</td>
    <td width="275" rowspan="4" style='border-bottom:.5pt solid windowtext;' x:str>Musyawarah Desa II (Lokasi)</td>
    <td width="400" colspan="2" rowspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Tandatangan Perjanjian Kerja Sama (PKS)</td>
    <td width="2500" colspan="8" style='border-bottom:.5pt solid windowtext;' x:str>Realisasi Pencairan Dana P3-TGAI</td>
    <td width="300" rowspan="4" style='border-bottom:.5pt solid windowtext;' x:str>Capaian Progres Fisik (%)</td>
    <td width="650" colspan="2" rowspan="2" x:str>TARGET PENYERAPAN TENAGA KERJA DALAM RKP3A/GP3A/IP3A</td>
    <td width="2800" colspan="7" style='border-bottom:.5pt solid windowtext;' x:str>REALISASI PENYERAPAN TENAGA KERJA</td>
    <!-- <td width="294" colspan="3" style='border-bottom:.5pt solid windowtext;' x:str>Tenaga Kerja Terdampak COVID-19 (Orang)</td> -->
    <td width="275" rowspan="4" style='border-bottom:.5pt solid windowtext;' x:str>Musyawarah Desa III (Lokasi)</td>
    <td width="475" rowspan="4" style='border-bottom:.5pt solid windowtext;' x:str>Penyerahan Hasil Pekerjaan P3-TGAI ke PPK (Lokasi)</td>
    <td width="500" rowspan="4" style='border-bottom:.5pt solid windowtext;' x:str>Penyerahan Hasil Pekerjaan P3-TGAI dari PPK ke Kasatker</td>
    <td width="600" rowspan="4" style='border-bottom:.5pt solid windowtext;' x:str>Penyerahan Hasil Pekerjaan P3-TGAI dari Kasatker ke Pemerintah Desa</td>
    <td width="425" rowspan="4" style='border-bottom:.5pt solid windowtext;' x:str>Lokasi Yang Sudah Selesai / Progres 100% (Lokasi)</td>
    <!-- <td class="xl68" width="64" style=''></td>
    <td class="xl68" width="64" style=''></td>
    <td class="xl68" width="64" style=''></td> -->
   </tr>
   <tr height="16" class="xl68" style='height:12.00pt;background:#353c4e;'>
    <td width="600" colspan="4" style='border-bottom:.5pt solid windowtext;' x:str>Dana Fisik P3TGAI</td>
    <td colspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Pendampingan P3-TGAI</td>
    <td colspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Total</td>
    <td colspan="6" width="400" style='border-bottom:.5pt solid windowtext;' x:str>Penyerapan Tenaga Kerja</td>
    <td  width="600" rowspan="3" style='border-bottom:.5pt solid windowtext;' x:str>Realisasi Alokasi Dana Untuk Tenaga Kerja (Rp.)</td>
    <!-- <td rowspan="3" style='border-bottom:.5pt solid windowtext;' x:str>Buruh</td>
    <td rowspan="3" style='border-bottom:.5pt solid windowtext;' x:str>Terkena PHK/ Kehilangan Kerjaan</td>
    <td rowspan="3" style='border-bottom:.5pt solid windowtext;' x:str>TKI</td> -->
    <!-- <td class="xl68" colspan="3" style='mso-ignore:colspan;'></td> -->
   </tr>
   <tr height="16" class="xl68" style='height:12.00pt;background:#353c4e;'>
    <td width="250" rowspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Dana Fisik P3TGAI</td>
    <td width="250" rowspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Pendampingan</td>
    <td width="250" rowspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Total</td>
    <td rowspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Total (Lokasi)</td>
    <td rowspan="2"  width="128" style='border-bottom:.5pt solid windowtext;' x:str>Nilai PKS (Rp.)</td>
    <td rowspan="2" width="350" style='border-bottom:.5pt solid windowtext;' x:str>Pencairan Tahap I (Rp.)</td>
    <td rowspan="2" width="350" style='border-bottom:.5pt solid windowtext;' x:str>Pencairan Tahap II (Rp.)</td>
    <td rowspan="2" width="350" style='border-bottom:.5pt solid windowtext;' x:str>Total (Rp.)</td>
    <td rowspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Capaian Prog. (%)</td>
    <td rowspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Realisasi (Rp.)</td>
    <td rowspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Capaian Progres (%)</td>
    <td rowspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Realisasi (Rp.)</td>
    <td rowspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Capaian Progres Keuangan Total (%)</td>
    <td rowspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Tenaga Kerja (HOK)</td>
    <td rowspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Alokasi Dana Untuk Tenaga Kerja (Rp.)</td>
    <td colspan="3" style='border-bottom:.5pt solid windowtext;' x:str>Jumlah Tenaga Kerja (Orang)</td>
    <td width="250" rowspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Rerata Hari Kerja (hari)</td>
    <td class="xl71" colspan="2" style='border-bottom:.5pt solid windowtext;' x:str>Total HOK</td>
    <!-- <td class="xl68" colspan="3" style='mso-ignore:colspan;'></td> -->
   </tr>
   <tr height="32" class="xl68" style='height:24.00pt;background:#353c4e;'>
    <td x:str>Laki-laki</td>
    <td x:str>Perempuan</td>
    <td x:str>Jumlah</td>
    <td x:str>Realisasi HOK</td>
    <td x:str>Capaian (%)</td>
    <!-- <td class="xl68" colspan="3" style='mso-ignore:colspan;'></td> -->
   </tr>
               </thead>
               <tbody id="isi_tabel">
                  <!-- Table data -->
               </tbody>
            </table>
         </div>
      </div>
   </div>
   <!-- Server Side Processing table end -->
</div>
<?php echo $modal_tambah; ?>
<?php /* echo $modal_download; */ ?>
<?php echo $jv_script; ?>