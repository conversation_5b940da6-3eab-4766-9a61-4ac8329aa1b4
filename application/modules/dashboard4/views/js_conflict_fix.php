<script type="text/javascript">
/**
 * JavaScript Conflict Resolution for Dashboard4 Module
 * Fixes modal and library conflicts when navigating between modules
 */

// Namespace for dashboard4 to prevent conflicts
var Dashboard4 = Dashboard4 || {};

// Store original jQuery if it exists
Dashboard4.originalJQuery = window.jQuery;

// Conflict resolution function
Dashboard4.resolveConflicts = function() {
    // Clear any existing modal backdrops
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    
    // Reset modal z-index issues
    $('.modal').css('z-index', '');
    
    // Clear any hanging event handlers
    $(document).off('.modal');
    
    // Reinitialize Bootstrap modals with unique namespace
    $('.modal').each(function() {
        var $modal = $(this);
        var modalId = $modal.attr('id');
        
        // Add module prefix to prevent ID conflicts
        if (modalId && !modalId.startsWith('dashboard4_')) {
            $modal.attr('id', 'dashboard4_' + modalId);
        }
        
        // Reinitialize modal
        $modal.modal('dispose').modal({
            backdrop: 'static',
            keyboard: false
        });
    });
    
    // Fix DataTables conflicts
    if ($.fn.DataTable) {
        // Clear existing DataTables
        $('.dataTable').each(function() {
            if ($.fn.DataTable.isDataTable(this)) {
                $(this).DataTable().destroy();
            }
        });
    }
    
    // Clear any existing tooltips/popovers
    $('[data-toggle="tooltip"]').tooltip('dispose');
    $('[data-toggle="popover"]').popover('dispose');
};

// Auto-resolve conflicts when page loads
$(document).ready(function() {
    Dashboard4.resolveConflicts();
    
    // Monitor for modal events and resolve conflicts
    $(document).on('show.bs.modal', '.modal', function() {
        // Ensure only one modal is open at a time
        $('.modal').not(this).modal('hide');
        
        // Fix backdrop issues
        setTimeout(function() {
            if ($('.modal-backdrop').length > 1) {
                $('.modal-backdrop').not(':last').remove();
            }
        }, 100);
    });
    
    // Clean up when modal is hidden
    $(document).on('hidden.bs.modal', '.modal', function() {
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();
    });
});

// Function to safely load external modals
Dashboard4.loadExternalModal = function(url, modalId, callback) {
    // Clear existing modal content
    $('#' + modalId).remove();
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    
    // Load new modal content
    $.get(url, function(data) {
        $('body').append(data);
        
        // Initialize the new modal
        $('#' + modalId).modal({
            backdrop: 'static',
            keyboard: false
        });
        
        if (typeof callback === 'function') {
            callback();
        }
    }).fail(function() {
        console.error('Failed to load modal from: ' + url);
        alert('Error loading modal content');
    });
};

// Function to safely navigate between modules
Dashboard4.navigateToModule = function(moduleUrl) {
    // Clean up current page
    Dashboard4.resolveConflicts();
    
    // Clear any timers or intervals
    if (window.dashboardTimers) {
        window.dashboardTimers.forEach(function(timer) {
            clearInterval(timer);
            clearTimeout(timer);
        });
        window.dashboardTimers = [];
    }
    
    // Navigate to new module
    window.location.href = moduleUrl;
};

// Error handler for AJAX requests
Dashboard4.handleAjaxError = function(xhr, status, error) {
    console.error('AJAX Error:', status, error);
    
    if (xhr.status === 403) {
        alert('Access denied. Please check your permissions.');
    } else if (xhr.status === 404) {
        alert('Resource not found.');
    } else if (xhr.status === 500) {
        alert('Server error. Please try again later.');
    } else {
        alert('An error occurred: ' + error);
    }
};

// Setup global AJAX error handler
$(document).ajaxError(function(event, xhr, settings, error) {
    Dashboard4.handleAjaxError(xhr, 'error', error);
});

// Setup CSRF token for all AJAX requests
$(document).ajaxSend(function(event, xhr, settings) {
    if (settings.type === 'POST') {
        var csrfName = '<?php echo $this->security->get_csrf_token_name(); ?>';
        var csrfHash = '<?php echo $this->security->get_csrf_hash(); ?>';
        
        if (settings.data) {
            settings.data += '&' + csrfName + '=' + csrfHash;
        } else {
            settings.data = csrfName + '=' + csrfHash;
        }
    }
});

// Prevent multiple form submissions
Dashboard4.preventDoubleSubmit = function() {
    $('form').on('submit', function() {
        var $form = $(this);
        var $submitBtn = $form.find('button[type="submit"], input[type="submit"]');
        
        if ($form.data('submitted')) {
            return false;
        }
        
        $form.data('submitted', true);
        $submitBtn.prop('disabled', true);
        
        // Re-enable after 3 seconds as fallback
        setTimeout(function() {
            $form.data('submitted', false);
            $submitBtn.prop('disabled', false);
        }, 3000);
    });
};

// Initialize double submit prevention
$(document).ready(function() {
    Dashboard4.preventDoubleSubmit();
});

// Export to global scope for compatibility
window.Dashboard4 = Dashboard4;

</script>
