<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<style type="text/css">
    fieldset.scheduler-border {
        background: #f4f4f4;
        box-shadow: 0px 5px 4px 5px black;
        border: 1px groove #ddd !important;
        padding: 0 1.4em 1.4em 1.4em !important;
        margin: 0 0 1.5em 0 !important;
        -webkit-box-shadow:   0px 2px 2px 2px #eaeaea;
        box-shadow: 0px 2px 2px 2px #eaeaea;

                border-radius: 0px 0px 10px 10px;

    }

    fieldset .form-group > select{
        font-weight: normal !important;
    background: #cadada !important;
    color: #000 !important;
    font-weight: bold;
    }

    legend.scheduler-border {
        font-size: 14px;
        font-weight: bold;
        width:inherit; /* Or auto */
        padding:0 10px; /* To give a bit of padding on the left and right */
        border-bottom:none;
    }

    .modal-confirm {
        color: #434e65;
        width: 525px;
    }
    .modal-confirm .modal-content {
        padding: 20px;
        font-size: 16px;
        border-radius: 5px;
        border: none;
    }
    .modal-confirm .modal-header {
        background: #47c9a2;
        border-bottom: none;
        position: relative;
        text-align: center;
        margin: -20px -20px 0;
        border-radius: 5px 5px 0 0;
        padding: 35px;
    }
    .modal-confirm h4 {
        text-align: center;
        font-size: 36px;
        margin: 10px 0;
    }
    .modal-confirm .form-control, .modal-confirm .btn {
        min-height: 40px;
        border-radius: 3px;
    }
    .modal-confirm .close {
        position: absolute;
        top: 15px;
        right: 15px;
        color: #fff;
        text-shadow: none;
        opacity: 0.5;
    }
    .modal-confirm .close:hover {
        opacity: 0.8;
    }
    .modal-confirm .icon-box {
        color: #fff;
        width: 95px;
        height: 95px;
        display: inline-block;
        border-radius: 50%;
        z-index: 9;
        border: 5px solid #fff;
        padding: 15px;
        text-align: center;
    }
    .modal-confirm .icon-box i {
        font-size: 64px;
        margin: -4px 0 0 -4px;
    }
    .modal-confirm.modal-dialog {
        margin-top: 80px;
    }
    .modal-confirm .btn {
        color: #fff;
        border-radius: 4px;
        background: #eeb711;
        text-decoration: none;
        transition: all 0.4s;
        line-height: normal;
        border-radius: 30px;
        margin-top: 10px;
        padding: 6px 20px;
        border: none;
    }
    .modal-confirm .btn:hover, .modal-confirm .btn:focus {
        background: #eda645;
        outline: none;
    }
    .modal-confirm .btn span {
        margin: 1px 3px 0;
        float: left;
    }
    .modal-confirm .btn i {
        margin-left: 1px;
        font-size: 20px;
        float: right;
    }
    .wrapper1, .wrapper2 { width: 100%; overflow-x: scroll; overflow-y: hidden; }
    .wrapper1 { height: 20px; }
    .wrapper2 {}
    .div1 { height: 20px; }
    .div2 { overflow: none; }
    #btspagu{
        color: white;
    }
    #boxx #btspagu{
        margin: 10px 20px ;
        text-align: center;

    }
    #box-bts-prov{
        background: #f5e7e7;
        padding: 4px;
    }
    #box-bts-output{
        background: #f5e7e7;
        padding: 4px;
    }

    .a{
        /*        background: #ffffff;*/
        background: url(<?= base_url(); ?>assets/img/loader.gif) center center no-repeat;
        color: #666666;
        position: fixed;
        height: 100%;
        width: 100%;
        z-index: 999999999;
        top: 0;
        left: 0;
        float: left;
        text-align: center;
        padding-top: 25%;
        -webkit-transition: opacity 1s ease-in-out;
        -moz-transition: opacity 1s ease-in-out;
        -ms-transition: opacity 1s ease-in-out;
        -o-transition: opacity 1s ease-in-out
    }
    .content{
        padding: 2px;
        height:40px ;
        margin-bottom:0px;
    }
    th {
        font-size: 13px !important;
        text-transform: none !important;
        text-align: center !important;
    }
    td{
        font-size: 11px !important;
    }
    .dataTables_length{
        display: block !important;
    }
    th td button{
        float: left;
        border-color: white;
    }
    #tlist_paket thead{
        background: #cadada6e ;
    }
    #tlist_paket th{
        height: 20px;
        padding: 8px;
    }
    #tlist_paket #pakets th{
        height: 20px;
        padding: 12px;
    }
    #tlist_paket #pakets{
        background: #cadada ;
    }
    .kolomjumlah{
        text-align: right;
    }
    #tlist_paket_wrapper input{
        height:25px;
        font-size: 12px;
    }
    #tlist_paket_wrapper select{
        height:35px;
        font-size: 10px;
    }
    .block-content{
        padding: 5px 20px 1px;
    }
    #xxx {
        /* width: 200px; /* width of container */
        height: 200px; /* height of container */
        object-fit: cover;
        border: 5px solid black;
    }
    #myModals{
        width: 95% !important;
    }

    .bootstrap-tagsinput .tag {color:#525252;}
    .label-kl{
        background:#FFA07A;
        color:black;
    }
    .label-akademisi{
        background:#52BE80;
        color:black;
    }
    .label-pemda{
        background:#a6cee3;
        color:black;
    }
    .label-dpr{
        background:#b2df8a;
        color:black;
    }
    .label-renstra{
        background:#cab2d6;
        color:black;
    }
    .label-eprog{
        background:#fb9a99;
        color:black;
    }
    .label-irms{
        background:#fdbf6f;
        color:black;
    }
    .label-sipro{
        background:#8dd3c7;
        color:black;
    }
    .label-irmsjln{
        background:#fdbf6f;
        color:black;
    }
    .label-irmsjem{
        background:#fcc956;
        color:black;
    }
    .label-diskresi{
        background:#ffeda0;
        color:black;
    }
    .label-dprd{
        background:#EDBB99;
        color:black;
    }
    .kolomvalid{
        display: none;
    }

    #ull .active a {
        filter: brightness(70%) !important;
/*        background-color: green !important;*/
    }

    /*    .notification {
            position: relative;
            display: inline-block;
        }

        .notification .badge {
            position: absolute;
            top: -10px;
            right: -10px;
            padding: 5px 10px;
            border-radius: 50%;
            background-color: red;
            color: white;
        }*/

    .badge {
        background: none;
        color: red;
        border: 1px solid white;
    }

    .point {
        pointer-events: none;
    }
</style>

<script type="text/javascript">
    var child_satker = "<?php echo $child_satker; ?>";
</script>

<!--<div class="content bg-gray-lighter">
    <?php
    // $url = $_SERVER['REQUEST_URI'];
    // $pecah = explode("/", $url);
    // $xxx = "'" . $pecah[6] . "'";
    //   echo count($pecah);
    //   print_r($pecah);
    // $pe = explode("?", $xxx);
    // $b=str_replace("'", '', $pe[0]);
    // $kuy = "'" . $b . "'";
    // $yyy = $this->session->users['id_user_group_real'];
    //     print_r($kuy);
    //     //        echo "<script>alert($kuy)</script>";
    //     //        die();
    // // $x=$this->db->get_where('module',array('url'=>$pecah[1]))->result_array();
    // $x = $this->db->query("SELECT kode_module from module WHERE url=" . $kuy . "")->row();
    //     $u = $this->db->query("SELECT id_user_group from group_modules WHERE kode_module=" . $x->kode_module . " and id_user_group=$yyy")->result_array();
    //     $y = count($u);
    ?>
    <div class="row items-push">
         <div class="col-sm-7">
            <h1 class="page-heading">
                &nbsp;
            </h1>
        </div>
        <div class="col-sm-5 text-left hidden-xs" style="margin-bottom:0px;">
            <ol class="breadcrumb push-10-t" style="font-size:20px;">
                <li>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo $title; ?></li>
                 <li><a class="link-effect" href="">Listing </a></li>
            </ol>
        </div>
    </div>
</div>-->
<div class="block col-sm-12">
 <!-- <img src="<?php /* echo base_url(); */ ?>uploads/internal.png" class="img-responsive"> -->
    <?php
    // if ($kdtahaps == $this->session->konfig_kd_tahapan) {
    //     $hidd = 'block';
    //     $display = 'none';
    // } else if ($kdtahaps === 'PPI') {
    //     $hidd = 'none';
    //     $display = 'block';
    //     // $th='<th>Kelengkapan Paket</th><th>Kelengkapan Detail</th>';
    // } else {
    //     $hidd = 'none';
    //     $display = 'none';
    // }

    // //if ($this->session->users['id_user_group_real'] != 99) {
    // if ($this->session->users['role'] === 'itjen') {

    // } else {
    //     $th = '<th>Validasi</th>';
    // }

    // if ($this->session->users['role'] === 'satkerfisik') {
    //     $hiddr = 'block';
    // } else if ($this->session->users['role'] === 'satkernonfisik') {
    //     if ($this->session->users['kode_satker'] === $this->session->users['kdinduk']){
    //         $hiddr = 'block';
    //     } else {
    //         $hiddr = 'none';
    //     }
    // } else {
    //     $hiddr = 'none';
    // }

    // //new
    // if ($this->session->konfig_kd_tahapan === 'PI') {
    //     $hidd = 'block';
    // } else {
    //     $hidd = 'none';
    // }

    // if ($this->session->users['role'] === 'pakln') {
    //     $sh = 'block';
    // } else {
    //     $sh = 'none';
    // }

    ?>
    <br>
    <div class="row" style="display:block">
    <div class="col-md-12">
    <fieldset class="scheduler-border">
    <legend class="scheduler-border">Filter</legend>
    <div class="control-group">
        <div class="col-md-3">
            <div class="form-group">
                <label class="control-label">Jalan Tol</label>
                <select class="form-control" id="ruas" name="ruas" size="1" onchange="change_filter('ruas',this);">
                </select>
            </div>
        </div>
        <div class="col-md-3" id="bujthide" style='display:none'>
            <div class="form-group">
                <label class="control-label">BUJT</label>
                <select class="form-control" id="bujt" name="bujt" size="1" onchange="change_filter('bujt',this);">
                </select>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label class="control-label">Jenis Bangunan</label>
                <select class="form-control" id="jns_bangun" name="jns_bangun" size="1" onchange="change_filter('jns_bangun',this);">
                </select>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label class="control-label">Kondisi</label>
                <select class="form-control" id="kondisi_filter" name="kondisi" size="1" onchange="change_filter('kondisi',this);">
                </select>
            </div>
        </div>
        <input type="hidden" value="<?php echo $kdtahaps; ?>" id="tap">
        </div>
</fieldset>
    </div>

    <div class="row">
        <div class="col-md-12 btn-group">
            <button class = "btn btn-default" onclick = "javascript:dtTambahRowPaket()" style="display:block; margin-top:10px;margin-left:20px;">
                <i class = "fa fa-plus" aria-hidden = "true"> </i>&nbsp; Tambah <?php echo $title; ?>
            </button>
            <button class = "btn btn-primary" onclick = "javascript:dtTambahRowPencatatan()" style="display:block; margin-top:10px;margin-left:20px;">
                <i class = "fa fa-plus" aria-hidden = "true"> </i>&nbsp; Form Rekap Pencatatan <?php echo $title; ?>
            </button>
            <!--button class = "btn notification" onclick = "javascript:checkTagging();" style="display:<?php echo $hiddr; ?>; margin-top:10px;margin-left:20px; background: #bae8e8;">
                Usulan Eksternal &nbsp; <span id="notiftag" class="badge badge-pill"></span>

            </button>
            <button class = "btn notification point" onclick = "" style="display:<?php echo $hiddr; ?>; margin-top:10px;margin-left:20px; background: #fafba4;">
                Jumlah Paket Yang Belum Di Tagging &nbsp; <span id="notifbelumtag" class="badge badge-pill"></span>
            </button-->
        </div>
    </div>

<!--        <button class = "btn label-dpr"  id="btnexport" onclick = "javascript:dtExportIndikatif()" style="display:<?php echo $display; ?>; margin-top:10px;margin-left:20px;">
            <i class = "fa fa-upload" aria-hidden = "true"> </i>&nbsp; Export Indikatif
        </button>-->

    <div class="block-content">
        <?php if ($this->session->flashdata('message') != "") { ?>
            <div class="alert alert-success" ><?php echo $this->session->flashdata('message'); ?></div>
        <?php } ?>
        <!--                //tlist_paket-->
        <table id="tlist_paket">
            <thead id="pakets">
                <tr>
                    <th>#</th>
                    <th>ID</th>
                    <th>Jalan Tol</th>
                    <th>BUJT</th>
                    <th>Bangunan</th>
                    <th>Kuantitas</th>
                    <!--th>Nilai Perolehan</th-->
                    <th>Kondisi</th>
                    <th>Foto Aset </th>
                    <th>Action</th>
                </tr>
            </thead>
        </table>
    </div>
    <div class="col-md-12" style="display:none;">
        <div class="form-group">
            <label for="exampleInput7">ID Provinsi</label>
            <input type="text" id="kd_prov_irmsv3" name="kd_prov_irmsv3" value="<?php echo $kd_prov_irmsv3; ?>"/>
        </div>
        <div class="form-group">
            <label for="exampleInput7">ID rProvinsi</label>
            <input type="text" id="id_rprov" name="id_rprov" value="<?php echo $id_rprov; ?>"/>
        </div>
    </div>
</div>
<div id="myModals" class="modal fade" role="dialog">
    <div class="modal-dialog" style="width:95%;">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
                <h4 class="modal-title">Data Tindak Lanjut Rujukan</h4>
            </div>

            <iframe id="iframefollow" src="" style="width:100%;border:0px;min-height:400px;"></iframe>


            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-bs-dismiss="modal">Close</button>
            </div>
        </div>

    </div>
</div>

<?php echo $modal_download; ?>
<?php // echo $modal_edit;   ?>
<?php echo $modal_tambah; ?>
<?php echo $modal_tambah_pencatatan; ?>
<?php echo $modal_detail; ?>
<?php echo $modal_history; ?>
<?php echo $modal_edit_paket; ?>
<?php echo $modal_edit_detail; ?>

<?php echo $modal_tambahr; ?>

<?php echo $modal_tambahj; ?>

<?php //echo $modal_tunggu; ?>

<?php // echo $modal_upload; ?>

<?php echo $modal_tagging; ?>
<?php echo $modal_view_paket; ?>
<?php echo $modal_view_detail; ?>

<script>
    var kd_tahapan = "<?php echo $kdtahaps; ?>";
    var id_pak = "<?php echo $idpak; ?>";

</script>
