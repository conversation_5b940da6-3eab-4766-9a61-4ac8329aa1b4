<?php

defined('BASEPATH') or exit('No direct script access allowed');
// ini_set('display_errors', 1);
// error_reporting(E_ALL);

include_once APPPATH . "/vendor/autoload.php";




class Persiapan extends MY_Controller
{

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http:
     * 	- or -
     * 		http:
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http:
     *
     * So any other public methods not prefixed with an underscore will
     * map to
     * @see https:
     */
    public function __construct()
    {

        parent::__construct();

        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();

        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }






    function page($tahaps = '', $idpak = '')
    {











        $data = array();























        $title = "Tahapan Persiapan P3-TGAI";



        $js_file = $this->load->view('persiapan/js_file', '', true);
        $modal_tambah = $this->load->view('persiapan/modal_tambah', '', true);
        $modal_download = $this->load->view('persiapan/modal_download', '', true);




        $data = array(/* "modal_filter" => $modal_filter, */
            "modal_tambah" => $modal_tambah,
            "modal_download" => $modal_download,
            "title" => $title,
            "jv_script" => $js_file
        );





        $this->template->set('title', $title);

        $this->template->load('default_layout', 'contents', 'index', $data);

    }

    public function index($tahaps = '', $idpak = '')
    {

        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }


        // header("Access-Control-Allow-Origin: *");
        $data = array();























        $title = "Tahapan Persiapan P3-TGAI";



        $js_file = $this->load->view('persiapan/js_file', '', true);
        $modal_tambah = $this->load->view('persiapan/modal_tambah', '', true);
        $modal_download = $this->load->view('persiapan/modal_download', '', true);




        $data = array(/* "modal_filter" => $modal_filter, */
            "modal_tambah" => $modal_tambah,
            "modal_download" => $modal_download,
            "title" => $title,
            "jv_script" => $js_file
        );



        $this->load->view('index', $data);


    }

    function bindWilayah($role, $iduser, $kat, $idgiat='')
    {

        // echo $kat; die();

        $yearnow = $this->session->users['tahun_p3tgai'];


        $wil = '';



        if ($kat == 'edit' || $kat == 'detail') {

            // if ($role == 7 || $role == 1) {

                $sDataSiap = "select kd_prov, kd_kabkot, kd_kec, kd_desa, kd_di from kegiatan_p3a where id_giat = ?";
                $rDataSiap = $this->db->query($sDataSiap, array($idgiat));

            // } else {
            //     $sDataSiap = "select kd_prov, kd_kabkot, kd_kec, kd_desa, kd_di from kegiatan_p3a where id_giat = ?";
            //     $rDataSiap = $this->db->query($sDataSiap, array($idgiat));
            // }

            foreach ($rDataSiap->result() as $rSiap) {

                $provSiap = $rSiap->kd_prov;
                $kabSiap = $rSiap->kd_kabkot;
                $kecSiap = $rSiap->kd_kec;
                $desaSiap = $rSiap->kd_desa;
                $diSiap = $rSiap->kd_di;
            }

        }



        // $qSatker = "select kd_satker, nm_satker from r_satker";
        // $rSatker = $this->db->query($qSatker);

        if ($role == 1 || $role == 5) {
            $q2Satker = "select kd_satker from kegiatan_p3a where id_giat= ?";
            $r2Satker = $this->db->query($q2Satker, array($idgiat));
        } else {
            // echo 'here'; die();
            $q2Satker = "select kd_satker from view_user_management where id_user = ?";
            $r2Satker = $this->db->query($q2Satker, array($iduser));
        }



        $selValue = $r2Satker->row()->kd_satker;




        if ($kat == 'edit' || $kat == 'detail') {
            if ($role == 7 || $role == 1) {
                $q2Prov = "select kd_prov from kegiatan_p3a where id_giat = ?";
                $r2Prov = $this->db->query($q2Prov, array($idgiat));

            } else {
                $q2Prov = "select kd_prov from view_user_management where id_user = ?";
                $r2Prov = $this->db->query($q2Prov, array($iduser));
            }
        } else {
            // echo 'here'; die();
            $q2Prov = "select kd_prov from view_user_management where id_user = ?";
            $r2Prov = $this->db->query($q2Prov, array($iduser));
        }







        $selValue2 = $r2Prov->row()->kd_prov;













        $qProv = "select kd_prov, nama_prov from v_r_satker_prov where kd_satker = ? and tahun = ?";
        $rProv = $this->db->query($qProv, array($selValue, $yearnow));







        $wil .= '<div class="form-group row">';
        $wil .= '<div class="col-sm-3 col-form-div">Provinsi</div>';
        $wil .= '<div class="col-sm-9">';
        $wil .= '<select  id="prov" name="prov" class="form-control bootstrap-select" data-live-search="true">';
        $wil .= '<option value="">--Pilih--</option>';
        foreach ($rProv->result() as $rsP) {
            if ($kat == 'edit' || $kat == 'detail') {
                $wil .= '<option value="' . $rsP->kd_prov . '"' . (($rsP->kd_prov == $provSiap) ? 'selected' : '') . '>' . $rsP->nama_prov . '</option>';

            } else {
                $wil .= '<option value="' . $rsP->kd_prov . '">' . $rsP->nama_prov . '</option>';

            }

        }

        $wil .= '</select>';
        $wil .= '</div>';
        $wil .= '</div>';

















        if ($kat == 'edit' || $kat == 'detail') {


            $sDataKab = "select kd_kabkot, nama_kabkot from aset_r_kabkota where kd_prov = ? and tahun = ?";
            $rKabSiap = $this->db->query($sDataKab, array($provSiap, $yearnow));

        }

        $wil .= '<div class="form-group row">';
        $wil .= '<div class="col-sm-3 col-form-div">Kabupaten</div>';
        $wil .= '<div class="col-sm-9">';
        $wil .= '<select id="kab" name="kab" class="form-control bootstrap-select" data-live-search="true">';
        $wil .= '<option value="">--Pilih--</option>';
        if ($kat == 'edit' || $kat == 'detail') {

            foreach ($rKabSiap->result() as $rsKb) {
                $wil .= '<option value="' . $rsKb->kd_kabkot . '"' . (($rsKb->kd_kabkot == $kabSiap) ? 'selected' : '') . '>' . $rsKb->nama_kabkot . '</option>';
            }
        }
        $wil .= '</select>';
        $wil .= '</div>';
        $wil .= '</div>';




















        if ($kat == 'edit' || $kat == 'detail') {

            $sDataKec = "select kd_camat, nama_camat from aset_r_kecamatan where kd_kabkot = ? and tahun = ?";
            $rKecSiap = $this->db->query($sDataKec, array($kabSiap, $yearnow));

        }

        $wil .= '<div class="form-group row">';
        $wil .= '<div class="col-sm-3 col-form-div">Kecamatan</div>';
        $wil .= '<div class="col-sm-9">';
        $wil .= '<select  id="kec" name="kec" class="form-control bootstrap-select" data-live-search="true">';
        $wil .= '<option value="">--Pilih--</option>';
        if ($kat == 'edit' || $kat == 'detail') {
            foreach ($rKecSiap->result() as $rsKc) {
                $wil .= '<option value="' . $rsKc->kd_camat . '"' . (($rsKc->kd_camat == $kecSiap) ? 'selected' : '') . '>' . $rsKc->nama_camat . '</option>';
            }
        }
        $wil .= '</select>';
        $wil .= '</div>';
        $wil .= '</div>';




















        if ($kat == 'edit' || $kat == 'detail') {

            $sDataDesa = "select kd_lurah, nama_lurah from aset_r_kelurahan where kd_camat = ? and tahun = ?";
            $rDesaSiap = $this->db->query($sDataDesa, array($kecSiap, $yearnow));

        }


        $wil .= '<div class="form-group row">';
        $wil .= '<div class="col-sm-3 col-form-div">Desa</div>';
        $wil .= '<div class="col-sm-9">';
        $wil .= '<select id="desa" name="desa" class="form-control bootstrap-select" data-live-search="true">';
        $wil .= '<option value="">--Pilih--</option>';
        if ($kat == 'edit' || $kat == 'detail') {
            foreach ($rDesaSiap->result() as $rsDs) {
                $wil .= '<option value="' . $rsDs->kd_lurah . '"' . (($rsDs->kd_lurah == $desaSiap) ? 'selected' : '') . '>' . $rsDs->nama_lurah . '</option>';
            }
        }
        $wil .= '</select>';
        $wil .= '</div>';
        $wil .= '</div>';












        echo $wil;




    }

    function multi_prov($kdprov = NULL)
    {













        if ($kdprov == NULL) {
            $setProv = NULL;
        } else {

            $setProv = 'and (';
            if (strpos($kdprov, ',') !== false) {
                $dtProv = explode(',', $kdprov);
                for ($i = 0; $i < count($dtProv); $i++) {
                    $setProv .= "kd_prov like '" . $dtProv[$i] . "' or ";
                }
                $setProv .= ' 1=2)';
            } else {
                $setProv = "and kd_prov='" . $kdprov . "'";
            }

            return $setProv;
        }

    }

    public function ssp_paket()
    {



        $yearnow = $this->session->users['tahun_p3tgai'];

        $kd_tahapan = $this->input->post('konfig_tahapan', TRUE);

        $id_user = $this->session->users['id_user'];
        $role = $this->session->users['id_user_group_real'];

        $roledesc = $this->session->users['role'];

        $satker = $this->session->users['kd_satker'];

        $kdprov = $this->session->users['kd_prov'];

        $status = $this->input->post('fs', TRUE);



























        $wh = "";
        if ($role == 4) {
            $wh = "tahun = $yearnow and kd_satker='$satker' and";
        } elseif ($role == 2) {
            if ($kdprov !== NULL) {
                if (strpos($kdprov, ',') !== false) {
                    $retProv = $this->multi_prov($kdprov);
                    $wh = "tahun = $yearnow and kd_satker='$satker' and id_sub_user_group=$id_user and";

                } else {

                    $wh = "tahun = $yearnow and kd_satker='$satker' and id_sub_user_group=$id_user and";
                }

            } else {

                $wh = "tahun = $yearnow and kd_satker='$satker' and id_sub_user_group=$id_user and";
            }
        } elseif ($role == 7) {
            $wh = "tahun = $yearnow and created_by=$id_user and";
        } elseif ($role == 1 || $role == 3 || $role == 5 || $role == 6) {
            $wh = "tahun = $yearnow and";
        }




        $table = 'v_all_kegiatan_opt';
        $primaryKey = 'id_giat';

        $columns = array(
            array('db' => 'id_giat', 'dt' => 0),
            array('db' => 'nm_satker', 'dt' => 1),
            array('db' => 'nama_prov', 'dt' => 2),
            array('db' => 'nama_kabkot', 'dt' => 3),
            array('db' => 'nama_camat', 'dt' => 4),
            array('db' => 'nama_lurah', 'dt' => 5),
            array('db' => 'nama_jnsp3a', 'dt' => 6),
            array('db' => 'nm_pppa', 'dt' => 7),
            array('db' => 'nm_tpm', 'dt' => 8),
            array('db' => 'valid_1_p1', 'dt' => 9),
            array('db' => 'valid_2_p1', 'dt' => 10),
            array('db' => 'valid_1_p2', 'dt' => 11),
            array('db' => 'valid_1_p3', 'dt' => 12),
            array('db' => 'valid_1_p4', 'dt' => 13),
            array('db' => 'kd_tahap', 'dt' => 14),
            array('db' => 'is_valid_1', 'dt' => 15),
            array('db' => 'is_valid_2', 'dt' => 16),
            array('db' => 'is_valid_3', 'dt' => 17),
            array('db' => 'is_valid_4', 'dt' => 18)


        );






        if ($status == "Belum Validasi") {

            $wh .= " (kd_tahap = '2') and (valid_1_p1 = 'Data Sudah Lengkap' and valid_2_p1 = 'Data Sudah Lengkap') and (is_valid_1 IS NULL)";
        } else if ($status == "Sudah Validasi") {

            $wh .= " (is_valid_1 IS NOT NULL and (valid_1_p1 != 'Data Belum Lengkap' and valid_2_p1 != 'Data Belum Lengkap'))";
        } else if ($status == "Belum Lengkap") {

            $wh .= " kd_tahap = '1' and (valid_1_p1 = 'Data Belum Lengkap' or valid_2_p1 = 'Data Belum Lengkap') and (is_valid_1 IS NULL or is_valid_1 IS NOT NULL)";
        } else if ($status == "" || empty($status) || $status == "Tampil Semua") {

            $wh .= " 'True'='True'";
        } else {
            $wh .= $wh;
        }

        datatable_ssp($table, $primaryKey, $columns, "$wh");












    }


    public function addform()
    {
        error_reporting(3);
        $role = $this->session->users['id_user_group_real'];
        $param = $this->input->post('formData', TRUE);
        $id = $this->input->post("formData")["id"];
        $data = [
            "ruas_id_ruas" => $this->input->post("formData")["id_ruas"],
            "aset_r_bujt_id_bujt" => $this->input->post("formData")["id_bujt"],
            "staa" => $this->input->post("formData")["staa"],
            "stae" => $this->input->post("formData")["stae"],
            "temuan" => $this->input->post("formData")["temuan"],
            "penanganan" => $this->input->post("formData")["penanganan"],
            "catatan" => $this->input->post("formData")["catatan"],
            "persiapan_1" => $this->input->post("formData")["persiapan_1"],
            "persiapan_2" => $this->input->post("formData")["persiapan_2"],

        ];



        $this->db->set('created_at', 'NOW()', FALSE);
        $this->db->set('updated_at', 'NOW()', FALSE);
        if ($role == 7) {
            $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
            $this->db->set('created_by', $this->session->users['id_user'], FALSE);
        }


        if ($id != '') {
            $this->db->where("id_periksa", $id);
            $dt = $this->db->update('persiapan', $data);

        } else {
            $dt = $this->db->insert('persiapan', $data);

        }
        if ($dt) {
            echo "sukses";
        } else {
            echo "gagal";
        }
    }
    function tampildata($tab = '', $colum = '', $val_colum = '')
    {
        try {
            // Set proper headers
            header('Content-Type: application/json');
            header('Access-Control-Allow-Origin: *');

            // Log the request for debugging
            log_message('debug', 'tampildata called with: tab=' . $tab . ', colum=' . $colum . ', val_colum=' . $val_colum);

            // Validate parameters
            if (empty($tab) || empty($colum) || empty($val_colum)) {
                echo json_encode(array('error' => 'Missing required parameters'));
                return;
            }

            // Clean the value
            $clean_val = str_replace('_', ' ', $val_colum);

            // Execute query with error handling
            $this->db->where($colum, $clean_val);
            $query = $this->db->get($tab);

            if ($this->db->error()['code'] != 0) {
                log_message('error', 'Database error in tampildata: ' . $this->db->error()['message']);
                echo json_encode(array('error' => 'Database error'));
                return;
            }

            $data = $query->result_array();

            // Log the result
            log_message('debug', 'tampildata returning ' . count($data) . ' records');

            echo json_encode($data);

        } catch (Exception $e) {
            log_message('error', 'Exception in tampildata: ' . $e->getMessage());
            echo json_encode(array('error' => 'Server error'));
        }
    }

    function cekChildMembers($id)
    {

        $this->db->select('id_sub_user_group');
        $this->db->where('id_giat', $id);
        $query = $this->db->get('v_check_data_members');


        return $query->row()->id_sub_user_group;


    }

    function getback_file($stat, $id_giat)
    {
        if ($stat == 'persiapan') {

            $kategori = array(
                'profil_p3_tgai' => 'profil_p3_tgai',
                'musdes1' => 'musyawarah_desa',
                'sosialisasi' => 'kehadiran_sosialisasi_tingkat_penerima_p3_tgai'
            );



            $this->db->select('created_by');
            $this->db->where('id_giat', $id_giat);
            $q = $this->db->get('kegiatan_p3a');
            $id_user = $q->row()->created_by;








            foreach ($kategori as $key => $val) {







                $nama_dir = FCPATH . 'uploads/' . $key . "/";
                $url_path = "/sismonp3tgai/uploads/" . $key;

                $fileContainName = '*' . $id_giat . '_' . $val . '*.*';
                $fileContainName2 = '*' . $id_user . '_' . $id_giat . '_' . $val . '*.*';





                chdir($nama_dir);
                $i = 0;
                $len = count(glob($fileContainName));

                foreach (glob($fileContainName) as $filename) {





                    $idgiatdok = explode('_', $filename)[2];
                    if ($id_giat === $idgiatdok) {



                    } else {


                        $this->db->where("id_giat", $id_giat);
                        $this->db->where("judul_dok", $val);
                        $dt = $this->db->delete('dok_kegiatanp3a');


                    }













                }


                foreach (glob($fileContainName2) as $filename2) {







                    if ($i == 0) {







                        $this->db->insert(
                            'dok_kegiatanp3a',
                            array(
                                'path' => $filename2,
                                'judul_dok' => $val,
                                'nm_dok' => $filename2,
                                'filename' => $filename2,
                                'filepath' => $url_path,
                                'id_giat' => $id_giat,
                                'created_by' => $id_user,
                                'updated_by' => $id_user
                            )
                        );
                    } elseif ($i == $len - 1) {





                        $this->db->insert(
                            'dok_kegiatanp3a',
                            array(
                                'path' => $filename2,
                                'judul_dok' => $val,
                                'nm_dok' => $filename2,
                                'filename' => $filename2,
                                'filepath' => $url_path,
                                'id_giat' => $id_giat,
                                'created_by' => $id_user,
                                'updated_by' => $id_user
                            )
                        );
                    }

                    $i++;
















                }

            }

        }

    }

    function update_data()
    {

        $id = $this->input->post('id');
        $stat=$this->input->post('stat');
        $onlyKMB = $this->input->post('mustKMB');

        $checkIDMembers = $this->cekChildMembers($id);
        if ($checkIDMembers == $onlyKMB) {
            $this->db->where('id_giat', $id);
            $res = $this->db->update('kegiatan_p3a', array('is_valid_1' => $stat));
            if ($res) {
                echo 0;
            } else {
                echo 1;
            }
        } else {
            echo 1;
        }

    }
    public function ajax_delete($id)
    {
        $id_user = $this->session->users['id_user'];
        $this->db->trans_start();


        $this->db->where("id_giat", $id);
        $this->db->delete('dok_kegiatanp3a');

        $this->db->where("id_giat", $id);
        $this->db->delete('kegiatan_p3a');


        $this->db->where("id_giat", $id);
        $this->db->delete('r_p3a');

        $this->db->where("id_giat", $id);
        $this->db->delete('r_tpm');





        $this->db->trans_complete();
        if ($this->db->trans_status() === FALSE) {
            # Something went wrong.
            $this->db->trans_rollback();
            echo "gagal";
            return FALSE;
        } else {

            $this->db->trans_commit();
            $z = "delete";
            echo 0;
            return TRUE;

        }





    }
    public function up()
    {
        $role = $this->session->users['id_user_group_real'];
        if (is_array($_FILES)) {
            $x = str_replace('-', '', date('Y-m'));
            $nama_dir = FCPATH . 'uploads/' . $x . "/";

            if (is_dir($nama_dir)) {

            } else {
                mkdir(FCPATH . 'uploads/' . $x, 0777, true);

            }
            $upload_path_url = str_replace('-', '', date('Y-m'));
            if (is_uploaded_file($_FILES['filess']['tmp_name'])) {
                $sourcePath = $_FILES['filess']['tmp_name'];
                $namf = $_FILES['filess']['name'];
                $rep = str_replace(" ", "_", $namf);
                $fil = date('Ymd') . date("his") . $rep;
                $targetPath = FCPATH . "uploads/" . $upload_path_url . "/" . $fil;
                move_uploaded_file($sourcePath, $targetPath);



                $this->db->set('created_at', 'NOW()', FALSE);
                $this->db->set('updated_at', 'NOW()', FALSE);
                if ($role == 7) {
                    $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
                    $this->db->set('created_by', $this->session->users['id_user'], FALSE);
                }

                $res = $this->db->insert(
                    'dok_periksa',
                    array(
                        'path' => $fil,
                        'judul_dok' => $this->input->post('judul', TRUE),

                        'id_periksa' => $this->input->post('id_up', TRUE)
                    )
                );


            }
        }
    }
    public function ssp_attachment()
    {
        $id = $this->input->post("id", TRUE);
        $role = $this->input->post("role", TRUE);

        $table = 'dok_periksa';
        $primaryKey = 'id_dokperiksa';
        $columns = array(
            array('db' => 'path', 'dt' => 0),
            array('db' => 'id_dokperiksa', 'dt' => 1),
            array('db' => 'id_periksa', 'dt' => 2),
            array('db' => 'judul_dok', 'dt' => 3),
        );
        datatable_ssp($table, $primaryKey, $columns, "id_periksa=$id");
    }


    public function hps_lampiran($id)
    {

        $this->db->delete('dok_periksa', array('id_dokperiksa' => $id));

        echo json_encode(array("status" => TRUE));
    }

    private function imageCreateFromAny($filepath)
    {

        $type = exif_imagetype($filepath);

        $allowedTypes = array(

            1,

            2,

            3,

            6

        );

        if (!in_array($type, $allowedTypes)) {

            return false;

        }

        switch ($type) {

            case 1:

                $im = imageCreateFromGif($filepath);

                break;

            case 2:

                $im = imageCreateFromJpeg($filepath);


                break;

            case 3:

                $im = imageCreateFromPng($filepath);

                break;

            case 6:

                $im = imageCreateFromBmp($filepath);

                break;

        }

        return $im;

    }

    function upload_dt($folder, $tmp, $name, $judul, $id_giat, $kat, $id_user)
    {
        $role = $this->session->users['id_user_group_real'];
        $yearnow = $this->session->users['tahun_p3tgai'];


        $jmltxt = count(explode(".", $name));
        $format = strtolower(pathinfo($name, PATHINFO_EXTENSION));

        if (is_uploaded_file($tmp)) {






            $sourcePath = $tmp;
            $validImg = getimagesize($sourcePath);














            $rep = str_replace(" ", "_", $judul);


            if ($validImg == false) {





            } else {
                // $fil = date('Y') . "_" . $id_user . "_" . $id_giat . "_" . $rep . "." . $format;
                // $targetPath = FCPATH . "uploads/" . $folder . "/" . $fil;
                // $url_path = "/sismonp3tgai/uploads/" . $folder;


                // $fil = date('Ymd') . date("his") . "_" . $id_user . "_" . $id_giat . "_" . $rep . "." . $format;
                // $fil = date('Y') . "_" . $id_user . "_" . $id_giat . "_" . $rep . "." . $format;
                // // $folder = ($yearnow > 2023) ? $folder . "/" . $yearnow : $folder;
                // $targetPath = FCPATH . "uploads/" . $folder . "/" . $fil;
                // $url_path = "/sismonp3tgai/uploads/" . $folder;
                $putFile = true;
    //             if ($putFile) {

    // // ... (rest of the code remains the same)


    //                 // Check if the file already exists in the database
    //                 // $this->db->where('id_giat', $id_giat);
    //                 // $this->db->where('tahun', $yearnow);
    //                 // $this->db->where('nm_dok', $fil);
    //                 // $query = $this->db->get('dok_kegiatanp3a');
    //                 // $existing_file = $query->row();

    //                 // // print_r($existing_file); die();

    //                 // if ($existing_file) {
    //                 //     // File exists, update the file and unlink the old file

    //                 //     // echo 'update';
    //                 //     // $old_file_path = FCPATH . "uploads/" . $existing_file->judul_dok . "/" . $existing_file->filename;

    //                 //     $old_file_path = $existing_file->filepath."/".$existing_file->filename;

    //                 //     // echo $old_file_path; die();


    //                 //     if (file_exists($old_file_path)) {
    //                 //         unlink($old_file_path);
    //                 //     }

    //                 //     die();

    //                 //     $this->db->where('id_dok', $existing_file->id_dok);
    //                 //     // $this->db->where('id_giat', $id_giat);
    //                 //     $this->db->update('dok_kegiatanp3a', array(
    //                 //         'path' => $fil,
    //                 //         'nm_dok' => $fil,
    //                 //         'filename' => $fil,
    //                 //         'filepath' => $url_path,
    //                 //         'updated_at' => date("Y-m-d H:i:s"),
    //                 //         'updated_by' => $id_user
    //                 //     ));
    //                 // } else {
    //                 //     // File does not exist, insert a new record
    //                 //     // echo 'insert'; die();
    //                 //     $this->db->insert(
    //                 //         'dok_kegiatanp3a',
    //                 //         array(
    //                 //             'path' => $fil,
    //                 //             'judul_dok' => $judul,
    //                 //             'nm_dok' => $fil,
    //                 //             'filename' => $fil,
    //                 //             'filepath' => $url_path,
    //                 //             'id_giat' => $id_giat,
    //                 //             'created_at' => date("Y-m-d H:i:s"),
    //                 //             'created_by' => $id_user,
    //                 //             'updated_at' => date("Y-m-d H:i:s"),
    //                 //             'updated_by' => $id_user,
    //                 //             'tahun' => $yearnow
    //                 //         )
    //                 //     );
    //                 // }

    //                 move_uploaded_file($sourcePath, $targetPath);

    //                 $res = $this->db->insert(
    //                     'dok_kegiatanp3a',
    //                     array(
    //                         'path' => $fil,
    //                         'judul_dok' => $judul,
    //                         'nm_dok' => $fil,
    //                         'filename' => $fil,
    //                         'filepath' => $url_path,
    //                         'id_giat' => $id_giat,
    //                         'created_at' => date("Y-m-d H:i:s"),
    //                         'created_by' => $id_user,
    //                         'updated_at' => date("Y-m-d H:i:s"),
    //                         'updated_by' => $id_user,
    //                         'tahun' => $yearnow
    //                     )
    //                 );
    //             } else {

    //             }

            }


            if ($putFile) {
                $fil = date('Y') . "_" . $id_user . "_" . $id_giat . "_" . $rep . "." . $format;
                $targetPath = FCPATH . "uploads/" . $folder . "/" . $fil;
                $url_path = "/sismonp3tgai/uploads/" . $folder;

                // Handle edit case - delete old file and record
                if ($kat == 'edit') {
                    // Get existing record
                    $this->db->where('judul_dok', $judul);
                    $this->db->where('id_giat', $id_giat);
                    $this->db->where('created_by', $id_user);
                    $existing = $this->db->get('dok_kegiatanp3a')->row();

                    if ($existing) {
                        // Delete old file
                        $old_file = FCPATH . "uploads/" . $folder . "/" . $existing->path;
                        if (file_exists($old_file)) {
                            unlink($old_file);
                        }

                        // Delete old record
                        $this->db->where('judul_dok', $judul);
                        $this->db->where('id_giat', $id_giat);
                        $this->db->where('created_by', $id_user);
                        $this->db->delete('dok_kegiatanp3a');
                    }
                }

                // Upload new file and insert record
                move_uploaded_file($sourcePath, $targetPath);
                $res = $this->db->insert(
                    'dok_kegiatanp3a',
                    array(
                        'path' => $fil,
                        'judul_dok' => $judul,
                        'nm_dok' => $fil,
                        'filename' => $fil,
                        'filepath' => $url_path,
                        'id_giat' => $id_giat,
                        'created_at' => date("Y-m-d H:i:s"),
                        'created_by' => $id_user,
                        'updated_at' => date("Y-m-d H:i:s"),
                        'updated_by' => $id_user,
                        'tahun' => $yearnow
                    )
                );
            }
        }
    }

    function insert_data()
    {





        // Set content type for proper response handling
        header('Content-Type: text/plain');

        $yearnow = $this->session->users['tahun_p3tgai'];
        $role = $this->session->users['id_user_group_real'];
        $id_user = $this->session->users['id_user'];

        $no_p = $this->input->post('no_p', true);
        $tgl_pen = $this->input->post('tgl_pen', true);


        $id = $this->input->post('id', true, true);
        $kab = $this->input->post('kab', true);
        $kec = $this->input->post('kec', true);
        $desa = $this->input->post('desa', true);
        $jen_penerima = $this->input->post('jen_penerima', true);
        $nm_penerima = $this->input->post('nm_penerima', true);
        $j_legalitas = $this->input->post('j_legalitas', true);
        $t_legalitas = $this->input->post('t_legalitas', true);
        $nm_ketua = $this->input->post('nm_ketua', true);
        $ala_ketua = $this->input->post('ala_ketua', true);
        $no_ketua = $this->input->post('no_ketua', true);
        $no_npwp = $this->input->post('no_npwp', true);
        $file_npwp = $this->input->post('file_npwp', true);
        $nm_tpm = $this->input->post('nm_tpm', true);
        $jum_dp = $this->input->post('jum_dp', true);
        $j_kelamin = $this->input->post('j_kelamin', true);
        $lbp = $this->input->post('lbp', true);
        $ala_tpm = $this->input->post('ala_tpm', true);
        $tgl_pelaksana = $this->input->post('tgl_pelaksana', true);
        $jum_pes_l = $this->input->post('jum_pes_l', true);
        $jum_pes_p = $this->input->post('jum_pes_p', true);
        $jum_t = $this->input->post('jum_t', true);
        $jum_lan = $this->input->post('jum_lan', true);
        $nm_dae_irigasi = $this->input->post('nm_dae_irigasi', true);
        $j_irigasi = $this->input->post('j_irigasi', true);
        $k_irigasi = $this->input->post('k_irigasi', true);
        $jum_ket_l = $this->input->post('jum_ket_l', true);
        $jum_ket_p = $this->input->post('jum_ket_p', true);
        $jum_ket_pgrus = $this->input->post('jum_ket_pgrus', true);
        $jum_ben_p = $this->input->post('jum_ben_p', true);
        $jum_ben_l = $this->input->post('jum_ben_l', true);
        $jum_sek_l = $this->input->post('jum_sek_l', true);
        $jum_sek_p = $this->input->post('jum_sek_p', true);
        $jum_anggota = $this->input->post('jum_anggota', true);
        $jum_tim_per_l = $this->input->post('jum_tim_per_l', true);
        $jum_tim_per_p = $this->input->post('jum_tim_per_p', true);
        $jum_tim_total = $this->input->post('jum_tim_total', true);
        $jum_tim_pelak_l = $this->input->post('jum_tim_pelak_l', true);
        $jum_tim_pelak_p = $this->input->post('jum_tim_pelak_p', true);
        $jum_tim_pngws_l = $this->input->post('jum_tim_pngws_l', true);
        $jum_tim_pngws_p = $this->input->post('jum_tim_pngws_p', true);
        $jum_tim_pngws = $this->input->post('jum_tim_pngws', true);
        $tgl_pelak = $this->input->post('tgl_pelak', true);
        $jum_pese_l = $this->input->post('jum_pese_l', true);
        $jum_pese_p = $this->input->post('jum_pese_p', true);
        $jum_pese_lan = $this->input->post('jum_pese_lan', true);
        $F_NPWP = $this->input->post('F_profil_p3_tgai', true);
        $F_MUSYAWARAH_I = $this->input->post('F_musyawarah_desa', true);
        $F_SOSIALISASI = $this->input->post('F_kehadiran_sosialisasi_tingkat_penerima_p3_tgai', true);
        if ($role == 7) {
            $prov = $this->input->post('prov', true);
            $kd_satker = $this->session->users['kd_satker'];
        } else {
            $kd_satker = $this->input->post('bbws', true);
            $prov = $this->input->post('prov', true);
        }
        $kat1 = 'tambah';
        $kat2 = 'tambah';
        $kat3 = 'tambah';
        $data_kegiatan = array(
            'kd_satker' => ($kd_satker == '') ? NULL : $kd_satker,
            'kd_prov' => ($prov == '') ? NULL : $prov,
            'kd_kabkot' => ($kab == '') ? NULL : $kab,
            'kd_kec' => ($kec == '') ? NULL : $kec,
            'kd_desa' => ($desa == '') ? NULL : $desa,
            'tgl_sosialisasi' => ($tgl_pelaksana == '') ? NULL : $tgl_pelaksana,
            'sos_pria' => ($jum_pes_l == '') ? NULL : $jum_pes_l,
            'sos_wanita' => ($jum_pes_p == '') ? NULL : $jum_pes_p,
            'sos_lansia' => ($jum_lan == '') ? NULL : $jum_lan,
            'kd_di' => ($nm_dae_irigasi == '') ? NULL : $nm_dae_irigasi,

            'musy_desa' => ($tgl_pelak == '') ? NULL : $tgl_pelak,
            'musdes_pria' => ($jum_pese_l == '') ? NULL : $jum_pese_l,
            'musdes_wanita' => ($jum_pese_p == '') ? NULL : $jum_pese_p,
            'musdes_lansia' => ($jum_pese_lan == '') ? NULL : $jum_pese_lan,
            'no_pntp_p3a' => ($no_p == '') ? NULL : $no_p,
            'tgl_pntp_p3a' => ($tgl_pen == '') ? NULL : $tgl_pen
        );
        $data_p3a = array(
            'nm_pppa' => $nm_penerima,
            'id_jnsp3a' => ($jen_penerima == '') ? NULL : $jen_penerima,
            'id_jnslgl' => ($j_legalitas == '') ? NULL : $j_legalitas,
            'thn_berdiri' => ($t_legalitas == '') ? NULL : $t_legalitas,
            'nm_ketua' => ($nm_ketua == '') ? NULL : $nm_ketua,
            'alamat_ketua' => ($ala_ketua == '') ? NULL : $ala_ketua,
            'kontak_ketua' => ($no_ketua == '') ? NULL : $no_ketua,
            'npwp_p3a' => ($no_npwp == '') ? NULL : $no_npwp,


            'id_jnsdi' => ($j_irigasi == '') ? NULL : $j_irigasi,
            'id_kwdi' => ($k_irigasi == '') ? NULL : $k_irigasi,
            'jm_pengurus_pria' => ($jum_ket_l == '') ? NULL : $jum_ket_l,
            'jm_pengurus_wanita' => ($jum_ket_p == '') ? NULL : $jum_ket_p,
            'jm_bendahara_pria' => ($jum_ben_l == '') ? NULL : $jum_ben_l,
            'jm_bendahara_wanita' => ($jum_ben_p == '') ? NULL : $jum_ben_p,
            'jm_sekretaris_pria' => ($jum_sek_l == '') ? NULL : $jum_sek_l,
            'jm_sekretaris_wanita' => ($jum_sek_p == '') ? NULL : $jum_sek_p,
            'jml_anggota' => ($jum_anggota == '') ? NULL : $jum_anggota,
            'timsiap_pria' => ($jum_tim_per_l == '') ? NULL : $jum_tim_per_l,
            'timsiap_wanita' => ($jum_tim_per_p == '') ? NULL : $jum_tim_per_p,
            'timlak_pria' => ($jum_tim_pelak_l == '') ? NULL : $jum_tim_pelak_l,
            'timlak_wanita' => ($jum_tim_pelak_p == '') ? NULL : $jum_tim_pelak_p,
            'timwas_pria' => ($jum_tim_pngws_l == '') ? NULL : $jum_tim_pngws_l,
            'timwas_wanita' => ($jum_tim_pngws_p == '') ? NULL : $jum_tim_pngws_p,
        );
        $data_tpm = array(
            'nm_tpm' => ($nm_tpm == '') ? NULL : $nm_tpm,
            'jml_desa' => ($jum_dp == '') ? NULL : $jum_dp,
            'jns_kelamin' => ($j_kelamin == '') ? NULL : $j_kelamin,
            'pendidikan' => ($lbp == '') ? NULL : $lbp,
            'alamat_tpm' => $ala_tpm,
        );
        if (!isset($id) || $id == '' || empty($id) || $id == null) {
            $this->db->trans_start();






            $data_kegiatan['tahun'] = $yearnow;
            $data_kegiatan['created_by'] = $this->session->users['id_user'];
            $data_kegiatan['created_at'] = date("Y-m-d H:i:s");
            $this->db->insert('kegiatan_p3a', $data_kegiatan);

            $id_giat = $this->db->insert_id();

            if ($id_giat) {







                $data_p3a['tahun'] = $yearnow;
                $data_p3a['id_giat'] = $id_giat;
                $data_p3a['created_by'] = $this->session->users['id_user'];
                $data_p3a['created_at'] = date("Y-m-d H:i:s");


                $this->db->insert('r_p3a', $data_p3a);









                $data_tpm['tahun'] = $yearnow;
                $data_tpm['id_giat'] = $id_giat;
                $data_tpm['created_by'] = $this->session->users['id_user'];
                $data_tpm['created_at'] = date("Y-m-d H:i:s");
                $this->db->insert('r_tpm', $data_tpm);

                if (!file_exists($_FILES['file_npwp']['tmp_name']) || !is_uploaded_file($_FILES['file_npwp']['tmp_name'])) {

                } else {
                    $kat1 = 'edit';
                    $this->upload_dt('profil_p3_tgai', $_FILES['file_npwp']['tmp_name'], $_FILES['file_npwp']['name'], 'profil_p3_tgai', $id_giat, 'tambah', $id_user);
                }

                if (!file_exists($_FILES['fot_musya']['tmp_name']) || !is_uploaded_file($_FILES['fot_musya']['tmp_name'])) {

                } else {
                    $kat2 = 'edit';
                    $this->upload_dt('musdes1', $_FILES['fot_musya']['tmp_name'], $_FILES['fot_musya']['name'], 'musyawarah_desa', $id_giat, 'tambah', $id_user);
                }

                if (!file_exists($_FILES['fot_sosial']['tmp_name']) || !is_uploaded_file($_FILES['fot_sosial']['tmp_name'])) {

                } else {
                    $kat3 = 'edit';
                    $this->upload_dt('sosialisasi', $_FILES['fot_sosial']['tmp_name'], $_FILES['fot_sosial']['name'], 'kehadiran_sosialisasi_tingkat_penerima_p3_tgai', $id_giat, 'tambah', $id_user);
                }

















                # Everything is Perfect.
                # Committing data to the database.




                $this->db->where('id_giat', $id_giat);

                $cek = $this->db->get('v_all_kegiatan_opt')->row();
                $thp = 1;


                if ($cek->valid_1_p1 == 'Data Sudah Lengkap' && $cek->valid_2_p1 == 'Data Sudah Lengkap') {
                    $thp = 2;
                }
                if ($cek->valid_1_p1 == 'Data Sudah Lengkap' && $cek->valid_2_p1 == 'Data Sudah Lengkap' && $cek->valid_1_p2 == 'Data Sudah Lengkap') {
                    $thp = 3;
                }
                if ($cek->valid_1_p1 == 'Data Sudah Lengkap' && $cek->valid_2_p1 == 'Data Sudah Lengkap' && $cek->valid_1_p2 == 'Data Sudah Lengkap' && $cek->valid_1_p3 == 'Data Sudah Lengkap') {
                    $thp = 4;
                }

                $this->db->where('id_giat', $id_giat);

                $this->db->update('kegiatan_p3a', array('kd_tahap' => $thp));

                $this->db->trans_complete();


                if ($this->db->trans_status() === FALSE) {
                    # Something went wrong.
                    $this->db->trans_rollback();
                    echo "1_insert"; // Error response for insert
                } else {
                    $this->db->trans_commit();
                    echo "0_insert"; // Success response for insert
                }

            }




















        } else {



            $this->db->trans_start();











            $data_kegiatan['updated_by'] = $this->session->users['id_user'];
            $data_kegiatan['updated_at'] = date("Y-m-d H:i:s");
            $this->db->where('id_giat', $id);
            $this->db->update('kegiatan_p3a', $data_kegiatan);












            $data_p3a['updated_by'] = $this->session->users['id_user'];
            $data_p3a['updated_at'] = date("Y-m-d H:i:s");
            $this->db->where('id_giat', $id);
            $this->db->update('r_p3a', $data_p3a);












            $data_tpm['updated_by'] = $this->session->users['id_user'];
            $data_tpm['updated_at'] = date("Y-m-d H:i:s");
            $this->db->where('id_giat', $id);
            $this->db->update('r_tpm', $data_tpm);



















            if (!file_exists($_FILES['file_npwp']['tmp_name']) || !is_uploaded_file($_FILES['file_npwp']['tmp_name'])) {

            } else {
                $kat2 = 'tambah';
                $this->upload_dt('profil_p3_tgai', $_FILES['file_npwp']['tmp_name'], $_FILES['file_npwp']['name'], 'profil_p3_tgai', $id, $kat2, $id_user);
            }



            if (!file_exists($_FILES['fot_musya']['tmp_name']) || !is_uploaded_file($_FILES['fot_musya']['tmp_name'])) {

            } else {

                $kat1 = 'tambah';
                $this->upload_dt('musdes1', $_FILES['fot_musya']['tmp_name'], $_FILES['fot_musya']['name'], 'musyawarah_desa', $id, $kat1, $id_user);
            }



            if (!file_exists($_FILES['fot_sosial']['tmp_name']) || !is_uploaded_file($_FILES['fot_sosial']['tmp_name'])) {

            } else {
                $kat3 = 'tambah';
                $this->upload_dt('sosialisasi', $_FILES['fot_sosial']['tmp_name'], $_FILES['fot_sosial']['name'], 'kehadiran_sosialisasi_tingkat_penerima_p3_tgai', $id, $kat3, $id_user);
            }































            # Everything is Perfect.
            # Committing data to the database.




            $this->db->where('id_giat', $id);
            $cek = $this->db->get('v_all_kegiatan_opt')->row();
            $thp = 1;
            if ($cek->valid_1_p1 == 'Data Sudah Lengkap' && $cek->valid_2_p1 == 'Data Sudah Lengkap') {
                $thp = 2;
            }
            if ($cek->valid_1_p1 == 'Data Sudah Lengkap' && $cek->valid_2_p1 == 'Data Sudah Lengkap' && $cek->valid_1_p2 == 'Data Sudah Lengkap') {
                $thp = 3;
            }
            if ($cek->valid_1_p1 == 'Data Sudah Lengkap' && $cek->valid_2_p1 == 'Data Sudah Lengkap' && $cek->valid_1_p2 == 'Data Sudah Lengkap' && $cek->valid_1_p3 == 'Data Sudah Lengkap') {
                $thp = 4;
            }


            $this->db->where('id_giat', $id);
            $this->db->update('kegiatan_p3a', array('kd_tahap' => $thp));

            $this->db->trans_complete();

            if ($this->db->trans_status() === FALSE) {
                # Something went wrong.
                $this->db->trans_rollback();
                echo "1_edit"; // Error response for edit
            } else {
                $this->db->trans_commit();
                echo "0_edit"; // Success response for edit
            }


        }







    }


}