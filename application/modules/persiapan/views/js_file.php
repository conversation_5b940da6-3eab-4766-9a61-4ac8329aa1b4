<!-- <script type="text/javascript" charset="utf8" src="<?php //echo base_url(); ?>assets/js/wayjs/way.js"></script> -->
<script>
     <?php
        require_once(FCPATH."env.php");
        echo 'var WGI_APP_BASE_URL = "'.WGI_APP_BASE_URL.'"; ';
    ?>
    var thangs = "<?php echo $this->session->users['tahun_p3tgai']; ?>";
    var satker = "<?php echo $this->session->users['kd_satker']; ?>";
    var nama_user = "<?php echo $this->session->users['nama']; ?>";
    var kd_prov_ses = "<?php echo $this->session->users['kd_prov']; ?>";
    var iduser = "<?php echo $this->session->users['id_user'] ?>"
    var xhrdata = null;
    var table = null;
    var group = "<?php echo $this->session->users['id_user_group_real']; ?>";
    var urlBase = WGI_APP_BASE_URL;
    var sessLocked = "<?php echo $this->session->userdata("satker" . $this->session->users['tahun_p3tgai'] . "_" . $this->session->users['kd_satker']); ?>";
    var v0 = '';
    var v1 = '';
    var v2 = '';
    var v3 = '';
    var v4 = '';
    var tahap = '';
    var progress = '';
    var but = '';
//  alert(sessLocked);

    //console.log('------>'+sessLocked)


    function clear_input() {
        //  $("#formData :input").val("");
        $('#formData')[0].reset();
    }



    function lockEntri(mod, id) {
        if (sessLocked == 1) {
            cssAct = (mod == 'edit') ? but : 'display:none !important;';
            // console.log("The key "+ satker +" exists in local storage.");
        } else {
            cssAct = (mod == 'edit') ? "onclick= dtTambahRow('edit','" + id + "')" : 'display:block;';
            // console.log("The key "+ satker +" does not exist in local storage.");
        }
        return cssAct;
    }

    function tracking_data(v0, v1, v2, v3, v4, tahap, ver1, ver2, ver3, ver4, id) {

        // "<span class='badge badge-primary'><i class='fa fa-edit' onclick= dtTambahRow('edit','" + id + "')></i></span>",
        // "<span class='badge badge-danger'><i class='fa fa-trash' onclick= dtDeleteRow('" + id + "')></i></span>",
        // "<span class='badge badge-warning'><i class='fa fa-search' onclick= dtTambahRow('detail','" + id + "')></i></span>",
        if (group == 3 || group == 4 || group == 5) {

            but = "onclick= dtTambahRow('detail','" + id + "')";
        } else if (group == 7 || group == 1 || group == 2) {

            but = lockEntri('edit', id);

        }
        var tahap = parseInt(tahap);


        if (v0 === 'Data Belum Lengkap' || v1 === 'Data Belum Lengkap') {
            progress1 = "<span style='cursor:pointer;' " + but +
                "  title='belum lengkap & belum diverifikasi' class='badge badge-pill badge-danger'><span class='blink_me'><i class='fa fa-exclamation-triangle'></i></span>&nbsp;Persiapan</span>";
        } else {
            if (ver1 == 1) {
                progress1 = "<span style='cursor:pointer;' " + but +
                    " title='lengkap & valid' class='badge badge-pill badge-success'><span class='blink_me'><i class='fa fa-check-circle'></i></span>&nbsp;Persiapan</span>";
            } else if (ver1 == 2) {
                progress1 = "<span style='cursor:pointer;' " + but +
                    " title='lengkap & tdk vaild' class='badge badge-pill badge-warning'><span class='blink_me'><i class='fa fa-times'></i></span>&nbsp;Persiapan</span>";
            } else {
                progress1 = "<span style='cursor:pointer;' " + but +
                    " title='lengkap & belum diverifikasi' class='badge badge-pill badge-warning'><span class='blink_me'><i class='fa fa-question-circle'></i></span>&nbsp;Persiapan</span>";
            }
        }

        if (ver1 == 1 && v0 == 'Data Sudah Lengkap' && v1 == 'Data Sudah Lengkap') {
            if (v2 === 'Data Belum Lengkap') {
                progress2 =
                    "<span style='cursor:help;' title='belum lengkap & belum diverifikasi' class='badge badge-pill badge-danger'><i class='fa fa-exclamation-triangle'></i>&nbsp;Perencanaan</span>";

            } else {
                if (ver2 == 1) {
                    progress2 =
                        "<span style='cursor:help;' title='lengkap & valid'  class='badge badge-pill badge-success'><span class=''><i class='fa fa-check-circle'></i></span>&nbsp;Perencanaan</span>";
                } else if (ver2 == 2) {
                    progress2 =
                        "<span style='cursor:help;' title='lengkap & tdk vaild'  class='badge badge-pill badge-warning'><span class=''><i class='fa fa-times'></i></span>&nbsp;Perencanaan</span>";
                } else {
                    progress2 =
                        "<span style='cursor:help;' title='lengkap & belum diverifikasi' class='badge badge-pill badge-warning'><span class=''><i class='fa fa-question-circle'></i></span>&nbsp;Perencanaan</span>";
                }
                // progress2 = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span>";
            }
        } else {
            progress2 =
                "<span style='cursor:help;' title='Belum sampai ke tahapan Perencanaan' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Perencanaan</span>";
        }

        if (ver2 == 1 && ver1 == 1 && v0 == 'Data Sudah Lengkap' && v1 == 'Data Sudah Lengkap' && v2 ==
            'Data Sudah Lengkap') {
            if (v3 === 'Data Belum Lengkap') {
                progress3 =
                    "<span style='cursor:help;' title='belum lengkap & belum diverifikasi' class='badge badge-pill badge-danger'><i class='fa fa-exclamation-triangle'></i>&nbsp;Pelaksanaan</span>";
            } else {
                if (ver3 == 1) {
                    progress3 =
                        "<span style='cursor:help;' title='lengkap & valid'  class='badge badge-pill badge-success'><span class=''><i class='fa fa-check-circle'></i></span>&nbsp;Pelaksanaan</span>";
                } else if (ver3 == 2) {
                    progress3 =
                        "<span style='cursor:help;' title='lengkap & tdk vaild'  class='badge badge-pill badge-warning'><span class=''><i class='fa fa-times'></i></span>&nbsp;Pelaksanaan</span>";
                } else {
                    progress3 =
                        "<span style='cursor:help;' title='lengkap & belum diverifikasi' class='badge badge-pill badge-warning'><span class=''><i class='fa fa-question-circle'></i></span>&nbsp;Pelaksanaan</span>";
                }
                // progress3 = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Pelaksanaan</span>";
            }
        } else {
            progress3 =
                "<span style='cursor:help;' title='Belum sampai ke tahapan Pelaksanaan' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span>";

        }

        if (ver3 == 1 && ver2 == 1 && ver1 == 1 && v0 == 'Data Sudah Lengkap' && v1 == 'Data Sudah Lengkap' && v2 ==
            'Data Sudah Lengkap' && v3 == 'Data Sudah Lengkap') {
            if (v4 === 'Data Belum Lengkap') {
                progress4 =
                    "<span style='cursor:help;' title='belum lengkap & belum diverifikasi' class='badge badge-pill badge-danger'><i class='fa fa-exclamation-triangle'></i>&nbsp;Penyelesaian</span>";
            } else {
                if (ver4 == 1) {
                    progress4 =
                        "<span style='cursor:help;' title='lengkap & valid'  class='badge badge-pill badge-success'><span class=''><i class='fa fa-check-circle'></i></span>&nbsp;Penyelesaian</span>";
                } else if (ver4 == 2) {
                    progress4 =
                        "<span style='cursor:help;' title='lengkap & tdk vaild'  class='badge badge-pill badge-warning'><span class=''><i class='fa fa-times'></i></span>&nbsp;Penyelesaian</span>";
                } else {
                    progress4 =
                        "<span style='cursor:help;' title='lengkap & belum diverifikasi' class='badge badge-pill badge-warning'><span class=''><i class='fa fa-question-circle'></i></span>&nbsp;Penyelesaian</span>";
                }
                // progress4 = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Penyelesaian</span>";
            }
        } else {
            progress4 =
                "<span style='cursor:help;' title='Belum sampai ke tahapan Penyelesaian' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";

        }


        if (tahap == 4) {
            progress = progress1 + progress2 + progress3 + progress4;
        } else if (tahap == 3) {
            progress = progress1 + progress2 + progress3 +
                "<span style='cursor:help;' title='Belum sampai ke tahapan Penyelesaian'  class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        } else if (tahap == 2) {
            progress = progress1 + progress2 +
                "<span style='cursor:help;' title='Belum sampai ke tahapan Pelaksanaan' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span>" +
                "<span  style='cursor:help;' title='Belum sampai ke tahapan Penyelesaian' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        } else if (tahap == 1) {
            progress = progress1 +
                "<span style='cursor:help;' title='Belum sampai ke tahapan Perencanaan' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Perencanaan</span>" +
                "<span style='cursor:help;' title='Belum sampai ke tahapan Pelaksanaan' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span>" +
                "<span  style='cursor:help;' title='Belum sampai ke tahapan Penyelesaian' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        }







        //    if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Belum Lengkap' && v3=='Data Belum Lengkap' && v4=='Data Belum Lengkap') {
        //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-check-circle'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        //    } else if(v0=='Data Belum Lengkap' && v1=='Data Belum Lengkap' & v2=='Data Belum Lengkap' && v3=='Data Belum Lengkap' && v4=='Data Belum Lengkap') {
        //         progress = "<span class='badge badge-pill badge-warning'><i class='fa fa-exclamation-triangle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        //    }

        //     if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Sudah Lengkap' && v3=='Data Belum Lengkap' && v4=='Data Belum Lengkap') {
        //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        //     } else  if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Belum Lengkap' && v3=='Data Belum Lengkap' && v4=='Data Belum Lengkap') {
        //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-warning'><i class='fa fa-exclamation-triangle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        //     }

        //     if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Sudah Lengkap' && v3=='Data Sudah Lengkap' && v4=='Data Belum Lengkap') {
        //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        //     } else if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Sudah Lengkap' && v3=='Data Belum Lengkap' && v4=='Data Belum Lengkap') {
        //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-warning'><i class='fa fa-exclamation-triangle'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
        //     }


        //     if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Sudah Lengkap' && v3=='Data Sudah Lengkap' && v4=='Data Sudah Lengkap') {
        //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Penyelesaian</span>"
        //     } else if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Sudah Lengkap' && v3=='Data Sudah Lengkap' && v4=='Data Belum Lengkap') {
        //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-warning'><i class='fa fa-exclamation-triangle'></i>&nbsp;Penyelesaian</span>";
        //     }


        return progress;
    }

    function dtRefresh(stat, id) {
        // var mustKMB = "<?php //echo $this->session->users['id_user']; ?>";
        // var id = $("#id").val()
        var url = "<?php echo base_url(); ?>persiapan/getback_file/" + stat + "/" + id;
        var text = 'Anda akan memproses perbaikan dokumen gambar?'
        // if(stat == 1){
        //     text = "Anda akan memproses perbaikan dokumen gambar?"
        // }else{
        //     text = ""
        // }
        swal({
            title: text,
            //   text: tex,
            icon: "info",
            buttons: true,
            dangerMode: true,
        })
            .then((willDelete) => {
                if (willDelete) {
                    $.get(url, {}).done(function (data) {
                        if (data == 0) {
                            swal({
                                title: "",
                                text: "Berhasil Mengembalikan Dokumen Foto",
                                icon: "success",
                                showConfirmButton: false,
                                timer: 3000,
                                type: "success"
                            });
                            // table.ajax.reload();
                            $('#modal-tambah').modal('hide');
                        }
                    })
                } else {
                    swal("Anda tidak berhasil mengembalikan Dokumen Foto");
                }
            })
    }

    // function clean_str(dirtyString) {
    //     var cleanString = dirtyString.replace(/[|&;$%@"<>()+,]/g, "");

    //     return cleanString;
    // }



    function listing() {

table = $('#dt-server-processing').DataTable({
    "draw": 0,
    "order": [
        [0, "asc"]
    ],
    "processing": true,
    "deferRender": true,
    "serverSide": true,
    "ajax": {
        type: "post",
        url: "<?php echo base_url(); ?>persiapan/ssp_paket",
        "data": function (d) {
            d.<?php echo $this->security->get_csrf_token_name(); ?> = "<?php echo $this->security->get_csrf_hash(); ?>";
            d.fs = $(".statustahap").val();
        }
    },
    "columnDefs": [
        {
            "aTargets": [0],
            "mRender": function (data, type, full) {
                return full[1];
            }
        },
        {
            "aTargets": [1],
            "mRender": function (data, type, full) {
                return full[2];
            }
        },
        {
            "aTargets": [2],
            "mRender": function (data, type, full) {
                return full[3];
            }
        },
        {
            "aTargets": [3],
            "mRender": function (data, type, full) {
                return full[4];
            }
        },
        {
            "aTargets": [4],
            "mRender": function (data, type, full) {
                return full[5];
            }
        },
        {
            "aTargets": [5],
            "mRender": function (data, type, full) {
                return full[6];
            }
        },
        {
            "aTargets": [6],
            "mRender": function (data, type, full) {
                return full[7];
            }
        },
        {
            "aTargets": [7],
            "mRender": function (data, type, full) {
                return full[8];
            }
        },
        {
            "aTargets": [8],
            "mRender": function (data, type, full) {
                return tracking_data(full[9], full[10], full[11], full[12], full[13], full[14], full[15], full[16], full[17], full[18], full[0]);
            }
        },
        {
            "aTargets": [9],
            "mRender": function (data, type, row) {
                var id = row[0];
                var show = 'display:none !important;';
                var show2 = 'display:none !important;';
                if (group == 2) {
                    show = 'display:block !important;';
                } else if (group == 1 || group == 7) {
                    show2 = lockEntri('del', id);
                }
                var html_button = [
                    "<span class='badge badge-warning' style='" + show + "cursor:pointer;background:#4f9dbb;' onclick= dtTambahRow('detail','" + id + "') title='Validasi data'>Validasi</span>",
                    "<span class='badge badge-danger' style='" + show2 + "cursor:pointer;'><i class='fa fa-trash' onclick= dtDeleteRow('" + id + "')></i></span>"
                ].join("\n");
                return html_button;
            }
        }
    ],
    "language": {
        "decimal": "",
        "emptyTable": "Data tidak ditemukan",
        "info": "Data _START_ s/d _END_ dari _TOTAL_",
        "infoEmpty": "Tidak ada data",
        "infoFiltered": "(tersaring dari _MAX_)",
        "thousands": ",",
        "lengthMenu": "_MENU_  data per halaman",
        "loadingRecords": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
        "processing": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
        "search": "Cari:",
        "zeroRecords": "Tidak ada data ditemukan",
        "aria": {
            "sortAscending": ": aktifkan untuk mengurutkan naik",
            "sortDescending": ": aktifkan untuk mengurutkan turun"
        }
    },
    "initComplete": function () {
        var $searchInput = $('#dt-server-processing_filter input');
        $searchInput.attr('placeholder', 'Lalu Tekan Enter'); // Add placeholder text
        $searchInput.unbind();
        $searchInput.bind('keyup', function (e) {
            if (e.keyCode == 13) { // Enter key
                table.search(this.value).draw();
            }
        });

    // Add a reset button next to the search input with additional styling
    var $resetButton = $('<button type="button" style="display: none; margin-left: 10px; padding: 5px 10px; background-color: #f0f0f0; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;">Reset</button>').click(function () {
        $searchInput.val(''); // Clear the search input
        table.search('').draw(); // Reset the table search
        $resetButton.hide(); // Hide the reset button after resetting
    });

    // Append the reset button to the filter element
    $('#dt-server-processing_filter').append($resetButton);

    // Show the reset button after hitting enter in the search input
    $searchInput.on('keyup', function (e) {
        if (e.keyCode == 13 && $searchInput.val().length > 0) { // Check if Enter key is pressed
            $resetButton.show(); // Show the reset button
        } else {
            $resetButton.hide(); // Hide the reset button if input is empty
        }
    });
    }
});

table.on('xhr', function () {
    xhrdata = table.ajax.json();
});
        //});
    }
    // function simpanForm() {
    //     var mode = $('#modeform').val();
    //     var data = $('.form-tambah').serialize();
    //     console.log(new FormData(this))

    //     // if($( "#frm-tambah" ).valid() === true){
    //         // var na=parseFloat($("#nilai_asuransi").val().replace(/\D/g, ''));
    //         // var realisasi=parseFloat($("#realisasi").val().replace(/\D/g, ''));
    //         // var deviasi=parseFloat($("#deviasi").val().replace(/\D/g, ''));
    //         var p_satu = $('input[name="persiapan_1"]:checked').val();
    //         var p_dua = $('input[name="persiapan_2"]:checked').val();
    //         if(p_satu=== undefined){
    //             p_satu = 0;
    //         }
    //         if(p_satu=== undefined){
    //             p_dua = 0;
    //         }
    //         var id = $("#id").val();

    //         // var serializeData = {
    //         //     "id_bujt" :  $("#id_bujt").val(),
    //         //     "id_ruas": $("#id_ruas").val(),
    //         //     "staa": $("#staa").val(),
    //         //     "stae": $("#stae").val(),
    //         //     "temuan" : $("#temuan").val(),
    //         //     "penanganan": $("#penanganan").val(),
    //         //     "catatan": $("#catatan").val(),
    //         //     "persiapan_1": p_satu,
    //         //     "persiapan_2": p_dua,
    //         //     "id": id,
    //         // };

    //         // console.log(serializeData)
    //             url = "<?php //echo base_url(); ?>persiapan/addform";

    //         var text_up = 'menambah';
    //         var params = {"formData": data,"<?php //echo $this->security->get_csrf_token_name(); ?>":"<?php //echo $this->security->get_csrf_hash(); ?>"};
    //         $.post(url, params).done(function (data) {
    //             console.log(data)

    //             $(".alert").css("display","block")
    //             $('#modal-tambah').modal('hide');
    //             // alert(data[0].status)
    //             if(id != ''){
    //                 text_up = 'merubah';
    //             }
    //             $("#text_i").empty();
    //             if(data=='sukses'){
    //                 $("#text_i").append("<strong>Berhasil !</strong> "+text_up+" data")
    //             }else{
    //                 $("#text_i").append("<strong>Ggagal !</strong> "+text_up+" data")
    //             }
    //             table.ajax.reload();
    //             setTimeout(close_alert, 3000);
    //         });
    //     // } else {
    //     //     $("label.error").css("color","red");
    //     // }
    // }
    function getFormUser(urole, kat, idgiat) {

        // var urole = e.value;
        urlWilayah = (idgiat == '') ? urole + "/" + iduser + "/" + kat : urole + "/" + iduser + "/" + kat + "/" + idgiat

        $("#form-user").empty();
        // $("#form-di").empty();

        // $('body div#modal-tambah.modal').one('shown.bs.modal', function (e) {

            //   $('#kd_satker').val(satker).selectpicker('refresh');
            // $('#prov').val('').selectpicker('refresh');
            // $('#kab').val('').selectpicker('refresh');
            // $('#kec').val('').selectpicker('refresh');
            // $('#desa').val('').selectpicker('refresh');


            //   $("#prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
            //             // e.stopPropagation();

            //             var selected_value = $("[id='prov']").toArray().map(x => $(x).val());
            //             if (selected_value[0].length > 1) {

            //                 var params = selected_value[0].toString();
            //                 params = params.replace(/,/g, "::");

            //                 refreshMultibootvar('kab', 20, 'kd_prov', params);

            //             } else {
            //                 refreshSelectbootvar('kab', 20, 'kd_prov', selected_value[0][0]);
            //             }
            //         });


            // $("#kab").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
            //     // e.stopPropagation();

            //     var selected_value = $("[id='kab']").toArray().map(x => $(x).val());
            //     if (selected_value[0].length > 1) {

            //         var params = selected_value[0].toString();
            //         params = params.replace(/,/g, "::");

            //         refreshMultibootvar('kec', 21, 'kd_kabkot', params);

            //     } else {
            //         refreshSelectbootvar('kec', 21, 'kd_kabkot', selected_value[0][0]);
            //     }



            // });
            // $("#kec").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
            //     // e.stopPropagation();
            //     var selected_value = $("[id='kec']").toArray().map(x => $(x).val());
            //     if (selected_value[0].length > 1) {

            //         var params = selected_value[0].toString();
            //         params = params.replace(/,/g, "::");

            //         refreshMultibootvar('desa', 22, 'kd_camat', params);

            //     } else {
            //         //refreshSelectbootvar('kec', 21, 'kd_kabkot', selected_value[0][0]);
            //         refreshSelectbootvar('desa', 22, 'kd_camat', selected_value[0][0]);
            //     }


            // });
            //   $('#prov').val(prov).selectpicker('refresh');
            //    $('#kab').val('').selectpicker('refresh');
            //    $('#kec').val('').selectpicker('refresh');
            //    $('#desa').val('').selectpicker('refresh');


        // });


        $.get("<?php echo base_url(); ?>" + "persiapan/bindWilayah/" + urlWilayah)
            .done(function (data) {
                // Set the HTML content
                $("#form-user").html(data);

                // Initialize selectpicker for newly added elements
                setTimeout(function() {
                    // Destroy existing selectpicker instances in the form-user div
                    $("#form-user .bootstrap-select").each(function() {
                        if ($(this).hasClass('selectpicker')) {
                            $(this).selectpicker('destroy');
                        }
                    });

                    // Initialize selectpicker for all select elements with bootstrap-select class
                    $("#form-user select.bootstrap-select").selectpicker({
                        liveSearch: true,
                        size: 10,
                        dropupAuto: false,
                        style: 'btn-default',
                        width: '100%'
                    });

                    // Refresh all selectpickers
                    $("#form-user .bootstrap-select").selectpicker('refresh');

                    // Setup cascading dropdown handlers for the newly loaded form
                    setupCascadingDropdowns();
                }, 100);
            });



        // $("#id_user_groups").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
        //   refreshSelectbootvar('kd_satker', 37, 'id_user', iduser);
        // });

        // $("#prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
        //   refreshSelectbootvar('kab', 20, 'kd_prov', this.value);
        // });
        // $("#kab").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
        //   refreshSelectbootvar('kec', 21, 'kd_kabkot', this.value);
        // });
        // $("#kec").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
        //   refreshSelectbootvar('desa', 22, 'kd_camat', this.value);
        // });



    }




    function validasiEkstensi(id, ext = '') {
        var inputFile = document.getElementById(id);
        var pathFile = inputFile.value;
        var file_size = inputFile.files[0].size / 1024;
        var bts = ''
        var txt = ''
        // var ekstensiOk = /(\.jpg|\.jpeg|\.png|\.gif)$/i;

        if (ext != '') {
            ekstensiOk = /(\.pdf)$/i;
            bts = 200
            txt = 'Silakan upload file yang dengan ekstensi .pdf'

        } else {
            ekstensiOk = /(\.jpg|\.jpeg|\.png|\.gif)$/i;
            bts = 500
            txt = 'Silakan upload file yang dengan ekstensi .png , .jpg,'


        }

        if (file_size > bts) {
            swal({
                // title: "Anda yakin menghapus data ?",
                text: 'Ukuran file tidak boleh lebih dari ' + bts + ' Kb',
                icon: "warning",
                buttons: false,
                timer: 2000,
                dangerMode: true,
            })
            $(inputFile).val(''); //for clearing with Jquery
            return
        }
        if (!ekstensiOk.exec(pathFile)) {
            swal({
                // title: "Anda yakin menghapus data ?",
                text: txt,
                icon: "warning",
                buttons: false,
                timer: 2000,
                dangerMode: true,
            })
            // alert('Silakan upload file yang dengan ekstensi .pdf/.jpg/.png/.gif');
            inputFile.value = '';
            return false;
        } else {
            // Preview gambar
            if (inputFile.files && inputFile.files[0]) {
                var reader = new FileReader();
                reader.onload = function (e) {
                    // document.getElementById('upload_peta_di').innerHTML = '<iframe src="'+e.target.result+'" class="img-responsive" style="width:100%;height:200px;"/>';
                };
                reader.readAsDataURL(inputFile.files[0]);
            }
        }
    }

    function shows() {
        $(".error").text('')
        $(".error").css({
            'font-size': '14px',
            'color': '#353c4e'
        })
        $(".form-tambah .form-group input").prop('readonly', false)
        $(".form-tambah .form-group input").css({
            "border": "1px solid #7889bd8f",
            "background": "#e9ecef45"
        })
        $(".form-tambah .form-group select").prop('disabled', false)
        $(".form-tambah .form-group select").css({
            "border": "1px solid #7889bd8f",
            "background": "#e9ecef45"
        })
        $(".form-tambah .form-group textarea").prop('readonly', false)
        $(".form-tambah .form-group textarea").css({
            "border": "1px solid #7889bd8f",
            "background": "#e9ecef45"
        })
        $(".form-tambah .form-group button").prop('disabled', false)
        $(".form-tambah .form-group button").css({
            "border": "1px solid #7889bd8f",
            "background": "#e9ecef45"
        })
        $(".form-tambah .form-group").css({
            "border-bottom": "0px"
        })
        $(".form-tambah .form-group input").val('')
        $(".form-tambah .form-group select").val('')
        $("#gb_npwp").html('')
        $(".form-tambah .form-group textarea").val('')
        $("#file_npwp").show()
        $("#fot_musya").show()
        $("#fot_sosial").show()
    }

    function bs_input_file() {
        $(".input-file").before(
            function () {
                if (!$(this).prev().hasClass('input-ghost')) {
                    var element = $("<input type='file' class='input-ghost' style='visibility:hidden; height:0'>");
                    element.attr("name", $(this).attr("name"));
                    element.change(function () {
                        element.next(element).find('input').val((element.val()).split('\\').pop());
                    });
                    $(this).find("button.btn-choose").click(function () {
                        element.click();
                    });
                    $(this).find("button.btn-reset").click(function () {
                        element.val(null);
                        $(this).parents(".input-file").find('input').val('');
                    });
                    $(this).find('input').css("cursor", "pointer");
                    $(this).find('input').mousedown(function () {
                        $(this).parents('.input-file').prev().click();
                        return false;
                    });
                    return element;
                }
            }
        );
    }
    $(function () {
        bs_input_file();
    });


    // function formatNpwp(z) {
    //     // console.log(z);
    //     if (z === null) {
    //         $("#no_npwp").val('');
    //     } else {
    //         var dt = z.value
    //         if (typeof (dt) === 'undefined' || dt === null) {
    //             $("#no_npwp").val('')
    //             dt = z;
    //             $("#no_npwp").val(dt.replace(/(\d{2})(\d{3})(\d{3})(\d{1})(\d{3})(\d{3})/, '$1.$2.$3.$4-$5.$6'))
    //         }

    //         if (!dt.startsWith('0.')) {
    //             dt = '0.' + dt.replace(/^0\.?/, ''); // Remove existing 0 or 0. to prevent duplicates
    //         }

    //         // alert(value.length)
    //         if (dt.length === 4) {  // After 0.[first digit]
    //             $("#no_npwp").val(dt + '.');
    //         } else if (dt.length === 8) {  // After 0.XX.[next 3 digits]
    //             $("#no_npwp").val(dt + '.');
    //         } else if (dt.length === 12) {  // After 0.XX.XXX.[next 3 digits]
    //             $("#no_npwp").val(dt + '.');
    //         } else if (dt.length === 14) {  // After 0.XX.XXX.XXX.[1 digit]
    //             $("#no_npwp").val(dt + '-');
    //         } else if (dt.length === 18) {  // After 0.XX.XXX.XXX.X-[3 digits]
    //             $("#no_npwp").val(dt + '.');
    //         } else if (dt.length > 20) {
    //             $("#no_npwp").val(dt.replace(/^0\.(\d{2})\.?(\d{3})\.?(\d{3})\.?(\d{1})-?(\d{3})\.?(\d{3})$/, '0.$1.$2.$3.$4-$5.$6'));
    //         }
    //         //  return value.replace(/(\d{2})(\d{3})/, '$1.$2.')
    //         //  return value.replace(/(\d{2})(\d{3})(\d{3})(\d{1})(\d{3})(\d{3})/, '$1.$2.$3.$4-$5.$6');
    //     }
    // }

    function formatNpwp(z) {
    // Handle null input
    if (z === null) {
        $("#no_npwp").val('');
        return;
    }

    // Get the input value (whether z is an element or direct value)
    let value = typeof z === 'object' && z.value !== undefined ? z.value : z;

    // Remove all non-digit characters to get clean digits for processing
    const digits = value.replace(/\D/g, '');

    // If no digits, clear the field
    if (digits.length === 0) {
        $("#no_npwp").val('');
        return;
    }

    // Format the digits according to NPWP pattern: X.XX.XXX.XXX.X-XXX.XXX
    let formatted = '';

    // First digit
    formatted += digits.substring(0, Math.min(1, digits.length));

    // Add dot after first digit
    if (digits.length > 1) {
        formatted += '.' + digits.substring(1, Math.min(3, digits.length));
    }

    // Add dot after next 2 digits
    if (digits.length > 3) {
        formatted += '.' + digits.substring(3, Math.min(6, digits.length));
    }

    // Add dot after next 3 digits
    if (digits.length > 6) {
        formatted += '.' + digits.substring(6, Math.min(9, digits.length));
    }

    // Add dot after next 3 digits
    if (digits.length > 9) {
        formatted += '.' + digits.substring(9, Math.min(10, digits.length));
    }

    // Add hyphen after next 1 digit
    if (digits.length > 10) {
        formatted += '-' + digits.substring(10, Math.min(13, digits.length));
    }

    // Add dot after next 3 digits
    if (digits.length > 13) {
        formatted += '.' + digits.substring(13);
    }

    // Set the formatted value
    $("#no_npwp").val(formatted);
}

    // Custom refreshCombobox5 function for persiapan module with proper selectpicker handling
    function refreshCombobox5Persiapan(divname, refindex, refresh_field, refresh_value, selectedValue = '') {
        return new Promise((resolve, reject) => {
            var url = WGI_APP_BASE_URL + "lookup/refreshlook4/" + refindex + "/" + refresh_field + "/" + refresh_value;

            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'json',
                success: function(jdata) {
                    console.log('Refreshing data for ' + divname + ':', jdata);

                    // Special logging for Nama Daerah Irigasi
                    if (divname === 'nm_dae_irigasi') {
                        console.log('=== NAMA DAERAH IRIGASI DEBUG ===');
                        console.log('API URL:', url);
                        console.log('Data received:', jdata);
                        console.log('Number of items:', jdata.length);
                        if (jdata.length > 0) {
                            console.log('First item:', jdata[0]);
                        }
                        console.log('Selected value to set:', selectedValue);
                        console.log('================================');
                    }

                    // Clear and populate options
                    $('#' + divname).empty();
                    $('#' + divname).append(new Option("--Pilih--", ""));
                    $.each(jdata, function(i, el) {
                        $('#' + divname).append(new Option(el.val, el.id));
                        if (divname === 'nm_dae_irigasi') {
                            console.log('Added irrigation option:', el.val, '(ID:', el.id + ')');
                        }
                    });

                    // Ensure selectpicker is initialized
                    if (!$('#' + divname).hasClass('selectpicker')) {
                        $('#' + divname).selectpicker({
                            liveSearch: true,
                            size: 10,
                            dropupAuto: false
                        });
                    }

                    // Set selected value if provided
                    if (selectedValue) {
                        $('#' + divname).val(selectedValue);
                        if (divname === 'nm_dae_irigasi') {
                            console.log('Set irrigation value to:', selectedValue);
                        }
                    }

                    // Refresh selectpicker
                    $('#' + divname).selectpicker('refresh');

                    if (divname === 'nm_dae_irigasi') {
                        console.log('Final irrigation dropdown value:', $('#' + divname).val());
                    }

                    resolve(jdata);
                },
                error: function(xhr, status, error) {
                    console.error('Error refreshing data for ' + divname + ':', error);
                    // Add default option on error
                    $('#' + divname).empty();
                    $('#' + divname).append(new Option("--Pilih--", ""));
                    if (!$('#' + divname).hasClass('selectpicker')) {
                        $('#' + divname).selectpicker({
                            liveSearch: true,
                            size: 10,
                            dropupAuto: false
                        });
                    } else {
                        $('#' + divname).selectpicker('refresh');
                    }
                    reject(error);
                }
            });
        });
    }

    // Custom initCombobox function for persiapan module with proper selectpicker handling
    function initComboboxPersiapan(divname, refindex, selectedValue = '') {
        return new Promise((resolve, reject) => {
            var url = WGI_APP_BASE_URL + "lookup/fieldlook/" + refindex;

            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'json',
                success: function(jdata) {
                    console.log('Loading data for ' + divname + ':', jdata);

                    // Special debugging for BBWS dropdown
                    if (divname === 'bbws') {
                        console.log('=== BBWS DROPDOWN DEBUG ===');
                        console.log('API URL:', url);
                        console.log('Data received:', jdata);
                        console.log('Number of BBWS items:', jdata.length);
                        console.log('Selected value to set:', selectedValue);
                        if (jdata.length > 0) {
                            console.log('First BBWS item:', jdata[0]);
                        }
                        console.log('===========================');
                    }

                    // Clear and populate options
                    $('#' + divname).empty();
                    $('#' + divname).append(new Option("--Pilih--", ""));
                    $.each(jdata, function(i, el) {
                        $('#' + divname).append(new Option(el.val, el.id));
                        if (divname === 'bbws') {
                            console.log('Added BBWS option:', el.val, '(ID:', el.id + ')');
                        }
                    });

                    // Ensure selectpicker is initialized
                    if (!$('#' + divname).hasClass('selectpicker')) {
                        $('#' + divname).selectpicker({
                            liveSearch: true,
                            size: 10,
                            dropupAuto: false
                        });
                    }

                    // Set selected value if provided
                    if (selectedValue) {
                        $('#' + divname).val(selectedValue);
                        if (divname === 'bbws') {
                            console.log('Set BBWS value to:', selectedValue);
                        }
                    }

                    // Refresh selectpicker
                    $('#' + divname).selectpicker('refresh');

                    if (divname === 'bbws') {
                        console.log('Final BBWS dropdown value:', $('#' + divname).val());
                        console.log('BBWS dropdown HTML:', $('#' + divname).html().substring(0, 200) + '...');
                    }

                    resolve(jdata);
                },
                error: function(xhr, status, error) {
                    console.error('Error loading data for ' + divname + ':', error);
                    // Add default option on error
                    $('#' + divname).empty();
                    $('#' + divname).append(new Option("--Pilih--", ""));
                    if (!$('#' + divname).hasClass('selectpicker')) {
                        $('#' + divname).selectpicker({
                            liveSearch: true,
                            size: 10,
                            dropupAuto: false
                        });
                    } else {
                        $('#' + divname).selectpicker('refresh');
                    }
                    reject(error);
                }
            });
        });
    }

    // Function to initialize all selectpickers in the modal
    function initializeAllSelectpickers() {
        console.log('Initializing all selectpickers...');
        // Initialize all selectpickers in the modal
        $('#modal-tambah select.bootstrap-select').each(function() {
            var elementId = $(this).attr('id');
            console.log('Processing selectpicker for:', elementId);

            if (!$(this).hasClass('selectpicker')) {
                console.log('Initializing selectpicker for:', elementId);
                $(this).selectpicker({
                    liveSearch: true,
                    size: 10,
                    dropupAuto: false,
                    style: 'btn-default',
                    width: '100%'
                });
            } else {
                console.log('Selectpicker already initialized for:', elementId);
                $(this).selectpicker('refresh');
            }
        });
        console.log('All selectpickers initialized');
    }

    // Function to debug selectpicker values
    function debugSelectpickerValues() {
        console.log('=== Debugging Selectpicker Values ===');
        $('#modal-tambah select.bootstrap-select').each(function() {
            var elementId = $(this).attr('id');
            var currentValue = $(this).val();
            var optionsCount = $(this).find('option').length;
            console.log(elementId + ': value="' + currentValue + '", options=' + optionsCount);

            // Log first few options
            $(this).find('option').slice(0, 3).each(function(index) {
                console.log('  Option ' + index + ': value="' + $(this).val() + '", text="' + $(this).text() + '"');
            });
        });
        console.log('=== End Debug ===');
    }

    // Setup cascading dropdown event handlers
    function setupCascadingDropdowns() {
        console.log('Setting up cascading dropdown handlers...');
        console.log('Current thangs (year):', thangs);

        // Remove existing event handlers to prevent duplicates
        $(document).off('changed.bs.select', '#prov');
        $(document).off('changed.bs.select', '#kab');
        $(document).off('changed.bs.select', '#kec');

        // Provinsi change handler
        $(document).on('changed.bs.select', '#prov', function(e, clickedIndex, newValue, oldValue) {
            console.log('Provinsi changed to:', this.value);

            if (this.value && this.value !== '') {
                // Clear dependent dropdowns
                $('#kab').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');
                $('#kec').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');
                $('#desa').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');
                $('#nm_dae_irigasi').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');

                // Load kabupaten data
                refreshCombobox5Persiapan('kab', 20, 'kd_prov::tahun', this.value + '::' + thangs, '')
                    .then(() => {
                        console.log('Kabupaten data loaded for provinsi:', this.value);
                    })
                    .catch(error => {
                        console.error('Error loading kabupaten data:', error);
                    });
            } else {
                // Clear all dependent dropdowns if no provinsi selected
                $('#kab').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');
                $('#kec').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');
                $('#desa').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');
                $('#nm_dae_irigasi').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');
            }
        });

        // Kabupaten change handler
        $(document).on('changed.bs.select', '#kab', function(e, clickedIndex, newValue, oldValue) {
            console.log('Kabupaten changed to:', this.value);

            if (this.value && this.value !== '') {
                // Clear dependent dropdowns
                $('#kec').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');
                $('#desa').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');
                $('#nm_dae_irigasi').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');

                // Load kecamatan data
                refreshCombobox5Persiapan('kec', 21, 'kd_kabkot::tahun', this.value + '::' + thangs, '')
                    .then(() => {
                        console.log('Kecamatan data loaded for kabupaten:', this.value);
                    })
                    .catch(error => {
                        console.error('Error loading kecamatan data:', error);
                    });
            } else {
                // Clear dependent dropdowns if no kabupaten selected
                $('#kec').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');
                $('#desa').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');
                $('#nm_dae_irigasi').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');
            }
        });

        // Kecamatan change handler - This is where Nama Daerah Irigasi should be loaded
        $(document).on('changed.bs.select', '#kec', function(e, clickedIndex, newValue, oldValue) {
            console.log('Kecamatan changed to:', this.value);

            if (this.value && this.value !== '') {
                // Clear dependent dropdowns
                $('#desa').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');
                $('#nm_dae_irigasi').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');

                // Load desa data
                refreshCombobox5Persiapan('desa', 22, 'kd_camat::tahun', this.value + '::' + thangs, '')
                    .then(() => {
                        console.log('Desa data loaded for kecamatan:', this.value);
                    })
                    .catch(error => {
                        console.error('Error loading desa data:', error);
                    });

                // Load nm_dae_irigasi data based on kecamatan - THIS IS THE KEY DEPENDENCY
                console.log('Loading Nama Daerah Irigasi for kecamatan:', this.value);
                refreshCombobox5Persiapan('nm_dae_irigasi', 31, 'kd_kec::tahun', this.value + '::' + thangs, '')
                    .then(() => {
                        console.log('Nama Daerah Irigasi data loaded successfully for kecamatan:', this.value);
                    })
                    .catch(error => {
                        console.error('Error loading Nama Daerah Irigasi data:', error);
                        console.error('API URL was:', WGI_APP_BASE_URL + "lookup/refreshlook4/31/kd_kec::tahun/" + this.value + "::" + thangs);
                    });
            } else {
                // Clear dependent dropdowns if no kecamatan selected
                $('#desa').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');
                $('#nm_dae_irigasi').empty().append(new Option("--Pilih--", "")).selectpicker('refresh');
            }
        });

        console.log('Cascading dropdown handlers set up successfully');
    }

    // Test function for cascading dropdowns
    function testCascadingDropdowns() {
        console.log('Testing cascading dropdowns...');
        console.log('Available elements:');
        console.log('- #prov exists:', $('#prov').length > 0);
        console.log('- #kab exists:', $('#kab').length > 0);
        console.log('- #kec exists:', $('#kec').length > 0);
        console.log('- #desa exists:', $('#desa').length > 0);

        // Test if event handlers are attached
        var provEvents = $._data($('#prov')[0], 'events');
        console.log('Prov events:', provEvents);

        // Test API endpoint for Nama Daerah Irigasi
        var testKecId = $('#kec').val();
        if (testKecId) {
            var testIrigasiUrl = WGI_APP_BASE_URL + "lookup/refreshlook4/31/kd_kec::tahun/" + testKecId + "::" + thangs;
            console.log('Test URL for Nama Daerah Irigasi:', testIrigasiUrl);

            $.ajax({
                url: testIrigasiUrl,
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    console.log('Nama Daerah Irigasi API call successful:', data);
                    if (data.length === 0) {
                        console.warn('No Nama Daerah Irigasi data found for kecamatan:', testKecId);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Nama Daerah Irigasi API call failed:', error);
                    console.error('Response:', xhr.responseText);
                }
            });
        } else {
            console.log('No kecamatan selected, cannot test Nama Daerah Irigasi API');
        }

        // Test general API endpoint
        var testUrl = WGI_APP_BASE_URL + "lookup/refreshlook4/20/kd_prov::tahun/11::" + thangs;
        console.log('Test URL for kabupaten:', testUrl);

        $.ajax({
            url: testUrl,
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                console.log('Test API call successful:', data);
            },
            error: function(xhr, status, error) {
                console.error('Test API call failed:', error);
                console.error('Response:', xhr.responseText);
            }
        });
    }

    // Specific test function for Nama Daerah Irigasi
    function testNamaDaerahIrigasi(kecamatanId) {
        if (!kecamatanId) {
            console.log('Please provide a kecamatan ID to test Nama Daerah Irigasi');
            return;
        }

        console.log('Testing Nama Daerah Irigasi for kecamatan:', kecamatanId);
        var url = WGI_APP_BASE_URL + "lookup/refreshlook4/31/kd_kec::tahun/" + kecamatanId + "::" + thangs;
        console.log('API URL:', url);

        $.ajax({
            url: url,
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                console.log('SUCCESS: Nama Daerah Irigasi data:', data);
                console.log('Number of irrigation areas found:', data.length);

                if (data.length > 0) {
                    console.log('Sample irrigation area:', data[0]);

                    // Populate the dropdown for testing
                    $('#nm_dae_irigasi').empty();
                    $('#nm_dae_irigasi').append(new Option("--Pilih--", ""));
                    $.each(data, function(i, el) {
                        $('#nm_dae_irigasi').append(new Option(el.val, el.id));
                        console.log('Added option:', el.val, '(ID:', el.id + ')');
                    });
                    $('#nm_dae_irigasi').selectpicker('refresh');
                    console.log('Dropdown populated successfully');
                } else {
                    console.warn('No irrigation areas found for this kecamatan');
                }
            },
            error: function(xhr, status, error) {
                console.error('ERROR: Failed to load Nama Daerah Irigasi');
                console.error('Status:', status);
                console.error('Error:', error);
                console.error('Response Text:', xhr.responseText);
                console.error('Response Status:', xhr.status);
            }
        });
    }

    // Force set selectpicker values - fallback function
    function forceSetSelectpickerValues() {
        console.log('Force setting selectpicker values...');

        var valuesToSet = {
            'bbws': bbws,
            'jen_penerima': jen_penerima,
            'j_legalitas': j_legalitas,
            't_legalitas': t_legalitas,
            'j_irigasi': j_irigasi,
            'k_irigasi': k_irigasi,
            'j_kelamin': j_kelamin,
            'lbp': lbp,
            'nm_dae_irigasi': nm_dae_irigasi,
            'prov': prov,
            'kab': kab,
            'kec': kec,
            'desa': kel
        };

        Object.keys(valuesToSet).forEach(function(elementId) {
            var value = valuesToSet[elementId];
            if (value && $('#' + elementId).length > 0) {
                console.log('Force setting ' + elementId + ' = ' + value);

                // Special handling for BBWS
                if (elementId === 'bbws') {
                    console.log('=== FORCE SETTING BBWS ===');
                    console.log('BBWS value to set:', value);
                    console.log('BBWS dropdown exists:', $('#bbws').length > 0);
                    console.log('BBWS options count:', $('#bbws option').length);
                    $('#bbws option').each(function(index) {
                        console.log('BBWS option ' + index + ':', $(this).val(), '=', $(this).text());
                    });
                }

                // Check if option exists
                var optionExists = $('#' + elementId + ' option[value="' + value + '"]').length > 0;
                console.log('Option exists for ' + elementId + ': ' + optionExists);

                if (optionExists) {
                    $('#' + elementId).val(value);
                    $('#' + elementId).selectpicker('refresh');
                    console.log('Successfully set ' + elementId + ' to ' + value);

                    if (elementId === 'bbws') {
                        console.log('Final BBWS value after force set:', $('#bbws').val());
                        console.log('========================');
                    }
                } else {
                    console.warn('Option with value "' + value + '" not found for ' + elementId);

                    if (elementId === 'bbws') {
                        console.error('BBWS option not found! Available options:');
                        $('#bbws option').each(function() {
                            console.error('  - Value:', $(this).val(), 'Text:', $(this).text());
                        });
                    }
                }
            }
        });
    }

    // Old dtTambahRow function moved outside document ready - removing duplicate
    function dtTambahRowOld(kat, id) {
        $('#modal-tambah').modal({
            backdrop: 'static',
            keyboard: false
        })
        //  $("#frm-tambah").validate().resetForm();
        //  $('#frm-tambah').trigger('reset');
        //  $('#frm-tambah')[0].reset();

        // $('.card').each(function() {
        //     if($(this).val() == '') {
        //        $(this).css('background-color' , '#FF0000');
        //     }
        // });

        shows()




        $(".form-tambah #valid_2 input").prop('readonly', true)
        $(".form-tambah #valid_2 select").prop('disabled', true)
        $(".form-tambah #valid_2 textarea").prop('readonly', true)
        $(".form-tambah #valid_2 button").prop('disabled', true)

        var prov = ''
        var kab = ''
        var kec = ''
        var kel = ''
        var jen_penerima = ''
        var j_legalitas = ''
        var t_legalitas = ''
        var j_kelamin = ''
        var j_irigasi = ''
        var k_irigasi = ''
        var lbp = ''
        var jen_penerima = ''
        var nm_dae_irigasi = ''
        var bbws = ''



        // Initialize all selectpickers first
        setTimeout(function() {
            initializeAllSelectpickers();
            // Setup cascading dropdown handlers
            setupCascadingDropdowns();
        }, 100);

        // Load all dropdown data asynchronously
        async function loadAllDropdownData() {
            try {
                // Load all basic dropdowns in parallel
                await Promise.all([
                    initComboboxPersiapan('bbws', 23, bbws),
                    initComboboxPersiapan('jen_penerima', 30, jen_penerima),
                    initComboboxPersiapan('j_legalitas', 24, j_legalitas),
                    initComboboxPersiapan('t_legalitas', 42, t_legalitas),
                    initComboboxPersiapan('j_irigasi', 27, j_irigasi),
                    initComboboxPersiapan('k_irigasi', 28, k_irigasi),
                    initComboboxPersiapan('j_kelamin', 26, j_kelamin),
                    initComboboxPersiapan('lbp', 29, lbp)
                ]);

                console.log('All dropdown data loaded successfully');
            } catch (error) {
                console.error('Error loading dropdown data:', error);
            }
        }

        // Start loading dropdown data
        loadAllDropdownData();

        //  initCombobox('nm_dae_irigasi', 31);
        //alert(dt[0].jns_kelamin);
        if (group == 2) {
            $("#btn_tambah_edit").css('display', 'block')
            $("#btn_verifikasi").css('display', 'none')
        } else {
            $("#btn_tambah_edit").css('display', 'block')
        }

        if (group == 7) {
            $(".form-tambah .form-group #prov").prop('disabled', true)
            $(".form-tambah .form-group #bbws").prop('disabled', true)
        }

        $('#nm_tpm').prop('readonly', true);
        if (kat == 'tambah') {
            $("#profil_p3_tgai").empty();
            $("#kehadiran_sosialisasi_tingkat_penerima_p3_tgai").empty();
            $("#musyawarah_desa").empty();
            $("#id").val('')
            $('#nm_tpm').val(nama_user);
            bbws = satker
            prov = kd_prov_ses
            //  refreshSelectbootvar('prov', 36, 'kd_satker', bbws);
            //refreshSelectbootvar('kab', 20, 'kd_prov', prov);
            getFormUser(group, kat, '', thangs);

            //  $('.tbhItem').text('Tambah Paket Pagu');
        } else {

            $('#prov').selectpicker('refresh');
            $('#kab').selectpicker('refresh');
            $('#kec').selectpicker('refresh');
            $('#desa').selectpicker('refresh');
            $('#nm_dae_irigasi').selectpicker('refresh');
            $('#k_irigasi').selectpicker('refresh');

            var dt = tampil_data('v_all_kegiatan', 'id_giat', id);
            valid_1 = dt[0].valid_1_p1
            if (valid_1 == 'Data Sudah Lengkap') {
                $(".form-tambah #valid_2 input").prop('readonly', false)
                $(".form-tambah #valid_2 select").prop('disabled', false)
                $(".form-tambah #valid_2 textarea").prop('readonly', false)
                $(".form-tambah #valid_2 button").prop('disabled', false)
            }
            j_legalitas = dt[0].id_jnslgl
            jen_penerima = dt[0].id_jnsp3a
            t_legalitas = dt[0].thn_berdiri
            j_kelamin = dt[0].jns_kelamin
            j_irigasi = dt[0].id_jnsdi
            k_irigasi = dt[0].id_kwdi
            lbp = dt[0].pendidikan
            nm_dae_irigasi = dt[0].kd_di
            prov = dt[0].kd_prov
            kab = dt[0].kd_kabkot
            kec = dt[0].kd_kec
            kel = dt[0].kd_desa
            bbws = dt[0].kd_satker

            // Load dropdown data for edit mode with proper cascading sequence
            async function loadEditDropdownData() {
                try {
                    console.log('Loading edit mode data with values:', {
                        prov: prov, kab: kab, kec: kec, kel: kel, nm_dae_irigasi: nm_dae_irigasi
                    });

                    // Load all basic dropdowns with their values
                    await Promise.all([
                        initComboboxPersiapan('bbws', 23, bbws),
                        initComboboxPersiapan('jen_penerima', 30, jen_penerima),
                        initComboboxPersiapan('j_legalitas', 24, j_legalitas),
                        initComboboxPersiapan('t_legalitas', 42, t_legalitas),
                        initComboboxPersiapan('j_irigasi', 27, j_irigasi),
                        initComboboxPersiapan('k_irigasi', 28, k_irigasi),
                        initComboboxPersiapan('j_kelamin', 26, j_kelamin),
                        initComboboxPersiapan('lbp', 29, lbp)
                    ]);

                    // Load cascading dropdowns in sequence if we have the data
                    if (prov) {
                        console.log('Loading kabupaten for provinsi:', prov);
                        await refreshCombobox5Persiapan('kab', 20, 'kd_prov::tahun', prov + '::' + thangs, kab);

                        if (kab) {
                            console.log('Loading kecamatan for kabupaten:', kab);
                            await refreshCombobox5Persiapan('kec', 21, 'kd_kabkot::tahun', kab + '::' + thangs, kec);

                            if (kec) {
                                console.log('Loading desa for kecamatan:', kec);
                                await refreshCombobox5Persiapan('desa', 22, 'kd_camat::tahun', kec + '::' + thangs, kel);

                                // Load nm_dae_irigasi based on kecamatan
                                console.log('Loading daerah irigasi for kecamatan:', kec);
                                await refreshCombobox5Persiapan('nm_dae_irigasi', 31, 'kd_kec::tahun', kec + '::' + thangs, nm_dae_irigasi);
                            }
                        }
                    }

                    console.log('All edit mode dropdown data loaded successfully');
                } catch (error) {
                    console.error('Error loading edit mode dropdown data:', error);
                }
            }

            // Start loading dropdown data for edit mode
            loadEditDropdownData();

            var dt_file = tampil_data('dok_kegiatanp3a', 'id_giat', id);
            for (let i = 0; i < dt_file.length; i++) {
                var judul = dt_file[i].judul_dok;
                var filepath = dt_file[i].filepath.replace("/sismonp3tgai/uploads/", "/uploads/");
                var timestamp = new Date().getTime();
                $("#" + judul).html("<a class='fancybox' target='_blank' href='" + filepath + "/" + dt_file[i].filename +
                    "'><img 'class='img-responsive' style='width:100%;height:200px;' src='" + filepath + "/" + dt_file[
                        i].filename + "?t=" + timestamp + "'></a>")
                $("#F_" + judul).val(judul)

            }

            $('#nm_penerima').val(dt[0].nm_pppa);
            $('#id').val(dt[0].id_giat);
            $('#nm_ketua').val(dt[0].nm_ketua);
            $('#ala_ketua').val(dt[0].alamat_ketua);
            $('#no_ketua').val(dt[0].kontak_ketua);
            formatNpwp(dt[0].npwp_p3a)
            // $('#no_npwp').val(dt[0].npwp_p3a);
            $('#nm_tpm').val(dt[0].nm_tpm);
            $('#jum_dp').val(dt[0].jml_desa);
            $('#no_p').val(dt[0].no_pntp_p3a);
            if (dt[0].tgl_pntp_p3a != null) {
                $('#tgl_pen').val(dt[0].tgl_pntp_p3a.split(' ')[0]);
            }
            // $('#j_kelamin').val();
            //$('#lbp').val();
            $('#ala_tpm').val(dt[0].alamat_tpm);
            if (dt[0].tgl_sosialisasi != null) {
                $('#tgl_pelaksana').val(dt[0].tgl_sosialisasi.split(' ')[0]);
            }
            $('#jum_pes_l').val(dt[0].sos_pria);
            $('#jum_pes_p').val(dt[0].sos_wanita);
            var has = parseInt(dt[0].sos_pria) + parseInt(dt[0].sos_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_t').val(has);
            $('#jum_lan').val(dt[0].sos_lansia);
            // $('#nm_dae_irigasi').val();
            // $('#j_irigasi').val();
            // $('#k_irigasi').val();
            $('#jum_ket_l').val(dt[0].jm_pengurus_pria);
            $('#jum_ket_p').val(dt[0].jm_pengurus_wanita);
            var has = parseInt(dt[0].jm_pengurus_pria) + parseInt(dt[0].jm_pengurus_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_ket_pgrus').val(has);
            $('#jum_ben_p').val(dt[0].jm_bendahara_wanita);
            $('#jum_ben_l').val(dt[0].jm_bendahara_pria);
            $('#jum_sek_p').val(dt[0].jm_sekretaris_wanita);
            $('#jum_sek_l').val(dt[0].jm_sekretaris_pria);
            $('#jum_anggota').val(dt[0].jml_anggota);

            $('#jum_tim_per_l').val(dt[0].timsiap_pria);
            $('#jum_tim_per_p').val(dt[0].timsiap_wanita);
            var has = parseInt(dt[0].timsiap_pria) + parseInt(dt[0].timsiap_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_tim_total').val(has);
            $('#jum_tim_pelak_l').val(dt[0].timlak_pria);
            $('#jum_tim_pelak_p').val(dt[0].timlak_wanita);

            has = parseInt(dt[0].timlak_pria) + parseInt(dt[0].timlak_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_tim_pelak').val(has);
            $('#jum_tim_pngws_l').val(dt[0].timwas_pria);
            $('#jum_tim_pngws_p').val(dt[0].timwas_wanita);

            has = parseInt(dt[0].timwas_pria) + parseInt(dt[0].timwas_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_tim_pngws').val(has);
            if (dt[0].musy_desa != null) {
                $('#tgl_pelak').val(dt[0].musy_desa.split(' ')[0]);
            }

            $('#jum_pese_l').val(dt[0].musdes_pria);
            $('#jum_pese_p').val(dt[0].musdes_wanita);
            $('#jum_pese_lan').val(dt[0].musdes_lansia);
            has = parseInt(dt[0].musdes_pria) + parseInt(dt[0].musdes_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_tot_pes').val(has);

            getFormUser(group, kat, id);
            // refreshCombobox5('prov', 36, 'kd_satker::1', bbws+'::1',prov);
            // refreshCombobox5('kab', 20, 'kd_prov::1', prov+'::'+'1', kab);
            // refreshCombobox5('kec', 21, 'kd_kabkot::1', kab+'::'+'1', kec);
            // refreshCombobox5('desa', 22, 'kd_camat::1', kec+'::'+'1', kel);




        }

        $('body div#modal-tambah.modal').one('shown.bs.modal', function (e) {
            // Wait for all content to be loaded and selectpickers to be initialized
            async function setAllValues() {
                try {
                    // Wait a bit for modal to fully render
                    await new Promise(resolve => setTimeout(resolve, 300));

                    // Ensure all selectpickers are initialized
                    initializeAllSelectpickers();

                    // Wait for wilayah data to load if in edit mode
                    if (kat === 'edit' || kat === 'detail') {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }

                    // Set values for all selectpickers
                    console.log('Setting selectpicker values...');

                    // Main form selectpickers - values should already be set by the promise-based functions
                    // But we'll refresh them to ensure they're displayed correctly
                    if (bbws) {
                        $('#bbws').val(bbws);
                        $('#bbws').selectpicker('refresh');
                        console.log('Set bbws:', bbws);
                    }
                    if (j_legalitas) {
                        $('#j_legalitas').val(j_legalitas);
                        $('#j_legalitas').selectpicker('refresh');
                        console.log('Set j_legalitas:', j_legalitas);
                    }
                    if (t_legalitas) {
                        $('#t_legalitas').val(t_legalitas);
                        $('#t_legalitas').selectpicker('refresh');
                        console.log('Set t_legalitas:', t_legalitas);
                    }
                    if (j_kelamin) {
                        $('#j_kelamin').val(j_kelamin);
                        $('#j_kelamin').selectpicker('refresh');
                        console.log('Set j_kelamin:', j_kelamin);
                    }
                    if (j_irigasi) {
                        $('#j_irigasi').val(j_irigasi);
                        $('#j_irigasi').selectpicker('refresh');
                        console.log('Set j_irigasi:', j_irigasi);
                    }
                    if (k_irigasi) {
                        $('#k_irigasi').val(k_irigasi);
                        $('#k_irigasi').selectpicker('refresh');
                        console.log('Set k_irigasi:', k_irigasi);
                    }
                    if (jen_penerima) {
                        $('#jen_penerima').val(jen_penerima);
                        $('#jen_penerima').selectpicker('refresh');
                        console.log('Set jen_penerima:', jen_penerima);
                    }
                    if (nm_dae_irigasi) {
                        $('#nm_dae_irigasi').val(nm_dae_irigasi);
                        $('#nm_dae_irigasi').selectpicker('refresh');
                        console.log('Set nm_dae_irigasi:', nm_dae_irigasi);
                    }
                    if (lbp) {
                        $('#lbp').val(lbp);
                        $('#lbp').selectpicker('refresh');
                        console.log('Set lbp:', lbp);
                    }

                    // Set values for dynamically loaded wilayah selectpickers
                    if (prov) {
                        $('#prov').val(prov);
                        $('#prov').selectpicker('refresh');
                        console.log('Set prov:', prov);
                    }
                    if (kab) {
                        $('#kab').val(kab);
                        $('#kab').selectpicker('refresh');
                        console.log('Set kab:', kab);
                    }
                    if (kec) {
                        $('#kec').val(kec);
                        $('#kec').selectpicker('refresh');
                        console.log('Set kec:', kec);
                    }
                    if (kel) {
                        $('#desa').val(kel);
                        $('#desa').selectpicker('refresh');
                        console.log('Set desa:', kel);
                    }

                    console.log('All selectpicker values set successfully');

                    // Debug final state
                    debugSelectpickerValues();

                    // Force set values as fallback after a short delay
                    setTimeout(function() {
                        forceSetSelectpickerValues();
                        debugSelectpickerValues();

                        // Test cascading dropdowns
                        testCascadingDropdowns();

                        // Test Nama Daerah Irigasi specifically if kecamatan is selected
                        var currentKec = $('#kec').val();
                        if (currentKec) {
                            console.log('Testing Nama Daerah Irigasi for current kecamatan:', currentKec);
                            testNamaDaerahIrigasi(currentKec);
                        }

                        // Test BBWS dropdown
                        console.log('Testing BBWS dropdown...');
                        testBBWS();
                    }, 1000);

                } catch (error) {
                    console.error('Error setting selectpicker values:', error);
                    // Try force set as fallback
                    setTimeout(function() {
                        forceSetSelectpickerValues();
                    }, 500);
                }
            }

            setAllValues();
        });



        //alert(group);



        // $("#bbws").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {

        //     refreshSelectbootvar('prov', 36, 'kd_satker', this.value);
        //  });



        if (kat == 'detail') {
            // alert('fsdfcsf');
            $(".form-tambah .form-group input").prop('readonly', true)
            $(".form-tambah .form-group input").css({
                "border": "0px",
                "background": "#e9ecef45"
            })
            $(".form-tambah .form-group select").prop('disabled', true)
            $(".form-tambah .form-group select").css({
                "border": "0px",
                "background": "#e9ecef45"
            })
            $(".form-tambah .form-group textarea").prop('readonly', true)
            $(".form-tambah .form-group textarea").css({
                "border": "0px",
                "background": "#e9ecef45"
            })
            $(".form-tambah .form-group button").prop('disabled', true)
            $(".form-tambah .form-group button").css({
                "border": "0px",
                "background": "#e9ecef45"
            })
            $(".form-tambah .form-group").css({
                "border-bottom": "1px solid #80808054"
            })
            $("#file_npwp").hide()
            $("#fot_musya").hide()
            $("#fot_sosial").hide()
            if (group == 2) {
                $("#btn_tambah_edit").css('display', 'none')
                $("#btn_verifikasi").css('display', 'block')
                $("#btn_verifikasi button").prop('disabled', true)
                $("#btn_verifikasi").attr('title', 'Data belum lengkap, tidak bisa mem-validasi')
                if (dt[0].valid_1_p1 == 'Data Sudah Lengkap' && dt[0].valid_2_p1 == 'Data Sudah Lengkap') {
                    $("#btn_verifikasi button").prop('disabled', false)
                    $("#btn_verifikasi").attr('title', '')

                }
            } else {
                $("#btn_tambah_edit").css('display', 'none')

            }


        }
        $(".form-tambah #jum_tim_total").prop('readonly', true)
        $(".form-tambah #jum_tim_pelak").prop('readonly', true)
        $(".form-tambah #jum_tim_pngws").prop('readonly', true)
        $(".form-tambah #jum_t").prop('readonly', true)
        $(".form-tambah #jum_tot_pes").prop('readonly', true)

        $(".decformat").keyup(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;
            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            });
        });


        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('#modal-tambah').modal('show');
        $('.tbhItem').text('Form persiapan');
        // $("#urutans").empty();
        // $("#urutans").val("2");

        //--- end dt tambah row ---

    }

    function handlePaste(e) {
        var clipboardData, pastedData;

        // Stop data actually being pasted into div
        e.stopPropagation();
        e.preventDefault();

        // Get pasted data via clipboard API
        clipboardData = e.clipboardData || window.clipboardData;
        pastedData = clipboardData.getData('Text');

        // Do whatever with pasteddata
        formatNpwp(pastedData);
    }

    document.getElementById('no_npwp').addEventListener('paste', handlePaste);

    function update_verifikasi(stat) {
        var mustKMB = "<?php echo $this->session->users['id_user']; ?>";
        var id = $("#id").val()
        // var url = "<?php //echo base_url(); ?>persiapan/update_data/" + id + "/" + stat + "/" + mustKMB;
        var text = ''
        if (stat == 1) {
            text = "Anda yakin data tersebut valid ?"
        } else {
            text = "Anda yakin data tersebut tidak valid ?"
        }


        data_post = {
            "id": id,
            "stat": stat,
            "mustKMB": mustKMB,
            "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>",
        }

        swal({
            title: text,
            //   text: tex,
            icon: "info",
            buttons: true,
            dangerMode: true,
        })
            .then((willDelete) => {
                if (willDelete) {

                        jQuery.ajax({
                            // contentType: 'application/json',
                            type: "post",
                            dataType: "json",
                            data: data_post,
                            // crossDomain: true,
                            url: "<?= base_url('persiapan/update_data'); ?>",
                            success: function(data) {
                                if (data == 0) {
                                    swal({
                                        title: "",
                                        text: "Berhasil Verifikasi",
                                        icon: "success",
                                        showConfirmButton: false,
                                        timer: 3000,
                                        type: "success"
                                    });
                                    table.ajax.reload();
                                    $('#modal-tambah').modal('hide');
                                }


                            },
                            error: function(xhr, ajaxOptions, thrownError) {
                                alert(xhr.status);
                                alert(thrownError);
                            }
                        });

                    // console.log('dmasdhak');
                    // $.get(url).done(function (data) {
                    //     if (data == 0) {
                    //         swal({
                    //             title: "",
                    //             text: "Berhasil Merubah Status",
                    //             icon: "success",
                    //             showConfirmButton: false,
                    //             timer: 3000,
                    //             type: "success"
                    //         });
                    //         table.ajax.reload();
                    //         $('#modal-tambah').modal('hide');
                    //     }
                    // })
                } else {
                    swal("Anda tidak Memverifiaksi data");
                }
            })
    }

    function refreshCombobox5(divname, refindex, refresh_field, refresh_value, selvalue, $rev = 1) {
        var url = null;
        if ($rev === 1) {
            url = urlBase + "lookup/refreshlook4/" + refindex + "/" + refresh_field + "/" + refresh_value;
        } else {
            url = urlBase + "lookup/refreshlook4/" + refindex + "/" + refresh_field + "/" + refresh_value + "/" + 0;
        }
        wgiAjaxCache(url, function (ajaxdata) {
            jdata = JSON.parse(ajaxdata);
            $('#' + divname).empty();
            $('#' + divname).append(new Option("--Pilih--", ''));
            $.each(jdata, function (i, el) {
                $('#' + divname).append(new Option(el.val, el.id));
            });

            // alert(selvalue)

            if (selvalue) {
                $('#' + divname).val(selvalue).selectpicker('refresh');
            } else {
                $('#' + divname).selectpicker('refresh')
            }
        });
    }

    function dtDeleteRow(id) {

        swal({
            title: "Data di semua tahapan akan terhapus, apakah anda yakin ingin menghapus  ?",
            // text: tex,
            icon: "warning",
            buttons: true,
            dangerMode: true,
        })
            .then((willDelete) => {
                if (willDelete) {
                    var url = ''
                    url = "<?php echo base_url(); ?>" + "persiapan/ajax_delete/" + id;
                    $.get(url).done(function (data) {
                        if (data == 0) {
                            swal({
                                title: "",
                                text: "Berhasil Menghapus Data",
                                icon: "success",
                                showConfirmButton: false,
                                timer: 3000,
                                type: "success"
                            });
                            table.ajax.reload();
                        }
                    })
                } else {
                    swal("Anda Tidak Menjadi Menghapus Data");
                }
            });
    }

    function hitung(yuhu, hasil) {
        var total = 0;
        var yuhu = yuhu.split('+')
        var satu = $("#" + yuhu[0]).val()
        var dua = $("#" + yuhu[1]).val()
        if (isNaN(parseInt(satu))) {
            satu = 0
        }
        if (isNaN(parseInt(dua))) {
            dua = 0
        }
        var to = parseInt(satu) + parseInt(dua)
        $("#" + hasil).val(to)
        // for (let i = 0; i < satu.length; i++) {
        //     var satu =  $("#"+satu[i]).val()
        //     if (isNaN(parseInt(satu))) {
        //       satu = 0
        //   }
        //     parseInt(total) += satu;
        //     }
        //     alert(total)



    }

    function isNumber(evt, a) {

        evt = (evt) ? evt : window.event;
        var charCode = (evt.which) ? evt.which : evt.keyCode;
        if (charCode > 31 && (charCode < 48 || charCode > 57)) {
            return false;
        }
        return true;
    }






    // }


    function get_extentsion_file(file) {
        var extension = file.substr((file.lastIndexOf('.') + 1));
        switch (extension) {
            case 'jpg':
            case 'png':
            case 'PNG':
            case 'jpeg':
            case 'gif':
            case 'JPG':
                return 'feather icon-image'; // There's was a typo in the example where
                break; // the alert ended with pdf instead of gif.
            case 'zip':
            case 'rar':
                //alert('was zip rar');
                return 'feather icon-archive';
                break;
            case 'pdf':
                return 'feather icon-file-text';
            case 'xlsx':
                return 'feather icon-file-text';
                break;
            default:
                return 'feather icon-file-text';

        }
    }

    function hapus_lampiran(id) {
        if (confirm('Yakin untuk menghapus data ini?')) {

            var url = "<?php echo base_url(); ?>" + "persiapan/hps_lampiran/" + id;
            var params = {
                "formData": {},
                "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"
            };
            $.post(url, params)
                .done(function (data) {
                    var tlist_paket = $('#tlist_paket').DataTable();
                    tlist_paket.ajax.reload()
                    var tab = $('#table_id2').DataTable();
                    tab.ajax.reload();;
                })
                .fail(function () {
                    alert("error");
                })
        }
    }

    function tampil_data(table, colum, id) {
        var url = ''
        var tadata = ''
        urls = "<?php echo base_url(); ?>persiapan/tampildata/" + table + "/" + colum + "/" + id;
        $.ajax({
            url: urls,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                tadata = data;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return tadata;
    }

    function cek_kolom() {
        $(".form-tambah #valid_2 input").prop('readonly', true)
        $(".form-tambah #valid_2 select").prop('disabled', true)
        $(".form-tambah #valid_2 textarea").prop('readonly', true)
        $(".form-tambah #valid_2 button").prop('disabled', true)
        if ($(".form-tambah").valid() == true) {
            $(".form-tambah #valid_2 input").prop('readonly', false)
            $(".form-tambah #valid_2 select").prop('disabled', false)
            $(".form-tambah #valid_2 textarea").prop('readonly', false)
            $(".form-tambah #valid_2 button").prop('disabled', false)
        }
        var lab = $("label").text().split("This field is required.").length - 1;
        if (lab == 0) {

        }
        $(".form-tambah label").remove()
        $(".form-tambah label").text()
    }

    // Global test function for Nama Daerah Irigasi - can be called from browser console
    window.testIrigasi = function(kecamatanId) {
        if (!kecamatanId) {
            console.log('Usage: testIrigasi("kecamatan_id")');
            console.log('Example: testIrigasi("3201010")');
            return;
        }
        testNamaDaerahIrigasi(kecamatanId);
    }

    // Global function to manually trigger kecamatan change
    window.triggerKecamatanChange = function() {
        var kecValue = $('#kec').val();
        if (kecValue) {
            console.log('Manually triggering kecamatan change for:', kecValue);
            $('#kec').trigger('changed.bs.select');
        } else {
            console.log('No kecamatan selected');
        }
    }

    // Global test function for BBWS dropdown
    window.testBBWS = function(bbwsId) {
        console.log('Testing BBWS dropdown...');
        console.log('Current BBWS value:', $('#bbws').val());
        console.log('BBWS options count:', $('#bbws option').length);

        if (bbwsId) {
            console.log('Setting BBWS to:', bbwsId);
            $('#bbws').val(bbwsId);
            $('#bbws').selectpicker('refresh');
            console.log('New BBWS value:', $('#bbws').val());
        }

        // Test API call
        var url = WGI_APP_BASE_URL + "lookup/fieldlook/23";
        console.log('Testing BBWS API:', url);

        $.ajax({
            url: url,
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                console.log('BBWS API success:', data);
                console.log('Number of BBWS options:', data.length);
                if (data.length > 0) {
                    console.log('Sample BBWS:', data[0]);
                }
            },
            error: function(xhr, status, error) {
                console.error('BBWS API failed:', error);
                console.error('Response:', xhr.responseText);
            }
        });
    }

    // Move dtTambahRow function outside document ready to make it globally accessible
    window.dtTambahRow = function(kat, id) {
        $('#modal-tambah').modal({
            backdrop: 'static',
            keyboard: false
        })

        shows()

        $(".form-tambah #valid_2 input").prop('readonly', true)
        $(".form-tambah #valid_2 select").prop('disabled', true)
        $(".form-tambah #valid_2 textarea").prop('readonly', true)
        $(".form-tambah #valid_2 button").prop('disabled', true)

        var prov = ''
        var kab = ''
        var kec = ''
        var kel = ''
        var jen_penerima = ''
        var j_legalitas = ''
        var t_legalitas = ''
        var j_kelamin = ''
        var j_irigasi = ''
        var k_irigasi = ''
        var lbp = ''
        var jen_penerima = ''
        var nm_dae_irigasi = ''
        var bbws = ''

        // Initialize all selectpickers first
        setTimeout(function() {
            initializeAllSelectpickers();
            setupCascadingDropdowns();
        }, 100);

        // Set initial values based on mode BEFORE loading dropdown data
        if (kat == 'tambah') {
            bbws = satker;
            prov = kd_prov_ses;
            console.log('Add mode - Setting initial values:', {bbws: bbws, prov: prov});
        }

        // Load all dropdown data asynchronously
        async function loadAllDropdownData() {
            try {
                console.log('Loading dropdown data with bbws value:', bbws);
                await Promise.all([
                    initComboboxPersiapan('bbws', 23, bbws),
                    initComboboxPersiapan('jen_penerima', 30, jen_penerima),
                    initComboboxPersiapan('j_legalitas', 24, j_legalitas),
                    initComboboxPersiapan('t_legalitas', 42, t_legalitas),
                    initComboboxPersiapan('j_irigasi', 27, j_irigasi),
                    initComboboxPersiapan('k_irigasi', 28, k_irigasi),
                    initComboboxPersiapan('j_kelamin', 26, j_kelamin),
                    initComboboxPersiapan('lbp', 29, lbp)
                ]);
                console.log('All dropdown data loaded successfully');
            } catch (error) {
                console.error('Error loading dropdown data:', error);
            }
        }

        loadAllDropdownData();

        if (group == 2) {
            $("#btn_tambah_edit").css('display', 'block')
            $("#btn_verifikasi").css('display', 'none')
        } else {
            $("#btn_tambah_edit").css('display', 'block')
        }

        if (group == 7) {
            $(".form-tambah .form-group #prov").prop('disabled', true)
            $(".form-tambah .form-group #bbws").prop('disabled', true)
        }

        $('#nm_tpm').prop('readonly', true);

        if (kat == 'tambah') {
            $("#profil_p3_tgai").empty();
            $("#kehadiran_sosialisasi_tingkat_penerima_p3_tgai").empty();
            $("#musyawarah_desa").empty();
            $("#id").val('')
            $('#nm_tpm').val(nama_user);
            // bbws and prov already set above before loading dropdown data
            getFormUser(group, kat, '', thangs);
        } else {
            $('#prov').selectpicker('refresh');
            $('#kab').selectpicker('refresh');
            $('#kec').selectpicker('refresh');
            $('#desa').selectpicker('refresh');
            $('#nm_dae_irigasi').selectpicker('refresh');
            $('#k_irigasi').selectpicker('refresh');

            var dt = tampil_data('v_all_kegiatan', 'id_giat', id);
            valid_1 = dt[0].valid_1_p1
            if (valid_1 == 'Data Sudah Lengkap') {
                $(".form-tambah #valid_2 input").prop('readonly', false)
                $(".form-tambah #valid_2 select").prop('disabled', false)
                $(".form-tambah #valid_2 textarea").prop('readonly', false)
                $(".form-tambah #valid_2 button").prop('disabled', false)
            }

            // Set all values from database first
            j_legalitas = dt[0].id_jnslgl
            jen_penerima = dt[0].id_jnsp3a
            t_legalitas = dt[0].thn_berdiri
            j_kelamin = dt[0].jns_kelamin
            j_irigasi = dt[0].id_jnsdi
            k_irigasi = dt[0].id_kwdi
            lbp = dt[0].pendidikan
            nm_dae_irigasi = dt[0].kd_di
            prov = dt[0].kd_prov
            kab = dt[0].kd_kabkot
            kec = dt[0].kd_kec
            kel = dt[0].kd_desa
            bbws = dt[0].kd_satker

            console.log('Edit mode - Values from database:', {
                bbws: bbws, j_legalitas: j_legalitas, jen_penerima: jen_penerima,
                prov: prov, kab: kab, kec: kec, kel: kel, nm_dae_irigasi: nm_dae_irigasi
            });

            // Load dropdown data for edit mode
            async function loadEditDropdownData() {
                try {
                    console.log('Loading edit mode dropdown data with bbws:', bbws);

                    await Promise.all([
                        initComboboxPersiapan('bbws', 23, bbws),
                        initComboboxPersiapan('jen_penerima', 30, jen_penerima),
                        initComboboxPersiapan('j_legalitas', 24, j_legalitas),
                        initComboboxPersiapan('t_legalitas', 42, t_legalitas),
                        initComboboxPersiapan('j_irigasi', 27, j_irigasi),
                        initComboboxPersiapan('k_irigasi', 28, k_irigasi),
                        initComboboxPersiapan('j_kelamin', 26, j_kelamin),
                        initComboboxPersiapan('lbp', 29, lbp)
                    ]);

                    if (prov) {
                        console.log('Loading kabupaten for provinsi:', prov);
                        await refreshCombobox5Persiapan('kab', 20, 'kd_prov::tahun', prov + '::' + thangs, kab);

                        if (kab) {
                            console.log('Loading kecamatan for kabupaten:', kab);
                            await refreshCombobox5Persiapan('kec', 21, 'kd_kabkot::tahun', kab + '::' + thangs, kec);

                            if (kec) {
                                console.log('Loading desa for kecamatan:', kec);
                                await refreshCombobox5Persiapan('desa', 22, 'kd_camat::tahun', kec + '::' + thangs, kel);

                                console.log('Loading daerah irigasi for kecamatan:', kec);
                                await refreshCombobox5Persiapan('nm_dae_irigasi', 31, 'kd_kec::tahun', kec + '::' + thangs, nm_dae_irigasi);
                            }
                        }
                    }

                    console.log('All edit mode dropdown data loaded successfully');
                } catch (error) {
                    console.error('Error loading edit mode dropdown data:', error);
                }
            }

            loadEditDropdownData();

            var dt_file = tampil_data('dok_kegiatanp3a', 'id_giat', id);
            for (let i = 0; i < dt_file.length; i++) {
                var judul = dt_file[i].judul_dok;
                var filepath = dt_file[i].filepath.replace("/sismonp3tgai/uploads/", "/uploads/");
                var timestamp = new Date().getTime();
                $("#" + judul).html("<a class='fancybox' target='_blank' href='" + filepath + "/" + dt_file[i].filename +
                    "'><img 'class='img-responsive' style='width:100%;height:200px;' src='" + filepath + "/" + dt_file[
                        i].filename + "?t=" + timestamp + "'></a>")
                $("#F_" + judul).val(judul)
            }

            $('#nm_penerima').val(dt[0].nm_pppa);
            $('#id').val(dt[0].id_giat);
            $('#nm_ketua').val(dt[0].nm_ketua);
            $('#ala_ketua').val(dt[0].alamat_ketua);
            $('#no_ketua').val(dt[0].kontak_ketua);
            formatNpwp(dt[0].npwp_p3a)
            $('#nm_tpm').val(dt[0].nm_tpm);
            $('#jum_dp').val(dt[0].jml_desa);
            $('#no_p').val(dt[0].no_pntp_p3a);
            if (dt[0].tgl_pntp_p3a != null) {
                $('#tgl_pen').val(dt[0].tgl_pntp_p3a.split(' ')[0]);
            }
            $('#ala_tpm').val(dt[0].alamat_tpm);
            if (dt[0].tgl_sosialisasi != null) {
                $('#tgl_pelaksana').val(dt[0].tgl_sosialisasi.split(' ')[0]);
            }
            $('#jum_pes_l').val(dt[0].sos_pria);
            $('#jum_pes_p').val(dt[0].sos_wanita);
            var has = parseInt(dt[0].sos_pria) + parseInt(dt[0].sos_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_t').val(has);
            $('#jum_lan').val(dt[0].sos_lansia);
            $('#jum_ket_l').val(dt[0].jm_pengurus_pria);
            $('#jum_ket_p').val(dt[0].jm_pengurus_wanita);
            var has = parseInt(dt[0].jm_pengurus_pria) + parseInt(dt[0].jm_pengurus_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_ket_pgrus').val(has);
            $('#jum_ben_p').val(dt[0].jm_bendahara_wanita);
            $('#jum_ben_l').val(dt[0].jm_bendahara_pria);
            $('#jum_sek_p').val(dt[0].jm_sekretaris_wanita);
            $('#jum_sek_l').val(dt[0].jm_sekretaris_pria);
            $('#jum_anggota').val(dt[0].jml_anggota);

            $('#jum_tim_per_l').val(dt[0].timsiap_pria);
            $('#jum_tim_per_p').val(dt[0].timsiap_wanita);
            var has = parseInt(dt[0].timsiap_pria) + parseInt(dt[0].timsiap_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_tim_total').val(has);
            $('#jum_tim_pelak_l').val(dt[0].timlak_pria);
            $('#jum_tim_pelak_p').val(dt[0].timlak_wanita);

            has = parseInt(dt[0].timlak_pria) + parseInt(dt[0].timlak_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_tim_pelak').val(has);
            $('#jum_tim_pngws_l').val(dt[0].timwas_pria);
            $('#jum_tim_pngws_p').val(dt[0].timwas_wanita);

            has = parseInt(dt[0].timwas_pria) + parseInt(dt[0].timwas_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_tim_pngws').val(has);
            if (dt[0].musy_desa != null) {
                $('#tgl_pelak').val(dt[0].musy_desa.split(' ')[0]);
            }

            $('#jum_pese_l').val(dt[0].musdes_pria);
            $('#jum_pese_p').val(dt[0].musdes_wanita);
            $('#jum_pese_lan').val(dt[0].musdes_lansia);
            has = parseInt(dt[0].musdes_pria) + parseInt(dt[0].musdes_wanita)
            if (isNaN(has)) {
                has = '';
            }
            $('#jum_tot_pes').val(has);

            getFormUser(group, kat, id);
        }

        $('body div#modal-tambah.modal').one('shown.bs.modal', function (e) {
            async function setAllValues() {
                try {
                    await new Promise(resolve => setTimeout(resolve, 300));
                    initializeAllSelectpickers();

                    if (kat === 'edit' || kat === 'detail') {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }

                    console.log('Setting selectpicker values...');

                    if (bbws) {
                        $('#bbws').val(bbws);
                        $('#bbws').selectpicker('refresh');
                        console.log('Set bbws:', bbws);
                    }
                    if (j_legalitas) {
                        $('#j_legalitas').val(j_legalitas);
                        $('#j_legalitas').selectpicker('refresh');
                        console.log('Set j_legalitas:', j_legalitas);
                    }
                    if (t_legalitas) {
                        $('#t_legalitas').val(t_legalitas);
                        $('#t_legalitas').selectpicker('refresh');
                        console.log('Set t_legalitas:', t_legalitas);
                    }
                    if (j_kelamin) {
                        $('#j_kelamin').val(j_kelamin);
                        $('#j_kelamin').selectpicker('refresh');
                        console.log('Set j_kelamin:', j_kelamin);
                    }
                    if (j_irigasi) {
                        $('#j_irigasi').val(j_irigasi);
                        $('#j_irigasi').selectpicker('refresh');
                        console.log('Set j_irigasi:', j_irigasi);
                    }
                    if (k_irigasi) {
                        $('#k_irigasi').val(k_irigasi);
                        $('#k_irigasi').selectpicker('refresh');
                        console.log('Set k_irigasi:', k_irigasi);
                    }
                    if (jen_penerima) {
                        $('#jen_penerima').val(jen_penerima);
                        $('#jen_penerima').selectpicker('refresh');
                        console.log('Set jen_penerima:', jen_penerima);
                    }
                    if (nm_dae_irigasi) {
                        $('#nm_dae_irigasi').val(nm_dae_irigasi);
                        $('#nm_dae_irigasi').selectpicker('refresh');
                        console.log('Set nm_dae_irigasi:', nm_dae_irigasi);
                    }
                    if (lbp) {
                        $('#lbp').val(lbp);
                        $('#lbp').selectpicker('refresh');
                        console.log('Set lbp:', lbp);
                    }

                    if (prov) {
                        $('#prov').val(prov);
                        $('#prov').selectpicker('refresh');
                        console.log('Set prov:', prov);
                    }
                    if (kab) {
                        $('#kab').val(kab);
                        $('#kab').selectpicker('refresh');
                        console.log('Set kab:', kab);
                    }
                    if (kec) {
                        $('#kec').val(kec);
                        $('#kec').selectpicker('refresh');
                        console.log('Set kec:', kec);
                    }
                    if (kel) {
                        $('#desa').val(kel);
                        $('#desa').selectpicker('refresh');
                        console.log('Set desa:', kel);
                    }

                    console.log('All selectpicker values set successfully');
                    debugSelectpickerValues();

                    setTimeout(function() {
                        forceSetSelectpickerValues();
                        debugSelectpickerValues();
                        testCascadingDropdowns();
                    }, 1000);

                } catch (error) {
                    console.error('Error setting selectpicker values:', error);
                    setTimeout(function() {
                        forceSetSelectpickerValues();
                    }, 500);
                }
            }

            setAllValues();
        });

        if (kat == 'detail') {
            $(".form-tambah .form-group input").prop('readonly', true)
            $(".form-tambah .form-group input").css({
                "border": "0px",
                "background": "#e9ecef45"
            })
            $(".form-tambah .form-group select").prop('disabled', true)
            $(".form-tambah .form-group select").css({
                "border": "0px",
                "background": "#e9ecef45"
            })
            $(".form-tambah .form-group textarea").prop('readonly', true)
            $(".form-tambah .form-group textarea").css({
                "border": "0px",
                "background": "#e9ecef45"
            })
            $(".form-tambah .form-group button").prop('disabled', true)
            $(".form-tambah .form-group button").css({
                "border": "0px",
                "background": "#e9ecef45"
            })
            $(".form-tambah .form-group").css({
                "border-bottom": "1px solid #80808054"
            })
            $("#file_npwp").hide()
            $("#fot_musya").hide()
            $("#fot_sosial").hide()
            if (group == 2) {
                $("#btn_tambah_edit").css('display', 'none')
                $("#btn_verifikasi").css('display', 'block')
                $("#btn_verifikasi button").prop('disabled', true)
                $("#btn_verifikasi").attr('title', 'Data belum lengkap, tidak bisa mem-validasi')
                if (dt[0].valid_1_p1 == 'Data Sudah Lengkap' && dt[0].valid_2_p1 == 'Data Sudah Lengkap') {
                    $("#btn_verifikasi button").prop('disabled', false)
                    $("#btn_verifikasi").attr('title', '')
                }
            } else {
                $("#btn_tambah_edit").css('display', 'none')
            }
        }

        $(".form-tambah #jum_tim_total").prop('readonly', true)
        $(".form-tambah #jum_tim_pelak").prop('readonly', true)
        $(".form-tambah #jum_tim_pngws").prop('readonly', true)
        $(".form-tambah #jum_t").prop('readonly', true)
        $(".form-tambah #jum_tot_pes").prop('readonly', true)

        $(".decformat").keyup(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            });
        });

        $('.form-group').removeClass('has-error');
        $('.help-block').empty();
        $('#modal-tambah').modal('show');
        $('.tbhItem').text('Form persiapan');
    }

    $(document).ready(function () {
        isBlocked = sessLocked;
        if (group == 7) {

            if (isBlocked == 1) {
                $("#btnTambah").hide()
            } else {
                $("#btnTambah").show()
            }

        }



        $(".form-tambah").validate({
            ignore: ':not(select:visible,button:visible, input:visible, textarea:visible, .selectpicker)',
            rules: {
                bbws: {
                    required: true
                }, //profil
                prov: {
                    required: true
                },
                kab: {
                    required: true
                },
                kec: {
                    required: true
                },
                desa: {
                    required: true
                },
                jen_penerima: {
                    required: true
                },
                nm_penerima: {
                    required: true
                },
                j_legalitas: {
                    required: true
                },
                t_legalitas: {
                    required: true
                },
                nm_ketua: {
                    required: true
                },
                ala_ketua: {
                    required: true
                },
                no_ketua: {
                    required: true
                },
                no_npwp: {
                    required: true
                },
                file_npwp: {
                    required: false
                },
                nm_dae_irigasi: {
                    required: true
                }, //irigasi
                j_irigasi: {
                    required: true
                },
                k_irigasi: {
                    required: true
                },
                jum_ket_l: {
                    required: true
                }, //kepengurusan
                jum_ket_p: {
                    required: true
                },
                jum_ben_p: {
                    required: true
                },
                jum_ben_l: {
                    required: true
                },
                jum_anggota: {
                    required: true
                },
                jum_tim_per_l: {
                    required: true
                }, //swakelola
                jum_tim_per_p: {
                    required: true
                },
                jum_tim_pelak_l: {
                    required: true
                },
                jum_tim_pelak_p: {
                    required: true
                },
                jum_tim_pngws_l: {
                    required: true
                },
                jum_tim_pngws_p: {
                    required: true
                },
                nm_tpm: {
                    required: true
                }, //tpm
                jum_dp: {
                    required: true
                },
                j_kelamin: {
                    required: true
                },
                lbp: {
                    required: true
                },
                ala_tpm: {
                    required: true
                },
                // file_npwp: { required: true  },
                // jum_tim_total: { required: true  },
                // jum_tim_pelak: {required: true },
                // jum_tim_pngws: {required: true },
                // tgl_pelaksana: { required: true  }, //
                // jum_pes_l: { required: true  },
                // jum_pes_p: { required: true  },
                // jum_t: { required: true  },
                // jum_lan: { required: true  },
                // tgl_pelak: { required: true  },
                // jum_pese_l: { required: true  },
                // jum_pese_p: { required: true  },
                // jum_tot_pes: { required: true  },
                // no_p: { required: true  },
                // tgl_pen: { required: true  },
            }
        });
        $(".form-tambah .form-control").keypress(function () {
            cek_kolom();
        });
        $(".form-tambah .form-control").change(function () {
            cek_kolom();
        });
        // bind_combo_thang(thangs);
        //  tes_1()
        listing();
        $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");


        // document.querySelectorAll(".search th").forEach(function (element) {
        //     var title = element.innerText;
        //     element.innerHTML = '<input type="text" class="form-control" placeholder="' + title.trim() +
        //         '">';
        // });

        // table.columns().every(function () {
        //     var that = this;
        //     document.querySelectorAll('input[type=text]').forEach(function (element) {
        //         element.addEventListener("keyup", function (event) {
        //             event.preventDefault();
        //             // console.log('Search index', that.index())
        //             if (that.search() !== this.value) {
        //                 that.search(this.value).draw();
        //             }
        //         });
        //     });

        // });

        $('.fs a').on('click', function (e) {
            e.preventDefault();
            var fs = $(this).text();

            $(".statustahap:first-child").text(fs);
            $(".statustahap:first-child").val(fs);


            table.ajax.reload();

            // return;
            // e.stopPropagation();

        });

        $("#submit").submit(function (e) {

            e.preventDefault();
            // ;
            //  alert(tes_1)
            //   if(tes_1==true){
            var data = $('.form-tambah').serialize();
            // console.log(new FormData(this))
            $.ajax({
                type: 'POST',
                url: "<?php echo base_url('persiapan/insert_data'); ?>",
                data: new FormData(this),
                processData: false,
                contentType: false,
                cache: false,
                async: false,
                success: function (data) {
                    // console.log(data);

                    var ops = ''
                    if (data.split('_')[1] == 'insert') {
                        ops = 'Menambah';
                    } else {
                        ops = 'Merubah';
                    }
                    if (data.split('_')[0] == 0) {
                        swal({
                            icon: "success",
                            text: "Berhasil " + ops + " Data",
                            showConfirmButton: false,
                            timer: 2000,
                            type: "success"
                        });
                        table.ajax.reload();
                        $('#modal-tambah').modal('hide');
                    } else {
                        swal({
                            title: "",
                            text: "Gagal " + ops + " Data",
                            showConfirmButton: false,
                            timer: 1000,
                            icon: "warning"
                        });
                    }

                }
            });
            //  }
        });



    });
</script>