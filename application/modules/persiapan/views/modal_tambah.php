<style>
.control-div {
    font-weight: bold;
}

.sub-title {
    padding: 10px;
    background: #054e8ca1;
    color: white !important;
    font-weight: bold;
}

.error {
    color: red;
    font-size: 11px;
}

.badge-yuhu {
    background-color: #b9b74f;
}
</style>

<!-- <link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet"> -->

<div class="modal fade" id="modal-tambah" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" role="document">
        <form class="form-tambah" id="submit" name="frm-tambah">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title"> Persiapan</h4>
                    <button type="button" class="btn-close"
                        data-bs-dismiss="modal"
                        aria-label="Close">
                        <span
                            aria-hidden="true"></span>
                    </button>
                </div>
                <div class="modal-body">


                    <div class="row">
                        <div class="card col-md-12">
                            <h4 class="sub-title">Profil P3-TGAI</h4>
                            <input type="hidden" id="id" name="id">
                            <input type="hidden" name="<? echo $this->security->get_csrf_token_name();?>"
                                value="<? echo $this->security->get_csrf_hash();?>" />
                            <!-- <div class="form-group row">
                <div class="col-sm-3 col-form-div">Kode Satker</div>
                <div class="col-sm-9">
                <input name="kd_satker" id="kd_satker" type="text" class="form-control" placeholder="">
                </div>
              </div> -->
                            <div id="valid_1">
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Nama BBWS/BWS</div>
                                    <div class="col-sm-9">
                                        <select id="bbws" name="bbws" class="bootstrap-select form-control"
                                            data-live-search="true">
                                        </select>
                                    </div>
                                </div>
                                <!-- <div class=card> -->
                                <div id="form-user">
                                    <!-- <div class="form-group row">
                <div class="col-sm-3 col-form-div">Provinsi</div>
                <div class="col-sm-9">
                <select id="prov" name="prov" class="bootstrap-select form-control" data-live-search="true">
                <option value=''>--Pilih--</option>   
                </select>
                </div>
              </div>
              <div class="form-group row">
                <div class="col-sm-3 col-form-div">Kabupaten</div>
                <div class="col-sm-9">
                <select id="kab" name="kab" class="bootstrap-select form-control" data-live-search="true">
                <option value=''>--Pilih--</option>   
                </select>
                </div>
              </div>
              <div class="form-group row">
                <div class="col-sm-3 col-form-div">Kecamatan</div>
                <div class="col-sm-9">
                <select id="kec" name="kec" class="bootstrap-select  form-control" data-live-search="true">
                <option value=''>--Pilih--</option>     
              </select>
                </div>
              </div>
              <div class="form-group row">
                <div class="col-sm-3 col-form-div">Desa</div>
                <div class="col-sm-9">
                <select id="desa" name="desa" class="bootstrap-select  form-control" data-live-search="true">
                  <option value=''>--Pilih--</option>     
                </select>
                </div>
              </div> -->
                                </div>

                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jenis Penerima P3-TGAI</div>
                                    <div class="col-sm-9">
                                        <select id="jen_penerima" name="jen_penerima"
                                            class="bootstrap-select form-control" data-live-search="true">
                                            <option value=''>--Pilih--</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Nama Penerima P3-TGAI</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="nm_penerima"
                                            name="nm_penerima">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jenis Legalitas</div>
                                    <div class="col-sm-9">
                                        <select id="j_legalitas" name="j_legalitas"
                                            class="bootstrap-select form-control" data-live-search="true">
                                            <option value=''>--Pilih--</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Tahun Legalitas</div>
                                    <div class="col-sm-9">
                                        <select id="t_legalitas" name="t_legalitas"
                                            class="bootstrap-select form-control" data-live-search="true">
                                            <option value=''>--Pilih--</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Nama Ketua</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="nm_ketua"
                                            name="nm_ketua">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Alamat Ketua</div>
                                    <div class="col-sm-9">
                                        <textarea id="ala_ketua" name="ala_ketua" class="form-control"
                                            placeholder=""></textarea>
                                        <span class="input-group-addon"></span>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Nomor Kontak Ketua</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="no_ketua"
                                            name="no_ketua" maxlength="15">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Nomor NPWP P3A/GP3A/IP3A </div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="no_npwp"
                                            name="no_npwp" onkeypress="formatNpwp(this)" maxlength="22">
                                    </div>
                                </div>
                                <!-- 1 -->
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Upload Foto NPWP</div>
                                    <div class="col-sm-9">
                                        <div id="profil_p3_tgai" class='col-sm-5'></div>
                                        <h6 style='color:red;font-style:italic;'>(format file: .png,.jpg, ukuran file:
                                            500 Kb)</h6>
                                        <input type="file" class="form-control" placeholder="" id="file_npwp"
                                            name="file_npwp" onchange="return validasiEkstensi('file_npwp')">
                                        <input type="hidden" class="form-control" placeholder="" id="F_profil_p3_tgai"
                                            name="F_profil_p3_tgai">
                                    </div>
                                </div>
                                <h4 class="sub-title">Daerah Irigasi</h4>
                                <div id="form-di">
                                    <div class="form-group row">
                                        <div class="col-sm-3 col-form-div">Nama Daerah Irigiasi</div>
                                        <div class="col-sm-9">
                                            <select id="nm_dae_irigasi" name="nm_dae_irigasi"
                                                class="bootstrap-select form-control" data-live-search="true">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jenis Irigiasi</div>
                                    <div class="col-sm-9">
                                        <select id="j_irigasi" name="j_irigasi" class="bootstrap-select form-control"
                                            data-live-search="true">
                                            <option value=''>--Pilih--</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Kewenangan Irigasi</div>
                                    <div class="col-sm-9">
                                        <select id="k_irigasi" name="k_irigasi" class="bootstrap-select form-control"
                                            data-live-search="true">
                                            <option value=''>--Pilih--</option>
                                        </select>
                                    </div>
                                </div>

                                <h4 class="sub-title">Kepengurusan P3A/GP3A/IP3A</h4>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Pengurus (L)</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_ket_l"
                                            onkeyup="hitung('jum_ket_l+jum_ket_p','jum_ket_pgrus')"
                                            name="jum_ket_l" onkeypress="return isNumber(event)">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Pengurus (P)</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_ket_p"
                                            onkeyup="hitung('jum_ket_l+jum_ket_p','jum_ket_pgrus')"
                                            name="jum_ket_p" onkeypress="return isNumber(event)">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Total Pengurus</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_ket_pgrus"
                                            name="jum_ket_pgrus" onkeypress="return isNumber(event)">
                                    </div>
                                </div>
                                <!-- <div class="form-group row">
                <div class="col-sm-3 col-form-div">Jumlah Sekretaris (L)</div>
                <div class="col-sm-9">
                <input type="text" class="form-control" placeholder="" id="jum_sek_l" name="jum_sek_l" onkeypress="return isNumber(event)">
                </div>
              </div>
              <div class="form-group row">
                <div class="col-sm-3 col-form-div">Jumlah Sekretaris (P)</div>
                <div class="col-sm-9">
                <input type="text" class="form-control" placeholder="" id="jum_sek_p" name="jum_sek_p" onkeypress="return isNumber(event)">
                </div>
              </div> -->
                                <!--  <div class="form-group row">
               <div class="col-sm-3 col-form-div">Jumlah Bendahara (P)</div>
                <div class="col-sm-9">
                <input type="text" class="form-control" placeholder="" id="jum_ben_p" name="jum_ben_p" onkeypress="return isNumber(event)">
                </div>
              </div> -->
                                <!-- <div class="form-group row">
                <div class="col-sm-3 col-form-div">Jumlah Bendahara (L)</div>
                <div class="col-sm-9">
                <input type="text" class="form-control" placeholder="" id="jum_ben_l" name="jum_ben_l" onkeypress="return isNumber(event)">
                </div>
              </div>
              <div class="form-group row">
                <div class="col-sm-3 col-form-div">Jumlah Bendahara (P)</div>
                <div class="col-sm-9">
                <input type="text" class="form-control" placeholder="" id="jum_ben_p" name="jum_ben_p" onkeypress="return isNumber(event)">
                </div>
              </div> -->

                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Anggota</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_anggota"
                                            name="jum_anggota" onkeypress="return isNumber(event)">

                                    </div>
                                </div>

                                <h4 class="sub-title">Penyelenggara Swakelola</h4>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Tim Persiapan (L)</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_tim_per_l"
                                            onkeyup="hitung('jum_tim_per_l+jum_tim_per_p','jum_tim_total')"
                                            name="jum_tim_per_l" onkeypress="return isNumber(event)">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Tim Persiapan (P)</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_tim_per_p"
                                            onkeyup="hitung('jum_tim_per_l+jum_tim_per_p','jum_tim_total')"
                                            name="jum_tim_per_p" onkeypress="return isNumber(event)">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Total Tim Persiapan</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_tim_total"
                                            name="jum_tim_total" onkeypress="return isNumber(event)" readonly>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Tim Pelaksana (L)</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_tim_pelak_l"
                                            name="jum_tim_pelak_l"
                                            onkeyup="hitung('jum_tim_pelak_l+jum_tim_pelak_p','jum_tim_pelak')"
                                            onkeypress="return isNumber(event)">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Tim Pelaksana (P)</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_tim_pelak_p"
                                            name="jum_tim_pelak_p"
                                            onkeyup="hitung('jum_tim_pelak_l+jum_tim_pelak_p','jum_tim_pelak')"
                                            onkeypress="return isNumber(event)">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Total Tim Pelaksana</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_tim_pelak"
                                            name="jum_tim_pelak" onkeypress="return isNumber(event)" readonly>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Tim Pengawas (L)</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_tim_pngws_l"
                                            name="jum_tim_pngws_l"
                                            onkeyup="hitung('jum_tim_pngws_l+jum_tim_pngws_p','jum_tim_pngws')"
                                            onkeypress="return isNumber(event)">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Tim Pengawas (P)</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_tim_pngws_p"
                                            name="jum_tim_pngws_p"
                                            onkeyup="hitung('jum_tim_pngws_l+jum_tim_pngws_p','jum_tim_pngws')"
                                            onkeypress="return isNumber(event)">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Total Tim Pengawas</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_tim_pngws"
                                            name="jum_tim_pngws" onkeypress="return isNumber(event)" readonly>
                                    </div>
                                </div>


                                <h4 class="sub-title">Data TPM</h4>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Nama TPM</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="nm_tpm"
                                            name="nm_tpm">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Desa Pendampingan</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_dp" name="jum_dp"
                                            onkeypress="return isNumber(event)">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jenis Kelamin</div>
                                    <div class="col-sm-9">
                                        <select id="j_kelamin" name="j_kelamin" class="bootstrap-select form-control"
                                            data-live-search="false">
                                            <option value=''>--Pilih--</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Latar Belakang Pendidikan</div>
                                    <div class="col-sm-9">
                                        <select id="lbp" name="lbp" class="bootstrap-select form-control"
                                            data-live-search="true">
                                            <option value=''>--Pilih--</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Alamat TPM</div>
                                    <div class="col-sm-9">
                                        <textarea id="ala_tpm" name="ala_tpm" class="form-control"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div id="valid_2">
                                <h4 class="sub-title">Kehadiran Sosialisasi Tingkat Penerima P3-TGAI</h4>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Tanggal Pelaksana</div>
                                    <div class="col-sm-9">
                                        <input type="text" onfocus="(this.type='date')"
                                            onblur="if(this.value=='')this.type='text'" class="form-control htmldate"
                                            placeholder="" id="tgl_pelaksana" name="tgl_pelaksana">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Peserta (L)</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_pes_l"
                                            name="jum_pes_l" onkeypress="return isNumber(event)"
                                            onkeyup="hitung('jum_pes_l+jum_pes_p','jum_t')">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Peserta (P)</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_pes_p"
                                            name="jum_pes_p" onkeypress="return isNumber(event)"
                                            onkeyup="hitung('jum_pes_l+jum_pes_p','jum_t')">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Total</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_t" name="jum_t"
                                            onkeypress="return isNumber(event)" readonly>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Lansia</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_lan"
                                            name="jum_lan" onkeypress="return isNumber(event)">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Upload Foto Sosialisasi</div>
                                    <div class="col-sm-9">
                                        <div id="kehadiran_sosialisasi_tingkat_penerima_p3_tgai" class='col-sm-5'></div>
                                        <h6 style='color:red;font-style:italic;'>(format file: .png,.jpg, ukuran file:
                                            500 Kb)</h6>
                                        <input type="file" class="form-control" placeholder="" id="fot_sosial"
                                            name="fot_sosial" value="" onchange="return validasiEkstensi('fot_sosial')">
                                        <input type="hidden" class="form-control" placeholder=""
                                            id="F_kehadiran_sosialisasi_tingkat_penerima_p3_tgai"
                                            name="F_kehadiran_sosialisasi_tingkat_penerima_p3_tgai">
                                    </div>
                                </div>
                                <h4 class="sub-title">Musyawarah Desa I</h4>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Tanggal Pelaksanaan</div>
                                    <div class="col-sm-9">
                                        <input type="text" onfocus="(this.type='date')"
                                            onblur="if(this.value=='')this.type='text'" class="form-control"
                                            placeholder="" id="tgl_pelak" name="tgl_pelak">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Peserta (L)</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_pese_l"
                                            name="jum_pese_l" onkeyup="hitung('jum_pese_l+jum_pese_p','jum_tot_pes')"
                                            onkeypress="return isNumber(event)">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Peserta (P)</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_pese_p"
                                            name="jum_pese_p" onkeyup="hitung('jum_pese_l+jum_pese_p','jum_tot_pes')"
                                            onkeypress="return isNumber(event)">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Total Peserta</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_tot_pes"
                                            name="jum_tot_pes" onkeypress="return isNumber(event)">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Jumlah Lansia</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="jum_pese_lan"
                                            name="jum_pese_lan" onkeypress="return isNumber(event)">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Upload Foto Musyawarah I</div>
                                    <div class="col-sm-9">
                                        <div id="musyawarah_desa" class='col-sm-5'></div>
                                        <h6 style='color:red;font-style:italic;'>(format file: .png,.jpg, ukuran file:
                                            500 Kb)</h6>
                                        <input type="file" class="form-control" placeholder="" id="fot_musya"
                                            name="fot_musya" value="" onchange="return validasiEkstensi('fot_musya')">
                                        <input type="hidden" class="form-control" placeholder="" id="F_musyawarah_desa"
                                            name="F_musyawarah_desa">
                                    </div>
                                </div>
                                <h4 class="sub-title">Penetapan P3A/GP3A/IP3A</h4>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Nomor Penetapan</div>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" placeholder="" id="no_p" name="no_p">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-sm-3 col-form-div">Tanggal Penetapan</div>
                                    <div class="col-sm-9">
                                        <input type="text" onfocus="(this.type='date')"
                                            onblur="if(this.value=='')this.type='text'" class="form-control"
                                            placeholder="" id="tgl_pen" name="tgl_pen">
                                    </div>
                                </div>
                            </div>
                            <!-- <h4 class="sub-title">Daerah Irigasi</h4>
              <div class="form-group row">
                <div class="col-sm-3 col-form-div">Nama Daerah Irigiasi</div>
                <div class="col-sm-9">
                <select id="nm_dae_irigasi" name="nm_dae_irigasi"  class="bootstrap-select form-control" data-live-search="true">
                <option value=''>--Pilih--</option>     
              </select>
              
                </div>
              </div>
              <div class="form-group row">
                <div class="col-sm-3 col-form-div">Jenis Irigiasi</div>
                <div class="col-sm-9">
                <select id="j_irigasi" name="j_irigasi" class="bootstrap-select form-control" data-live-search="true"  id="jen_irigasi" name="jen_irigasi" >
                <option value=''>--Pilih--</option>     
                </select>
                </div>
              </div>
              <div class="form-group row">
                <div class="col-sm-3 col-form-div">Kewenangan Irigasi</div>
                <div class="col-sm-9">
                <select id="k_irigasi" name="k_irigasi"  class="bootstrap-select form-control" data-live-search="true">
                <option value=''>--Pilih--</option>     
              </select>
                </div>
              </div> -->
                        </div>

                    </div>
                    <div class="modal-footer">
                        <div id="btn_tambah_edit" style='display:none;'>
                            <button type="button" class="btn btn-default waves-effect "
                                data-bs-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary waves-effect waves-light ">Simpan</button>
                        </div>
                        <div id="btn_verifikasi" style='display:none;'>
                            <button type="button" class="btn btn-success waves-effect " onclick="update_verifikasi('1')"
                                data-bs-dismiss="modal">Valid</button>
                            <button type="button" class="btn btn-danger waves-effect waves-light "
                                onclick="update_verifikasi('2')">Tidak Valid</button>
                        </div>
                    </div>
                </div>
        </form>

    </div>
</div>