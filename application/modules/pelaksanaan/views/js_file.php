<script>
var group = "<?php echo $this->session->users['id_user_group_real'];?>";
var satker = "<?php echo $this->session->users['kd_satker']; ?>";
var sessLocked = "<?php echo $this->session->userdata("satker" . $this->session->users['tahun_p3tgai'] . "_" . $this->session->users['kd_satker']); ?>";
var v0 = '';
var v1 = '';
var v2 = '';
var v3 = '';
var v4 = '';
var tahap = '';
var progress = '';
var but = "";

function lockEntri(mod, id) {
    if (sessLocked == 1) {
        cssAct = (mod == 'edit') ? but : 'display:none !important;';
        // console.log("The key "+ satker +" exists in local storage.");
    } else {
        cssAct = (mod == 'edit') ? "onclick= dtTambahRow('edit','" + id + "')" : 'display:block;';
        // console.log("The key "+ satker +" does not exist in local storage.");
    }
    return cssAct;
}

function tracking_data(v0, v1, v2, v3, v4, tahap, ver1, ver2, ver3, ver4, id) {
    if (group == 3 || group == 4 || group == 5) {
        but = "onclick= dtTambahRow('detail','" + id + "')";
    } else if (group == 7 || group == 1 || group == 2) {
        // but = "onclick= dtTambahRow('edit','" + id + "')";
        but = lockEntri('edit', id);
    }

    var tahap = parseInt(tahap);


    if (v0 === 'Data Belum Lengkap' || v1 === 'Data Belum Lengkap') {
        progress1 =
            "<span style='cursor:help;'  title='belum lengkap & belum diverifikasi' class='badge badge-pill badge-danger'><span class=''><i class='fa fa-exclamation-triangle'></i></span>&nbsp;Persiapan</span>";
    } else {
        if (ver1 == 1) {
            progress1 =
                "<span style='cursor:help;' title='lengkap & valid' class='badge badge-pill badge-success'><span class=''><i class='fa fa-check-circle'></i></span>&nbsp;Persiapan</span>";
        } else if (ver1 == 2) {
            progress1 =
                "<span style='cursor:help;' title='lengkap & tdk vaild' class='badge badge-pill badge-warning'><span class=''><i class='fa fa-times'></i></span>&nbsp;Persiapan</span>";
        } else {
            progress1 =
                "<span style='cursor:help;' title='lengkap & belum diverifikasi' class='badge badge-pill badge-warning'><span class=''><i class='fa fa-question-circle'></i></span>&nbsp;Persiapan</span>";
        }
    }

    if (ver1 == 1 && v0 == 'Data Sudah Lengkap' && v1 == 'Data Sudah Lengkap') {
        if (v2 === 'Data Belum Lengkap') {
            progress2 =
                "<span style='cursor:help;' title='belum lengkap & belum diverifikasi' class='badge badge-pill badge-danger'><i class='fa fa-exclamation-triangle'></i>&nbsp;Perencanaan</span>";

        } else {
            if (ver2 == 1) {
                progress2 =
                    "<span style='cursor:help;' title='lengkap & valid'  class='badge badge-pill badge-success'><span class=''><i class='fa fa-check-circle'></i></span>&nbsp;Perencanaan</span>";
            } else if (ver2 == 2) {
                progress2 =
                    "<span style='cursor:help;' title='lengkap & tdk vaild'  class='badge badge-pill badge-warning'><span class=''><i class='fa fa-times'></i></span>&nbsp;Perencanaan</span>";
            } else {
                progress2 =
                    "<span style='cursor:help;' title='lengkap & belum diverifikasi' class='badge badge-pill badge-warning'><span class=''><i class='fa fa-question-circle'></i></span>&nbsp;Perencanaan</span>";
            }
            // progress2 = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span>";
        }
    } else {
        progress2 =
            "<span style='cursor:help;' title='Belum sampai ke tahapan Perencanaan' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Perencanaan</span>";
    }

    // console.log(ver2+'-'+ver1+'-'+v0+'-'+v1+'-'+v2);
    if (ver2 == 1 && ver1 == 1 && v0 == 'Data Sudah Lengkap' && v1 == 'Data Sudah Lengkap' && v2 ==
        'Data Sudah Lengkap') {
        if (v3 === 'Data Belum Lengkap') {
            progress3 = "<span style='cursor:pointer;'  " + but +
                "  title='belum lengkap & belum diverifikasi' class='badge badge-pill badge-danger'><span class='blink_me'><i class='fa fa-exclamation-triangle'></i></span>&nbsp;Pelaksanaan</span>";
        } else {
            if (ver3 == 1) {
                progress3 = "<span style='cursor:pointer;'  " + but +
                    "  title='lengkap & valid'  class='badge badge-pill badge-success'><span class='blink_me'><i class='fa fa-check-circle'></i></span>&nbsp;Pelaksanaan</span>";
            } else if (ver3 == 2) {
                progress3 = "<span style='cursor:pointer;'  " + but +
                    "  title='lengkap & tdk vaild'  class='badge badge-pill badge-warning'><span class='blink_me'><i class='fa fa-times'></i></span>&nbsp;Pelaksanaan</span>";
            } else {
                progress3 = "<span style='cursor:pointer;'  " + but +
                    "  title='lengkap & belum diverifikasi' class='badge badge-pill badge-warning'><span class='blink_me'><i class='fa fa-question-circle'></i></span>&nbsp;Pelaksanaan</span>";
            }
            // progress3 = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Pelaksanaan</span>";
        }
    } else {
        progress3 =
            "<span style='cursor:help;' title='Belum sampai ke tahapan Pelaksanaan' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span>";

    }

    if (ver3 == 1 && ver2 == 1 && ver1 == 1 && v0 == 'Data Sudah Lengkap' && v1 == 'Data Sudah Lengkap' && v2 ==
        'Data Sudah Lengkap' && v3 == 'Data Sudah Lengkap') {
        if (v4 === 'Data Belum Lengkap') {
            progress4 =
                "<span style='cursor:help;' title='belum lengkap & belum diverifikasi' class='badge badge-pill badge-danger'><i class='fa fa-exclamation-triangle'></i>&nbsp;Penyelesaian</span>";
        } else {
            if (ver4 == 1) {
                progress4 =
                    "<span style='cursor:help;' title='lengkap & valid'  class='badge badge-pill badge-success'><span class=''><i class='fa fa-check-circle'></i></span>&nbsp;Penyelesaian</span>";
            } else if (ver4 == 2) {
                progress4 =
                    "<span style='cursor:help;' title='lengkap & tdk vaild'  class='badge badge-pill badge-warning'><span class=''><i class='fa fa-times'></i></span>&nbsp;Penyelesaian</span>";
            } else {
                progress4 =
                    "<span style='cursor:help;' title='lengkap & belum diverifikasi' class='badge badge-pill badge-warning'><span class=''><i class='fa fa-question-circle'></i></span>&nbsp;Penyelesaian</span>";
            }
            // progress4 = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Penyelesaian</span>";
        }
    } else {
        progress4 =
            "<span style='cursor:help;' title='Belum sampai ke tahapan Penyelesaian' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";

    }

    if (tahap == 4) {
        progress = progress1 + progress2 + progress3 + progress4;
    } else if (tahap == 3) {
        progress = progress1 + progress2 + progress3 +
            "<span style='cursor:help;' title='Belum sampai ke tahapan Penyelesaian'  class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
    } else if (tahap == 2) {
        progress = progress1 + progress2 +
            "<span style='cursor:help;' title='Belum sampai ke tahapan Pelaksanaan' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span>" +
            "<span  style='cursor:help;' title='Belum sampai ke tahapan Penyelesaian' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
    } else if (tahap == 1) {
        progress = progress1 +
            "<span style='cursor:help;' title='Belum sampai ke tahapan Perencanaan' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Perencanaan</span>" +
            "<span style='cursor:help;' title='Belum sampai ke tahapan Pelaksanaan' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span>" +
            "<span  style='cursor:help;' title='Belum sampai ke tahapan Penyelesaian' class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
    }







    //    if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Belum Lengkap' && v3=='Data Belum Lengkap' && v4=='Data Belum Lengkap') {
    //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-check-circle'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
    //    } else if(v0=='Data Belum Lengkap' && v1=='Data Belum Lengkap' & v2=='Data Belum Lengkap' && v3=='Data Belum Lengkap' && v4=='Data Belum Lengkap') {
    //         progress = "<span class='badge badge-pill badge-warning'><i class='fa fa-exclamation-triangle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
    //    }

    //     if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Sudah Lengkap' && v3=='Data Belum Lengkap' && v4=='Data Belum Lengkap') {
    //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
    //     } else  if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Belum Lengkap' && v3=='Data Belum Lengkap' && v4=='Data Belum Lengkap') {
    //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-warning'><i class='fa fa-exclamation-triangle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
    //     }

    //     if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Sudah Lengkap' && v3=='Data Sudah Lengkap' && v4=='Data Belum Lengkap') {
    //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
    //     } else if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Sudah Lengkap' && v3=='Data Belum Lengkap' && v4=='Data Belum Lengkap') {
    //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-warning'><i class='fa fa-exclamation-triangle'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
    //     }


    //     if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Sudah Lengkap' && v3=='Data Sudah Lengkap' && v4=='Data Sudah Lengkap') {
    //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Penyelesaian</span>"
    //     } else if(v0=='Data Sudah Lengkap' && v1=='Data Sudah Lengkap' && v2=='Data Sudah Lengkap' && v3=='Data Sudah Lengkap' && v4=='Data Belum Lengkap') {
    //         progress = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span><span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Pelaksanaan</span><span class='badge badge-pill badge-warning'><i class='fa fa-exclamation-triangle'></i>&nbsp;Penyelesaian</span>";
    //     }


    return progress;
}
// function tracking_data(v0, v1, v2, v3, v4, tahap) {
//     var tahap = parseInt(tahap);


//         if(v0==='Data Belum Lengkap' || v1==='Data Belum Lengkap') {
//             progress1 = "<span class='badge badge-pill badge-warning'><i class='fa fa-exclamation-triangle'></i>&nbsp;Persiapan</span>";
//         } else {
//               progress1 = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Persiapan</span>";
//         }

//         if(v2==='Data Belum Lengkap') {
//             progress2 = "<span class='badge badge-pill badge-warning'><i class='fa fa-exclamation-triangle'></i>&nbsp;Perencanaan</span>";

//         } else {
//             progress2 = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Perencanaan</span>";
//         }

//         if(v3==='Data Belum Lengkap') {
//             progress3 = "<span class='badge badge-pill badge-warning'><span class='blink_me'><i class='fa fa-exclamation-triangle'></i></span>&nbsp;Pelaksanaan</span>";
//         } else {
//             progress3 = "<span class='badge badge-pill badge-success'><span class='blink_me'><i class='fa fa-check-circle'></i></span>&nbsp;Pelaksanaan</span>";
//         }

//         if(v4==='Data Belum Lengkap') {
//             progress4 = "<span class='badge badge-pill badge-warning'><i class='fa fa-exclamation-triangle'></i>&nbsp;Penyelesaian</span>";
//         } else {
//             progress4 = "<span class='badge badge-pill badge-success'><i class='fa fa-check-circle'></i>&nbsp;Penyelesaian</span>";
//         }


//         if(tahap == 4) {
//             progress = progress1+progress2+progress3+progress4;
//         } else if(tahap == 3) {
//             progress = progress1+progress2+progress3+"<span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
//         } else if(tahap == 2) {
//             progress = progress1+progress2+"<span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span>"+"<span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
//         } else if(tahap == 1) {
//             progress = progress1+"<span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Perencanaan</span>"+"<span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Pelaksanaan</span>"+"<span class='badge badge-pill badge-secondary'><i class='fa fa-arrow-right'></i>&nbsp;Penyelesaian</span>";
//         }

//      return progress;
// }
function dtRefresh(stat, id) {
    // var mustKMB = "<?php //echo $this->session->users['id_user'];?>";
    // var id = $("#id").val()
    var url = "<?php echo base_url(); ?>pelaksanaan/getback_file/" + stat + "/" + id;
    var text = 'Anda akan memproses perbaikan dokumen gambar?'
    // if(stat == 1){
    //     text = "Anda akan memproses perbaikan dokumen gambar?"
    // }else{
    //     text = ""
    // }
    swal({
            title: text,
            //   text: tex,
            icon: "info",
            buttons: true,
            dangerMode: true,
        })
        .then((willDelete) => {
            if (willDelete) {
                $.get(url, {}).done(function(data) {
                    if (data == 0) {
                        swal({
                            title: "",
                            text: "Berhasil Mengembalikan Dokumen Foto",
                            icon: "success",
                            showConfirmButton: false,
                            timer: 3000,
                            type: "success"
                        });
                        // table.ajax.reload();
                        $('#modal-tambah').modal('hide');
                    }
                })
            } else {
                swal("Anda tidak berhasil mengembalikan Dokumen Foto");
            }
        })
}

function addSelectItem(t, ev) {
    ev.stopPropagation();

    var bs = $(t).closest('.bootstrap-select')
    var txt = bs.find('.bss-input').val().replace(/[|]/g, "");
    var txt = $(t).prev().val().replace(/[|]/g, "");
    if ($.trim(txt) == '') return;

    // Changed from previous version to cater to new
    // layout used by bootstrap-select.
    var p = bs.find('select');
    var o = $('option', p).eq(-2);
    o.before($("<option>", {
        "selected": true,
        "text": txt
    }));
    p.selectpicker('refresh');
}

function addSelectInpKeyPress(t, ev) {
    ev.stopPropagation();

    // do not allow pipe character
    if (ev.which == 124) ev.preventDefault();

    // enter character adds the option
    if (ev.which == 13) {
        ev.preventDefault();
        addSelectItem($(t).next(), ev);
    }
}

var thangs = "<?php echo $this->session->konfig_tahun_ang;?>";
var xhrdata = null;
var table = null;

function clear_input() {
    $("#formData :input").val("");
}



function listing() {
    table = $('#dt-server-processing').DataTable({
        "draw": 0,
        // "columnDefs": [{"orderable": true, "targets": [0, 1,2]}],
        "order": [
            [0, "asc"]
        ],
        "processing": true,
        "deferRender": true,
        "serverSide": true,
        "ajax": {
            type: "POST",
            url: "<?php echo base_url();?>pelaksanaan/ssp_paket",
            "data": function(d) {
                d.<?php echo $this->security->get_csrf_token_name();?> =
                    "<?php echo $this->security->get_csrf_hash();?>"
                d.fs = $(".statustahap").val();
                // d.thang = $('#fthang').val();

            }
        },
        "columnDefs": [{
                "aTargets": [0],
                "mRender": function(data, type, full) {
                    return full[1];
                }
            },
            {
                "aTargets": [1],
                "mRender": function(data, type, full) {
                    return full[2];
                }
            },
            {
                "aTargets": [2],
                "mRender": function(data, type, full) {
                    return full[3];
                }
            },
            {
                "aTargets": [3],
                "mRender": function(data, type, full) {
                    return full[4];

                }
            },
            {
                "aTargets": [4],
                "mRender": function(data, type, full) {
                    return full[5];
                }
            },

            {
                "aTargets": [5],
                "mRender": function(data, type, full) {
                    return full[6];
                }
            },

            {
                "aTargets": [6],
                "mRender": function(data, type, full) {
                    return full[7];
                }
            },
            {
                "aTargets": [7],
                "mRender": function(data, type, full) {
                    return full[8];
                }
            },
            {
                "aTargets": [8],
                "mRender": function(data, type, full) {
                    // if(full[8] == 'selesai'){
                    //     return  "<button class='btn btn-success btn-xs'>Form Lengkap</button>"
                    // }else{
                    //     return  "<button class='btn btn-danger btn-xs'>Tidak Lengkap</button>"

                    // }
                    return (tracking_data(full[9], full[10], full[11], full[12], full[13], full[14],
                        full[15], full[16], full[17], full[18], full[0]));


                }
            },
            {
                "aTargets": [9],
                "mRender": function(data, type, row) {
                    var id = row[0];
                    var show = 'display:none !important;'
                    var show2 = 'display:none !important;'
                    if (group == 2) {
                        show = 'display:block !important;'
                    } else if (group == 7 || group == 1) {
                        show2 = 'display:block;'
                    }
                    var html_button = [
                        // "<span class='badge badge-primary'><i class='fa fa-edit' onclick= dtTambahRow('edit','" + id + "')></i></span>",
                        // "<span class='badge badge-success' style='" + show +
                        // "cursor:pointer;' onclick= dtRefresh('pelaksanaan','" + id +
                        // "') title='Pulihkan data' >Refresh</span>",
                        "<span class='badge badge-warning' style='" + show +
                        "cursor:pointer;background:#4f9dbb;' onclick= dtTambahRow('detail','" + id +
                        "') title='Validasi data' >Validasi</span>",
                        // "<span class='badge badge-danger' style='" + show2 +
                        // "cursor:pointer;'><i class='fa fa-trash' onclick= dtDeleteRow('" + id +
                        // "')></i></span>",

                        // "<span class='badge badge-warning'><i class='fa fa-search' onclick= dtTambahRow('detail','" + id + "')></i></span>",
                        //"<button onclick= dtdetail('" + id + "') class='btn btn-warning btn-xs '><i class='fa fa-search'></i></button>"
                    ].join("\n");
                    return html_button;
                }
            }
        ],
        "language": {
            "decimal": "",
            "emptyTable": "Data tidak ditemukan",
            "info": "Data _START_ s/d _END_ dari _TOTAL_",
            "infoEmpty": "Tidak ada data",
            "infoFiltered": "(tersaring dari _MAX_)",
            "infoPostFix": "",
            "thousands": ",",
            "lengthMenu": "_MENU_  data per halaman",
            "loadingRecords": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
            "processing": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
            "search": "Cari:",
            "zeroRecords": "Tidak ada data ditemukan",
            // "paginate": {
            //     "first": "<i class='fast backward ui icon'></i>",
            //     "last": "<i class='fast forward ui icon'></i>",
            //     "next": "<i class='step forward ui icon'></i>",
            //     "previous": "<i class='step backward ui icon'></i>"
            // },
            "aria": {
                "sortAscending": ": aktifkan untuk mengurutkan naik",
                "sortDescending": ": aktifkan untuk mengurutkan turun"
            }
        },
        "initComplete": function () {
        var $searchInput = $('#dt-server-processing_filter input');
        $searchInput.attr('placeholder', 'Lalu Tekan Enter'); // Add placeholder text
        $searchInput.unbind();
        $searchInput.bind('keyup', function (e) {
            if (e.keyCode == 13) { // Enter key
                table.search(this.value).draw();
            }
        });

    // Add a reset button next to the search input with additional styling
    var $resetButton = $('<button type="button" style="display: none; margin-left: 10px; padding: 5px 10px; background-color: #f0f0f0; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;">Reset</button>').click(function () {
        $searchInput.val(''); // Clear the search input
        table.search('').draw(); // Reset the table search
        $resetButton.hide(); // Hide the reset button after resetting
    });

    // Append the reset button to the filter element
    $('#dt-server-processing_filter').append($resetButton);

    // Show the reset button after hitting enter in the search input
    $searchInput.on('keyup', function (e) {
        if (e.keyCode == 13 && $searchInput.val().length > 0) { // Check if Enter key is pressed
            $resetButton.show(); // Show the reset button
        } else {
            $resetButton.hide(); // Hide the reset button if input is empty
        }
    });
    }
    });

    table.on('xhr', function() {
        xhrdata = table.ajax.json();
        //console.log(xhrdata);
    });
    //});
}

var i = 0;
var y = 0;

function bang_p(val = '') {
    if (val != '') {
        i = val + y;
    }
    i++;
    y++;
    var html = ' <div class="form-group row" id="yuhu' + i + '"><div class="col-sm-6">' +
        '<select id="bp' + i + '" name="bp[]" class="bootstrap-select form-control" data-live-search="true">' +
        '<option value="">--Pilih Bangunan Pelengkap--</option>  ' +
        ' </select></div>' +
        '<div class="col-sm-5">' +
        '<input min="0" type="number" class="form-control" placeholder="" id="jml_bang_kap' + i +
        '" name="jml_bang_kap[]" onkeypress="return isNumber(event)">' +
        '</div>' +
        '<div class="col-sm-1">' +
        ' <button type="button" onclick="remove_combo(' + i + ')" name="remove" id="' + i +
        '" class="btn btn-danger btn_remove" >X</button>' +
        '</div></div>';
    $('#bang_pen').append(html);
    setTimeout(function() {
        $('#bp' + i).val('').selectpicker('refresh');
    }, 500);
    initCombobox('bp' + i, 33);

}
var ii = 0;
var yy = 0;

function bang_l(val = '') {
    if (val != '') {
        ii = val + yy;
    }
    ii++;
    yy++;
    var html = ' <div class="form-group row" id="yuhui' + ii + '"><div class="col-sm-6">' +
        '<select id="bl' + ii + '" name="bl[]" class="bootstrap-select form-control" data-live-search="true">' +
        '<option value="">--Pilih Bangunan Lain-lain--</option>  ' +
        ' </select></div>' +
        '<div class="col-sm-5">' +
        '<input min="0" type="number" class="form-control" placeholder="" id="jml_bang_lain' + ii +
        '" name="jml_bang_lain[]" onkeypress="return isNumber(event)">' +
        '  </div>' +
        '<div class="col-sm-1">' +
        ' <button type="button" onclick="remove_comboi(' + ii + ')" name="remove" id="' + ii +
        '" class="btn btn-danger btn_remove" >X</button>' +
        '</div></div>';
    $('#bang_lai').append(html);
    setTimeout(function() {
        $('#bl' + ii).val('').selectpicker('refresh');
    }, 500);

    initCombobox('bl' + ii, 34);
}


var jj = 0;
var ww = 0;

function segmentasi(val = '') {

    if (val != '') {
        jj = val + ww;

    }
    jj++;
    ww++;

    // (abc + 9).toString(36).toUpperCase()
    var html = '<div class="form-group row" id="yuhuj' + jj + '">' +
        '<div class="col-sm-12" style="padding:11px;font-weight:bold;">' +
        '<center id="abjad">Segmen</center>' +
        '</div>' +
        '<div class="row">' +
        '<div class="col-sm-3">' +
        '<label class="col-sm-12 col-form-label"> Titik Awal</label>' +
        '</div>' +
        '<div class="col-sm-4">' +
        '<input type="text" class="form-control" placeholder="Latitude (x1)" id="xawal' + jj + '" name="xawal[]">' +
        '</div>' +
        '<div class="col-sm-4">' +
        '<input type="text" class="form-control" placeholder="Longitude (y1)" id="yawal' + jj + '" name="yawal[]">' +
        '</div>' +
        '<div class="col-sm-1">' +
        ' <button type="button" onclick="remove_comboj(' + jj + ')" name="remove" id="' + jj +
        '" class="btn btn-danger btn_remove" >X</button>' +
        '</div>' +
        '</div>' +
        '<div class="row">' +
        '<div class="col-sm-3">' +
        '<label class="col-sm-12 col-form-label">Titik Akhir</label>' +
        '</div>' +
        '<div class="col-sm-4">' +
        '<input type="text" class="form-control" placeholder="Latitude (x2)" id="xakhir' + jj + '" name="xakhir[]">' +
        '</div>' +
        '<div class="col-sm-4">' +
        '<input type="text" class="form-control" placeholder="Longitude (y2)" id="yakhir' + jj + '" name="yakhir[]">' +
        '</div>' +
        '</div>' +
        '<hr>' +
        '</div>';

    $('#segmen_koord').append(html);



}

function remove_combo(x) {
    // alert("ASDASd")
    // alert(x.value)
    // var button_id = $(this).attr("id");
    $('#yuhu' + x + '').remove();
}

function remove_comboi(x) {
    // alert("ASDASd")
    // alert(x.value)
    // var button_id = $(this).attr("id");
    $('#yuhui' + x + '').remove();
}

function remove_comboj(x) {
    // alert("ASDASd")
    // alert(x.value)
    // var button_id = $(this).attr("id");
    $('#yuhuj' + x + '').remove();
}

function shows() {
    $(".error").text('')
    $(".error").css({
        'font-size': '14px',
        'color': '#353c4e'
    })
    $(".form-tambah .form-group input").prop('readonly', false)
    $(".form-tambah .form-group input").css({
        "border": "1px solid #7889bd8f",
        "background": "#e9ecef45"
    })
    $(".form-tambah .form-group select").prop('disabled', false)
    $(".form-tambah .form-group select").css({
        "border": "1px solid #7889bd8f",
        "background": "#e9ecef45"
    })
    $(".form-tambah .form-group textarea").prop('readonly', false)
    $(".form-tambah .form-group textarea").css({
        "border": "1px solid #7889bd8f",
        "background": "#e9ecef45"
    })
    $(".form-tambah .form-group button").prop('disabled', false)
    $(".form-tambah .form-group button").css({
        "border": "1px solid #7889bd8f",
        "background": "#e9ecef45"
    })
    $(".form-tambah .form-group").css({
        "border-bottom": "0px"
    })
    $(".form-tambah .form-group input").val('')
    $(".form-tambah .form-group select").val('')
    $("#BUKU_REKENING").html('')
    $("#PETA_DI").html('')
    $("#BUKU_REKENING").html('')
    $("#JARINGAN_KESELURUHAN").html('')
    $("#JARINGAN_PETAK").html('')
    $("#PADAT_KARYA").html('')
    $("#PROGRES_FISIK_0").html('')
    $("#PROGRES_FISIK_50").html('')
    $("#PROGRES_FISIK_100").html('')
    $(".form-tambah .form-group textarea").val('')
    $("#fot_buku_rekening").show()
    $("#fot_peta_di").show()
    $("#fot_jaringan_keseluruhan").show()
    $("#fot_jaringan_petak").show()
    $("#fot_padat_karya").show()
    $("#fot_progres_fisik_0").show()
    $("#fot_progres_fisik_50").show()
    $("#fot_progres_fisik_100").show()
}

function update_verifikasi(stat) {
    var mustKMB = "<?php echo $this->session->users['id_user'];?>";
    var id = $("#id").val()
    // var url = "<?php echo base_url(); ?>pelaksanaan/update_data/" + id + "/" + stat + "/" + mustKMB;
    var text = ''
    if (stat == 1) {
        text = "Anda yakin data tersebut valid ?"
    } else {
        text = "Anda yakin data tersebut tidak valid ?"
    }


    data_post = {
            "id": id,
            "stat": stat,
            "mustKMB": mustKMB,
            "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>",
        }

    swal({
            title: text,
            //   text: tex,
            icon: "info",
            buttons: true,
            dangerMode: true,
        })
        .then((willDelete) => {
            if (willDelete) {
                jQuery.ajax({
                            // contentType: 'application/json',
                            type: "post",
                            dataType: "json",
                            data: data_post,
                            // crossDomain: true,
                            url: "<?= base_url('pelaksanaan/update_data'); ?>",
                            success: function(data) {
                                if (data == 0) {
                                    swal({
                                        title: "",
                                        text: "Berhasil Verifikasi",
                                        icon: "success",
                                        showConfirmButton: false,
                                        timer: 3000,
                                        type: "success"
                                    });
                                    table.ajax.reload();
                                    $('#modal-tambah').modal('hide');
                                }


                            },
                            error: function(xhr, ajaxOptions, thrownError) {
                                alert(xhr.status);
                                alert(thrownError);
                            }
                        });

                // $.get(url, {}).done(function(data) {
                //     if (data == 0) {
                //         swal({
                //             title: "",
                //             text: "Berhasil Merubah Status",
                //             icon: "success",
                //             showConfirmButton: false,
                //             timer: 3000,
                //             type: "success"
                //         });
                //         table.ajax.reload();
                //         $('#modal-tambah').modal('hide');
                //     }
                // })
            } else {
                swal("Anda tidak merubah status");
            }
        })
}

function disable_cair12() {
    $("#rp_cair_1_rek").prop("readonly", true)
    $("#rp_cair_1_pajak").prop("disabled", false)
    $("#hasil_nilai_pks_70").prop("readonly", true)
    $("#rp_cair_1_total").prop("readonly", true)
    $("#rp_cair_1_tgl").prop("readonly", true)
    $("#masa_1").prop("disabled", true)

    $("#rp_cair_2_rek").prop("readonly", true)
    $("#rp_cair_2_pajak").prop("disabled", false)
    $("#hasil_nilai_pks_30").prop("readonly", true)
    $("#rp_cair_2_total").prop("readonly", true)
    $("#rp_cair_2_tgl").prop("readonly", true)
    $("#masa_2").prop("disabled", true)
}

function dtTambahRow(kat, id) {

    // alert(id);

    //  $("#frm-tambah").validate().resetForm();
    //  $('#frm-tambah').trigger('reset');
    //  $('#frm-tambah')[0].reset();
    $('#modal-tambah').modal({
        backdrop: 'static',
        keyboard: false
    })
    shows()




    // $(".form-tambah #valid_2 input").prop('readonly',true)
    // $(".form-tambah #valid_2 select").prop('disabled',true)
    // $(".form-tambah #valid_2 textarea").prop('readonly',true)
    // $(".form-tambah #valid_2 button").prop('disabled',true)

    // $("#id").val('')

    initCombobox('bu', 32);
    initCombobox('bp', 33);
    initCombobox('bl', 34);
    initCombobox('pen_bank', 11);
    $('#nilai_pks').val('').selectpicker('');
    disable_cair12()

    // $('#rp_cair_2_pajak').val(persen).selectpicker('refresh')
    //    $('.js-example-tags').select2();
    var bank = '';
    var id_bu = '';
    if (group == 2) {
        $("#btn_tambah_edit").css('display', 'block')
        $("#btn_verifikasi").css('display', 'none')
    } else {
        $("#btn_tambah_edit").css('display', 'block')
    }

    if (kat == 'tambah') {
        $("#id").val('')
        $("#buku_rekening").empty();
        $("#upload_foto_padat_karya").empty();
        $("#upload_foto_bangunan_responsif").empty();
        $("#upload_foto_progress_0").empty();
        $("#upload_foto_progress_50").empty();
        $("#upload_foto_progress_100").empty();
        $("#upload_peta_di").empty();
        $("#upload_skema_jaringan_keseluruhan").empty();
        $("#upload_skema_jaringan_pada_petak_tersier").empty();
        $("#upload_foto_padat_karya").empty();
        $("#upload_foto_bangunan_responsif").empty();



        // alert('tambah');
    } else {
        var dt = tampil_data('v_all_kegiatan', 'id_giat', id);
        $("#id").val(id)
        $('#no_pks').val(dt[0].no_pks)
        //    cek_strip(dt[0].no_pks)
        if (dt[0].tgl_pks != null) {
            $('#tgl_pks').val(dt[0].tgl_pks.split(' ')[0]);
        }
        $('#no_ttd_pks').val(dt[0].no_ttd_pks)
        if (dt[0].tgl_ttd_pks != null) {
            $('#tgl_ttd_pks').val(dt[0].tgl_ttd_pks.split(' ')[0]);
        }
        $('#nilai_pks').val(dt[0].nilai_pks)
        $('#nilai_pks').val(dt[0].nilai_pks).selectpicker('refresh')
        //    $('.js-example-tags').select2({
        //         tags:true
        //    });



        $('#lok_nm_sal_tersier').val(dt[0].lok_nm_sal_tersier)
        $('#lok_nm_petak').val(dt[0].lok_nm_petak)
        $('#lok_luas_petak').val(dt[0].lok_luas_petak)
        $('#pjg_induk_bbl').val(dt[0].pjg_induk_bbl)
        $('#pjg_induk_tanah').val(dt[0].pjg_induk_tanah)
        $('#pjg_sekunder_bbl').val(dt[0].pjg_sekunder_bbl)
        $('#pjg_sekunder_tnh').val(dt[0].pjg_sekunder_tnh)
        $('#pjg_tersier_bbl').val(dt[0].pjg_tersier_bbl)
        $('#pjg_tersier_tanah').val(dt[0].pjg_tersier_tanah)
        $('#pjg_sal_buang').val(dt[0].pjg_sal_buang)
        $('#pjg_sal_pipa').val(dt[0].pjg_sal_pipa)
        $('#jml_kk_petani').val(dt[0].jml_kk_petani)
        $('#ls_layanan_awal').val(dt[0].ls_layanan_awal)
        $('#ls_layanan_menjadi').val(dt[0].ls_layanan_menjadi)


        $('#ip_semula').val(dt[0].ip_semula)
        $('#ip_menjadi').val(dt[0].ip_menjadi)
        $('#prod_padi_semula').val(dt[0].prod_padi_semula)
        $('#prod_padi_menjadi').val(dt[0].prod_padi_menjadi)
        $('#serapan_tk_hok').val(dt[0].serapan_tk_hok)
        $('#serapan_alokasi').val(dt[0].serapan_alokasi)
        $('#tk_pria').val(dt[0].tk_pria)
        $('#tk_wanita').val(dt[0].tk_wanita)
        var has = parseInt(dt[0].tk_pria) + parseInt(dt[0].tk_wanita)
        if (isNaN(has)) {
            has = '';
        }
        $('#tk_total').val(has)
        $('#tk_lansia').val(dt[0].tk_lansia)
        var rerata = parseInt(dt[0].tk_totalhok) / parseInt(has)
        rerata = parseFloat(rerata.toFixed(2))
        if (isNaN(rerata)) {
            rerata = ''
        }

        $("#tk_rerata").val(rerata)
        // $('#tk_rerata').val(dt[0].tk_rerata)
        $('#tk_totalhok').val(dt[0].tk_totalhok)
        var upah = parseFloat(parseInt(dt[0].upah_tk_total) / parseInt(dt[0].tk_totalhok))
        upah = parseFloat(upah.toFixed(2))
        if (isNaN(upah)) {
            upah = '';
        }
        $("#upah_tk_rerata").val(upah)
        // $('#upah_tk_rerata').val(dt[0].upah_tk_rerata)
        $('#upah_tk_total').val(dt[0].upah_tk_total)
        // $('#tk_buruh').val(dt[0].tk_buruh)
        // $('#tk_phk').val(dt[0].tk_phk)
        $('#xawal').val(dt[0].xawal)
        $('#yawal').val(dt[0].yawal)
        $('#xakhir').val(dt[0].xakhir)
        $('#yakhir').val(dt[0].yakhir)
        // $('#tki').val(dt[0].tki)
        $('#hasil_nilai_pks_70').val(dt[0].rp_cair_1_pajak)
        $('#rp_cair_1_rek').val(dt[0].rp_cair_1_rek)
        $('#masa_1').val(dt[0].masa_pajak_thp1)
        $('#rp_cair_1_total').val(dt[0].rp_cair_1_total)
        if (dt[0].rp_cair_1_tgl != null) {
            $('#rp_cair_1_tgl').val(dt[0].rp_cair_1_tgl.split(' ')[0]);
        }
        $('#hasil_nilai_pks_30').val(dt[0].rp_cair_2_pajak)
        $('#rp_cair_2_rek').val(dt[0].rp_cair_2_rek)
        $('#masa_2').val(dt[0].masa_pajak_thp2)
        $('#rp_cair_2_total').val(dt[0].rp_cair_2_total)
        if (dt[0].rp_cair_2_tgl != null) {
            $('#rp_cair_2_tgl').val(dt[0].rp_cair_2_tgl.split(' ')[0]);
        }

        // console.log(dt[0].rp_cair_1_pajak);
        // console.log(dt[0].rp_cair_2_pajak);
        var persen1 = 0
        if (dt[0].rp_cair_1_pajak !== null) {
            persen1 = 0.04
        }

        var persen2 = 0
        if (dt[0].rp_cair_2_pajak != null) {
            persen2 = 0.04
        }

        $('#rp_cair_1_pajak').val(dt[0].rp_cair_1_pajak).selectpicker('refresh')
        $('#rp_cair_2_pajak').val(dt[0].rp_cair_2_pajak).selectpicker('refresh')
        // console.log(dt[0].pjk_persen_1)
        // console.log(dt[0].pjk_persen_2)



        $('#rp_cair_total').val(dt[0].rp_cair_total)

        $('#pen_bank').val(dt[0].pen_bank)
        $('#pen_nama').val(dt[0].pen_nama)
        $('#pen_norek').val(dt[0].pen_norek)
        $('#bank_saldoawal').val(dt[0].bank_saldoawal)
        $('#bank_saldoakhir').val(dt[0].bank_saldoakhir)
        $('#sisa_dana').val(dt[0].sisa_dana)
        if (dt[0].tgl_setoran_sisa != null) {
            $('#tgl_setoran_sisa').val(dt[0].tgl_setoran_sisa.split(' ')[0]);
        }


        // var progkeu = (dt[0].nilai_pks*100)/dt[0].rp_cair_total ;
        $('#ntpn_setoran_sisa').val(dt[0].ntpn_setoran_sisa)
        if (dt[0].progres_fisik == '' || dt[0].progres_fisik == null || dt[0].progres_fisik == 0) {
            dt[0].progres_fisik = 0;
        }
        // alert(dt[0].pjk_persen_1)
        // alert(dt[0].pjk_persen_2)
        $('#progres_fisik').val(parseFloat(dt[0].progres_fisik))
        $('#txtfis').text(parseFloat(dt[0].progres_fisik) + '%')
        dis_thp(parseFloat(dt[0].progres_fisik), dt[0].pjk_persen_1, dt[0].pjk_persen_2,kat)
        $("#profis").css('width', parseFloat(dt[0].progres_fisik) + '%')

        var progkeu = parseFloat(dt[0].progres_keu);
        if (dt[0].rp_cair_total == '' || dt[0].rp_cair_total == null || dt[0].rp_cair_total == 0) {
            progkeu = 0;
        } else {
            // progkeu = (parseFloat(dt[0].rp_cair_total) / parseFloat(dt[0].nilai_pks)) * 100;
            // progkeu = parseFloat(progres_keu);

        }
        $('#progres_keu').val(progkeu)
        $('#txtkeu').text(progkeu + '%')
        $("#progkeu").css('width', progkeu + '%')
        bank = dt[0].id_bank



        var dt_file = tampil_data('dok_kegiatanp3a', 'id_giat', id);
        for (let i = 0; i < dt_file.length; i++) {
            var nmfile = dt_file[i].filename;
            if (nmfile != '' || nmfile != null) {
                var ext = nmfile.split('.')[1];
                var judul = dt_file[i].judul_dok;
                var filepath = dt_file[i].filepath.replace("/sismonp3tgai/uploads/", "/uploads/");
                var timestamp = new Date().getTime(); // Add timestamp for cache busting

                if (ext == 'pdf') {
                    $("#" + judul).html("<a class='fancybox' target='_blank' href='" + filepath + "/" +
                        dt_file[i].filename + "?t=" + timestamp +
                        "'>Perbesar File Pdf<iframe 'class='img-responsive' style='width:100%;height:200px;' src='" +
                        filepath + "/" + dt_file[i].filename + "?t=" + timestamp + "' /></a><div class='d-flex align-items-center'>" +
                                        "<a type='button' class='btn btn-danger del-bres' onclick='deleteImage(\""+judul+"\", \"F_"+judul+"\", \"" + filepath + "/" + dt_file[i].filename + "\", \"" + dt_file[i].id_dok + "\")'" +
                                        "style='background:red !important;'>delete</a></div>");
                } else {
                    $("#" + judul).html("<a class='fancybox' target='_blank' href='" + filepath + "/" +
                        dt_file[i].filename + "?t=" + timestamp +
                        "'><img 'class='img-responsive' style='width:100%;height:200px;' src='" + filepath + "/" +
                        dt_file[i].filename + "?t=" + timestamp + "'></a><div class='d-flex align-items-center'>" +
                                        "<a type='button' class='btn btn-danger del-bres' onclick='deleteImage(\""+judul+"\", \"F_"+judul+"\", \"" + filepath + "/" + dt_file[i].filename + "\", \"" + dt_file[i].id_dok + "\")'" +
                                        "style='background:red !important;'>delete</a></div>");
                }

                $("#F_" + judul).val(judul);
            }
        }



        var dt_bu = tampil_data('kegiatan_p3a', 'id_giat', id);
        // alert(dt_bu[0].jml_bang_ut)
        // var le_bu = dt_bu.length-1;
        // for (let i = 0; i < dt_bu.length; i++) {
        //       if(dt_bu[i].id_jnsbgnut != null){
        //         // var but =' <button type="button" onclick="remove_combo('+i+')" name="remove" id="'+i+'" class="btn btn-danger btn_remove" >X</button>';
        //         // var yuh = 'yuhu'
        //         // if(i==0){
        //         //    but = ' <button type="button" class="btn btn-warning" onclick="bang_p('+le_bu+')">add</button>'
        //         //     yuh=''
        //         // }
        //         $("#yuhu").remove()
        //         $("#yuhu"+i).remove()
        //         var html =' <div class="form-group row '+yuh+'" id="yuhu'+i+'"><div class="col-sm-6">'+
        //             '<select id="bu'+i+'" name="bu[]" class=" form-control" data-live-search="true">'+
        //             '<option value="">--Pilih Bangunan Utama--</option>  '+
        //             ' </select></div>'+
        //             '<div class="col-sm-5">'+
        //             '<input type="number" class="form-control '+yuh+'" placeholder="" value="'+dt_bu[i].jml_bang_ut+'" id="jml_bang_ut" name="jml_bang_ut[]">'+
        //             '</div>'+
        //             '<div class="col-sm-1">'+but+'</div></div>';
        //       $('#bang_ut').append(html);
        //      initCombobox('bu'+i, 32);
        //      setTimeout(function() {
        //      $('#bu'+i).val(dt_bu[i].id_jnsbgnut).selectpicker('refresh');}, 1000);


        //      }
        // }
        if (dt_bu.length > 0) {
            $('#jml_bang_ut').val(dt_bu[0].jml_bang_ut)
            id_bu = dt_bu[0].id_jnsbgnut;

            //    alert(id_bu);
        }


        $(".yuhu").remove()
        $(".yuhui").remove()
        $(".yuhuj").remove()
        var dt_kap = tampil_data('r_rjns_bgn_plngkp', 'id_giat', id);
        var le_kep = dt_kap.length - 1;
        for (let i = 0; i < dt_kap.length; i++) {
            if (dt_kap[i].id_jnsbgnpl != null) {
                var but = ' <button type="button" onclick="remove_combo(' + i + ')" name="remove" id="' + i +
                    '" class="btn btn-danger btn_remove">X</button>';
                var yuh = 'yuhu'
                if (i == 0) {
                    but = ' <button type="button" class="btn btn-warning" onclick="bang_p(' + le_kep +
                        ')" style="background:orange !important;">add</button>'
                    yuh = ''
                }
                $("#yuhu").remove()
                $("#yuhu" + i).remove()
                var html = ' <div class="form-group row ' + yuh + '" id="yuhu' + i + '"><div class="col-sm-6">' +
                    '<select id="bp' + i +
                    '" name="bp[]" class="bootstrap-select form-control" data-live-search="true">' +
                    '<option value="">--Pilih Bangunan Pelengkap--</option>  ' +
                    ' </select></div>' +
                    '<div class="col-sm-5">' +
                    '<input min="0" type="number" class="form-control ' + yuh + '" placeholder="" value="' + dt_kap[i]
                    .jml_bang_kap + '" id="jml_bang_kap' + i +
                    '" name="jml_bang_kap[]" onkeypress="return isNumber(event)">' +
                    '</div>' +
                    '<div class="col-sm-1">' + but + '</div></div>';

                $('#bang_pen').append(html);
                initCombobox('bp' + i, 33);
                setTimeout(function() {
                    $('#bp' + i).val(dt_kap[i].id_jnsbgnpl).selectpicker('refresh');
                }, 500);
            }
        }

        var dt_lain = tampil_data('r_rjns_bgnlain', 'id_giat', id);
        var le_lain = dt_lain.length - 1;
        for (let i = 0; i < dt_lain.length; i++) {
            if (dt_lain[i].id_jnsbgnln != null) {
                var but = ' <button type="button" onclick="remove_comboi(' + i + ')" name="remove" id="' + i +
                    '" class="btn btn-danger btn_remove">X</button>';
                var yuh = 'yuhui'

                if (i == 0) {
                    but = ' <button type="button" class="btn btn-warning" onclick="bang_l(' + le_lain +
                        ')" style="background:orange !important;">add</button>'
                    yuh = ''

                }
                $("#yuhui").remove()
                $("#yuhui" + i).remove()
                var html = ' <div class="form-group row ' + yuh + '" id="yuhui' + i + '"><div class="col-sm-6">' +
                    '<select id="bl' + i +
                    '" name="bl[]" class="bootstrap-select form-control" data-live-search="true">' +
                    '<option value="">--Pilih Bangunan Lain-lain--</option>  ' +
                    ' </select></div>' +
                    '<div class="col-sm-5">' +
                    '<input min="0" type="number" class="form-control ' + yuh + '" placeholder="" value="' + dt_lain[i]
                    .jml_bang_lain + '" id="jml_bang_lain' + i +
                    '" name="jml_bang_lain[]" onkeypress="return isNumber(event)">' +
                    '</div>' +
                    '<div class="col-sm-1">' + but + '</div></div>';
                $('#bang_lai').append(html);
                initCombobox('bl' + i, 34);
                setTimeout(function() {
                    // alert(dt_lain[i].id_jnsbgnln)
                    $('#bl' + i).val(dt_lain[i].id_jnsbgnln).selectpicker('refresh');
                }, 500);

            }
        }

        var dt_segmen = tampil_data('r_rtracking', 'id_giat', id);
        var le_segmen = dt_segmen.length - 1;
        for (let i = 0; i < dt_segmen.length; i++) {

            var koord = dt_segmen[i].koordinat;
            var koordObj = koord.replace('],[', '#').replace('[[', '').replace(']]', '');

            var arrKoord = koordObj.split('#');
            var x1 = arrKoord[0].split(',')[0];
            var y1 = arrKoord[0].split(',')[1];
            var x2 = arrKoord[1].split(',')[0];
            var y2 = arrKoord[1].split(',')[1];

            if (dt_segmen[i].id_tracking != null) {
                var but = ' <button type="button" onclick="remove_comboj(' + i + ')" name="remove" id="' + i +
                    '" class="btn btn-danger btn_remove">X</button>';
                var yuh = 'yuhuj'
                if (i == 0) {
                    but = ' <button type="button" class="btn btn-warning" onclick="segmentasi(' + le_segmen +
                        ')" style="background:orange !important;">add</button>'
                    yuh = ''
                }

                $("#yuhuj").remove()
                $("#yuhuj" + i).remove()
                var html = '<div class="form-group row ' + yuh + '"  id="yuhuj' + i + '">' +
                    '<div class="col-sm-12" style="padding:11px;font-weight:bold;">' +
                    '<center id="abjad">Segmen</center>' +
                    '</div>' +
                    '<div class="row">' +
                    '<div class="col-sm-3">' +
                    '<label class="col-sm-12 col-form-label "> Titik Awal</label>' +
                    '</div>' +
                    '<div class="col-sm-4">' +
                    '<input type="text" class="form-control ' + yuh + '" placeholder="Longitude (x1)" id="xawal' + i +
                    '" name="xawal[]" value="' + x1 + '">' +
                    '</div>' +
                    '<div class="col-sm-4">' +
                    '<input type="text" class="form-control ' + yuh + '" placeholder="Latitude (y1)" id="yawal' + i +
                    '" name="yawal[]" value="' + y1 + '">' +
                    '</div>' +
                    '<div class="col-sm-1">' + but + '</div>' +
                    '</div>' +
                    '<div class="row">' +
                    '<div class="col-sm-3">' +
                    '<label class="col-sm-12 col-form-label">Titik Akhir</label>' +
                    '</div>' +
                    '<div class="col-sm-4">' +
                    '<input type="text" class="form-control ' + yuh + '" placeholder="Longitude (x2)" id="xakhir' + i +
                    '" name="xakhir[]" value="' + x2 + '">' +
                    '</div>' +
                    '<div class="col-sm-4">' +
                    '<input type="text" class="form-control ' + yuh + '" placeholder="Latitude (y2)" id="yakhir' + i +
                    '" name="yakhir[]" value="' + y2 + '">' +
                    '</div>' +
                    '</div>' +
                    '<hr>' +
                    '</div>';

                $('#segmen_koord').append(html);

            }
        }

        $('body div#modal-tambah.modal').one('shown.bs.modal', function(e) {
            $(".simpan").show();

            $('#bu').val(id_bu).selectpicker('refresh');
            $('#bp').val('').selectpicker('refresh');
            $('#bl').val('').selectpicker('refresh');
            $('#pen_bank').val(bank).selectpicker('refresh');
            $( "#ls_layanan_menjadi" ).on( "keyup", function() {
                 if($("#ls_layanan_menjadi").val() < $("#ls_layanan_awal").val()){
                    swal({
            // title: "Anda yakin menghapus data ?",
                            text: 'Layanan Menjadi Harus Lebih Besar Atau Sama Dengan Layanan Semula',
                            icon: "warning",
                            buttons: false,
                            timer: 2000,
                            dangerMode: true,
                        })
                        $("#ls_layanan_menjadi").val(''); //for clearing with Jquery
                        return
                 } else {
                    $(".simpan").show();

                 }

            } );

            $( "#tk_pria, #tk_wanita, #tk_lansia, #tk_total" ).on( "keyup", function() {
                //leng digit not grater than 2 return swal notif

                if($(this).val().length > 2){
                    swal({
            // title: "Anda yakin menghapus data ?",
                            text: 'Cek kembali data Anda Melebihi 2 digit',
                            icon: "warning",
                            buttons: false,
                            timer: 2000,
                            dangerMode: true,
                        })
                        $("#tk_total").val('');
                        return

                } else {
                    $(".simpan").show();

                }


            } );


            $( "#tk_pria, #tk_wanita, #tk_lansia, #tk_total" ).on( "focus", function() {
                //leng digit not grater than 2 return swal notif

                if($(this).val().length > 2){
                    swal({
            // title: "Anda yakin menghapus data ?",
                            text: 'Cek kembali data Anda Melebihi 2 digit',
                            icon: "warning",
                            buttons: false,
                            timer: 2000,
                            dangerMode: true,
                        })
                        $("#tk_total").val('');
                        return

                } else {
                    $(".simpan").show();

                }


            } );


            $( "#pjg_induk_bbl").on( "keyup", function() {
                //leng digit not grater than 2 return swal notif

                if($(this).val() > 1500){
                    swal({
            // title: "Anda yakin menghapus data ?",
                            text: 'Cek kembali data Anda, Tidak Melebihi 1500 meter',
                            icon: "warning",
                            buttons: false,
                            timer: 2000,
                            dangerMode: true,
                        })
                        $("#pjg_induk_bbl").val('');
                        return

                } else {
                    $(".simpan").show();

                }


            } );


            $( "#pjg_induk_bbl").on( "focus", function() {
                //leng digit not grater than 2 return swal notif

                if($(this).val() > 1500){
                    swal({
            // title: "Anda yakin menghapus data ?",
                            text: 'Cek kembali data Anda, Tidak Melebihi 1500 meter',
                            icon: "warning",
                            buttons: false,
                            timer: 2000,
                            dangerMode: true,
                        })
                        $("#pjg_induk_bbl").val('');
                        return

                } else {
                    $(".simpan").show();

                }


            } );

            $( "#pjg_sekunder_bbl").on( "keyup", function() {
                //leng digit not grater than 2 return swal notif

                if($(this).val() > 1500){
                    swal({
            // title: "Anda yakin menghapus data ?",
                            text: 'Cek kembali data Anda, Tidak Melebihi 1500 meter',
                            icon: "warning",
                            buttons: false,
                            timer: 2000,
                            dangerMode: true,
                        })
                        $("#pjg_sekunder_bbl").val('');
                        return

                } else {
                    $(".simpan").show();

                }


            } );

            $( "#pjg_sekunder_bbl").on( "focus", function() {
                //leng digit not grater than 2 return swal notif

                if($(this).val() > 1500){
                    swal({
            // title: "Anda yakin menghapus data ?",
                            text: 'Cek kembali data Anda, Tidak Melebihi 1500 meter',
                            icon: "warning",
                            buttons: false,
                            timer: 2000,
                            dangerMode: true,
                        })
                        $("#pjg_sekunder_bbl").val('');
                        return

                } else {
                    $(".simpan").show();

                }


            } );


            $( "#pjg_tersier_bbl").on( "keyup", function() {
                //leng digit not grater than 2 return swal notif

                if($(this).val() > 1500){
                    swal({
            // title: "Anda yakin menghapus data ?",
                            text: 'Cek kembali data Anda, Tidak Melebihi 1500 meter',
                            icon: "warning",
                            buttons: false,
                            timer: 2000,
                            dangerMode: true,
                        })
                        $("#pjg_tersier_bb").val('');
                        return

                } else {
                    $(".simpan").show();

                }


            } );

            $( "#pjg_tersier_bbl").on( "focus", function() {
                //leng digit not grater than 2 return swal notif

                if($(this).val() > 1500){
                    swal({
            // title: "Anda yakin menghapus data ?",
                            text: 'Cek kembali data Anda, Tidak Melebihi 1500 meter',
                            icon: "warning",
                            buttons: false,
                            timer: 2000,
                            dangerMode: true,
                        })
                        $("#pjg_tersier_bb").val('');
                        return

                } else {
                    $(".simpan").show();

                }


            } );


            $( "#pjg_sal_buang").on( "keyup", function() {
                //leng digit not grater than 2 return swal notif

                if($(this).val() > 1500){
                    swal({
            // title: "Anda yakin menghapus data ?",
                            text: 'Cek kembali data Anda, Tidak Melebihi 1500 meter',
                            icon: "warning",
                            buttons: false,
                            timer: 2000,
                            dangerMode: true,
                        })
                        $("#pjg_sal_buang").val('');
                        return

                }else {
                    $(".simpan").show();

                }


            } );


            $( "#pjg_sal_buang").on( "focus", function() {
                //leng digit not grater than 2 return swal notif

                if($(this).val() > 1500){
                    swal({
            // title: "Anda yakin menghapus data ?",
                            text: 'Cek kembali data Anda, Tidak Melebihi 1500 meter',
                            icon: "warning",
                            buttons: false,
                            timer: 2000,
                            dangerMode: true,
                        })
                        $("#pjg_sal_buang").val('');
                        return

                }else {
                    $(".simpan").show();

                }


            } );


            $( "#serapan_alokasi").on( "keyup", function() {
                //leng digit not grater than 2 return swal notif

                if($(this).val() > 195000000){
                    swal({
            // title: "Anda yakin menghapus data ?",
                            text: 'Cek kembali data Anda, Tidak Melebihi 195.000.000 Rupiah',
                            icon: "warning",
                            buttons: false,
                            timer: 2000,
                            dangerMode: true,
                        })
                        $("#serapan_alokasi").val('');
                        $(".simpan").hide();
                        return

                } else {
                    $(".simpan").show();

                }


            } );


            $( "#serapan_alokasi").on( "focus", function() {
                //leng digit not grater than 2 return swal notif

                if($(this).val() > 195000000){
                    swal({
            // title: "Anda yakin menghapus data ?",
                            text: 'Cek kembali data Anda, Tidak Melebihi 195.000.000 Rupiah',
                            icon: "warning",
                            buttons: false,
                            timer: 2000,
                            dangerMode: true,
                        })
                        // $("#serapan_alokasi").val('');
                        $(".simpan").hide();
                        return

                } else {
                    $(".simpan").show();

                }


            } );

            $( "#upah_tk_total").on( "keyup", function() {
                //leng digit not grater than 2 return swal notif

                if($(this).val() > 195000000){
                    swal({
            // title: "Anda yakin menghapus data ?",
                            text: 'Cek kembali data Anda, Tidak Melebihi 195.000.000 Rupiah',
                            icon: "warning",
                            buttons: false,
                            timer: 2000,
                            dangerMode: true,
                        })
                        $("#serapan_alokasi").val('');
                        $(".simpan").hide();
                        return

                } else {
                    $(".simpan").show();

                }


            } );


            $( "#upah_tk_total").on( "focus", function() {
                //leng digit not grater than 2 return swal notif

                if($(this).val() > 195000000){
                    swal({
            // title: "Anda yakin menghapus data ?",
                            text: 'Cek kembali data Anda, Tidak Melebihi 195.000.000 Rupiah',
                            icon: "warning",
                            buttons: false,
                            timer: 2000,
                            dangerMode: true,
                        })
                        // $("#serapan_alokasi").val('');
                        $(".simpan").hide();
                        return

                } else {
                    $(".simpan").show();

                }


            } );

            $( "#tk_totalhok").on( "focus", function() {
                //leng digit not grater than 2 return swal notif

                if($(this).val() > 195000000){
                    swal({
            // title: "Anda yakin menghapus data ?",
                            text: 'Cek kembali data Anda, Tidak Melebihi 195.000.000 Rupiah',
                            icon: "warning",
                            buttons: false,
                            timer: 2000,
                            dangerMode: true,
                        })
                        $("#tk_totalhok").val('');
                        $(".simpan").hide();
                        return

                } else {
                    $(".simpan").show();

                }


            } );


        });
        // // if(dt[0].path != null){

        // // }
        // //  $('#file_npwp').val();
        // //  alert(subs_img)

        // $('#id').val(dt[0].id_giat);
        // if(dt[0].tgl_pelaksanaan != null){
        //     $('#tgl_survey').val(dt[0].tgl_pelaksanaan.split(' ')[0]);
        // }
        // if(dt[0].tgl_musy_desa_2 != null){
        //     $('#tgl_pelak').val(dt[0].tgl_musy_desa_2.split(' ')[0]);
        // }
        // if(dt[0].tgl_pengajuan_rencana != null){
        //     $('#tgl_peng').val(dt[0].tgl_pengajuan_rencana.split(' ')[0]);
        // }
        // if(dt[0].tgl_persetujuan_rencana != null){
        //     $('#tgl_perset').val(dt[0].tgl_persetujuan_rencana.split(' ')[0]);
        // }
        // $('#jum_pese_l').val(dt[0].musdes2_pria);
        // $('#jum_pese_p').val(dt[0].musdes2_wanita);
        // $('#jum_pese_lan').val(dt[0].musdes2_lansia);
        // var  has = parseInt(dt[0].musdes2_pria)+parseInt(dt[0].musdes2_wanita)
        // if(isNaN(has)){
        //     has ='';
        // }
        // $('#jum_tot_pes').val(has);


    }




    if (kat == 'detail') {
        $(".form-tambah .form-group input").prop('readonly', true)
        $(".form-tambah .form-group input").css({
            "border": "0px",
            "background": "#e9ecef45"
        })
        $(".form-tambah .form-group select").prop('disabled', true)
        $(".form-tambah .form-group select").css({
            "border": "0px",
            "background": "#e9ecef45"
        })
        $(".form-tambah .form-group textarea").prop('readonly', true)
        $(".form-tambah .form-group textarea").css({
            "border": "0px",
            "background": "#e9ecef45"
        })
        $(".form-tambah .form-group button").prop('disabled', true)
        $(".form-tambah .form-group button").css({
            "border": "0px",
            "background": "#e9ecef45"
        })
        $(".form-tambah .form-group").css({
            "border-bottom": "1px solid #80808054"
        })
        $("#fot_buku_rekening").hide()
        $("#fot_peta_di").hide()
        $("#fot_jaringan_keseluruhan").hide()
        $("#fot_jaringan_petak").hide()
        $("#fot_padat_karya").hide()
        $("#fot_progres_fisik_0").hide()
        $("#fot_progres_fisik_50").hide()
        $("#fot_progres_fisik_100").hide()
        if (group == 2) {
            $("#btn_tambah_edit").css('display', 'none')
            $("#btn_verifikasi").css('display', 'block')
            $("#btn_verifikasi button").prop('disabled', true)
            $("#btn_verifikasi").attr('title', 'Data belum lengkap, tidak bisa mem-validasi')
            if (dt[0].valid_1_p3 == 'Data Sudah Lengkap') {
                $("#btn_verifikasi button").prop('disabled', false)
                $("#btn_verifikasi").attr('title', '')

            }
        } else {
            $("#btn_tambah_edit").css('display', 'none')
        }
    }

    $("#upah_tk_rerata").prop("readonly", true)
    $("#tk_rerata").prop("readonly", true)
    // $("#rp_cair_1_rek").prop("readonly",true)
    // $("#rp_cair_1_total").prop("readonly",true)
    // $("#rp_cair_2_rek").prop("readonly",true)
    // $("#rp_cair_2_total").prop("readonly",true)


    $(".decformat").keyup(function(event) {
        if (event.which >= 37 && event.which <= 40)
            return;
        // format number
        $(this).val(function(index, value) {
            return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        });
    });


    $('.form-group').removeClass('has-error'); // clear error class
    $('.help-block').empty(); // clear error string
    $('#modal-tambah').modal('show');
    $('.tbhItem').text('Form pelaksanaan');
    // $("#urutans").empty();
    // $("#urutans").val("2");

    //--- end dt tambah row ---
}


function validasiEkstensi(id, ext = '') {
    var inputFile = document.getElementById(id);
    var pathFile = inputFile.value;
    var file_size = inputFile.files[0].size / 1024;
    var bts = ''
    var txt = ''
    // var ekstensiOk = /(\.jpg|\.jpeg|\.png|\.gif)$/i;

    if (ext != '') {
        ekstensiOk = /(\.pdf)$/i;
        bts = 200
        txt = 'Silakan upload file yang dengan ekstensi .pdf'

    } else {
        ekstensiOk = /(\.jpg|\.jpeg|\.png|\.gif)$/i;
        bts = 500
        txt = 'Silakan upload file yang dengan ekstensi .png , .jpg,'


    }

    if (file_size > bts) {
        swal({
            // title: "Anda yakin menghapus data ?",
            text: 'Ukuran file tidak boleh lebih dari ' + bts + ' Kb',
            icon: "warning",
            buttons: false,
            timer: 2000,
            dangerMode: true,
        })
        $(inputFile).val(''); //for clearing with Jquery
        return
    }
    if (!ekstensiOk.exec(pathFile)) {
        swal({
            // title: "Anda yakin menghapus data ?",
            text: txt,
            icon: "warning",
            buttons: false,
            timer: 2000,
            dangerMode: true,
        })
        // alert('Silakan upload file yang dengan ekstensi .pdf/.jpg/.png/.gif');
        inputFile.value = '';
        return false;
    } else {
        // Preview gambar
        if (inputFile.files && inputFile.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                // document.getElementById('upload_peta_di').innerHTML = '<iframe src="'+e.target.result+'" class="img-responsive" style="width:100%;height:200px;"/>';
            };
            reader.readAsDataURL(inputFile.files[0]);
        }
    }
}


function dis_thp(val, persen1 = '', persen2 = '', kat='') {
    prog = 0
    if (val != null) {
        var prog = val.value;
    }
    if (prog === undefined) {
        prog = val;
    }



    // $("#rp_cair_2_rek").prop("disabled",true)
    // $("#rp_cair_2_pajak").prop("disabled",true)
    // $("#hasil_nilai_pks_30").prop("readonly",true)
    // $("#rp_cair_2_total").prop("readonly",true)
    // $("#rp_cair_2_tgl").prop("readonly",true)
    $("#rp_cair_total").prop("readonly", true)
    $("#rp_cair_1_rek").prop("readonly", true)
    $("#rp_cair_1_pajak").prop("disabled", false)
    $("#hasil_nilai_pks_70").prop("readonly", true)
    $("#rp_cair_1_total").prop("readonly", true)
    $("#rp_cair_1_tgl").prop("readonly", false)
    $("#masa_1").prop("disabled", false)


    // $("#progres_fisik").keyup(function() {
    // console.log($(this).val());
    // prog = parseFloat($(this).val());
    prog = parseFloat(prog);
    prog = (isNaN(prog)) ? 0 : prog;
    $("#profis").css('width', prog + '%')
    $('#txtfis').text(prog + '%')

    if (prog >= 50) {
        $("#rp_cair_2_rek").prop("readonly", true)
        $("#rp_cair_2_pajak").prop("disabled", false)
        $("#hasil_nilai_pks_30").prop("readonly", true)
        $("#rp_cair_2_total").prop("readonly", true)
        $("#rp_cair_2_tgl").prop("readonly", false)
        $("#masa_2").prop("disabled", false)
        $('#rp_cair_1_pajak').val(persen1).selectpicker('refresh')
        $('#rp_cair_2_pajak').val(persen2).selectpicker('refresh')
        // $('#rp_cair_2_pajak').val(persen).selectpicker('refresh')

    } else {
        if(kat == 'edit' || kat == 'detail'){

        } else {
            $("#rp_cair_2_rek").prop("readonly", true)
            $("#rp_cair_2_pajak").prop("disabled", true)
            $("#hasil_nilai_pks_30").prop("readonly", true)
            $("#rp_cair_2_total").prop("readonly", true)
            $("#rp_cair_2_tgl").prop("readonly", true)
            $("#masa_2").prop("disabled", true)
            $("#rp_cair_2_rek").val('')
            $('#rp_cair_2_pajak').val(0).selectpicker('refresh')
            $("#hasil_nilai_pks_30").val('')
            $("#rp_cair_2_total").val('')
            $("#rp_cair_2_tgl").val('')
            $("#masa_2").val('')

        }

    }
    // })

    var satu = $("#rp_cair_1_rek").val()
    var dua = $("#rp_cair_2_rek").val()
    if (isNaN(parseInt(satu))) {
        satu = 0
    }
    if (isNaN(parseInt(dua))) {
        dua = 0
    }
    $("#rp_cair_total").val(has = parseInt(satu) + parseInt(dua))


}

function dtDeleteRow(id) {

    swal({
            title: "Anda yakin menghapus data ?",
            // text: tex,
            icon: "warning",
            buttons: true,
            dangerMode: true,
        })
        .then((willDelete) => {
            if (willDelete) {
                var url = ''
                url = "<?php echo base_url(); ?>" + "pelaksanaan/ajax_delete/" + id;
                $.get(url).done(function(data) {
                    if (data == 0) {
                        swal({
                            title: "",
                            text: "Berhasil Menghapus Data",
                            icon: "success",
                            showConfirmButton: false,
                            timer: 3000,
                            type: "success"
                        });
                        table.ajax.reload();
                    }
                })
            } else {
                swal("Anda Tidak Menjadi Menghapus Data");
            }
        });
}

function cek_strip(val) {
    var nil = ''
    if (val != null) {
        nil = val.value;
    }
    if (nil === undefined) {
        nil = val;
    }
    // var nil =a.value
    // if(val != null){
    //      nil = val.value;
    //     }
    // if(nil === undefined){
    //         nil = a;
    //     }
    // if(a === undefined){
    //     nil = a.value;
    // }
    // else{
    //     nil = a
    // }
    // alert(nil)
    if (nil == '-') {
        $("#tgl_pks").val("")
        $("#tgl_pks").prop("disabled", true)
    } else {
        $("#tgl_pks").prop("disabled", false)

    }
}

function hitung(yuhu, hasil) {
    var total = 0;
    var yuhu = yuhu.split('+')
    var satu = $("#" + yuhu[0]).val()
    var dua = $("#" + yuhu[1]).val()
    if (isNaN(parseInt(satu))) {
        satu = 0
    }
    if (isNaN(parseInt(dua))) {
        dua = 0
    }
    var to = parseInt(satu) + parseInt(dua)
    $("#" + hasil).val(to)
}

function hitung_x(yuhu, hasil) {
    var total = 0;
    var yuhu = yuhu.split('+')
    var satu = $("#" + yuhu[0]).val()
    var dua = $("#" + yuhu[1]).val()
    if (isNaN(parseInt(satu))) {
        satu = 0
    }
    if (isNaN(parseInt(dua))) {
        dua = 0
    }
    var to = parseInt(satu) + parseInt(dua)
    $("#" + hasil).val(to)
}

function hitung_bagi(yuhu, hasil) {
    var total = 0;
    var yuhu = yuhu.split('+')
    var satu = $("#" + yuhu[0]).val()
    var dua = $("#" + yuhu[1]).val()
    if (isNaN(parseInt(satu))) {
        satu = 0
    }
    if (isNaN(parseInt(dua))) {
        dua = 0
    }
    var to = parseInt(satu) / parseInt(dua)
    $("#" + hasil).val(parseFloat(to.toFixed(2)))
}

function isNumber(evt, a) {

    evt = (evt) ? evt : window.event;
    var charCode = (evt.which) ? evt.which : evt.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
        return false;
    }
    return true;
}

function hitung_nilai_pks(val) {
    $("#hasil_nilai_pks_70").prop("readonly", true)
    $("#hasil_nilai_pks_30").prop("readonly", true)
    $("#rp_cair_1_rek").prop("readonly", true)
    $("#rp_cair_1_total").prop("readonly", true)
    $("#rp_cair_2_rek").prop("readonly", true)
    $("#rp_cair_2_total").prop("readonly", true)
    var nil = val.value;
    var nil_70 = parseInt(nil) * 0.7;
    var nil_30 = parseInt(nil) * 0.3;
    hitung_persen($("#rp_cair_1_pajak").val(), 1)
    hitung_persen($("#rp_cair_2_pajak").val(), 2)

    $("#rp_cair_1_total").val(nil_70)
    $("#rp_cair_2_total").val(nil_30)

}


function hitung_pencairan(val, kat) {
    if (kat == 1) {
        var has_pencairan_1 = 0
        if (val != null) {
            has_pencairan_1 = val.value;
        }
        if (has_pencairan_1 === undefined) {
            has_pencairan_1 = val;
        }
        // alert(has_pencairan_1)

        var pers = parseInt($("#hasil_nilai_pks_70").val())
        var has = parseInt(has_pencairan_1)
        if (isNaN(pers)) {
            pers = 0;
        }
        if (isNaN(has)) {
            has = 0;
        }
        total_1 = has + pers
        $("#rp_cair_1_total").val(total_1)

    } else {
        var has_pencairan_2 = 0
        if (val != null) {
            has_pencairan_2 = val.value;
        }
        if (has_pencairan_2 === undefined) {
            has_pencairan_2 = val;
        }
        // alert(has_pencairan_1)

        var pers = parseInt($("#hasil_nilai_pks_30").val())
        var has = parseInt(has_pencairan_2)
        if (isNaN(has)) {
            has = 0;
        }
        if (isNaN(pers)) {
            pers = 0;
        }
        total_1 = has + pers
        $("#rp_cair_2_total").val(total_1)
    }
    var total_1 = parseInt($("#rp_cair_1_total").val())
    var total_2 = parseInt($("#rp_cair_2_total").val())
    if (isNaN(total_1)) {
        total_1 = 0;
    }
    if (isNaN(total_2)) {
        total_2 = 0;
    }
    var has_1_2 = total_1 + total_2

    $("#rp_cair_total").val(has_1_2)


}

function hitung_persen(val, kat) {

    // var total_pencairan_1 = $("#rp_cair_1_total").val();
    // var total_pencairan_2 =$("#rp_cair_2_total").val();
    var total_1 = 0;
    var total_2 = 0;
    var masuk_rekening = 0;
    var nil = 0
    if (val != null) {
        nil = val.value;
    }
    if (nil === undefined) {
        nil = val;
    }


    if (val.value == '') {

        if (kat == 1) {
            $("#hasil_nilai_pks_70").val(0)
            $("#rp_cair_1_rek").val(0)
            $("#rp_cair_1_total").val(0)
            // alert(total_pencairan)
            total_pencairan_2 = total_2

        } else if (kat == 2) {
            $("#hasil_nilai_pks_30").val(0)
            $("#rp_cair_2_rek").val(0)
            $("#rp_cair_2_total").val(0)
            total_pencairan_1 = total_1
        }
    } else {
        var total_1 = parseInt($("#rp_cair_1_total").val())
        var total_2 = parseInt($("#rp_cair_2_total").val())

        if (kat == 1) {
            // var nil = val.value;
            var nil_70 = parseInt($("#nilai_pks").val()) * 0.7;
            var has = nil_70 * parseFloat(nil)
            var has_pencairan_1 = parseInt($("#rp_cair_1_rek").val())
            if (isNaN(has_pencairan_1)) {
                has_pencairan_1 = 0;
            }
            total_1 = has_pencairan_1 + has
            masuk_rekening = nil_70 - has

            // console.log(has + "pajak")
            // console.log(masuk_rekening + "masuk")
            total_pencairan_1 = has + masuk_rekening
            $("#hasil_nilai_pks_70").val(has)
            $("#rp_cair_1_rek").val(masuk_rekening)
            $("#rp_cair_1_total").val(total_pencairan_1)
            // alert(total_pencairan)
            total_pencairan_2 = total_2
        } else if (kat == 2) {
            // var nil = val.value;
            var nil_30 = parseInt($("#nilai_pks").val()) * 0.3;
            var has = nil_30 * parseFloat(nil)
            var has_pencairan_2 = parseInt($("#rp_cair_2_rek").val())
            if (isNaN(has_pencairan_2)) {
                has_pencairan_2 = 0;
            }
            // var has_pencairan_2  = nil_30 - has
            masuk_rekening = nil_30 - has
            total_2 = has_pencairan_2 + has
            total_pencairan_2 = has + masuk_rekening
            $("#hasil_nilai_pks_30").val(has)
            $("#rp_cair_2_rek").val(masuk_rekening)
            $("#rp_cair_2_total").val(total_pencairan_2)
            total_pencairan_1 = total_1
        }

    }

    if (isNaN(total_pencairan_1)) {
        total_pencairan_1 = 0;
    }
    if (isNaN(total_pencairan_2)) {
        total_pencairan_2 = 0;
    }
    // alert(total_pencairan_1)
    var has_1_2 = total_pencairan_1 + total_pencairan_2
    $("#rp_cair_total").val(has_1_2)
    var persentase = (parseInt(has_1_2) / parseInt($("#nilai_pks").val())) * 100
    //  alert(persentase)
    if (isNaN(persentase)) {
        persentase = 0;
    }
    $("#progres_keu").val(persentase)
    $('#txtkeu').text(persentase + '%')
    $("#progkeu").css('width', persentase + '%')
}




// }


function get_extentsion_file(file) {
    var extension = file.substr((file.lastIndexOf('.') + 1));
    switch (extension) {
        case 'jpg':
        case 'png':
        case 'PNG':
        case 'jpeg':
        case 'gif':
        case 'JPG':
            return 'feather icon-image'; // There's was a typo in the example where
            break; // the alert ended with pdf instead of gif.
        case 'zip':
        case 'rar':
            //alert('was zip rar');
            return 'feather icon-archive';
            break;
        case 'pdf':
            return 'feather icon-file-text';
        case 'xlsx':
            return 'feather icon-file-text';
            break;
        default:
            return 'feather icon-file-text';

    }
}

function hapus_lampiran(id) {
    if (confirm('Yakin untuk menghapus data ini?')) {

        var url = "<?php echo base_url(); ?>" + "pelaksanaan/hps_lampiran/" + id;
        var params = {
            "formData": {},
            "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"
        };
        $.post(url, params)
            .done(function(data) {
                var tlist_paket = $('#tlist_paket').DataTable();
                tlist_paket.ajax.reload()
                var tab = $('#table_id2').DataTable();
                tab.ajax.reload();;
            })
            .fail(function() {
                alert("error");
            })
    }
}

function tampil_data(table, colum, id) {
    var url = ''
    var tadata = ''
    urls = "<?php echo base_url(); ?>pelaksanaan/tampildata/" + table + "/" + colum + "/" + id;
    $.ajax({
        url: urls,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function(data) {
            tadata = data;
        },
        failure: function(errMsg) {
            alert(errMsg);
        }
    });
    return tadata;
}

function addSelectItem(t, ev) {
    ev.stopPropagation();

    var bs = $(t).closest('.bootstrap-select')
    var txt = bs.find('.bss-input').val().replace(/[|]/g, "");
    var txt = $(t).prev().val().replace(/[|]/g, "");
    if ($.trim(txt) == '') return;

    // Changed from previous version to cater to new
    // layout used by bootstrap-select.
    var p = bs.find('select');
    var o = $('option', p).eq(-2);
    o.before($("<option>", {
        "selected": true,
        "text": txt
    }));
    p.selectpicker('refresh');
}

function addSelectInpKeyPress(t, ev) {
    ev.stopPropagation();

    // do not allow pipe character
    if (ev.which == 124) ev.preventDefault();

    // enter character adds the option
    if (ev.which == 13) {
        ev.preventDefault();
        addSelectItem($(t).next(), ev);
    }
}

var proceedDelImage = '';
function deleteImage(previewDivId, hiddenInputId, file, id) {

    // console.log(previewDivId);
    // console.log(hiddenInputId);
    //Clear the preview div

    document.getElementById(previewDivId).innerHTML = '';
    // Clear the hidden input value
    document.getElementById(hiddenInputId).value = '';

    $(".del-bres").hide();
    if(file!=='' && id!=='') {
        // proceeedDelImage= true, previewDivId, hiddenInputId, file, id);

    }
    return proceeedDelImage;

}

function delimage_proc() {
    // $.ajax({
    //         type: 'POST',
    //         url: "<?php /* echo base_url('pelaksanaan/delete_dtImage'); */ ?>",
    //         data: new FormData(this),
    //         processData: false,
    //         contentType: false,
    //         cache: false,
    //         async: false,
    //         success: function(data) {

    //             console.log('delete image');

    //         }
    //     });

}

$(document).ready(function() {
    var content =
        "<input type='text' class='bss-input' onKeyDown='event.stopPropagation();' onKeyPress='addSelectInpKeyPress(this,event)' onClick='event.stopPropagation()' placeholder='Add item'> <span class='glyphicon glyphicon-plus addnewicon' onClick='addSelectItem(this,event,1);'></span>";

    var divider = $('<option/>')
        .addClass('divider')
        .data('divider', true);


    var addoption = $('<option/>', {
            class: 'addItem'
        })
        .data('content', content)

    $('.bootstrap-select')
        .append(divider)
        .append(addoption)
        .selectpicker();
    // $(".js-example-tags").select2({
    //     tags: true
    //     });
    // bind_combo_thang(thangs);
    listing();
    $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");

    document.querySelectorAll(".search th").forEach(function(element) {
        var title = element.innerText;
        element.innerHTML = '<input type="text" class="form-control" placeholder="' + title.trim() +
            '">';
    });

    // table.columns().every(function() {
    //     var that = this;
    //     document.querySelectorAll('input[type=text]').forEach(function(element) {
    //         element.addEventListener("keyup", function(event) {
    //             event.preventDefault();
    //             console.log('Search index', that.index())
    //             if (that.search() !== this.value) {
    //                 that.search(this.value).draw();
    //             }
    //         });
    //     });

    // });

    $(".fs a").click(function(e) {
        e.preventDefault();
        var fs = $(this).text();

        $(".statustahap:first-child").text(fs);
        $(".statustahap:first-child").val(fs);


        table.ajax.reload();

        // return;
        // e.stopPropagation();

    });

    $("#submit").submit(function(e) {


        e.preventDefault()
        // ;
        //   if($(".form-tambah").valid()==true){
        var data = $('.form-tambah').serialize();
        // console.log(new FormData(this))
        $.ajax({
            type: 'POST',
            url: "<?php echo base_url('pelaksanaan/insert_data');?>",
            data: new FormData(this),
            processData: false,
            contentType: false,
            cache: false,
            async: false,
            success: function(data) {
                var ops = ''
                if (data.split('_')[1] == 'insert') {
                    ops = 'Menambah';
                } else {
                    ops = 'Merubah';
                }
                if (data.split('_')[0] == 0) {
                    swal({
                        icon: "success",
                        text: "Berhasil " + ops + " Data",
                        showConfirmButton: false,
                        timer: 2000,
                        type: "success"
                    });
                    table.ajax.reload();
                    $('#modal-tambah').modal('hide');
                } else {
                    swal({
                        title: "",
                        text: "Gagal " + ops + " Data",
                        showConfirmButton: false,
                        timer: 1000,
                        icon: "warning"
                    });
                }

            }
        });


        if(proceedDelImage == true) {
            delimage_proc();
        }

        // if(document.getElementById('upload_foto_bangunan_responsif').innerHTML == '' && document.getElementById('F_upload_foto_bangunan_responsif').value == '') {
        //     delimage_proc();
        // }
        //  }
    });
    //   $(".form-tambah").validate({
    //       ignore: ':not(select:visible,button:visible, input:visible, textarea:visible, .selectpicker)',
    //       rules: {
    //         bbws: { required: true  },
    //         prov: { required: true  },
    //         kab: { required: true  },
    //         kec: { required: true  },
    //         desa: { required: true  },
    //         jen_penerima: { required: true  },
    //         nm_penerima: { required: true  },
    //         j_legalitas: { required: true  },
    //         t_legalitas: { required: true  },
    //         nm_ketua: { required: true  },
    //         ala_ketua: { required: true  },
    //         no_ketua: { required: true  },
    //         no_npwp: { required: true  },
    //         nm_tpm: { required: true  },
    //         jum_dp: { required: true  },
    //         j_kelamin: { required: true  },
    //         lbp: { required: true  },
    //         ala_tpm: { required: true  },
    //         tgl_pelaksana: { required: true  },
    //         jum_pes_l: { required: true  },
    //         jum_pes_p: { required: true  },
    //         jum_t: { required: true  },
    //         jum_lan: { required: true  },
    //         nm_dae_irigasi: { required: true  },
    //         j_irigasi: { required: true  },
    //         k_irigasi: { required: true  },
    //         jum_ket_l: { required: true  },
    //         jum_ket_p: { required: true  },
    //         jum_ben_p: { required: true  },
    //         jum_ben_l: { required: true  },
    //         jum_anggota: { required: true  },
    //         jum_tim_per_l: { required: true  },
    //         jum_tim_per_p: { required: true  },
    //         jum_tim_total: { required: true  },
    //         jum_tim_pelak_l: { required: true  },
    //         jum_tim_pelak_p: { required: true  },
    //         jum_tim_pngws_l: { required: true  },
    //         jum_tim_pngws_p: { required: true  },
    //         jum_tim_pngws: { required: true  },
    //         tgl_pelak: { required: true  },
    //         jum_pese_l: { required: true  },
    //         jum_pese_p: { required: true  },
    //         jum_tot_pes: { required: true  },
    //         jum_pese_lan: { required: true  },
    //       }
    //     });


});
</script>