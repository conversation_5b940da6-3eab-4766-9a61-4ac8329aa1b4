<script>
function close_alert() {
    $("#alert_login").css({
        display: "none"
    });
}

function doesConnectionExist() {
    var xhr = new XMLHttpRequest();
    var file = "https://www.kirupa.com/blank.png";
    var randomNum = Math.round(Math.random() * 10000);

    xhr.open('HEAD', file + "?rand=" + randomNum, true);
    xhr.send();

    xhr.addEventListener("readystatechange", processRequest, false);

    function processRequest(e) {
        if (xhr.readyState == 4) {
            if (xhr.status >= 200 && xhr.status < 304) {
                alert("connection exists!");
            } else {
                alert("connection doesn't exist!");
            }
        }
    }
}

// var csrf_token = '';



$( document ).ready(function() {

    // console.log('fsefsfs')
    $('#tahun_p3tgai').selectpicker();

    initLoginSelect('tahun_p3tgai', 0);




})



function test_ajax() {
//     $.post("<?php /* = base_url('login/test'); */ ?>",
//   {
//     "name": "<PERSON>",
//     "city": "Duckburg",
//     "csrf_token": "<?php /* echo $this->security->get_csrf_hash(); */ ?>"
//   },
//   function(data, status){
//     alert("Data: " + data + "\nStatus: " + status);
//   });

$.ajax({
    type : 'post',
    url : '<?php echo base_url('test/code'); ?>',
    data : { "name": "Donald Duck", "csrf_token": "<?php echo $this->security->get_csrf_hash(); ?>"},
    dataType: 'json',
    // cache : false,
    success: function(res){
        // var h = 'Another Test'; //just a test to add to the console.log along with the response.
        // console.log({res, h});
    }
});

}



function login(thang) {

    //  console.log(thang);

     if(thang == "") {
            swal({
                title: "Perhatian!!!",
                text: "Tahun Anggaran Tidak Boleh Kosong",
                icon: "warning",
                showConfirmButton: false,
                timer: 5000,
                type: "warning"
            });

            return;
        }


    // swal({
    //     title: "Sedang Dalam Maintenance",
    //     text: "Mohon Menunggu Sejenak ",
    //     icon: "warning",
    //     showConfirmButton: false,
    //     timer: 5000,
    //     type: "warning"
    // });

    // return;
    //alert(check_connectiofbern());
    //  if(check_connection()=='conn_active'){
    var email = $("#email").val();
    var password = $("#password").val();

    // var csrf_



    //      var captcha = $('[name=g-recaptcha-response]').val()
    $("#pesan").empty()
    //  var data_post = {"captcha":captcha,"email": email, "password": password, "<?php /* echo $this->security->get_csrf_token_name(); */ ?>" : "<?php /* echo $this->security->get_csrf_hash(); */ ?>"};
    var data_post = {
        "email": email,
        "password": password,
        "tahun_p3tgai": thang,
        "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"
    };
    // console.log(data_post);



    jQuery.ajax({
        // contentType: 'application/json',
        type: "post",
        dataType: "json",
        data: data_post,
        // crossDomain: true,
        url: "<?= base_url('login/create_login'); ?>",
        success: function(response) {

            // if(response.csrf_token) {
            //     console.log(response.csrf_token);

            //     return;

            // }

            if (response.status == "sukses") {


                var role = [];
                $.each(response.data, function(index, value) {
                    role.push(value.id_user_group);
                    role.push(value.id_user);
                    //  role.push(value.is_approval);
                });




                if (role[0] == 7) {
                       window.location.href = "<?php echo base_url() . "persiapan/page"; ?>";
                    // if (role[1] == 6735 || role[1] == 4116 || role[1] == 4043) {
                    //     window.location.href = "<?php /* echo base_url() . "persiapan/page"; */ ?>";
                    // } else {
                    //     swal({
                    //         title: "Sedang Dalam Maintenance",
                    //         text: " Mohon Menunggu... ",
                    //         icon: "warning",
                    //         showConfirmButton: false,
                    //         timer: 5000,
                    //         type: "warning"
                    //     });

                    //     return;
                    // }
                } else {
                    window.location.href = "<?php echo base_url() . "persiapan/page"; ?>";
                    // if (role[0] == 1) {
                    //     window.location.href = "<?php /* echo base_url() . "persiapan/page"; */ ?>";
                    // } else {
                    //     // window.location.href = "<?php /* echo base_url() . "persiapan/page"; */ ?>";
                    //     if (role[1] == 432 || role[1] == 343 || role[1] == 357 || role[1] == 358 || role[1] == 1208 || role[1] == 1212){
                    //         // if (role[1] == 464)  {
                    //         window.location.href = "<?php /* echo base_url() . "persiapan/page"; */ ?>";
                    //     } else {
                    //         swal({
                    //             title: "Sedang Dalam Maintenance",
                    //             text: "Mohon Menunggu... ",
                    //             icon: "warning",
                    //             showConfirmButton: false,
                    //             timer: 5000,
                    //             type: "warning"
                    //         });

                    //         return;
                    //     }

                    // }
                }

            } else {
                if (response.status == "Password salah") {
                    var yuhu = parseInt($("#log").val())
                    if (isNaN(yuhu)) {
                        yuhu = 0;

                    }
                    var ind = localStorage.setItem("log", 1);
                    var val = localStorage.getItem("log")
                    var has = parseInt(val) + yuhu;
                    $("#log").val(has)



                    if (has == 3) {
                        var url = "<?php echo base_url(); ?>login/update_data/aset_users/email/" + response
                            .email + "/" + "is_approval/0";
                        $.get(url).done(function(data) {
                            swal({
                                title: "Akun anda Terblokir",
                                text: "Harap hubungi admin ",
                                icon: "warning",
                                showConfirmButton: false,
                                timer: 5000,
                                type: "warning"
                            });
                            $("#log").val('')

                        })
                    }

                }
                swal({
                    // title: "Anda yakin menghapus data ?",
                    text: response.status,
                    icon: "warning",
                    buttons: false,
                    timer: 2000,
                    dangerMode: true,
                })
                // alert(response.status)
                // $("#pesan").html("<i class='fa fa-warning'></i> password atau username salah, login gagal <a class='alert-link' href='javascript:void(0)''></a>!")

                // $("#alert_login").css({display: "block"});
                // setTimeout(close_alert, 3000);
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            alert(xhr.status);
            alert(thrownError);
        }
    });
}

function check_connection() {
    var status = "";
    var url = "<?php echo base_url('/login/check_connection') ?>";
    $.ajax({
        //type: "GET",
        url: url,
        async: false,
        success: function(msg) {
            status = "conn_active";
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            if (textStatus == 'timeout') {
                status = "conn_error";
            }
        }
    });

    return status;
    //alert(status);
}


$(".login100-form-btn").click(function() {
    var thang = $('#tahun_p3tgai').val();
    login(thang);
    return false;
});

$(".reset100-form-btn").click(function() {
    update_password();
    return false;
});

var yuhu = $('#password') || $('#email');
yuhu.keypress(function(e) {
    if (e.which == 13) {
        login();
        return false;
    }
});


// function lupa_password() {
//     $('.form-group').removeClass('has-error'); // clear error class
//     $('.help-block').empty(); // clear error string
//     $('#modal-tambah').modal('show');
//     $('.tbhItem').text('Form bbws');
// }

function reset_password() {
    var email = $("#email_reset").val()
    var dt = tampil_data('v_users', 'email', email);
    var kode = '';
    var nama = '';
    if (dt.length < 1) {
        swal({

            title: "Email anda tidak terdaftar",
            text: "",
            icon: "warning",
            // buttons: false,
            showConfirmButton: true,
            timer: 3000,
            dangerMode: true,
        });
        return;
    } else {
        kode = dt[0].kode;
        name = dt[0].nama;

    }


    url = "<?php echo base_url(); ?>login/send_email/";

    var data_post = {
        "email": email,
        "kode": kode,
        "nama": nama,
        "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"
    };
    // console.log(data_post);
    jQuery.ajax({
        contentType: 'application/x-www-form-urlencoded',
        dataType: "json",
        type: "POST",
        data: data_post,
        url: url,
        success: function(data) {
            if (data == 0) {
                swal({
                    title: "",
                    text: "Cek Email anda, untuk konfirmasi reset password",
                    showConfirmButton: true,
                    // timer:1000,
                    type: "success"
                });

            } else {
                swal({
                    title: "",
                    text: "Gagal Mengirim Email",
                    showConfirmButton: false,
                    timer: 1000,
                    type: "warning"
                });
            }
        }
    });
    //   var params = {"formData": ''};
    //   console.log(params)
    //   $.get(url, params).done(function (data) {
    //     if(data==0){
    //       swal({
    //             title: "",
    //             text: "Cek Email anda, untuk konfirmasi reset password",
    //             showConfirmButton: true,
    //             // timer:1000,
    //             type:"success"
    //           });

    //     }else{
    //       swal({
    //             title: "",
    //             text: "Gagal Mengirim Email",
    //             showConfirmButton: false,
    //             timer:1000,
    //             type:"warning"
    //           });
    //     }
    //   });


}

function update_password() {
    var password_b = $("#password_b").val();
    var k_password_b = $("#k_password_b").val();
    var kode = $("#kode").val();

    // var dt = tampil_data('aset_users','password',kode);
    // console.log(dt)

    if (password_b != k_password_b) {
        swal({

            title: "Gagal",
            text: "Password Tidak Sesuai Konirmasi Password",
            icon: "warning",
            // buttons: false,
            showConfirmButton: true,
            timer: 3000,
            dangerMode: true,
        });
        return;
    }
    url = "<?php echo base_url(); ?>login/update_password/";
    var data_post = {
        "password": password_b,
        "kode": kode,
        "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"
    };
    // console.log(data_post);
    jQuery.ajax({
        contentType: 'application/x-www-form-urlencoded',
        dataType: "json",
        type: "POST",
        data: data_post,
        url: url,
        success: function(data) {
            if (data == 0) {

                swal({
                        title: "Berhasil Merubah Password",
                        text: "Silahkan Login.",
                        icon: "warning",
                        buttons: true,
                        dangerMode: true,
                    })
                    .then((willDelete) => {
                        if (willDelete) {
                            var urls = "<?php echo base_url(); ?>login";
                            window.location.href = urls
                        } else {
                            // swal("Anda Tidak Menjadi Menghapus Data");
                        }
                    });

            } else {
                swal({
                    title: "",
                    text: "Url sudah tidak valid",
                    showConfirmButton: false,
                    timer: 1000,
                    type: "warning"
                });
            }
        }
    });
}

function tampil_data(table, colum, id) {
    var url = ''
    var tadata = ''
    urls = "<?php echo base_url(); ?>login/tampildata/" + table + "/" + colum + "/" + id;
    $.ajax({
        url: urls,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function(data) {
            tadata = data;
        },
        failure: function(errMsg) {
            alert(errMsg);
        }
    });
    return tadata;
}
</script>