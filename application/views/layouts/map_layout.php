<!DOCTYPE html>
<!--[if IE 9]>         <html class="ie9 no-focus" lang="en"> <![endif]-->
<!--[if gt IE 9]><!--> <html class="no-focus" lang="en"> <!--<![endif]-->
    <head>
        <meta charset="utf-8">

        <title>SiTIA <?php echo "~" . $title; ?></title>

        <meta name="description" content="">
        <meta name="author" content="pixelcave">
        <meta name="robots" content="noindex, nofollow">
        <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=0">
        <script type="text/javascript">
            <?php
                require_once(FCPATH."env.php");
                echo 'var WGI_APP_BASE_URL = "'.WGI_APP_BASE_URL.'"; ';
                echo 'var WGI_NODE_API_URL = "'.WGI_NODE_API_URL.'"; ';
            ?>
        </script>


        <link rel="shortcut icon" href="<?php echo base_url(); ?>assets/img/favicons/pudki.png">

        <link rel="apple-touch-icon" sizes="57x57" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-57x57.png">
        <link rel="apple-touch-icon" sizes="60x60" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-60x60.png">
        <link rel="apple-touch-icon" sizes="72x72" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-72x72.png">
        <link rel="apple-touch-icon" sizes="76x76" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-76x76.png">
        <link rel="apple-touch-icon" sizes="114x114" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-114x114.png">
        <link rel="apple-touch-icon" sizes="120x120" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-120x120.png">
        <link rel="apple-touch-icon" sizes="144x144" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-144x144.png">
        <link rel="apple-touch-icon" sizes="152x152" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-152x152.png">
        <link rel="apple-touch-icon" sizes="180x180" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-180x180.png">
        <!-- END Icons -->

        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">

        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/css/bootstrap.min.css">
        <link rel="stylesheet" id="css-main" href="<?php echo base_url(); ?>assets/css/oneui.css">

        <link rel="stylesheet" id="css-main" href="<?php echo base_url(); ?>assets/css/bootselect/bootstrap-select.min.css">

<!--        <link rel="stylesheet" href="<?php //echo base_url();      ?>assets/datatable/datatables.min.css">
        <link rel="stylesheet" href="<?php //echo base_url();      ?>assets/datatable/dataTables.bootstrap.min.css">-->
        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/js/plugins/datatables/jquery.dataTables.min.css">
        <!--<link rel="stylesheet" href="<?php //echo base_url();      ?>assets/datatable/dataTables.bootstrap.min.css">-->
        <link rel="stylesheet" href="<?php echo base_url();?>assets/local/fixedColumns.dataTables.min.css"/>

        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/js/plugins/bootstrap-datepicker/bootstrap-datepicker3.min.css">
        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/js/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.css">

        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/css/L.Control.MousePosition.css" />
        <!--link rel="stylesheet" href="<?php echo base_url(); ?>assets/css/L.Control.Locate.min.css" /-->

        <link rel="stylesheet" href="<?php echo base_url();?>assets/local/esri-leaflet-geocoder.css"
              integrity="sha512-IM3Hs+feyi40yZhDH6kV8vQMg4Fh20s9OzInIIAc4nx7aMYMfo+IenRUekoYsHZqGkREUgx0VvlEsgm7nCDW9g=="
              crossorigin="">

        <!-- <script type="text/javascript" src="<--?php echo base_url(); ?>assets/peta/libs/leaflet-wgi/basemap.js"></script> -->

        <STYLE TYPE="text/css">
            @import url(css/font-awesome.css);
        </STYLE>

        <link rel="stylesheet" id="css-main" href="<?php echo base_url(); ?>assets/peta/css/leaflet/leaflet.css">
        <link rel="stylesheet" id="css-main" href="<?php echo base_url(); ?>assets/peta/css/jquery-jspanel/jquery.jspanel.min.css">
        <link rel="stylesheet" id="css-main" href="<?php echo base_url(); ?>assets/peta/css/jstree/themes/default/style.css">
        <link rel="stylesheet" id="css-main" href="<?php echo base_url(); ?>assets/peta/css/wgi/wgi-table.css">

        <style>
            /*
            #map_container {
                width: 100%;
                height: 100%;
                min-height: 100%;
                display: block;
            }

            html, body {
                height: 100%;
            }

            .fill {
                min-height: 100%;
                height: 100%;
                padding-top:10px;
                padding-left: 10px;
                box-sizing:border-box;
            }

            body {

            }

            */


            .map_container {
                position: fixed;
                top:0 ;
                left: 0;
                height: 100%;
                width: 100%;
            }
            .map {
                margin-top: 0px;
                /*margin-left: 230px;*/
                /*margin-right: -380px;*/
                /*margin-bottom: 10px;*/
                position: relative;
                height: 100%;
                width: 100%;
                outline: none;
                touch-action: none;
                background-color: #445a71; position: relative;
            }
            .nav-main {

                margin: 0 -6px;

            }

            .block {
                margin: 0;
            }

            .block-content {
                margin: 0 auto;
                padding: 0px 5px 1px;
            }


            body {
                min-width: 400px;
                overflow: hidden;
            }

            .navbar {
                position: relative;
                min-height: 45px;
                height: 45px;
                margin-bottom: 5px;
                border: 1px solid transparent;
                opacity: 0.93;
            }
            .nav-main a.nav-submenu {
                position: relative;
                padding-right: 23px;
            }
            .container-fluid {
                padding-right: 2px;
                padding-left: 2px;
                margin-right: 0px;
                margin-left: 0px;
            }

            .navbar-nav>li>a {
                padding-top: 12px;
                padding-bottom: 12px;
                line-height: 20px;
            }

            .navbar-form {
                padding: 5px 5px;
                margin-top: 0px;
            }

            .navbar-collapse {
                padding-right: 0px;
                padding-left: 0px;
            }



            /*            .navbar-collapse.collapse.d-none,
                        #hide-navbar.d-none,
                        #show-navbar.d-none {
                          display: none !important;
                        }

                        .navbar-expand #show-navbar.navbar-toggler {
                          display: block;
                        }*/

        </style>

        <!--<script src="<?php //echo base_url();      ?>assets/js/core/jquery.min.js"></script>-->
        <!-- <script type="text/javascript" charset="utf8" src="<?php //echo base_url(); ?>assets/js/wayjs/way.js"></script> -->


        <script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAkhYjPhjmtrymfYQJiA1NbZjYQwFOfe9c&libraries=places"></script>

        <script type="text/javascript" src="<?php echo base_url(); ?>assets/peta/libs/jquery/jquery-3.2.1.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url(); ?>assets/peta/libs/jquery-jspanel/jquery.jspanel.min.js"></script>
        <script type="text/javascript" src="<?php echo base_url(); ?>assets/peta/libs/jstree/dist/jstree.min.js"></script>

        <script type="text/javascript" src="<?php echo base_url(); ?>assets/peta/libs/leaflet/leaflet.js"></script>
        <script type="text/javascript" src="<?php echo base_url(); ?>assets/peta/libs/esri-leaflet/dist/esri-leaflet.js"></script>

        <script src="<?php echo base_url(); ?>assets/js/L.Control.MousePosition.js"></script>
        <!--script src="<?php echo base_url(); ?>assets/js/L.Control.Locate.min.js"></script-->

        <script src="<?php echo base_url(); ?>assets/js/leaflet.latlng-graticule.js"></script>


        <script src="<?php echo base_url();?>assets/local/esri-leaflet-geocoder.js"
                integrity="sha512-HrFUyCEtIpxZloTgEKKMq4RFYhxjJkCiF5sDxuAokklOeZ68U2NPfh4MFtyIVWlsKtVbK5GD2/JzFyAfvT5ejA=="
                crossorigin="">
        </script>





        <script type="text/javascript" src="<?php echo base_url(); ?>assets/peta/libs/leaflet-customcontrol/Leaflet.Control.Custom.js"></script>
        <script type="text/javascript" src="<?php echo base_url(); ?>assets/peta/libs/leaflet-googlemutant/Leaflet.GoogleMutant.js"></script>
        <!--script type="text/javascript" src="<?php echo base_url(); ?>assets/peta/libs/leaflet-betterwms/leaflet-betterwms_wgi.js"></script-->


        <!--script type="text/javascript" src="<?php /* echo base_url(); */ ?>assets/peta/libs/leaflet-wgi/wgi-panel-tools.js"></script-->


        <!--script type="text/javascript" charset="utf8" src="<?php ///echo base_url(); ?>assets/webgis/js/lookuptool.js"></script-->

        <!--script src="<?php //echo base_url(); ?>assets/js/wgi_ajaxcache.js"-->











    </head>
    <body>
        <!-- Page Container -->
        <!--
            Available Classes:

            'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

            'sidebar-l'                  Left Sidebar and right Side Overlay
            'sidebar-r'                  Right Sidebar and left Side Overlay
            'sidebar-mini'               Mini hoverable Sidebar (> 991px)
            'sidebar-o'                  Visible Sidebar by default (> 991px)
            'sidebar-o-xs'               Visible Sidebar by default (< 992px)

            'side-overlay-hover'         Hoverable Side Overlay (> 991px)
            'side-overlay-o'             Visible Side Overlay by default (> 991px)

            'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

            'header-navbar-fixed'        Enables fixed header
        -->
        <div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">


            <!-- Sidebar -->
            <nav id="sidebar">
                <!-- Sidebar Scroll Container -->
                <div id="sidebar-scroll">
                    <!-- Sidebar Content -->
                    <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
                    <div class="sidebar-content">
                        <!-- Side Header -->
                        <div class="side-header side-content bg-white-op">
<!--                            <img  src="<?php echo base_url(); ?>assets/img/avatars/123.png" alt="PU Icon" style="width:65px; height:60px;display: none;" id="log"><span class=" sidebar-mini-hide" id="y"><img class="img-responsive" src="<?php echo base_url(); ?>assets/img/avatars/logo3.png" alt="PU Icon" id="zz"></span>-->
                            <img  src="<?php echo base_url(); ?>assets/img/avatars/1234.jpg" alt="PU Icon" width="60px" height="59px"><span class='sidebar-mini-hide'><img  src="<?php echo base_url(); ?>assets/img/avatars/5678.jpg" alt="PU Icon" width="170px" height="59px"></span>
                        </div>
                        <!-- END Side Header -->

                        <!-- Side Content -->
                        <div class="side-content side-content-full">
                            <ul class="nav-main" style="padding-left:10px;">
                              <?php

                              $this->load->library('wgisitia');
                              $url=$this->uri->uri_string();

                              $ci = & get_instance();
                              $ci->load->database();
                              $userss=$this->db->get_where('users',array('thang'=>$this->session->konfig_tahun_ang,'id_user'=>$this->session->users['id_user']))->row();

                              $thang=$this->session->konfig_tahun_ang;
                              $group = $this->session->users['id_user_group_real'];
                              $sgroup = $this->session->users['id_user_group'];
                              $this->db->order_by('urutan', 'ASC');
                              $this->db->where("id_user_group", $group);
                              $this->db->where("thang",$this->session->konfig_tahun_ang);
                              $this->db->where("parent", NULL)->or_where("parent", "");
                              $armenu = $ci->db->get('v_group_module')->result_array();
                              foreach ($armenu as $menu) {
                                  $this->db->order_by('urutan', 'ASC');
                                  $submenu = $this->db->get_where('v_group_module', array('id_user_group' => $group, 'parent' => $menu['kode_module'],'thang'=>$this->session->konfig_tahun_ang))->result_array();
                                    if($url==$menu['url']){
                                      $col="#6f88886e";
                                    }else{
                                      $col="";
                                    }
                                  if (count($submenu) == 0) {
                                      echo "<li style='background:$col'><a onclick='menu()' href='" . base_url() . $menu['url'] . "'>" . $menu['icon'] . "<span class='sidebar-mini-hide'>" . $menu['nama_module'] . "</span></a></li>";
                                  } else {
                                      $parents=$this->db->get_where('v_group_module',array('id_user_group' => $group,'url'=>$url,'thang'=>$this->session->konfig_tahun_ang));
                                      //echo $parents->parent;
                                      if($parents->num_rows() > 0){
                                          if($parents->row()->parent != NULL && $parents->row()->parent=='724' or $parents->row()->parent=='900'  or $parents->row()->parent=='923'){

                                              $parents1=$this->db->get_where('v_group_module',array('kode_module'=>$parents->row()->parent,'id_user_group' => $group,'thang'=>$this->session->konfig_tahun_ang))->row();

                                            }else{
                                              $parents1=$this->db->get_where('v_group_module',array('id_user_group' => $group,'url'=>$url,'thang'=>$this->session->konfig_tahun_ang))->row();
                                            }
                                            if($parents1->parent==$menu['kode_module']){
                                              $op='open';
                                              $col="#6f88886e";
                                            }else{
                                              $op='';
                                              $col="";
                                            }
                                        }else{
                                          $op='';
                                          $col="";
                                        }
                                        $onclick='';
                                        if($menu['kode_module']=='939'){
                                          $onclick="onclick='revisi_d()'";

                                        }
                                      echo "<li class='".$op."' style='background:$col' ><a $onclick  class='nav-submenu' data-toggle='nav-submenu' href='" . base_url() . $menu['url'] . "'>" . $menu['icon'] . "<span class='sidebar-mini-hide'>" . $menu['nama_module'] . "</span></a>";
                                  }
                                  echo "<ul>";
                                  foreach ($submenu as $x){
                                      $this->db->order_by('urutan', 'ASC');
                                    $subsummenu=$this->db->get_where('v_group_module', array('id_user_group' => $group, 'parent' => $x['kode_module'],'thang'=>$this->session->konfig_tahun_ang))->result_array();
                                    if($url==$x['url']){
                                      $col="#368a8a6e";
                                    }else{
                                      $col="#24242400";
                                    }
                                    if (count($subsummenu) == 0) {
                                      if($x['kode_module']=='793'){
                                            if($this->session->users['kode_satker'] == $this->session->users['kdinduk'] ){
                                          echo "<li style='background:$col;color:white;'><a onclick='menu()'href='" . base_url() . $x['url'] . "'>".$x['nama_module'] . "</a></li>";
                                          }
                                        }
                                        else {
                                            echo "<li style='background:$col'><a onclick='menu()'href='" . base_url() . $x['url'] . "'>" . $x['nama_module'] . "</a></li>";
                                        }
                                      }
                                      else
                                      {
                                        $parentss=$this->db->get_where('v_group_module',array('id_user_group' => $group,'url '=>$url,'thang'=>$this->session->konfig_tahun_ang));
                                        if($parentss->num_rows() > 0){
                                        if($parentss->row()->parent==$x['kode_module']){

                                          $op='open';
                                          $col="#90bdbd6e";
                                        }else{
                                          $op='';
                                          $col="";
                                          //echo $x['kode_module'];
                                        }
                                      }else{
                                        $op='';
                                        $col="";
                                      }
                                        echo "<li class='".$op."' style='background:$col'><a class='nav-submenu' data-toggle='nav-submenu' href='" . base_url() . $x['url'] . "'>" . $x['nama_module'] . "</a>";
                                        echo "<ul>";
                                        foreach ($subsummenu as $xx){
                                          if($url==$xx['url']){

                                            $cols="#6f88886e";
                                          }else{
                                            $cols="#24242400";
                                          }
                                        echo "<li style='background:$cols'><a onclick='menu()'href=' " . base_url() . $xx['url'] . "'>" . $xx['nama_module'] . "</a></li>";
                                        }
                                        echo "</ul>";
                                        echo "</li>";
                                      }

                                  }
                                  echo "</ul>";
                                  echo "</li>";
                              }

                              ?>
                            </ul>
                        </div>
                        <!-- END Side Content -->
                    </div>
                    <!-- Sidebar Content -->
                </div>
                <!-- END Sidebar Scroll Container -->
            </nav>
            <!-- END Sidebar -->

            <!-- Header -->
            <header id="header-navbar" class="content-mini content-mini-full">
                <!-- Header Navigation Right -->
                    <?php echo "<b>TAHAPAN : ".$this->session->konfig_tahapan." TAHUN ANGGARAN ".$this->session->konfig_tahun_ang."</b>";?>
                <ul class="nav-header pull-right">
                    <li>
                        <div class="btn-group">
                            <button class="btn btn-default btn-image dropdown-toggle" data-toggle="dropdown" type="button">
                                <img src="<?php echo base_url(); ?>assets/img/avatars/avatar10.jpg" alt="Avatar">
                                <?php
                                //$nameorg = ($this->session->users["nmsatker"] == "" || $this->session->users["nmsatker"] == NULL) ? $this->session->users["roledesc"] : $this->session->users["nmsatker"];

                               // $nmgrup = ($this->session->users["nmgrup"] == "" || $this->session->users["nmgrup"] == NULL) ? $this->session->users["roledesc"] : $this->session->users["nmgrup"];

                               echo $this->session->users["username"] . " (" . $this->session->users["roledesc"] .")";
                                ?>
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right">
                                <!--li class="dropdown-header">Profile</li-->
                                <li>
                                    <a tabindex="-1" href="<?php echo base_url("/profile"); ?>">
                                        <i class="si si-user pull-right"></i>
                                        Profil
                                    </a>
                                </li>
                                <!--li>
                                    <a tabindex="-1" href="javascript:void(0)">
                                        <i class="si si-settings pull-right"></i>Pengaturan
                                    </a>
                                </li-->
                                <li class="divider"></li>
                                <!--li class="dropdown-header">Actions</li-->
                                <!--li>
                                    <a tabindex="-1" href="base_pages_lock.html">
                                        <i class="si si-lock pull-right"></i>Kunci Akun
                                    </a>
                                </li-->
                                <li>
                                    <a tabindex="-1" href="<?php echo base_url('/login/logout?stat=logout'); ?>">
                                        <i class="si si-logout pull-right"></i>Log out
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>

                </ul>
                <!-- END Header Navigation Right -->

                <!-- Header Navigation Left -->
                <ul class="nav-header pull-left">
                    <li class="hidden-md hidden-lg">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                            <i class="fa fa-navicon"></i>
                        </button>
                    </li>
                    <li class="hidden-xs hidden-sm">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-default btn-window-option" data-toggle="layout" data-action="sidebar_mini_toggle" type="button" id="co">
                            <i class="fa fa-ellipsis-v"></i>
                        </button>
                    </li>
                    <li>
                        <!-- Opens the Apps modal found at the bottom of the page, before including JS code
                        <button class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
                            <i class="si si-grid"></i>
                        </button>
                        -->
                    </li>
                    <li class="visible-xs">
                        <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                        <!--
                        <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
                            <i class="fa fa-search"></i>
                        </button>
                        -->
                    </li>
                    <!--
                    <li class="js-header-search header-search">
                        <form class="form-horizontal" action="base_pages_search.html" method="post">
                            <div class="form-material form-material-primary input-group remove-margin-t remove-margin-b">
                                <input class="form-control" type="text" id="base-material-text" name="base-material-text" placeholder="Search..">
                                <span class="input-group-addon"><i class="si si-magnifier"></i></span>
                            </div>
                        </form>
                    </li>
                    -->
                </ul>
                <!-- END Header Navigation Left -->
            </header>
            <!-- END Header -->

            <!-- Main Container -->
            <main id="main-container">
              <div id="bread">
              <div class="content bg-gray-lighter">

                <div class="row items-push">
              <div class="col-sm-12 text-left hidden-xs">
                <ol class="breadcrumb push-10-t">
                  <?php
                  $hide='';
                  if($group=='60' or $group=='3' or $group=='7'){
                    if($url=='dashboard3'){ $hide='none';}
                    echo "<li style='display:".$hide."'><a class='link-effect' href='dashboard3'>Dashboard</a></li>";
                  }elseif ($group=='47') {
                    if($url=='dashboard'){ $hide='none';}
                    echo "<li style='display:".$hide."'><a class='link-effect' href='dashboard'>Dashboard</a></li>";
                  }else {
                    if($sgroup=='92' or $group=='1'){
                      if($url=='dashboard_tahapan'){ $hide='none';}
                        echo "<li style='display:".$hide."'><a class='link-effect' href='dashboard_tahapan'>Dashboard</a></li>";
                    }
                  }
                  if($url !='profile'){
                    $bread=$this->db->get_where('breadcumb_menu',array('url2'=>$url,'id_user_group'=>$group,'thang'=>$thang))->row();

                      if($bread->bread0 != NULL){
                        echo "<li><a class='link-effect' href='".$bread->url0."'>".$bread->bread0."</a></li>";
                      }
                      if($bread->bread1 != NULL){
                        echo "<li><a class='link-effect' href='".$bread->url1."'>".$bread->bread1."</a></li>";
                      }
                      if($bread->bread2 != NULL){
                        echo "<li><a class='link-effect' href='".$bread->url2."'>".$bread->bread2."</a></li>";
                      }
                    }

                   ?>
                </ol>
              </div>
              </div>
              </div>
            </div>
            <div class="col-sm-12 col-md-12" style="padding:0px;">
                <!-- Page Header -->
                <!--
                <div class="content bg-gray-lighter">
                    <div class="row items-push">
                        <div class="col-sm-7">
                            <h1 class="page-heading">
                                Blank <small>That feeling of delight when you start your awesome new project!</small>
                            </h1>
                        </div>
                        <div class="col-sm-5 text-right hidden-xs">
                            <ol class="breadcrumb push-10-t">
                                <li>Generic</li>
                                <li><a class="link-effect" href="">Blank</a></li>
                            </ol>
                        </div>
                    </div>
                </div>
                -->
                <!-- END Page Header -->

                <!-- Page Content -->
                <!--div class="content"-->
<?php echo $contents; ?>
                <!--/div-->
                <!-- END Page Content -->
              </div>
            </main>
            <!-- END Main Container -->

            <!-- Footer -->
            <footer id="page-footer" class="content-mini content-mini-full font-s12 bg-gray-lighter clearfix">
                <!--
                <div class="pull-right">
                    Crafted with <i class="fa fa-heart text-city"></i> by <a class="font-w600" href="https://goo.gl/vNS3I" target="_blank">pixelcave</a>
                </div>
                -->
                <div class="pull-left">
                    <a class="font-w600" href="#" target="_blank">P3TGAI</a> &copy; <span class="js-year-copy">2024</span>
                </div>
            </footer>
            <!-- END Footer -->
        </div>
        <!-- END Page Container -->

        <!-- Apps Modal -->
        <!-- Opens from the button in the header -->
        <div class="modal fade" id="apps-modal" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-sm modal-dialog modal-dialog-top">
                <div class="modal-content">
                    <!-- Apps Block -->
                    <div class="block block-themed block-transparent">
                        <div class="block-header bg-primary-dark">
                            <ul class="block-options">
                                <li>
                                    <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                                </li>
                            </ul>
                            <h3 class="block-title">Apps</h3>
                        </div>
                        <div class="block-content">
                            <div class="row text-center">
                                <div class="col-xs-6">
                                    <a class="block block-rounded" href="base_pages_dashboard.html">
                                        <div class="block-content text-white bg-default">
                                            <i class="si si-speedometer fa-2x"></i>
                                            <div class="font-w600 push-15-t push-15">Backend</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-xs-6">
                                    <a class="block block-rounded" href="bd_dashboard.html">
                                        <div class="block-content text-white bg-modern">
                                            <i class="si si-rocket fa-2x"></i>
                                            <div class="font-w600 push-15-t push-15">Boxed</div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END Apps Block -->
                </div>
            </div>
        </div>
        <!-- END Apps Modal -->

        <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
        <script src="<?php echo base_url(); ?>assets/js/core/bootstrap.min.js"></script>
        <script src="<?php echo base_url(); ?>assets/js/core/jquery.slimscroll.min.js"></script>
        <script src="<?php echo base_url(); ?>assets/js/core/jquery.scrollLock.min.js"></script>
        <script src="<?php echo base_url(); ?>assets/js/core/jquery.appear.min.js"></script>
        <script src="<?php echo base_url(); ?>assets/js/core/jquery.countTo.min.js"></script>
        <script src="<?php echo base_url(); ?>assets/js/core/jquery.placeholder.min.js"></script>
        <script src="<?php echo base_url(); ?>assets/js/core/js.cookie.min.js"></script>
        <!--<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.8.11/jquery-ui.min.js"></script>-->
        <script src="<?php echo base_url(); ?>assets/js/plugins/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
        <!--<script src=" https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.8.0/css/bootstrap-datepicker.css"></script>-->

        <script type="text/javascript" src="<?php echo base_url(); ?>assets/js/plugins/jquery-validation/jquery.validate.min.js"></script>

        <script src="<?php echo base_url(); ?>assets/js/app.js"></script>
        <!-- <script src="https://demo.pixelcave.com/oneui/assets/js/plugins/jquery-ui/jquery-ui.min.js"></script> -->

        <script>
        $(document).ready(function () {

         $(".modal"). attr('data-backdrop','static')
         if($(".breadcrumb").length==2){
           $(".breadcrumb")[1].remove()
           $(".bg-gray-lighter")[1].remove()
         }

        })
            jQuery(function () {
                App.initHelpers('draggable-items');
            });</script>


        <!--<script type="text/javascript" charset="utf8" src="<?php //echo base_url();    ?>assets/datatable/datatables.min.js"></script
        <script type="text/javascript" charset="utf8" src="<?php //echo base_url();    ?>assets/datatable/dataTables.bootstrap.min.js"></script>-->
        <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/js/plugins/datatables/jquery.dataTables.min.js"></script>
        <!--<script type="text/javascript" charset="utf8" src="<?php //echo base_url();    ?>assets/datatable/dataTables.bootstrap.min.js"></script>-->
        <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/js/pages/base_tables_datatables.js"></script>

        <script type="text/javascript" charset="utf8" src="<?php echo base_url();?>assets/local/dataTables.fixedColumns.min.js"></script>
        <script src='<?php echo base_url();?>assets/local/turf.min.js'></script>

        <!--<script type="text/javascript" charset="utf8" src="<?php //echo base_url();        ?>assets/webgis/js/lookuptool.js"></script>-->
        <!--<script type="text/javascript" charset="utf8" src="<?php //echo base_url();        ?>assets/sticky_table/js/jquery.stickytable.js">-->
    </script>
    <!--smart wizzard-->
    <!--
    <script src="<?php //echo base_url();        ?>assets/smartwizard/js/jquery.smartWizard.js"></script>
    -->
    <!--dropdown with search-->
    <script src="<?php echo base_url(); ?>assets/js/bootselect/bootstrap-select.min.js"></script>
    <!-- The jQuery UI widget factory, can be omitted if jQuery UI is already included -->




    <!--javascript module-->
<?php
if (isset($jv_script)) {
    echo $jv_script;
}
?>

</body>
</html>
