<!DOCTYPE html>
<!--[if IE 9]>         <html class="ie9 no-focus" lang="en"> <![endif]-->
<!--[if gt IE 9]><!--> <html class="no-focus" lang="en"> <!--<![endif]-->
    <head>
        <meta charset="utf-8">

        <title>Database Aset Konsesi Jasa BUJT - <?php echo "~".$title; ?></title>

        <meta name="description" content="">
        <meta name="author" content="pixelcave">
        <meta name="robots" content="noindex, nofollow">
        <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=0">

        <script type="text/javascript">
            <?php
                require_once(FCPATH."env.php");
                echo 'var WGI_APP_BASE_URL = "'.WGI_APP_BASE_URL.'"; ';
                echo 'var WGI_NODE_API_URL = "'.WGI_NODE_API_URL.'"; ';
            ?>
        </script>

        <!-- Icons -->
        <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->
        <!--link rel="shortcut icon" href="<?php echo base_url(); ?>assets/img/avatars/1234.jpg"-->
        <!--
        <link rel="icon" type="image/png" href="<?php //echo base_url();     ?>assets/img/favicons/favicon-16x16.png" sizes="16x16">
        <link rel="icon" type="image/png" href="<?php //echo base_url();     ?>assets/img/favicons/favicon-32x32.png" sizes="32x32">
        <link rel="icon" type="image/png" href="<?php // echo base_url();     ?>assets/img/favicons/favicon-96x96.png" sizes="96x96">
        <link rel="icon" type="image/png" href="<?php //echo base_url();     ?>assets/img/favicons/favicon-160x160.png" sizes="160x160">
        <link rel="icon" type="image/png" href="<?php //echo base_url();     ?>assets/img/favicons/favicon-192x192.png" sizes="192x192">
        -->
        <link rel="apple-touch-icon" sizes="57x57" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-57x57.png">
        <link rel="apple-touch-icon" sizes="60x60" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-60x60.png">
        <link rel="apple-touch-icon" sizes="72x72" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-72x72.png">
        <link rel="apple-touch-icon" sizes="76x76" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-76x76.png">
        <link rel="apple-touch-icon" sizes="114x114" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-114x114.png">
        <link rel="apple-touch-icon" sizes="120x120" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-120x120.png">
        <link rel="apple-touch-icon" sizes="144x144" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-144x144.png">
        <link rel="apple-touch-icon" sizes="152x152" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-152x152.png">
        <link rel="apple-touch-icon" sizes="180x180" href="<?php echo base_url(); ?>assets/img/favicons/apple-touch-icon-180x180.png">
        <!-- END Icons -->


        <!-- Stylesheets -->
        <!-- Web fonts -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">

        <!-- Bootstrap and OneUI CSS framework -->
        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/css/bootstrap.min.css">
        <link rel="stylesheet" id="css-main" href="<?php echo base_url(); ?>assets/css/oneui.css">
        <!--<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.19/css/jquery.dataTables.css">-->
        <!--link rel="stylesheet" id="css-main" href="<?php //echo base_url();     ?>assets/datatable/datatables.css"-->
        <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
        <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
        <!-- END Stylesheets -->
        <!--smart wizzard
        <link rel="stylesheet" id="css-main" href="<?php //echo base_url();     ?>assets/smartwizard/css/smart_wizard_theme_arrows.css">
        -->

        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/js/plugins/bootstrap-tagsinput/bootstrap-tagsinput.css">
        <!--dropdown with search-->
        <link rel="stylesheet" id="css-main" href="<?php echo base_url(); ?>assets/css/bootselect/bootstrap-select.css">

        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/js/plugins/datatables/jquery.dataTables.min.css">
        <!--sticky table-->
        <link rel="stylesheet" href="<?php echo base_url();?>assets/local/fixedColumns.dataTables.min.css"/>

        <!--link rel="stylesheet" id="css-main" href=" https://cdn.datatables.net/1.10.19/css/dataTables.bootstrap.min.css"-->

        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/js/plugins/bootstrap-datepicker/bootstrap-datepicker3.min.css">
        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/js/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.css">
        <!--webgis plugin-->
        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/webgis/css/webgis.css">

        <!--
         <link rel="stylesheet" id="css-main" href="<?php // echo base_url();     ?>assets/css/oneui.css">
         <link rel="stylesheet" id="css-main" href="<?php // echo base_url();     ?>assets/css/oneui.css">
        -->
        <link rel="stylesheet" href="<?php echo base_url();?>assets/local/style.min.css" />

        <style>

            .navbar {
                position: relative;
                min-height: 45px;
                height: 45px;
                margin-bottom: 5px;
                border: 1px solid transparent;
                opacity: 0.93;
            }
            .nav-main a.nav-submenu {
                position: relative;
                padding-right: 23px;
            }
            .nav-main {

                margin: 0 -6px;

            }
        </style>

    </head>
    <body>
        <!-- Page Container -->
        <!--
            Available Classes:

            'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

            'sidebar-l'                  Left Sidebar and right Side Overlay
            'sidebar-r'                  Right Sidebar and left Side Overlay
            'sidebar-mini'               Mini hoverable Sidebar (> 991px)
            'sidebar-o'                  Visible Sidebar by default (> 991px)
            'sidebar-o-xs'               Visible Sidebar by default (< 992px)

            'side-overlay-hover'         Hoverable Side Overlay (> 991px)
            'side-overlay-o'             Visible Side Overlay by default (> 991px)

            'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

            'header-navbar-fixed'        Enables fixed header
        -->
        <div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">


            <!-- Sidebar -->
            <nav id="sidebar">
                <!-- Sidebar Scroll Container -->
                <div id="sidebar-scroll">
                    <!-- Sidebar Content -->
                    <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
                    <div class="sidebar-content">
                        <!-- Side Header -->
                        <div class="side-header side-content bg-white-op">
<!--                            <img  src="<?php echo base_url(); ?>assets/img/avatars/123.png" alt="PU Icon" style="width:65px; height:60px;display: none;" id="log"><span class=" sidebar-mini-hide" id="y"><img class="img-responsive" src="<?php echo base_url(); ?>assets/img/avatars/logo3.png" alt="PU Icon" id="zz"></span>-->
                            <img  src="<?php echo base_url(); ?>assets/img/avatars/1234.jpg" alt="PU Icon" width="60px" height="59px"><span class='sidebar-mini-hide'><img  src="<?php echo base_url(); ?>assets/img/avatars/5678.jpg" alt="PU Icon" width="170px" height="59px"></span>
                        </div>
                        <!-- END Side Header -->

                        <!-- Side Content -->
                        <div class="side-content side-content-full">
                            <ul class="nav-main" style="padding-left:10px;">

                              <?php

                              $this->load->library('wgisitia');
                              $url=$this->uri->uri_string();

                              $ci = & get_instance();
                              $ci->load->database();
                              $group = $this->session->users['id_user_group_real'];
                              $sgroup = $this->session->users['id_user_group'];
                              $thang=$this->session->konfig_tahun_ang;
                              $this->db->order_by('urutan', 'ASC');
                              $this->db->where("id_user_group", $group);
                              $this->db->where("tahun",$this->session->konfig_tahun_ang);
                              $this->db->where("parent", NULL)->or_where("parent", "");
                              $armenu = $ci->db->get('v_group_module')->result_array();
                              foreach ($armenu as $menu) {
                                  $this->db->order_by('urutan', 'ASC');
                                  $submenu = $this->db->get_where('v_group_module', array('id_user_group' => $group, 'parent' => $menu['kode_module'],'tahun'=>$this->session->konfig_tahun_ang))->result_array();
                                    if($url==$menu['url']){
                                      $col="#6f88886e";
                                    }else{
                                      $col="";
                                    }
                                  if (count($submenu) == 0) {
                                      echo "<li style='background:$col'><a  href='" . base_url() . $menu['url'] . "'>" . $menu['icon'] . "<span class='sidebar-mini-hide'>" . $menu['nama_module'] . "</span></a></li>";
                                  } else {
                                      $parents=$this->db->get_where('v_group_module',array('id_user_group' => $group,'url'=>$url,'tahun'=>$this->session->konfig_tahun_ang));
                                      //echo $parents->parent;
                                      if($parents->num_rows() > 0){
                                        //   if($parents->row()->parent != NULL && $parents->row()->parent=='724' or $parents->row()->parent=='900'  or $parents->row()->parent=='923'){

                                        //       $parents1=$this->db->get_where('v_group_module',array('kode_module'=>$parents->row()->parent,'id_user_group' => $group,'tahun'=>$this->session->konfig_tahun_ang))->row();

                                        //     }else{
                                              $parents1=$this->db->get_where('v_group_module',array('id_user_group' => $group,'url'=>$url,'tahun'=>$this->session->konfig_tahun_ang))->row();
                                            // }
                                            if($parents1->parent==$menu['kode_module']){
                                              $op='open';
                                              $col="#6f88886e";
                                            }else{
                                              $op='';
                                              $col="";
                                            }
                                        }else{
                                          $op='';
                                          $col="";
                                        }
                                      echo "<li class='".$op."' style='background:$col'><a  class='nav-submenu' data-toggle='nav-submenu' href='" . base_url() . $menu['url'] . "'>" . $menu['icon'] . "<span class='sidebar-mini-hide'>" . $menu['nama_module'] . "</span></a>";
                                  }
                                  echo "<ul>";
                                  foreach ($submenu as $x){
                                      $this->db->order_by('urutan', 'ASC');
                                    $subsummenu=$this->db->get_where('v_group_module', array('id_user_group' => $group, 'parent' => $x['kode_module'],'tahun'=>$this->session->konfig_tahun_ang))->result_array();
                                    if($url==$x['url']){
                                      $col="#368a8a6e";
                                    }else{
                                      $col="#24242400";
                                    }
                                    // if (count($subsummenu) == 0) {
                                    //   if($x['kode_module']=='793'){
                                    //         if($this->session->users['kode_satker'] == $this->session->users['kdinduk'] ){
                                    //       echo "<li style='background:$col;color:white;'><a href='" . base_url() . $x['url'] . "'>" . $x['nama_module'] . "</a></li>";
                                    //       }
                                    //     }
                                    //     else {
                                    //         echo "<li style='background:$col'><a href='" . base_url() . $x['url'] . "'>" . $x['nama_module'] . "</a></li>";
                                    //     }
                                    //   }
                                    //   else
                                    //   {
                                    //     $parentss=$this->db->get_where('v_group_module',array('id_user_group' => $group,'url '=>$url,'tahun'=>$this->session->konfig_tahun_ang));
                                    //     if($parentss->num_rows() > 0){
                                    //     if($parentss->row()->parent==$x['kode_module']){

                                    //       $op='open';
                                    //       $col="#90bdbd6e";
                                    //     }else{
                                    //       $op='';
                                    //       $col="";
                                    //       //echo $x['kode_module'];
                                    //     }
                                    //   }else{
                                    //     $op='';
                                    //     $col="";
                                    //   }
                                    //     echo "<li class='".$op."' style='background:$col'><a class='nav-submenu' data-toggle='nav-submenu' href='" . base_url() . $x['url'] . "'>" . $x['nama_module'] . "</a>";
                                    //     echo "<ul>";
                                    //     foreach ($subsummenu as $xx){
                                    //       if($url==$xx['url']){

                                    //         $cols="#6f88886e";
                                    //       }else{
                                    //         $cols="#24242400";
                                    //       }
                                    //     echo "<li style='background:$cols'><a href=' " . base_url() . $xx['url'] . "'>" . $xx['nama_module'] . "</a></li>";
                                    //     }
                                    //     echo "</ul>";
                                    //     echo "</li>";
                                    //   }

                                  }
                                  echo "</ul>";
                                  echo "</li>";
                              }

                              ?>
                            </ul>
                        </div>
                        <!-- END Side Content -->
                    </div>
                    <!-- Sidebar Content -->
                </div>
                <!-- END Sidebar Scroll Container -->
            </nav>
            <!-- END Sidebar -->

            <!-- Header -->
            <header id="header-navbar" class="content-mini content-mini-full">
                <!-- Header Navigation Right -->
                <?php //echo "<b>TAHAPAN : ".$this->session->konfig_tahapan." TAHUN ANGGARAN ".$this->session->konfig_tahun_ang."</b>";?>
                <ul class="nav-header pull-right">
                    <li>
                        <div class="btn-group">
                            <button class="btn btn-default btn-image dropdown-toggle" data-toggle="dropdown" type="button">
                                <img src="<?php echo base_url(); ?>assets/img/avatars/avatar10.jpg" alt="Avatar">
                                <?php
//                                $nameorg = ($this->session->users["nmsatker"] == "" || $this->session->users["nmsatker"] == NULL) ? $this->session->users["roledesc"] : $this->session->users["nmsatker"];

//                                $nmgrup = ($this->session->users["nmgrup"] == "" || $this->session->users["nmgrup"] == NULL) ? $this->session->users["roledesc"] : $this->session->users["nmgrup"];

                           //     echo $this->session->users["username"] . " (" . $nameorg . " - ". $nmgrup .")";
                                ?>
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right">
                                <!--li class="dropdown-header">Profile</li-->
                                <li>
                                    <a tabindex="-1" href="<?php echo base_url("index.php/profile"); ?>">
                                        <i class="si si-user pull-right"></i>
                                        Profil
                                    </a>
                                </li>
                                <!--li>
                                    <a tabindex="-1" href="javascript:void(0)">
                                        <i class="si si-settings pull-right"></i>Pengaturan
                                    </a>
                                </li-->
                                <li class="divider"></li>
                                <!--li class="dropdown-header">Actions</li-->
                                <!--li>
                                    <a tabindex="-1" href="base_pages_lock.html">
                                        <i class="si si-lock pull-right"></i>Kunci Akun
                                    </a>
                                </li-->
                                <li onclick="force_logout()">
                                    <a tabindex="-1" href="<?php echo base_url('index.php/login/logout?stat=logout'); ?>">
                                        <i class="si si-logout pull-right"></i>Log out
                                    </a>
                                </li>
                                <script>
                                    function force_logout() {
                                        //alert("force_logout");
                                        var url = "<?php echo base_url('index.php/login/logout?stat=logout'); ?>";
                                        window.open(url, "_self")
                                    }
                                </script>
                            </ul>
                        </div>
                    </li>

                </ul>
                <!-- END Header Navigation Right -->

                <!-- Header Navigation Left -->
                <ul class="nav-header pull-left">
                    <li class="hidden-md hidden-lg">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                            <i class="fa fa-navicon"></i>
                        </button>
                    </li>
                    <li class="hidden-xs hidden-sm">
                        <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                        <button class="btn btn-default btn-window-option" data-toggle="layout" data-action="sidebar_mini_toggle" type="button" id="co">
                            <i class="fa fa-ellipsis-v"></i>
                        </button>
                    </li>
                    <li>
                        <!-- Opens the Apps modal found at the bottom of the page, before including JS code
                        <button class="btn btn-default pull-right" data-toggle="modal" data-target="#apps-modal" type="button">
                            <i class="si si-grid"></i>
                        </button>
                        -->
                    </li>
                    <li class="visible-xs">
                        <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                        <!--
                        <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search" data-class="header-search-xs-visible" type="button">
                            <i class="fa fa-search"></i>
                        </button>
                        -->
                    </li>
                    <!--
                    <li class="js-header-search header-search">
                        <form class="form-horizontal" action="base_pages_search.html" method="post">
                            <div class="form-material form-material-primary input-group remove-margin-t remove-margin-b">
                                <input class="form-control" type="text" id="base-material-text" name="base-material-text" placeholder="Search..">
                                <span class="input-group-addon"><i class="si si-magnifier"></i></span>
                            </div>
                        </form>
                    </li>
                    -->
                </ul>
                <!-- END Header Navigation Left -->
            </header>
            <!-- END Header -->

            <!-- Main Container -->
            <main id="main-container">
                <!-- Page Header -->
                <!--
                <div class="content bg-gray-lighter">
                    <div class="row items-push">
                        <div class="col-sm-7">
                            <h1 class="page-heading">
                                Blank <small>That feeling of delight when you start your awesome new project!</small>
                            </h1>
                        </div>
                        <div class="col-sm-5 text-right hidden-xs">
                            <ol class="breadcrumb push-10-t">
                                <li>Generic</li>
                                <li><a class="link-effect" href="">Blank</a></li>
                            </ol>
                        </div>
                    </div>
                </div>
                -->
                <!-- END Page Header -->

                <!-- Page Content -->
                <!--div class="content"-->
                <?php echo $contents; ?>
                <!--/div-->
                <!-- END Page Content -->
            </main>
            <!-- END Main Container -->

            <!-- Footer -->
            <footer id="page-footer" class="content-mini content-mini-full font-s12 bg-gray-lighter clearfix">
                <!--
                <div class="pull-right">
                    Crafted with <i class="fa fa-heart text-city"></i> by <a class="font-w600" href="https://goo.gl/vNS3I" target="_blank">pixelcave</a>
                </div>
                -->
                <div class="pull-left">
                    <a class="font-w600" href="#" target="_blank">Aset BPJT</a> &copy; <span class="js-year-copy">2021</span>
                </div>
            </footer>
            <!-- END Footer -->
        </div>
        <!-- END Page Container -->

        <!-- Apps Modal -->
        <!-- Opens from the button in the header -->
        <div class="modal fade" id="apps-modal" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-sm modal-dialog modal-dialog-top">
                <div class="modal-content">
                    <!-- Apps Block -->
                    <div class="block block-themed block-transparent">
                        <div class="block-header bg-primary-dark">
                            <ul class="block-options">
                                <li>
                                    <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                                </li>
                            </ul>
                            <h3 class="block-title">Apps</h3>
                        </div>
                        <div class="block-content">
                            <div class="row text-center">
                                <div class="col-xs-6">
                                    <a class="block block-rounded" href="base_pages_dashboard.html">
                                        <div class="block-content text-white bg-default">
                                            <i class="si si-speedometer fa-2x"></i>
                                            <div class="font-w600 push-15-t push-15">Backend</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-xs-6">
                                    <a class="block block-rounded" href="bd_dashboard.html">
                                        <div class="block-content text-white bg-modern">
                                            <i class="si si-rocket fa-2x"></i>
                                            <div class="font-w600 push-15-t push-15">Boxed</div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END Apps Block -->
                </div>
            </div>
        </div>
        <!-- END Apps Modal -->
        <script src="<?php echo base_url(); ?>assets/js/core/jquery.min.js"></script>
        <!-- <script type="text/javascript" charset="utf8" src="<?php //echo base_url(); ?>assets/js/wayjs/way.js"></script> -->

        <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
        <script src="<?php echo base_url(); ?>assets/js/core/bootstrap.min.js"></script>
        <script src="<?php echo base_url(); ?>assets/js/core/jquery.slimscroll.min.js"></script>
        <script src="<?php echo base_url(); ?>assets/js/core/jquery.scrollLock.min.js"></script>
        <script src="<?php echo base_url(); ?>assets/js/core/jquery.appear.min.js"></script>
        <script src="<?php echo base_url(); ?>assets/js/core/jquery.countTo.min.js"></script>
        <script src="<?php echo base_url(); ?>assets/js/core/jquery.placeholder.min.js"></script>
        <script src="<?php echo base_url(); ?>assets/js/core/js.cookie.min.js"></script>
        <!--<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.8.11/jquery-ui.min.js"></script>-->
        <script src="<?php echo base_url(); ?>assets/js/plugins/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
        <!--<script src=" https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.8.0/css/bootstrap-datepicker.css"></script>-->

        <script src="<?php echo base_url('assets/js/jspdf/html2canvas.js');?>"></script>
        <script src="<?php echo base_url('assets/js/jspdf/purify.min.js');?>"></script>
        <!--script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.3.1/jspdf.umd.min.js"></script-->
        <!--script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/0.9.0rc1/jspdf.min.js"></script-->
        <script src="<?php echo base_url('assets/js/jspdf/jspdf.min.js');?>"></script>
        <script src="<?php echo base_url('assets/js/jspdf/jspdf.plugin.autotable.min.js');?>"></script>

        <script src="<?php echo base_url(); ?>assets/js/jquery-ui.min.js"></script>
        <script src="<?php echo base_url(); ?>assets/js/app.js"></script>

        <script>jQuery(function () {
            App.initHelpers('draggable-items');
        });
        </script>


        <!--<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.10.19/js/jquery.dataTables.js"></script>-->
        <!--script type="text/javascript" charset="utf8" src="<?php //echo base_url();     ?>assets/datatable/datatables.js"></script-->
        <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/js/plugins/datatables/jquery.dataTables.min.js"></script>
        <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/js/pages/base_tables_datatables.js"></script>

        <script type="text/javascript" charset="utf8" src="<?php echo base_url();?>assets/local/dataTables.fixedColumns.min.js"></script>

        <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/webgis/js/lookuptool.js"></script>
        <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/webgis/js/validasi.js"></script>
        <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/webgis/js/formater.js"></script>
        <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/sticky_table/js/jquery.stickytable.js">
        </script>


        <script src="<?php echo base_url(); ?>assets/js/plugins/bootstrap-tagsinput/bootstrap-tagsinput.min.js"></script>
        <!--smart wizzard-->
        <!--
        <script src="<?php /* echo base_url(); */ ?>assets/smartwizard/js/jquery.smartWizard.js"></script>
        -->
        <!--dropdown with search-->
        <script src="<?php echo base_url(); ?>assets/js/bootselect/bootstrap-select.js"></script>
        <!--js tree-->
        <script src="<?php echo base_url(); ?>assets/js/jstree.min.js"></script>
        <script src="<?php echo base_url(); ?>assets/js/plugins/jquery-validation/jquery.validate.min.js"></script>
        <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.4.1/jspdf.debug.js" integrity="sha384-THVO/sM0mFD9h7dfSndI6TS0PgAGavwKvB5hAxRRvc0o9cPLohB0wb/PTA7LdUHs" crossorigin="anonymous"></script> -->
        <!-- <script src="<?php //echo base_url('assets/html2canvas/html2canvas.js'); ?>" crossorigin="anonymous"></script> -->
        <!-- <script src="<?php //echo base_url('assets/html2canvas/jquery.plugin.html2canvas.js'); ?>" crossorigin="anonymous"></script> -->
        <!-- The jQuery UI widget factory, can be omitted if jQuery UI is already included -->
        <!--javascript module-->
        <!--selalu letakan code ini di bagian paling bawah supaya oneUI/plugin yg anda pakai berjalan normal--->
        <?php
        if (isset($jv_script)) {
            echo $jv_script;
        }
        ?>
    </body>
</html>
