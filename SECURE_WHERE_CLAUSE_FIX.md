# Secure Remediation for $wh = '1=1' SQL Injection Vulnerability

## Problem Analysis

The current code uses string concatenation to build WHERE clauses, which is vulnerable to SQL injection:

```php
$wh = '1=1';
if($role==4){
    $wh = " kd_satker='$satker'";
}
elseif($role==7){
    $wh = " created_by=$id_user";
}
```

## Solution 1: CodeIgniter Query Builder (Recommended)

### Before (Vulnerable):
```php
public function ssp() {
    $role = $this->session->users['id_user_group_real'];
    $satker = $this->session->users['kd_satker'];
    $id_user = $this->session->users['id_user'];

    // Vulnerable string concatenation
    $aa = $this->input->post('bbws');
    $bb = $this->input->post('prov');
    $cc = $this->input->post('kab');
    $dd = $this->input->post('kec');
    $ee = $this->input->post('desa');

    $a = '';
    $b = '';
    $c = '';
    $d = '';
    $e = '';

    if($aa != ''){
        $a = " kd_satker='$aa' and ";
    }
    if($bb != ''){
       $b = " kd_prov='$bb' and ";
    }
    if($cc != ''){
      $c = " kd_kabkot='$cc' and ";
    }
    if($dd != ''){
      $d = " kd_kec='$dd' and ";
    }
    if($ee != ''){
      $e = "kd_desa='$ee' and ";
    }

    $wh1 = $a.$b.$c.$d.$e;
    $wh = '1=1';

    if($role == 4){
        $wh = " kd_satker='$satker'";
    }
    elseif($role == 7){
        $wh = " created_by=$id_user";
    }

    // Vulnerable usage in datatable_ssp
    datatable_ssp($table, $primaryKey, $columns, $where);
}
```

### After (Secure):
```php
public function ssp() {
    $role = $this->session->users['id_user_group_real'];
    $satker = $this->session->users['kd_satker'];
    $id_user = $this->session->users['id_user'];

    // Build WHERE conditions array safely
    $where_conditions = array();
    $additional_filters = array();

    // Validate and sanitize filter inputs
    $filters = $this->validate_filter_inputs();

    // Add filter conditions
    if (!empty($filters['bbws'])) {
        $additional_filters['kd_satker'] = $filters['bbws'];
    }
    if (!empty($filters['prov'])) {
        $additional_filters['kd_prov'] = $filters['prov'];
    }
    if (!empty($filters['kab'])) {
        $additional_filters['kd_kabkot'] = $filters['kab'];
    }
    if (!empty($filters['kec'])) {
        $additional_filters['kd_kec'] = $filters['kec'];
    }
    if (!empty($filters['desa'])) {
        $additional_filters['kd_desa'] = $filters['desa'];
    }

    // Add role-based conditions
    if ($role == 4) {
        $where_conditions['kd_satker'] = $satker;
    } elseif ($role == 7) {
        $where_conditions['created_by'] = $id_user;
    }

    // Merge all conditions
    $final_conditions = array_merge($where_conditions, $additional_filters);

    // Use secure datatable function
    $this->secure_datatable_ssp($table, $primaryKey, $columns, $final_conditions);
}

private function validate_filter_inputs() {
    $filters = array();

    // Validate each input
    $bbws = $this->input->post('bbws', TRUE);
    $prov = $this->input->post('prov', TRUE);
    $kab = $this->input->post('kab', TRUE);
    $kec = $this->input->post('kec', TRUE);
    $desa = $this->input->post('desa', TRUE);

    // Validate format (alphanumeric only for safety)
    if (!empty($bbws) && ctype_alnum($bbws)) {
        $filters['bbws'] = $bbws;
    }
    if (!empty($prov) && ctype_alnum($prov)) {
        $filters['prov'] = $prov;
    }
    if (!empty($kab) && ctype_alnum($kab)) {
        $filters['kab'] = $kab;
    }
    if (!empty($kec) && ctype_alnum($kec)) {
        $filters['kec'] = $kec;
    }
    if (!empty($desa) && ctype_alnum($desa)) {
        $filters['desa'] = $desa;
    }

    return $filters;
}

private function secure_datatable_ssp($table, $primaryKey, $columns, $where_conditions = array()) {
    // Start building query with Query Builder
    $this->db->select(implode(', ', array_column($columns, 'db')));
    $this->db->from($table);

    // Apply WHERE conditions safely
    if (!empty($where_conditions)) {
        $this->db->where($where_conditions);
    }

    // Apply DataTables search and ordering
    $this->apply_datatables_filters($columns);

    // Get total count
    $total_query = clone $this->db;
    $total_records = $total_query->count_all_results();

    // Apply pagination
    $this->apply_datatables_pagination();

    // Execute query
    $query = $this->db->get();
    $data = $query->result_array();

    // Return DataTables format
    $output = array(
        "draw" => intval($this->input->post('draw')),
        "recordsTotal" => $total_records,
        "recordsFiltered" => $total_records,
        "data" => $data
    );

    echo json_encode($output);
}
```

## Solution 2: Parameterized Queries with Prepared Statements

```php
public function ssp_with_prepared_statements() {
    $role = $this->session->users['id_user_group_real'];
    $satker = $this->session->users['kd_satker'];
    $id_user = $this->session->users['id_user'];

    // Build base query
    $sql = "SELECT * FROM {$table} WHERE 1=1";
    $params = array();
    $types = "";

    // Add role-based conditions
    if ($role == 4) {
        $sql .= " AND kd_satker = ?";
        $params[] = $satker;
        $types .= "s";
    } elseif ($role == 7) {
        $sql .= " AND created_by = ?";
        $params[] = $id_user;
        $types .= "i";
    }

    // Add filter conditions
    $filters = $this->validate_filter_inputs();

    if (!empty($filters['bbws'])) {
        $sql .= " AND kd_satker = ?";
        $params[] = $filters['bbws'];
        $types .= "s";
    }
    if (!empty($filters['prov'])) {
        $sql .= " AND kd_prov = ?";
        $params[] = $filters['prov'];
        $types .= "s";
    }
    if (!empty($filters['kab'])) {
        $sql .= " AND kd_kabkot = ?";
        $params[] = $filters['kab'];
        $types .= "s";
    }
    if (!empty($filters['kec'])) {
        $sql .= " AND kd_kec = ?";
        $params[] = $filters['kec'];
        $types .= "s";
    }
    if (!empty($filters['desa'])) {
        $sql .= " AND kd_desa = ?";
        $params[] = $filters['desa'];
        $types .= "s";
    }

    // Execute prepared statement
    $query = $this->db->query($sql, $params);
    return $query->result_array();
}
```

## Solution 3: Whitelist-Based Approach

```php
public function ssp_with_whitelist() {
    // Define allowed values for each filter
    $allowed_values = array(
        'bbws' => $this->get_allowed_satker_codes(),
        'prov' => $this->get_allowed_province_codes(),
        'kab' => $this->get_allowed_kabkot_codes(),
        'kec' => $this->get_allowed_kecamatan_codes(),
        'desa' => $this->get_allowed_desa_codes()
    );

    $where_conditions = array();

    // Validate against whitelist
    $bbws = $this->input->post('bbws', TRUE);
    if (!empty($bbws) && in_array($bbws, $allowed_values['bbws'])) {
        $where_conditions['kd_satker'] = $bbws;
    }

    $prov = $this->input->post('prov', TRUE);
    if (!empty($prov) && in_array($prov, $allowed_values['prov'])) {
        $where_conditions['kd_prov'] = $prov;
    }

    // Continue for other filters...

    // Apply role-based restrictions
    $role = $this->session->users['id_user_group_real'];
    if ($role == 4) {
        $where_conditions['kd_satker'] = $this->session->users['kd_satker'];
    } elseif ($role == 7) {
        $where_conditions['created_by'] = $this->session->users['id_user'];
    }

    // Use Query Builder
    if (!empty($where_conditions)) {
        $this->db->where($where_conditions);
    }

    return $this->db->get($table)->result_array();
}

private function get_allowed_satker_codes() {
    // Return array of valid satker codes from database or config
    $this->db->select('kd_satker');
    $query = $this->db->get('ref_satker');
    return array_column($query->result_array(), 'kd_satker');
}
```

## Solution 4: Complete Secure Implementation

```php
public function ssp() {
    try {
        // Validate session and permissions
        if (!$this->validate_user_session()) {
            throw new Exception('Invalid session');
        }

        // Get user context
        $user_context = $this->get_user_context();

        // Validate and sanitize inputs
        $filters = $this->validate_and_sanitize_filters();

        // Build secure query
        $query_builder = $this->build_secure_query($user_context, $filters);

        // Execute and return results
        $this->execute_secure_datatable($query_builder);

    } catch (Exception $e) {
        // Log security event
        $this->log_security_event('query_error', array(
            'error' => $e->getMessage(),
            'user_id' => $this->session->userdata('id_user'),
            'ip' => $this->input->ip_address()
        ));

        // Return safe error response
        echo json_encode(array(
            'error' => 'Query execution failed',
            'draw' => intval($this->input->post('draw')),
            'recordsTotal' => 0,
            'recordsFiltered' => 0,
            'data' => array()
        ));
    }
}

private function validate_user_session() {
    return $this->session->has_userdata('users') &&
           !empty($this->session->users['id_user_group_real']);
}

private function get_user_context() {
    return array(
        'role' => $this->session->users['id_user_group_real'],
        'satker' => $this->session->users['kd_satker'],
        'user_id' => $this->session->users['id_user']
    );
}

private function validate_and_sanitize_filters() {
    $filters = array();
    $input_map = array(
        'bbws' => 'kd_satker',
        'prov' => 'kd_prov',
        'kab' => 'kd_kabkot',
        'kec' => 'kd_kec',
        'desa' => 'kd_desa'
    );

    foreach ($input_map as $input_key => $db_field) {
        $value = $this->input->post($input_key, TRUE);
        if (!empty($value)) {
            // Validate format (adjust regex as needed)
            if (preg_match('/^[A-Z0-9]{1,10}$/', $value)) {
                $filters[$db_field] = $value;
            }
        }
    }

    return $filters;
}

private function build_secure_query($user_context, $filters) {
    $this->db->select('*');
    $this->db->from($this->table_name);

    // Apply role-based restrictions first
    switch ($user_context['role']) {
        case 4:
            $this->db->where('kd_satker', $user_context['satker']);
            break;
        case 7:
            $this->db->where('created_by', $user_context['user_id']);
            break;
        // Add other role cases as needed
    }

    // Apply additional filters
    if (!empty($filters)) {
        $this->db->where($filters);
    }

    return $this->db;
}
```

## Quick Fix for Immediate Implementation

**Replace this vulnerable code in `database_perlokasi/controllers/Database_perlokasi.php`:**

```php
// REMOVE THIS VULNERABLE CODE:
$wh = '1=1';
if($role==4){
    $wh = " kd_satker='$satker'";
}
elseif($role==7){
    $wh = " created_by=$id_user";
}
```

**With this secure code:**

```php
// SECURE REPLACEMENT:
$where_conditions = array();

// Apply role-based access control
if($role == 4){
    $where_conditions['kd_satker'] = $satker;
}
elseif($role == 7){
    $where_conditions['created_by'] = $id_user;
}

// Apply additional filters safely
$filters = $this->validate_filter_inputs();
$where_conditions = array_merge($where_conditions, $filters);

// Use in datatable_ssp with proper escaping
if (!empty($where_conditions)) {
    $this->db->where($where_conditions);
}
```

## Key Security Principles Applied:

1. **Input Validation**: All user inputs are validated and sanitized
2. **Parameterized Queries**: No string concatenation in SQL
3. **Whitelist Validation**: Only allowed values are accepted
4. **Role-Based Access Control**: Proper authorization checks
5. **Error Handling**: Secure error responses without information disclosure
6. **Logging**: Security events are logged for monitoring
7. **Session Validation**: User session is properly validated

## Implementation Priority:

1. **Immediate**: Replace all `$wh = '1=1'` patterns with Solution 1
2. **Short-term**: Add input validation and sanitization
3. **Medium-term**: Implement comprehensive logging and monitoring
4. **Long-term**: Add automated security testing

## Testing the Fix:

```php
// Test with malicious input
$_POST['bbws'] = "'; DROP TABLE users; --";

// Before fix: SQL injection possible
// After fix: Input rejected by validation

// Test with valid input
$_POST['bbws'] = "SAT001";

// Before fix: Vulnerable to injection
// After fix: Safely parameterized
```
