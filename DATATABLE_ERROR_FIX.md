# DataTable Error Fix - "Invalid session data"

## Problem
The DataTable is showing "Invalid session data" error after implementing security fixes.

## Root Cause
The new security validation was too strict and checking for session fields that might not exist or have different names in the actual session structure.

## Fixes Applied

### 1. Flexible Session Validation
Updated `validate_session_data()` to be more flexible:

```php
private function validate_session_data()
{
    // Check if users session exists
    if (!$this->session->has_userdata('users') || empty($this->session->users)) {
        log_message('debug', 'Session validation failed: No users session data');
        return false;
    }

    // Check required session fields (only essential ones)
    $required_fields = array('id_user');
    $optional_fields = array('id_user_group_real', 'kd_satker');
    
    // Check absolutely required fields
    foreach ($required_fields as $field) {
        if (!isset($this->session->users[$field]) || empty($this->session->users[$field])) {
            log_message('debug', 'Session validation failed: Missing required field ' . $field);
            return false;
        }
    }
    
    // Log warnings for missing optional fields but don't fail validation
    foreach ($optional_fields as $field) {
        if (!isset($this->session->users[$field]) || empty($this->session->users[$field])) {
            log_message('debug', 'Session warning: Missing optional field ' . $field);
        }
    }

    return true;
}
```

### 2. Flexible Year Handling
Added `get_session_year()` function to check multiple possible year sources:

```php
private function get_session_year()
{
    // Try different possible year fields in session
    if (!empty($this->session->users['tahun_p3tgai'])) {
        return $this->session->users['tahun_p3tgai'];
    }
    
    if (!empty($this->session->users['tahun'])) {
        return $this->session->users['tahun'];
    }
    
    if (!empty($this->session->konfig_tahun_ang)) {
        return $this->session->konfig_tahun_ang;
    }
    
    // Default to current year if no session year found
    return date('Y');
}
```

### 3. Graceful Error Handling
Updated session data access to use null coalescing operator:

```php
// Get validated session data
$yearnow = $this->get_session_year();
$id_user = $this->session->users['id_user'] ?? null;
$role = $this->session->users['id_user_group_real'] ?? null;
$satker = $this->session->users['kd_satker'] ?? null;
```

### 4. Role-Based Filtering with Null Checks
Updated role-based filtering to handle missing data:

```php
private function apply_role_based_filtering($role, $satker, $yearnow, $id_user)
{
    // Handle missing role gracefully
    if (empty($role)) {
        log_message('debug', 'No role specified, applying default filtering');
        return;
    }

    switch ($role) {
        case 4:
            // Role 4: Filter by province codes for the satker
            if (!empty($satker)) {
                $province_codes = $this->multi_prov();
                if (!empty($province_codes)) {
                    $this->db->where_in('kd_prov', $province_codes);
                } else {
                    $this->db->where('1=0');
                }
            } else {
                log_message('debug', 'Role 4 specified but no satker available');
            }
            break;
            
        case 7:
            // Role 7: Filter by created_by
            if (!empty($id_user)) {
                $this->db->where('created_by', $id_user);
            } else {
                log_message('debug', 'Role 7 specified but no user ID available');
            }
            break;
            
        default:
            log_message('debug', 'Role ' . $role . ' - no additional filtering applied');
            break;
    }
}
```

## Debug Function Added

Added a temporary debug function to check session structure:

```php
public function debug_session()
{
    header('Content-Type: application/json');
    
    $debug_info = array(
        'has_users_session' => $this->session->has_userdata('users'),
        'users_data' => $this->session->users ?? null,
        'all_session_data' => $this->session->userdata(),
        'validation_result' => $this->validate_session_data(),
        'year_from_getter' => $this->get_session_year()
    );
    
    echo json_encode($debug_info, JSON_PRETTY_PRINT);
}
```

## Testing Steps

1. **Check session structure**: Visit `/daerah_irigasi/debug_session` to see actual session data
2. **Test DataTable**: Try loading the DataTable again
3. **Check logs**: Look at application logs for debug messages
4. **Remove debug function**: Once working, remove the debug_session function

## Expected Results

- DataTable should load without "Invalid session data" error
- Security features remain intact
- Graceful handling of missing session fields
- Proper logging for debugging

## Security Notes

- Only essential session validation is enforced
- Missing optional fields are logged but don't break functionality
- All SQL injection protections remain in place
- Input validation and sanitization still active

## Rollback Plan

If issues persist, you can temporarily revert to a simpler validation:

```php
private function validate_session_data()
{
    return $this->session->has_userdata('users') && 
           !empty($this->session->users['id_user']);
}
```

This minimal validation ensures basic session existence while maintaining security.
