# Security Code Examples and Fixes

## Critical SQL Injection Fixes

### 1. User Management Controller Fix

**File**: `application/modules/user_managementt/controllers/User_managementt.php`

#### Before (Vulnerable):
```php
public function get_rowdata($tbl, $col, $w, $id) {
    $yearnow = $this->session->users['tahun_p3tgai'];
    $query = $this->db->query("select $col from $tbl where $w='$id' and tahun=$yearnow");
    $res = $query->row_array();
    // ... rest of code
}
```

#### After (Secure):
```php
public function get_rowdata($tbl, $col, $w, $id) {
    $yearnow = $this->session->users['tahun_p3tgai'];
    
    // Validate table and column names against whitelist
    $allowed_tables = ['aset_users', 'aset_user_groups', 'aset_satker'];
    $allowed_columns = ['id_user', 'username', 'email', 'nama'];
    $allowed_where = ['id_user', 'email', 'username'];
    
    if (!in_array($tbl, $allowed_tables) || 
        !in_array($col, $allowed_columns) || 
        !in_array($w, $allowed_where)) {
        return null;
    }
    
    // Use Query Builder with proper escaping
    $this->db->select($col);
    $this->db->from($tbl);
    $this->db->where($w, $id);
    $this->db->where('tahun', $yearnow);
    $query = $this->db->get();
    
    if ($query->num_rows() > 0) {
        $res = $query->row_array();
        return isset($res[$col]) ? $res[$col] : null;
    }
    
    return null;
}
```

### 2. Database Per Lokasi Controller Fix

**File**: `application/modules/database_perlokasi/controllers/Database_perlokasi.php`

#### Before (Vulnerable):
```php
$aa = $this->input->post('bbws');
$bb = $this->input->post('prov');
// ... more variables
if($aa !=''){
    $a=" kd_satker='$aa' and ";
}
if($bb !=''){
   $b=" kd_prov='$bb' and ";
}
$wh1=$a.$b.$c.$d.$e;
```

#### After (Secure):
```php
// Validate and sanitize input
$filters = array(
    'bbws' => $this->input->post('bbws', TRUE),
    'prov' => $this->input->post('prov', TRUE),
    'kab' => $this->input->post('kab', TRUE),
    'kec' => $this->input->post('kec', TRUE),
    'desa' => $this->input->post('desa', TRUE)
);

// Build WHERE conditions safely
$where_conditions = array();

if (!empty($filters['bbws']) && ctype_alnum($filters['bbws'])) {
    $where_conditions['kd_satker'] = $filters['bbws'];
}
if (!empty($filters['prov']) && ctype_alnum($filters['prov'])) {
    $where_conditions['kd_prov'] = $filters['prov'];
}
if (!empty($filters['kab']) && ctype_alnum($filters['kab'])) {
    $where_conditions['kd_kabkot'] = $filters['kab'];
}
if (!empty($filters['kec']) && ctype_alnum($filters['kec'])) {
    $where_conditions['kd_kec'] = $filters['kec'];
}
if (!empty($filters['desa']) && ctype_alnum($filters['desa'])) {
    $where_conditions['kd_desa'] = $filters['desa'];
}

// Apply conditions using Query Builder
if (!empty($where_conditions)) {
    $this->db->where($where_conditions);
}
```

## Authentication Security Fixes

### 3. Login Controller Fix

**File**: `application/modules/login/controllers/Login.php`

#### Before (Vulnerable):
```php
if (password_verify($password,$ruser[0]['password']) || password_verify($password,$sapujagat[0]['password'])) {
    // Login successful
}
```

#### After (Secure):
```php
// Remove universal fallback password completely
if (password_verify($password, $ruser[0]['password'])) {
    // Add additional security checks
    
    // Check for account lockout
    if ($this->is_account_locked($ruser[0]['id_user'])) {
        echo json_encode(array("status" => "Account locked due to multiple failed attempts"));
        return;
    }
    
    // Reset failed login attempts on successful login
    $this->reset_failed_attempts($ruser[0]['id_user']);
    
    // Log successful login
    $this->log_security_event('login_success', array(
        'user_id' => $ruser[0]['id_user'],
        'email' => $ruser[0]['email']
    ));
    
    // Continue with login process...
} else {
    // Increment failed login attempts
    $this->increment_failed_attempts($username);
    
    // Log failed login attempt
    $this->log_security_event('login_failed', array(
        'attempted_email' => $username
    ));
    
    echo json_encode(array("status" => "Invalid credentials"));
}
```

#### Add Account Lockout Methods:
```php
private function is_account_locked($user_id) {
    $this->db->where('user_id', $user_id);
    $this->db->where('locked_until >', date('Y-m-d H:i:s'));
    $query = $this->db->get('user_lockouts');
    return $query->num_rows() > 0;
}

private function increment_failed_attempts($email) {
    $max_attempts = 5;
    $lockout_duration = 30; // minutes
    
    // Get current failed attempts
    $this->db->where('email', $email);
    $this->db->where('attempt_time >', date('Y-m-d H:i:s', strtotime('-1 hour')));
    $attempts = $this->db->count_all_results('failed_login_attempts');
    
    // Log this attempt
    $this->db->insert('failed_login_attempts', array(
        'email' => $email,
        'ip_address' => $this->input->ip_address(),
        'attempt_time' => date('Y-m-d H:i:s')
    ));
    
    // Lock account if max attempts reached
    if ($attempts >= $max_attempts) {
        $user = $this->get_user($email, 'email');
        if (!empty($user)) {
            $this->db->insert('user_lockouts', array(
                'user_id' => $user[0]['id_user'],
                'locked_until' => date('Y-m-d H:i:s', strtotime("+{$lockout_duration} minutes"))
            ));
        }
    }
}
```

## File Upload Security

### 4. Secure File Upload Implementation

**File**: `application/modules/user_group/upload/controllers/Fileupload.php`

#### Before (Vulnerable):
```php
$config['allowed_types'] = 'jpg|jpeg|png|gif|pdf|docx|doc|xls|xlsx|rar|zip|ppt|pptx';
$config['upload_path'] = FCPATH . 'uploads/';
```

#### After (Secure):
```php
public function do_upload($id_user = "", $id_usulan = "", $thang = "") {
    // Validate user permissions first
    if (!$this->validate_upload_permissions($id_user, $id_usulan)) {
        echo json_encode(array("status" => "error", "message" => "Unauthorized upload"));
        return;
    }
    
    // Create secure upload directory outside web root
    $secure_upload_path = APPPATH . '../secure_uploads/' . date('Y/m/d') . '/';
    if (!is_dir($secure_upload_path)) {
        mkdir($secure_upload_path, 0755, true);
    }
    
    $config['upload_path'] = $secure_upload_path;
    $config['allowed_types'] = 'jpg|jpeg|png|gif|pdf'; // Remove dangerous types
    $config['max_size'] = 5120; // 5MB limit
    $config['encrypt_name'] = TRUE;
    $config['remove_spaces'] = TRUE;
    $config['detect_mime'] = TRUE;
    
    $this->load->library('upload', $config);
    
    if ($this->upload->do_upload('userfile')) {
        $upload_data = $this->upload->data();
        
        // Additional security validation
        if ($this->validate_file_security($upload_data)) {
            // Generate secure file reference
            $file_reference = $this->generate_file_reference($upload_data);
            
            // Store file metadata in database
            $this->store_file_metadata($file_reference, $upload_data, $id_user, $id_usulan);
            
            echo json_encode(array(
                "status" => "success",
                "file_reference" => $file_reference
            ));
        } else {
            // Delete uploaded file if validation fails
            unlink($upload_data['full_path']);
            echo json_encode(array("status" => "error", "message" => "File validation failed"));
        }
    } else {
        echo json_encode(array("status" => "error", "message" => $this->upload->display_errors()));
    }
}

private function validate_file_security($upload_data) {
    // Check file size
    if ($upload_data['file_size'] > 5120) {
        return false;
    }
    
    // Validate MIME type
    $allowed_mimes = array(
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'pdf' => 'application/pdf'
    );
    
    $file_ext = strtolower($upload_data['file_ext']);
    if (!isset($allowed_mimes[$file_ext]) || 
        $upload_data['file_type'] !== $allowed_mimes[$file_ext]) {
        return false;
    }
    
    // Check file signature/magic bytes
    return $this->validate_file_signature($upload_data['full_path'], $file_ext);
}

private function validate_file_signature($file_path, $extension) {
    $file_content = file_get_contents($file_path, false, null, 0, 20);
    
    $signatures = array(
        'pdf' => '%PDF',
        'jpg' => "\xFF\xD8\xFF",
        'jpeg' => "\xFF\xD8\xFF",
        'png' => "\x89PNG\r\n\x1a\n",
        'gif' => 'GIF8'
    );
    
    if (isset($signatures[$extension])) {
        return strpos($file_content, $signatures[$extension]) === 0;
    }
    
    return false;
}

private function generate_file_reference($upload_data) {
    return hash('sha256', $upload_data['file_name'] . time() . rand());
}
```

## CSRF Protection Enhancement

### 5. Enhanced CSRF Implementation

**File**: `application/core/MY_Controller.php`

```php
public function __construct() {
    parent::__construct();
    
    // Enhanced CSRF protection for AJAX requests
    if ($this->input->is_ajax_request()) {
        $this->validate_ajax_csrf();
    }
}

private function validate_ajax_csrf() {
    $csrf_token = $this->input->post($this->security->get_csrf_token_name());
    $csrf_hash = $this->security->get_csrf_hash();
    
    if (!$csrf_token || !hash_equals($csrf_hash, $csrf_token)) {
        http_response_code(403);
        echo json_encode(array('error' => 'CSRF token mismatch'));
        exit;
    }
}

// Method to get fresh CSRF token for AJAX responses
protected function get_csrf_data() {
    return array(
        'csrf_name' => $this->security->get_csrf_token_name(),
        'csrf_hash' => $this->security->get_csrf_hash()
    );
}
```

## XSS Prevention

### 6. Output Encoding Helper

**File**: `application/helpers/security_helper.php`

```php
<?php
if (!function_exists('safe_output')) {
    function safe_output($data, $context = 'html') {
        if (is_array($data)) {
            return array_map(function($item) use ($context) {
                return safe_output($item, $context);
            }, $data);
        }
        
        switch ($context) {
            case 'html':
                return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
            case 'js':
                return json_encode($data, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
            case 'url':
                return urlencode($data);
            case 'css':
                return preg_replace('/[^a-zA-Z0-9\-_]/', '', $data);
            default:
                return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
    }
}

if (!function_exists('validate_input')) {
    function validate_input($data, $type = 'string', $max_length = 255) {
        // Remove null bytes
        $data = str_replace(chr(0), '', $data);
        
        switch ($type) {
            case 'email':
                return filter_var($data, FILTER_VALIDATE_EMAIL);
            case 'int':
                return filter_var($data, FILTER_VALIDATE_INT);
            case 'float':
                return filter_var($data, FILTER_VALIDATE_FLOAT);
            case 'url':
                return filter_var($data, FILTER_VALIDATE_URL);
            case 'alphanumeric':
                return ctype_alnum($data) ? $data : false;
            case 'string':
            default:
                return strlen($data) <= $max_length ? trim($data) : false;
        }
    }
}
?>
```

---

*These code examples provide secure implementations for the most critical vulnerabilities found in the application.*
