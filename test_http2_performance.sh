#!/bin/bash

echo "=== HTTP/2 Performance Test for P3TGAI System ==="
echo ""

# Test HTTP/2 connection
echo "1. Testing HTTP/2 Connection:"
curl -w "HTTP Version: %{http_version}\nTotal Time: %{time_total}s\nConnect Time: %{time_connect}s\nSSL Handshake: %{time_appconnect}s\n" \
     -o /dev/null -s -k --http2 "https://localhost:8443/"
echo ""

# Test multiple concurrent requests (HTTP/2 multiplexing)
echo "2. Testing HTTP/2 Multiplexing (10 concurrent requests):"
start_time=$(date +%s.%N)
for i in {1..10}; do
    curl -s -k --http2 "https://localhost:8443/login/fieldlook/0" > /dev/null &
done
wait
end_time=$(date +%s.%N)
duration=$(echo "$end_time - $start_time" | bc)
echo "Total time for 10 concurrent requests: ${duration}s"
echo ""

# Test compression
echo "3. Testing Gzip Compression:"
curl -H "Accept-Encoding: gzip" -H "User-Agent: Mozilla/5.0" -s -k --http2 -w "Size: %{size_download} bytes\nCompressed: %{size_header} bytes\n" \
     -o /dev/null "https://localhost:8443/"
echo ""

# Test security headers
echo "4. Security Headers Check:"
curl -I -s -k --http2 "https://localhost:8443/" | grep -E "(strict-transport-security|x-frame-options|x-content-type-options|content-security-policy)"
echo ""

# Test static file caching
echo "5. Static File Caching Test:"
curl -I -s -k --http2 "https://localhost:8443/assets/vendor/jquery/jquery.min.js" | grep -E "(cache-control|expires|etag)"
echo ""

echo "=== Performance Test Complete ==="
