import { HttpContext } from '@adonisjs/core/http'

/**
 * Standardized request validation utility for CSD protection
 * Use this in controllers for additional validation beyond middleware
 */
export default class RequestValidator {
  /**
   * Suspicious header patterns that might indicate CSD attempts
   */
  private static readonly SUSPICIOUS_HEADER_PATTERNS = [
    /transfer.{0,10}encoding/i,
    /content.{0,10}length/i,
    /host.{0,5}:/i,
    /connection.{0,5}:/i,
  ]

  /**
   * Patterns that indicate potential HTTP request smuggling in body
   */
  private static readonly BODY_SMUGGLING_PATTERNS = [
    /(?:GET|POST|PUT|DELETE|HEAD|OPTIONS|CONNECT|TRACE|PATCH) \/[^ ]+ HTTP\/[0-9.]+/,
    /^[0-9a-fA-F]+\r\n/m, // Chunked encoding markers
    /\r\n\r\n.*(?:GET|POST|PUT|DELETE|HEAD|OPTIONS|CONNECT|TRACE|PATCH)/s,
    /Content-Length:\s*\d+.*\r\n.*Content-Length:\s*\d+/is,
    /Transfer-Encoding:\s*chunked.*Content-Length:/is,
  ]

  /**
   * Maximum allowed body size (50MB)
   */
  private static readonly MAX_BODY_SIZE = 50 * 1024 * 1024

  /**
   * Validate an incoming request for CSD vulnerabilities
   */
  public static validate(ctx: HttpContext): { valid: boolean; error?: string } {
    const { request } = ctx
    
    try {
      // 1. Check for multiple Content-Length headers
      const contentLengthHeaders = request.headers()['content-length']
      if (Array.isArray(contentLengthHeaders) && contentLengthHeaders.length > 1) {
        return { valid: false, error: 'Multiple Content-Length headers detected' }
      }
      
      // 2. Check for conflicting Transfer-Encoding and Content-Length
      if (request.header('content-length') && request.header('transfer-encoding')) {
        return { valid: false, error: 'Conflicting Content-Length and Transfer-Encoding headers' }
      }
      
      // 3. Validate Content-Length format
      const contentLength = request.header('content-length')
      if (contentLength && !/^\d+$/.test(contentLength)) {
        return { valid: false, error: 'Invalid Content-Length format' }
      }
      
      // 4. Check Content-Length value
      if (contentLength) {
        const length = parseInt(contentLength, 10)
        if (length > this.MAX_BODY_SIZE) {
          return { valid: false, error: 'Content-Length exceeds maximum allowed size' }
        }
      }
      
      // 5. Check for suspicious headers
      const suspiciousHeader = this.checkSuspiciousHeaders(request.headers())
      if (suspiciousHeader) {
        return { valid: false, error: `Suspicious header detected: ${suspiciousHeader}` }
      }
      
      // 6. Check for HTTP request smuggling in body
      const body = request.raw() || ''
      if (typeof body === 'string' && this.detectBodySmuggling(body)) {
        return { valid: false, error: 'HTTP request smuggling detected in body' }
      }
      
      // 7. Validate URL format
      const url = request.url()
      if (!this.validateUrl(url)) {
        return { valid: false, error: 'Invalid URL format detected' }
      }

      // 8. Check request method
      const method = request.method()
      if (!this.validateHttpMethod(method)) {
        return { valid: false, error: 'Invalid HTTP method' }
      }
      
      return { valid: true }
    } catch (error) {
      return { valid: false, error: 'Request validation failed' }
    }
  }

  /**
   * Check for suspicious headers that might indicate CSD attempts
   */
  private static checkSuspiciousHeaders(headers: Record<string, any>): string | null {
    for (const key of Object.keys(headers)) {
      for (const pattern of this.SUSPICIOUS_HEADER_PATTERNS) {
        if (pattern.test(key) && !['transfer-encoding', 'content-length', 'host', 'connection'].includes(key.toLowerCase())) {
          return key
        }
      }
      
      // Check for control characters in header names
      if (/[\x00-\x1F\x7F]/.test(key)) {
        return key
      }
      
      // Check for spaces in header names
      if (/\s/.test(key)) {
        return key
      }
    }
    
    return null
  }

  /**
   * Detect HTTP request smuggling in body
   */
  private static detectBodySmuggling(body: string): boolean {
    // Check first 8KB of body for performance
    const bodyToCheck = body.substring(0, 8192)
    
    for (const pattern of this.BODY_SMUGGLING_PATTERNS) {
      if (pattern.test(bodyToCheck)) {
        return true
      }
    }
    
    return false
  }

  /**
   * Validate URL format and security
   */
  private static validateUrl(url: string): boolean {
    try {
      // Check for encoded CRLF
      if (/%(?:25)?0[dDaA]/.test(url)) {
        return false
      }
      
      // Check for path traversal
      if (/%(?:25)?2[eE]%(?:25)?2[eE](?:%(?:25)?2[fF]|\/)/.test(url)) {
        return false
      }
      
      // Check for null bytes
      if (/%00/.test(url)) {
        return false
      }
      
      // Check for control characters
      if (/[\x00-\x1F\x7F]/.test(url)) {
        return false
      }
      
      return true
    } catch (e) {
      return false
    }
  }

  /**
   * Validate HTTP method
   */
  private static validateHttpMethod(method: string): boolean {
    const allowedMethods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS']
    return allowedMethods.includes(method.toUpperCase())
  }

  /**
   * Sanitize request parameters
   */
  public static sanitizeParams(params: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {}
    
    for (const [key, value] of Object.entries(params)) {
      // Skip if null or undefined
      if (value == null) continue
      
      // Recursively sanitize objects
      if (typeof value === 'object' && !Array.isArray(value)) {
        sanitized[this.sanitizeString(key)] = this.sanitizeParams(value)
        continue
      }
      
      // Sanitize arrays
      if (Array.isArray(value)) {
        sanitized[this.sanitizeString(key)] = value.map(item => 
          typeof item === 'object' ? this.sanitizeParams(item) : this.sanitizeValue(item)
        )
        continue
      }
      
      // Sanitize simple values
      sanitized[this.sanitizeString(key)] = this.sanitizeValue(value)
    }
    
    return sanitized
  }

  /**
   * Sanitize a string value
   */
  private static sanitizeString(str: string): string {
    if (typeof str !== 'string') return str
    
    // Remove control characters
    return str.replace(/[\x00-\x1F\x7F]/g, '')
  }

  /**
   * Sanitize a value based on its type
   */
  private static sanitizeValue(value: any): any {
    if (typeof value === 'string') {
      return this.sanitizeString(value)
    }
    
    return value
  }

  /**
   * Quick validation for high-risk endpoints
   */
  public static quickValidate(ctx: HttpContext): boolean {
    const { request } = ctx
    
    // Check for the most critical issues only
    const headers = request.headers()
    
    // Check for conflicting headers
    if (headers['content-length'] && headers['transfer-encoding']) {
      return false
    }
    
    // Check for duplicate Content-Length
    const contentLength = headers['content-length']
    if (Array.isArray(contentLength) && contentLength.length > 1) {
      return false
    }
    
    // Check for invalid Content-Length format
    if (contentLength && !/^\d+$/.test(contentLength)) {
      return false
    }
    
    return true
  }
}
