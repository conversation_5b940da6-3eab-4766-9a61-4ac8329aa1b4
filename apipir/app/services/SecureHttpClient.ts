import axios, { AxiosRequestConfig, AxiosResponse } from 'axios'

/**
 * Secure HTTP client wrapper to prevent request smuggling and CSD attacks
 * Use this for all outgoing HTTP requests from the backend
 */
export default class SecureHttpClient {
  /**
   * Default timeout for requests (30 seconds)
   */
  private static readonly DEFAULT_TIMEOUT = 30000

  /**
   * Maximum allowed response size (100MB)
   */
  private static readonly MAX_RESPONSE_SIZE = 100 * 1024 * 1024

  /**
   * Headers that should be removed to prevent smuggling
   */
  private static readonly DANGEROUS_HEADERS = [
    'transfer-encoding',
    'te',
    'trailer',
    'upgrade',
    'proxy-connection',
    'connection',
    'keep-alive'
  ]

  /**
   * Sanitize request configuration to prevent CSD vulnerabilities
   */
  private static sanitizeConfig(config: AxiosRequestConfig): AxiosRequestConfig {
    const secureConfig: AxiosRequestConfig = {
      ...config,
      timeout: config.timeout || this.DEFAULT_TIMEOUT,
      maxContentLength: this.MAX_RESPONSE_SIZE,
      maxBodyLength: this.MAX_RESPONSE_SIZE,
      headers: {
        ...config.headers,
        'Connection': 'close',
        'User-Agent': 'PIR-Backend/1.0',
      }
    }

    // Remove potentially dangerous headers
    if (secureConfig.headers) {
      for (const header of this.DANGEROUS_HEADERS) {
        delete secureConfig.headers[header]
        delete secureConfig.headers[header.toLowerCase()]
        delete secureConfig.headers[header.toUpperCase()]
      }
    }

    // Validate URL
    if (config.url && !this.validateUrl(config.url)) {
      throw new Error('Invalid URL format detected')
    }

    return secureConfig
  }

  /**
   * Validate URL for security issues
   */
  private static validateUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url)
      
      // Check for suspicious patterns
      if (/%(?:25)?0[dDaA]/.test(parsedUrl.href)) {
        return false
      }
      
      // Check for path traversal
      if (/%(?:25)?2[eE]%(?:25)?2[eE](?:%(?:25)?2[fF]|\/)/.test(parsedUrl.href)) {
        return false
      }
      
      // Check for null bytes
      if (/%00/.test(parsedUrl.href)) {
        return false
      }

      // Only allow HTTP and HTTPS protocols
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        return false
      }
      
      return true
    } catch (e) {
      return false
    }
  }

  /**
   * Make a secure HTTP request with standardized headers
   */
  public static async request(config: AxiosRequestConfig): Promise<AxiosResponse> {
    try {
      const secureConfig = this.sanitizeConfig(config)
      
      // Add request timestamp for logging
      secureConfig.headers = {
        ...secureConfig.headers,
        'X-Request-Time': Date.now().toString(),
        'X-Request-ID': this.generateRequestId()
      }

      const response = await axios(secureConfig)
      
      // Validate response headers
      this.validateResponseHeaders(response)
      
      return response
    } catch (error) {
      // Log security-related errors
      if (axios.isAxiosError(error)) {
        console.error('[SECURITY] HTTP request failed:', {
          url: config.url,
          method: config.method,
          status: error.response?.status,
          message: error.message
        })
      }
      throw error
    }
  }

  /**
   * Validate response headers for security issues
   */
  private static validateResponseHeaders(response: AxiosResponse): void {
    const headers = response.headers
    
    // Check for suspicious header combinations
    if (headers['content-length'] && headers['transfer-encoding']) {
      console.warn('[SECURITY] Response contains both Content-Length and Transfer-Encoding headers')
    }

    // Validate Content-Length if present
    const contentLength = headers['content-length']
    if (contentLength && !/^\d+$/.test(contentLength)) {
      console.warn('[SECURITY] Invalid Content-Length in response:', contentLength)
    }
  }

  /**
   * Generate a unique request ID for tracking
   */
  private static generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Secure GET request
   */
  public static async get(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.request({
      ...config,
      method: 'GET',
      url,
    })
  }

  /**
   * Secure POST request
   */
  public static async post(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.request({
      ...config,
      method: 'POST',
      url,
      data,
    })
  }

  /**
   * Secure PUT request
   */
  public static async put(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.request({
      ...config,
      method: 'PUT',
      url,
      data,
    })
  }

  /**
   * Secure PATCH request
   */
  public static async patch(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.request({
      ...config,
      method: 'PATCH',
      url,
      data,
    })
  }

  /**
   * Secure DELETE request
   */
  public static async delete(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.request({
      ...config,
      method: 'DELETE',
      url,
    })
  }

  /**
   * Secure HEAD request
   */
  public static async head(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.request({
      ...config,
      method: 'HEAD',
      url,
    })
  }

  /**
   * Secure OPTIONS request
   */
  public static async options(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.request({
      ...config,
      method: 'OPTIONS',
      url,
    })
  }
}
