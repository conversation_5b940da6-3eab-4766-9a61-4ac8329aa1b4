import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import env from '#start/env'

/**
 * Middleware to enforce HTTPS and add security headers
 * This middleware should be registered early in the middleware stack
 */
export default class HttpsEnforcementMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    const { request, response } = ctx
    
    // In production, redirect HTTP to HTTPS
    if (env.get('NODE_ENV') === 'production') {
      const protocol = request.protocol()
      const host = request.host()
      
      // Check if the request is using HTTP
      if (protocol === 'http') {
        const httpsUrl = `https://${host}${request.url()}`
        return response.redirect(httpsUrl, true) // Permanent redirect (301)
      }
      
      // Add Strict-Transport-Security header
      response.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')
    }
    
    // Add additional security headers
    response.header('Expect-CT', 'enforce, max-age=86400')
    response.header('Permissions-Policy', 'geolocation=(), microphone=(), camera=()')
    response.header('Referrer-Policy', 'strict-origin-when-cross-origin')
    
    await next()
  }
}
