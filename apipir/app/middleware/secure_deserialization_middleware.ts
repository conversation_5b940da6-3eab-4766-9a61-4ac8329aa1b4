import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'

/**
 * Secure Deserialization Middleware
 * 
 * This middleware validates and sanitizes incoming JSON data
 * to prevent insecure deserialization attacks and prototype pollution.
 */
export default class SecureDeserializationMiddleware {
  /**
   * Known dangerous patterns in JSON payloads
   */
  private dangerousPatterns = [
    // Prototype pollution patterns
    /__proto__/,
    /constructor\s*[.:]/,
    /prototype\s*[.:]/,
    
    // Potential RCE patterns
    /\$\{.+\}/,  // Template injection
    /\beval\s*\(/,  // eval function
    /\bFunction\s*\(/,  // Function constructor
    /\bsetTimeout\s*\(/,  // setTimeout
    /\bsetInterval\s*\(/,  // setInterval
    
    // NoSQL injection patterns
    /\$where\s*:/,
    /\$regex\s*:/,
    /\$gt\s*:/,
    /\$ne\s*:/,
    
    // Other dangerous patterns
    /\bprocess\s*\./,
    /\brequire\s*\(/,
    /\bimport\s*\(/,
    /\bglobal\s*\./,
  ]
  
  /**
   * Check if a string contains dangerous patterns
   */
  private hasDangerousPatterns(str: string): boolean {
    return this.dangerousPatterns.some(pattern => pattern.test(str))
  }

  /**
   * Check if the object contains potentially dangerous properties
   * that could lead to prototype pollution
   */
  private hasDangerousProperties(obj: any): boolean {
    const dangerousProps = ['__proto__', 'constructor', 'prototype']
    
    if (typeof obj !== 'object' || obj === null) {
      return false
    }
    
    for (const prop of dangerousProps) {
      if (Object.hasOwnProperty.call(obj, prop)) {
        return true
      }
    }
    
    // Check for dangerous string patterns
    for (const key in obj) {
      if (Object.hasOwnProperty.call(obj, key)) {
        // Check keys for dangerous patterns
        if (typeof key === 'string' && this.hasDangerousPatterns(key)) {
          return true
        }
        
        // Check string values for dangerous patterns
        if (typeof obj[key] === 'string' && this.hasDangerousPatterns(obj[key])) {
          return true
        }
        
        // Recursively check nested objects
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          if (this.hasDangerousProperties(obj[key])) {
            return true
          }
        }
      }
    }
    
    return false
  }
  
  /**
   * Sanitize object to prevent prototype pollution
   */
  private sanitizeObject(obj: any): any {
    if (typeof obj !== 'object' || obj === null) {
      return obj
    }
    
    const result: any = Array.isArray(obj) ? [] : {}
    
    for (const key in obj) {
      // Skip dangerous properties
      if (key === '__proto__' || key === 'constructor' || key === 'prototype') {
        continue
      }
      
      // Recursively sanitize nested objects
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        result[key] = this.sanitizeObject(obj[key])
      } else {
        result[key] = obj[key]
      }
    }
    
    return result
  }
  
  /**
   * Handle the request and validate/sanitize the body
   */
  async handle(ctx: HttpContext, next: NextFn) {
    const { request, response } = ctx
    const requestMethod = request.method().toUpperCase()
    
    // Only process POST, PUT, PATCH requests with JSON content
    if (['POST', 'PUT', 'PATCH'].includes(requestMethod) && 
        request.header('content-type')?.includes('application/json')) {
      try {
        const body = request.body()
        
        // Check for dangerous properties
        if (this.hasDangerousProperties(body)) {
          console.warn(`Security violation detected in request body from ${request.ip()}`)
          return response.status(400).json({
            error: 'Security violation',
            message: 'Request contains potentially dangerous properties'
          })
        }
        
        // Sanitize the body
        const sanitizedBody = this.sanitizeObject(body)
        
        // Replace the original body with the sanitized one
        Object.defineProperty(request, 'body', {
          value: () => sanitizedBody,
          writable: true,
          configurable: true
        })
      } catch (error) {
        console.error(`Error in secure deserialization middleware: ${error.message}`)
        return response.status(400).json({
          error: 'Invalid JSON',
          message: 'The request body contains invalid JSON'
        })
      }
    }
    
    await next()
  }
}
