import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import env from '#start/env'

/**
 * Content Security Policy Middleware
 * 
 * This middleware implements a comprehensive Content Security Policy (CSP)
 * for all routes in the application. It provides fine-grained control over
 * which resources can be loaded and executed on the page.
 */
export default class ContentSecurityPolicyMiddleware {
  /**
   * Default CSP directives for the application
   */
  private getDefaultDirectives(): Record<string, string[]> {
    const isProd = env.get('NODE_ENV') === 'production'
    
    return {
      // Default sources restriction
      'default-src': ["'self'"],
      
      // Script sources restriction
      'script-src': [
        "'self'",
        // Allow inline scripts with specific hash (replace with your actual script hashes)
        // "'sha256-specific-hash-here'",
        // Allow specific external scripts
        'https://cdn.jsdelivr.net',
        'https://unpkg.com',
        isProd ? '' : "'unsafe-eval'", // Allow eval in development only
      ].filter(Boolean),
      
      // Style sources restriction
      'style-src': [
        "'self'",
        "'unsafe-inline'", // Allow inline styles
        'https://fonts.googleapis.com',
        'https://cdn.jsdelivr.net',
      ],
      
      // Image sources restriction
      'img-src': [
        "'self'",
        'data:',
        'blob:',
        'https:',
      ],
      
      // Font sources restriction
      'font-src': [
        "'self'",
        'https://fonts.gstatic.com',
        'data:',
      ],
      
      // Connect sources restriction (APIs, WebSockets)
      'connect-src': [
        "'self'",
        isProd ? 'https://*.your-api-domain.com' : 'http://localhost:*',
        'wss://*.your-websocket-domain.com',
      ],
      
      // Object sources restriction
      'object-src': ["'none'"],
      
      // Media sources restriction
      'media-src': ["'self'"],
      
      // Frame sources restriction
      'frame-src': ["'self'"],
      
      // Worker sources restriction
      'worker-src': ["'self'", 'blob:'],
      
      // Form action restriction
      'form-action': ["'self'"],
      
      // Frame ancestors restriction (clickjacking protection)
      'frame-ancestors': ["'self'"],
      
      // Base URI restriction
      'base-uri': ["'self'"],
      
      // Upgrade insecure requests
      ...(isProd ? { 'upgrade-insecure-requests': [] } : {}),
      
      // Block mixed content
      'block-all-mixed-content': [],
    }
  }
  
  /**
   * Convert CSP directives object to CSP header string
   */
  private buildCspString(directives: Record<string, string[]>): string {
    return Object.entries(directives)
      .map(([directive, sources]) => {
        // For directives without sources (like block-all-mixed-content)
        if (sources.length === 0) {
          return directive
        }
        
        return `${directive} ${sources.join(' ')}`
      })
      .join('; ')
  }
  
  /**
   * Get route-specific CSP modifications
   */
  private getRouteSpecificDirectives(url: string): Record<string, string[]> {
    // Add route-specific CSP modifications here
    if (url.startsWith('/admin')) {
      // More restrictive CSP for admin routes
      return {
        'script-src': ["'self'"], // Only allow scripts from same origin
        'connect-src': ["'self'"], // Only allow connections to same origin
      }
    }
    
    if (url.startsWith('/api')) {
      // No need for CSP on API routes
      return {}
    }
    
    return {}
  }
  
  /**
   * Handle the request and apply CSP headers
   */
  async handle(ctx: HttpContext, next: NextFn) {
    const { request, response } = ctx
    const url = request.url()
    
    // Skip CSP for certain file types
    if (url.match(/\.(jpg|jpeg|png|gif|svg|css|js|woff|woff2|ttf|eot)$/i)) {
      return next()
    }
    
    // Get default CSP directives
    const defaultDirectives = this.getDefaultDirectives()
    
    // Get route-specific CSP modifications
    const routeSpecificDirectives = this.getRouteSpecificDirectives(url)
    
    // Merge default and route-specific directives
    const mergedDirectives = { ...defaultDirectives }
    
    for (const [directive, sources] of Object.entries(routeSpecificDirectives)) {
      if (mergedDirectives[directive]) {
        mergedDirectives[directive] = sources
      }
    }
    
    // Build the CSP header string
    const cspString = this.buildCspString(mergedDirectives)
    
    // Set the CSP header
    response.header('Content-Security-Policy', cspString)
    
    // Add Report-To header for CSP violation reporting in production
    if (env.get('NODE_ENV') === 'production') {
      response.header('Report-To', JSON.stringify({
        'group': 'csp-endpoint',
        'max_age': 10886400,
        'endpoints': [
          { 'url': '/api/csp-report' }
        ]
      }))
    }
    
    await next()
  }
}
