import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import { randomBytes } from 'crypto'

export default class CsrfMiddleware {
  /**
   * List of HTTP methods that need CSRF protection
   */
  private readonly protectedMethods = ['POST', 'PUT', 'PATCH', 'DELETE']

  /**
   * Routes that are exempted from CSRF protection
   */
  private readonly exceptRoutes = [
    '/be/login',
    '/be/register',
    '/login',
    '/csrf',
    // Add other API routes that need exemption
  ]

  /**
   * Generate a secure CSRF token
   */
  private generateToken(): string {
    return randomBytes(32).toString('hex')
  }

  /**
   * Check if the route is exempted from CSRF protection
   */
  private isExemptRoute(url: string): boolean {
    return this.exceptRoutes.some(route => url.startsWith(route))
  }

  /**
   * Validate the CSRF token
   */
  private validateToken(request: HttpContext['request']): boolean {
    const headerToken = request.header('X-CSRF-TOKEN')
    const bodyToken = request.input('_csrf')
    const cookieToken = request.cookie('XSRF-TOKEN')
    
    // Get the token from either the header, body, or cookie
    const token = headerToken || bodyToken
    
    // No token provided
    if (!token) {
      return false
    }
    
    // Compare with the cookie token
    return token === cookieToken
  }

  async handle(ctx: HttpContext, next: NextFn) {
    const { request, response } = ctx
    const method = request.method().toUpperCase()
    const url = request.url()
    
    // Ensure route exists before CSRF validation
    if (!ctx.route) {
      Object.defineProperty(ctx, 'route', {
        value: {
          pattern: url,
          name: `${method} ${url}`,
          middleware: [],
        },
        writable: true,
        configurable: true
      })
    }

    // For GET requests, set a new CSRF token
    if (method === 'GET') {
      const token = this.generateToken()
      
      // Set the token in a cookie with SameSite=Strict
      response.cookie('XSRF-TOKEN', token, {
        httpOnly: false, // Allow JavaScript access for SPA
        secure: process.env.NODE_ENV === 'production', // Secure in production
        sameSite: 'strict', // Prevent CSRF
        path: '/',
      })
      
      // Also expose the token in a header for SPAs
      response.header('X-CSRF-TOKEN', token)
    }
    
    // For protected methods, validate the token unless the route is exempt
    if (this.protectedMethods.includes(method) && !this.isExemptRoute(url)) {
      if (!this.validateToken(request)) {
        // Log the CSRF attempt
        console.warn(`[SECURITY] CSRF attempt detected: ${method} ${url} from ${request.ip()}`)
        
        // Return 403 Forbidden
        return response.status(403).json({
          error: 'CSRF token validation failed',
          message: 'Invalid or missing CSRF token',
        })
      }
    }

    await next()
  }
}
