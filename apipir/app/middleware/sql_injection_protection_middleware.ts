import { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'

@inject()
export default class SqlInjectionProtectionMiddleware {
  // Pola SQL Injection yang lebih komprehensif
  private static readonly SQL_PATTERNS = [
    // Basic SQL Injection patterns
    /(\s|^)(union(\s|%20)+select|select(\s|%20)+.*?(\s|%20)+from|insert(\s|%20)+into|update(\s|%20)+.*?(\s|%20)+set|delete(\s|%20)+from|drop(\s|%20)+table|drop(\s|%20)+database|truncate(\s|%20)+table|alter(\s|%20)+table|exec(\s|%20)+xp_|exec(\s|%20)+sp_|declare(\s|%20)+@|select(\s|%20)+@@|waitfor(\s|%20)+delay|cast\s*?\(\s*?0x|benchmark\s*?\(\s*?\d+)/i,
    // Comment patterns
    /(\s|^)(--|#|\/\*)/,
    // Boolean-based blind patterns
    /(\s|^)(and|or)(\s|%20)+\d+=\d+/i,
    /(\s|^)(and|or)(\s|%20)+'[^']*'='[^']*'/i,
    // Time-based blind patterns
    /(\s|^)(and|or)(\s|%20)+sleep\s*?\(\s*?\d+\s*?\)/i,
    /(\s|^)(and|or)(\s|%20)+benchmark\s*?\(\s*?\d+/i,
    // Error-based patterns
    /convert\s*?\(\s*?int,/i,
    /cast\s*?\(\s*?count\s*?\(\s*?\*\s*?\)/i,
    // UNION-based patterns with obfuscation
    /union.*?select/i,
    /union.*?all.*?select/i,
    // Hex encoding
    /0x[0-9a-f]{2,}/i,
    // Advanced obfuscation techniques
    /(\s|^)(c(?:o|%[0-9A-Fa-f]{2})n(?:c|%[0-9A-Fa-f]{2})a(?:t|%[0-9A-Fa-f]{2})e(?:n|%[0-9A-Fa-f]{2})a(?:t|%[0-9A-Fa-f]{2})e)/i,
    /(\s|^)(s(?:e|%[0-9A-Fa-f]{2})l(?:e|%[0-9A-Fa-f]{2})c(?:t|%[0-9A-Fa-f]{2}))/i,
    // Double-encoded patterns
    /%25[0-9A-Fa-f]{2}/i,
  ]

  // Whitelist untuk karakter yang diperbolehkan dalam parameter
  private static readonly SAFE_PARAM_PATTERN = /^[a-zA-Z0-9_\-\.,:;@\s]+$/

  // Fungsi untuk memeriksa SQL injection di path URL
  public static checkPathForSqlInjection(url: string): boolean {
    // Decode URL untuk menangani encoding
    const decodedUrl = decodeURIComponent(url)
    
    // Periksa pola SQL injection
    return this.SQL_PATTERNS.some(pattern => pattern.test(decodedUrl))
  }

  // Fungsi untuk memeriksa SQL injection di objek (query params atau body)
  // public static checkObjectForSqlInjection(obj: any): boolean {
  //   if (!obj) return false
    
  //   // Fungsi rekursif untuk memeriksa nilai dalam objek
  //   const checkValue = (value: any): boolean => {
  //     if (typeof value === 'string') {
  //       // Decode string untuk menangani encoding
  //       const decodedValue = decodeURIComponent(value)
        
  //       // Periksa pola SQL injection
  //       if (this.SQL_PATTERNS.some(pattern => pattern.test(decodedValue))) {
  //         return true
  //       }
        
  //       // Validasi whitelist untuk parameter sederhana (opsional, bisa diaktifkan jika diperlukan)
  //       // if (value.length < 100 && !this.SAFE_PARAM_PATTERN.test(value)) {
  //       //   return true
  //       // }
  //     } else if (typeof value === 'object' && value !== null) {
  //       // Periksa objek atau array secara rekursif
  //       return Object.values(value).some(v => checkValue(v))
  //     }
      
  //     return false
  //   }
    
  //   return checkValue(obj)
  // }
  public static checkObjectForSqlInjection(obj: any): boolean {
    if (!obj) return false
    
    // Safely decode URI component without throwing errors
    const safeDecodeURIComponent = (value: string): string => {
      try {
        return decodeURIComponent(value);
      } catch (e) {
        // If decoding fails (e.g., with standalone % characters), return the original string
        return value;
      }
    };
    
    // Fungsi rekursif untuk memeriksa nilai dalam objek
    const checkValue = (value: any): boolean => {
      if (typeof value === 'string') {
        // Safely decode string untuk menangani encoding
        const decodedValue = safeDecodeURIComponent(value);
        
        // Periksa pola SQL injection
        if (this.SQL_PATTERNS.some(pattern => pattern.test(decodedValue))) {
          return true
        }
        
        // Validasi whitelist untuk parameter sederhana (opsional, bisa diaktifkan jika diperlukan)
        // if (value.length < 100 && !this.SAFE_PARAM_PATTERN.test(value)) {
        //   return true
        // }
      } else if (typeof value === 'object' && value !== null) {
        // Periksa objek atau array secara rekursif
        return Object.values(value).some(v => checkValue(v))
      }
      
      return false
    }
    
    return checkValue(obj)
  }

  // Fungsi untuk sanitasi input (gunakan dengan hati-hati, lebih baik gunakan prepared statements)
  public static sanitizeInput(input: string): string {
    if (!input) return input
    
    // Hapus karakter berbahaya
    let sanitized = input
      .replace(/'/g, "''") // Escape single quotes
      .replace(/;/g, "") // Hapus semicolons
      .replace(/--/g, "") // Hapus SQL comments
      .replace(/\/\*/g, "") // Hapus block comment start
      .replace(/\*\//g, "") // Hapus block comment end
      .replace(/#/g, "") // Hapus hash comments
      .replace(/xp_/gi, "") // Hapus stored procedures
      .replace(/sp_/gi, "") // Hapus stored procedures
    
    return sanitized
  }

  async handle(ctx: HttpContext, next: () => Promise<void>) {
    const { request, response } = ctx
    
    // Get query parameters and request body
    const queryParams = request.qs()
    const body = request.body()
    const url = request.url()
    
    // Check for SQL injection patterns in URL path
    if (SqlInjectionProtectionMiddleware.checkPathForSqlInjection(url)) {
      // Log potential attack attempt with more details
      console.warn(`[SECURITY] Potential SQL injection detected in URL path: ${url}`)
      console.warn(`[SECURITY] Client IP: ${request.ip()}, User-Agent: ${request.header('user-agent')}`)
      
      // Return 403 Forbidden
      return response.status(403).json({
        success: false,
        message: 'Request blocked due to security concerns',
        error: 'Potentially malicious input detected',
        reference_id: `sql-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`
      })
    }
    
    // Check for SQL injection patterns in query params
    if (SqlInjectionProtectionMiddleware.checkObjectForSqlInjection(queryParams)) {
      // Log potential attack attempt
      console.warn(`[SECURITY] Potential SQL injection detected in query params: ${JSON.stringify(queryParams)}`)
      console.warn(`[SECURITY] Client IP: ${request.ip()}, User-Agent: ${request.header('user-agent')}`)
      
      // Return 403 Forbidden
      return response.status(403).json({
        success: false,
        message: 'Request blocked due to security concerns',
        error: 'Potentially malicious input detected',
        reference_id: `sql-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`
      })
    }
    
    // Check for SQL injection patterns in request body
    if (SqlInjectionProtectionMiddleware.checkObjectForSqlInjection(body)) {
      // Log potential attack attempt
      console.warn(`[SECURITY] Potential SQL injection detected in request body: ${JSON.stringify(body)}`)
      console.warn(`[SECURITY] Client IP: ${request.ip()}, User-Agent: ${request.header('user-agent')}`)
      
      // Return 403 Forbidden
      return response.status(403).json({
        success: false,
        message: 'Request blocked due to security concerns',
        error: 'Potentially malicious input detected',
        reference_id: `sql-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`
      })
    }
    
    // Add security headers
    response.header('X-Content-Type-Options', 'nosniff')
    response.header('X-XSS-Protection', '1; mode=block')
    
    await next()
  }
}
