import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import type { Authenticators } from '@adonisjs/auth/types'
import { PrismaClient } from '@prisma/client'
import { DateTime } from 'luxon'

const prisma = new PrismaClient()

// Atur zona waktu sesi untuk Prisma
prisma.$connect().then(async () => {
  await prisma.$executeRaw`SET TIME ZONE 'America/New_York';`
  console.log('Prisma: Set timezone to America/New_York')
}).catch((error) => {
  console.error('Prisma: Failed to set timezone:', error)
})

/**
 * Auth middleware is used to authenticate HTTP requests.
 * - Skips authentication for GET method, except for specific routes like /user/me.
 * - Requires authentication for PUT, POST, PATCH, and DELETE methods.
 */
export default class AuthMiddleware {
  redirectTo = '/login'

  async handle(
    ctx: HttpContext,
    next: NextFn,
    options: {
      guards?: (keyof Authenticators)[]
      role?: string[]
    } = {}
  ) {
    const method = ctx.request.method().toUpperCase()
    const url = ctx.request.url()
    const authRequiredGetRoutes = ['/user/me','/csrf','/csrf/']
    
    if (method === 'GET' && !authRequiredGetRoutes.includes(url)) {
      console.log(`Skipping authentication for ${method} request to ${url}`)
      return await next()
    }

    if (['PUT', 'POST', 'PATCH', 'DELETE'].includes(method) || authRequiredGetRoutes.includes(url)) {
      try {
        await ctx.auth.authenticateUsing(options.guards, { loginRoute: this.redirectTo })
        const user = ctx.auth.user as any
        const userId = user?.currentAccessToken?.identifier
        const roleId = user?.$attributes?.role_id

        // Get timezone from environment variable or default to 'Asia/Jakarta'
        const timezone = process.env.TZ || 'Asia/Jakarta'
        // Use consistent timezone from environment settings
        const now = DateTime.local().setZone(timezone)
        // Reduce session expiration from 550 minutes to 120 minutes (2 hours) for better security
        const newTime = now.plus({ minutes: 540 }).toISO({ includeOffset: true })
        
        console.log(`Current time in ${timezone}:`, now.toISO())
        console.log('Session expiration time:', newTime)

        if (userId) {
          await prisma.auth_access_tokens.update({
            where: { id: userId },
            data: { expires_at: newTime },
          })

          // Ambil data dari database untuk memastikan zona waktu sesi diterapkan
          const updatedToken = await prisma.auth_access_tokens.findFirst({
            where: { id: userId },
          })
          console.log('Waktu Kadaluarsa dari Database (America/New_York):', updatedToken?.expires_at)
        }

        const userRole = await prisma.role.findFirst({
          where: { id: roleId },
        })
        const roleName = userRole?.role_name

        if (!options.role || options.role.length === 0 || options.role.includes(roleName!)) {
          return await next()
        }

        return ctx.response.status(401).json({ message: 'Role Unauthorized' })
      } catch (error) {
        return ctx.response.status(401).json({ message: 'Authentication failed' })
      }
    }

    return await next()
  }
}