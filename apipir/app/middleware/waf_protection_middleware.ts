import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'

interface ThreatSignature {
  name: string
  pattern: RegExp
  severity: 'low' | 'medium' | 'high' | 'critical'
  action: 'log' | 'block' | 'challenge'
  description: string
}

interface IPReputation {
  ip: string
  score: number // 0-100, higher is more suspicious
  lastSeen: number
  violations: string[]
}

@inject()
export default class WAFProtectionMiddleware {
  private readonly ipReputationStore = new Map<string, IPReputation>()
  private readonly maxReputationEntries = 10000
  private readonly reputationCleanupInterval = 24 * 60 * 60 * 1000 // 24 hours
  
  // Comprehensive threat signatures
  // Only block truly dangerous headers (not standard ones like connection/keep-alive)
  // Allow 'x-method-override' only for auth routes
  private getBlockedHeadersForPath(path: string): Set<string> {
    const isAuth = this.isAuthRoute(path);
    return isAuth
      ? new Set([
          'proxy-connection',
          'x-http-method-override',
          'te',
          'trailer',
          'upgrade',
          'via'
        ])
      : new Set([
          'proxy-connection',
          'x-http-method-override',
          'x-method-override',
          'te',
          'trailer',
          'upgrade',
          'via'
        ]);
  }
// (Removed legacy request smuggling prevention headers)

  private readonly threatSignatures: ThreatSignature[] = [
    // SQL Injection signatures
    {
      name: 'SQL_INJECTION_UNION',
      pattern: /(\bunion\b.*\bselect\b)|(\bselect\b.*\bunion\b)/i,
      severity: 'critical',
      action: 'block',
      description: 'SQL injection attempt using UNION SELECT'
    },
    {
      name: 'SQL_INJECTION_BOOLEAN',
      pattern: /(\bor\b.*1\s*=\s*1)|(\band\b.*1\s*=\s*1)|(\bor\b.*'.*'.*=.*'.*')/i,
      severity: 'critical',
      action: 'block',
      description: 'SQL injection attempt using boolean logic'
    },
    {
      name: 'SQL_INJECTION_STACKED',
      pattern: /;\s*(drop|delete|insert|update|create|alter)\b/i,
      severity: 'critical',
      action: 'block',
      description: 'SQL injection attempt using stacked queries'
    },
    
    // XSS signatures
    {
      name: 'XSS_SCRIPT_TAG',
      pattern: /<script[\s\S]*?>[\s\S]*?<\/script>/i,
      severity: 'high',
      action: 'block',
      description: 'Cross-site scripting attempt using script tags'
    },
    {
      name: 'XSS_EVENT_HANDLER',
      pattern: /on(load|click|mouseover|error|focus|blur)\s*=/i,
      severity: 'high',
      action: 'block',
      description: 'Cross-site scripting attempt using event handlers'
    },
    {
      name: 'XSS_JAVASCRIPT_PROTOCOL',
      pattern: /javascript\s*:/i,
      severity: 'high',
      action: 'block',
      description: 'Cross-site scripting attempt using javascript protocol'
    },
    
    // Command Injection signatures
    {
      name: 'COMMAND_INJECTION_BASIC',
      pattern: /[;&|`]/,
      severity: 'critical',
      action: 'block',
      description: 'Command injection attempt using shell metacharacters'
    },
    {
      name: 'COMMAND_INJECTION_UNIX',
      // Updated pattern to require more specific malicious patterns
      pattern: /\b(cat\s+[\/?].*|ls\s+[\/?].*|pwd\s*;|whoami\s*;|id\s*;|uname\s+-[a-z];|ps\s+(aux|ef)|netstat\s+-[a-z]+|ifconfig\s+[a-z0-9]+|wget\s+http|curl\s+http|nc\s+-[a-z]+|telnet\s+\d|bash\s+-[ic]|sh\s+-[ic])/i,
      severity: 'critical',
      action: 'block',
      description: 'Command injection attempt using Unix commands with suspicious parameters'
    },
    
    // Path Traversal signatures
    {
      name: 'PATH_TRAVERSAL_BASIC',
      pattern: /\.\.[\/\\]/,
      severity: 'high',
      action: 'block',
      description: 'Path traversal attempt using dot-dot-slash'
    },
    {
      name: 'PATH_TRAVERSAL_ENCODED',
      pattern: /%2e%2e[%2f%5c]/i,
      severity: 'high',
      action: 'block',
      description: 'Path traversal attempt using URL encoding'
    },
    
    // LDAP Injection signatures
    {
      name: 'LDAP_INJECTION',
      pattern: /[()&|!*]/,
      severity: 'medium',
      action: 'log',
      description: 'LDAP injection attempt'
    },
    
    // XML/XXE signatures
    {
      name: 'XXE_EXTERNAL_ENTITY',
      pattern: /<!ENTITY.*SYSTEM/i,
      severity: 'high',
      action: 'block',
      description: 'XML External Entity (XXE) attack attempt'
    },
    
    // SSRF signatures
    {
      name: 'SSRF_LOCALHOST',
      pattern: /(localhost|127\.0\.0\.1|0\.0\.0\.0|::1)/i,
      severity: 'medium',
      action: 'log',
      description: 'Server-Side Request Forgery attempt targeting localhost'
    },
    {
      name: 'SSRF_PRIVATE_IP',
      pattern: /(10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.)/,
      severity: 'medium',
      action: 'log',
      description: 'Server-Side Request Forgery attempt targeting private IP'
    },
    
    // File Inclusion signatures
    {
      name: 'LFI_ATTEMPT',
      pattern: /(\/etc\/passwd|\/proc\/self\/environ|\.\.\/)/i,
      severity: 'high',
      action: 'block',
      description: 'Local File Inclusion attempt'
    },
    
    // Scanner detection
    {
      name: 'SCANNER_USER_AGENT',
      pattern: /(sqlmap|nikto|nessus|burp|nmap|masscan|zap|gobuster|dirb|dirbuster|acunetix)/i,
      severity: 'medium',
      action: 'challenge',
      description: 'Security scanner detected'
    },
    
    // Client-Side Desync signatures
    {
      name: 'CLIENT_DESYNC_HEADER_SMUGGLING',
      pattern: /\r\n(GET|POST|PUT|DELETE|HEAD|OPTIONS|PATCH|CONNECT|TRACE)\s+\//i,
      severity: 'critical',
      action: 'block',
      description: 'Client-side desync attempt with smuggled HTTP request in headers'
    },
    {
      name: 'CLIENT_DESYNC_PATH_TRAVERSAL',
      pattern: /\/(\.\.\/)|(\%2e\%2e\%2f)|(\%252e\%252e\%252f)/i,
      severity: 'high',
      action: 'block',
      description: 'Client-side desync attempt with path traversal (including double encoding)'
    },
    {
      name: 'CLIENT_DESYNC_CRLF_INJECTION',
      pattern: /(\%0d\%0a)|(\%0D\%0A)|(\r\n)/i,
      severity: 'high',
      action: 'block',
      description: 'Client-side desync attempt with CRLF injection'
    },
    {
      name: 'CLIENT_DESYNC_BODY_SMUGGLING',
      // This pattern detects a complete HTTP request smuggled in the body
      pattern: /^(GET|POST|PUT|DELETE|HEAD|OPTIONS|PATCH|CONNECT|TRACE)\s+\/[^\s]*\s+HTTP\/[0-9.]+\r?\n/im,
      severity: 'critical',
      action: 'block',
      description: 'Client-side desync attempt with smuggled HTTP request in body'
    },
    {
      name: 'CLIENT_DESYNC_BODY_HOST_HEADER',
      pattern: /\r?\nHost:\s+[^\r\n]+\r?\n/im,
      severity: 'critical',
      action: 'block',
      description: 'Client-side desync attempt with smuggled Host header in body'
    },
    {
      name: 'CLIENT_DESYNC_CONTENT_LENGTH_MISMATCH',
      pattern: /Content-Length:\s*(?:0|[1-9]\d*)\s*(?:,|$)/i,
      severity: 'critical',
      action: 'block',
      description: 'Multiple or malformed Content-Length headers'
    },
    {
      name: 'CLIENT_DESYNC_TRANSFER_ENCODING',
      pattern: /Transfer-Encoding:\s*(?!chunked$)[^\r\n]*/i,
      severity: 'critical',
      action: 'block',
      description: 'Invalid or malformed Transfer-Encoding header'
    },
    {
      name: 'CLIENT_DESYNC_CHUNKED_ENCODING_ABUSE',
      pattern: /(?:0\r\n\r\n|\r\n0\r\n\r\n).*(?:GET|POST|PUT|DELETE) /i,
      severity: 'critical',
      action: 'block',
      description: 'Chunked encoding abuse attempt'
    },
    {
      name: 'CLIENT_DESYNC_HTTP_PIPELINING',
      pattern: /HTTP\/1\.1\r\n.*(?:GET|POST|PUT|DELETE) /is,
      severity: 'critical',
      action: 'block',
      description: 'HTTP request pipelining abuse attempt'
    }
  ]

  private updateIPReputation(ip: string, violation: string, severityScore: number): void {
    let reputation = this.ipReputationStore.get(ip)
    
    if (!reputation) {
      reputation = {
        ip,
        score: 0,
        lastSeen: Date.now(),
        violations: []
      }
    }
    
    reputation.score = Math.min(100, reputation.score + severityScore)
    reputation.lastSeen = Date.now()
    reputation.violations.push(`${new Date().toISOString()}: ${violation}`)
    
    // Keep only last 10 violations
    if (reputation.violations.length > 10) {
      reputation.violations = reputation.violations.slice(-10)
    }
    
    this.ipReputationStore.set(ip, reputation)
    
    // Cleanup old entries if store is getting too large
    if (this.ipReputationStore.size > this.maxReputationEntries) {
      this.cleanupOldReputationEntries()
    }
  }

  private cleanupOldReputationEntries(): void {
    const now = Date.now()
    const cutoff = now - this.reputationCleanupInterval
    
    for (const [ip, reputation] of this.ipReputationStore.entries()) {
      if (reputation.lastSeen < cutoff) {
        this.ipReputationStore.delete(ip)
      }
    }
  }

  private getSeverityScore(severity: string): number {
    switch (severity) {
      case 'critical': return 25
      case 'high': return 15
      case 'medium': return 10
      case 'low': return 5
      default: return 0
    }
  }

  // Helper function to identify authentication routes that should be exempted from strict checks
  // Robustly match all authentication-related routes for exemption
  private isAuthRoute(url: string): boolean {
    return (
      /\/auth(\/|$)/.test(url) ||
      /\/login(\/|$)/.test(url) ||
      /\/register(\/|$)/.test(url) ||
      /\/admin(\/|$)/.test(url) ||
      /\/be(\/|$)/.test(url) ||
      /\/be\/logout(\/|$)/.test(url) ||  // Explicitly allow logout endpoint
      /\/forgot-password(\/|$)/.test(url)
    );
  }

  private isProtectedPath(url: string): boolean {
    // Split URL into segments and check if it starts with /be/ or /admin/
    const segments = url.split('/')
    // Remove empty segments
    const cleanSegments = segments.filter(s => s.length > 0)
    if (cleanSegments.length === 0) return false

    // Check if first segment is 'be' or 'admin' or specific protected paths
    if (cleanSegments[0] === 'be' || cleanSegments[0] === 'admin') return true;
    
    // Add slider endpoint as protected path
    if (cleanSegments[0] === 'home' && cleanSegments[1] === 'slider') return true;
    
    return false;
  }

  // Helper method to detect HTML content from common text editors
  private isTextEditorContent(text: string): boolean {
    // Common patterns from text editors like CKEditor, TinyMCE, etc.
    const textEditorPatterns = [
      // Common HTML tags used in text editors
      /<(p|div|span|strong|em|u|h[1-6]|ul|ol|li|blockquote|a|img|br|hr|table|tr|td|th)[\s>]/i,
      
      // Common HTML entities
      /&(nbsp|lt|gt|amp|quot|apos|#\d+);/i,
      
      // Common inline styles from text editors
      /<[^>]+(style|class)=["'][^"']*["']/i,
      
      // Common text editor attributes
      /<[^>]+(align|src|href|alt|title|width|height)=["'][^"']*["']/i
    ];
    
    // Check if the text matches any of the patterns
    return textEditorPatterns.some(pattern => pattern.test(text));
  }

  private analyzeRequest(request: any): { threats: ThreatSignature[]; riskScore: number } {
    const threats: ThreatSignature[] = []
    let riskScore = 0
    
    // Get request data to analyze
    const url = request.url()
    const userAgent = request.header('user-agent') || ''
    const referer = request.header('referer') || ''
    const queryParams = request.qs()
    const body = request.body()
    const headers = request.headers()
    const contentType = request.header('content-type') || ''

    // Add path exclusions for common false positives
    const excludedPaths = ['/posts', '/post', '/api/posts', '/api/post'];
    if (excludedPaths.some(path => url.includes(path))) {
      // For these paths, skip COMMAND_INJECTION_UNIX check
      this.threatSignatures = this.threatSignatures.filter(sig => sig.name !== 'COMMAND_INJECTION_UNIX');
    }
    
    // Check for header-based desync indicators
    const contentLength = headers['content-length']
    const transferEncoding = headers['transfer-encoding']
    
    // Check for CL.TE or TE.CL vulnerabilities
    if (contentLength && transferEncoding) {
      threats.push({
        name: 'CLIENT_DESYNC_HEADER_MISMATCH',
        pattern: /./,
        severity: 'critical',
        action: 'block',
        description: 'Content-Length and Transfer-Encoding headers present simultaneously'
      })
      riskScore += 25 // Critical severity
    }
    
    // Check for multiple Content-Length headers
    if (Array.isArray(contentLength)) {
      threats.push({
        name: 'CLIENT_DESYNC_MULTIPLE_CL',
        pattern: /./,
        severity: 'critical',
        action: 'block',
        description: 'Multiple Content-Length headers detected'
      })
      riskScore += 25
    }
    
    // Check for invalid Transfer-Encoding
    if (transferEncoding && !/^chunked$/i.test(transferEncoding)) {
      threats.push({
        name: 'CLIENT_DESYNC_INVALID_TE',
        pattern: /./,
        severity: 'critical',
        action: 'block',
        description: 'Invalid Transfer-Encoding header value'
      })
      riskScore += 25
    }
    
    // Analyze different parts of the request with appropriate signatures
    for (const signature of this.threatSignatures) {
      // For command injection, only check query params and body data
      if (signature.name.startsWith('COMMAND_INJECTION')) {
        // For query parameters, check values only
        const queryValues = Object.values(queryParams).join(' ')
        if (signature.pattern.test(queryValues)) {
          threats.push(signature)
          riskScore += this.getSeverityScore(signature.severity)
          continue
        }
        
        // For body, only check string values
        if (body && typeof body === 'object') {
          // Process body values
          const bodyValues = Object.values(body).filter(v => typeof v === 'string');
          
          // Check each string value individually
          for (const value of bodyValues) {
            // Skip command injection check if the content appears to be from a text editor
            if (signature.name === 'COMMAND_INJECTION_BASIC' && this.isTextEditorContent(value)) {
              continue;
            }
            
            // Otherwise, apply the normal check
            if (signature.pattern.test(value)) {
              threats.push(signature);
              riskScore += this.getSeverityScore(signature.severity);
              break; // Found a threat, no need to check other values
            }
          }
        }
        continue
      }
      
      // For other threats, check all request data
      const requestData = [
        url,
        userAgent,
        referer,
        JSON.stringify(queryParams),
        JSON.stringify(request.headers()),
        body ? JSON.stringify(body) : ''
      ].join(' ')
      
      if (signature.pattern.test(requestData)) {
        threats.push(signature)
        riskScore += this.getSeverityScore(signature.severity)
      }
    }
    
    return { threats, riskScore }
  }

  private logThreatDetection(
    ip: string,
    url: string,
    method: string,
    threats: ThreatSignature[],
    riskScore: number,
    userAgent: string
  ): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event_type: 'waf_threat_detection',
      ip_address: ip,
      url: url,
      method: method,
      user_agent: userAgent,
      threats_detected: threats.map(t => ({
        name: t.name,
        severity: t.severity,
        description: t.description,
        action: t.action
      })),
      risk_score: riskScore,
      ip_reputation: this.ipReputationStore.get(ip)
    };
    
    // Log the threat detection
    console.warn('[WAF] Threat detected:', JSON.stringify(logEntry, null, 2));
  }

  // Helper function to directly check for smuggled HTTP requests in the body
  private async checkForSmuggledRequests(body: string, url: string, request: HttpContext['request']): Promise<boolean> {
    // Special handling for slider endpoint
    if (url.includes('/home/<USER>')) {
      // Enforce strict content-type and length validation for slider endpoint
      const contentType = request.header('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        return true; // Block non-JSON requests to slider endpoint
      }
    }

    const requestBody = request.body();
    const headers = request.headers();
    
    if (!requestBody) return false;
    
    // Strict header validation
    const contentLength = headers['content-length'];
    const transferEncoding = headers['transfer-encoding'];
    const connection = headers['connection'];
    
    // Block if connection: keep-alive is present with transfer-encoding
    if (connection && transferEncoding) {
      return true;
    }
    
    // Ensure content-length is a single, valid number
    if (contentLength) {
      if (Array.isArray(contentLength) || !/^\d+$/.test(contentLength)) {
        return true;
      }
    }
    
    if (contentLength && transferEncoding) {
      return true; // CL.TE or TE.CL vulnerability
    }
    
    // Check for multiple differing Content-Length headers
    if (Array.isArray(headers['content-length'])) {
      const lengths = new Set(headers['content-length']);
      if (lengths.size > 1) return true;
    }
    
    // Check for multiple or malformed Transfer-Encoding headers
    if (Array.isArray(headers['transfer-encoding'])) {
      return true; // Multiple TE headers = potential desync
    }
    
    if (transferEncoding && !/^chunked$/i.test(transferEncoding)) {
      return true; // Invalid TE value
    }
    
    // For string bodies, perform deep inspection
    if (typeof requestBody === 'string') {
      // Check for complete HTTP requests in the body
      const requestPattern = /^(GET|POST|PUT|DELETE|HEAD|OPTIONS|PATCH|CONNECT|TRACE)\s+\/[^\s]*\s+HTTP\/[0-9.]+/im;
      if (requestPattern.test(requestBody)) {
        return true;
      }
      // Check if it contains HTTP headers
      if (/\r?\nHost:\s+[^\r\n]+\r?\n/im.test(requestBody)) {
        return true;
      }
    } else {
      // For JSON bodies, stringify the object
      const rawBody = JSON.stringify(requestBody);
    }
    
    // Check for HTTP request patterns in the raw body
    const smugglingPatterns = [
      /^(GET|POST|PUT|DELETE|HEAD|OPTIONS|PATCH)\s+\/[^\s]*\s+HTTP\/[0-9.]+/im,  // HTTP method pattern
      /\r?\nHost:\s+[^\r\n]+\r?\n/im,  // Host header
      /\r?\nUser-Agent:\s+[^\r\n]+\r?\n/im,  // User-Agent header
      /\r?\nConnection:\s+[^\r\n]+\r?\n/im,  // Connection header
    ];
    
    for (const pattern of smugglingPatterns) {
      if (pattern.test(body)) {
        return true;
      }
    }
    
    return false;
  }

  public async handle({ request, response }: HttpContext, next: () => Promise<void>) {
    const ip = request.ip();
    const url = request.url();
    const method = request.method();
    const userAgent = request.header('user-agent') || '';
    
    // Add special handling for logout endpoint
    if (url.includes('/be/logout') && method === 'DELETE') {
      await next(); // Skip WAF checks for logout
      return;
    }

    // Check IP reputation first
    const reputation = this.ipReputationStore.get(ip);
    // Only block if IP reputation is very low and not accessing admin paths legitimately
    if (reputation && reputation.score > 80) {
      // Log the detection
      this.logThreatDetection(ip, url, method, [], reputation.score, userAgent);
      
      // Special handling for admin paths - check if it's a legitimate admin access
      if (url.includes('/admin/')) {
        // For admin paths, we'll reset the reputation score to allow access
        // This helps legitimate admin users who might have triggered false positives
        console.warn(`[WAF] Resetting reputation score for admin access: ${url}`);
        this.ipReputationStore.delete(ip); // Remove the IP from reputation store
      } else {
        // For non-admin paths with bad reputation, block access
        return response.status(403).send({
          error: 'Access denied - Security violation detected',
          status: 403,
          timestamp: new Date().toISOString()
        })
      }
    }

    // Direct check for smuggled HTTP requests in the body for protected paths
    if (this.isProtectedPath(url) && 
        !this.isAuthRoute(url) && 
        ['POST', 'PUT', 'PATCH', 'DELETE'].includes(method.toUpperCase())) {
      const body = await request.raw();
      const hasSmuggled = await this.checkForSmuggledRequests(body, url, request);
      if (hasSmuggled) {
        // Update IP reputation for this severe attack
        this.updateIPReputation(ip, 'DIRECT_BODY_SMUGGLING', 25);
        
        // Log the detection
        this.logThreatDetection(ip, url, method, [{
          name: 'DIRECT_BODY_SMUGGLING',
          severity: 'critical',
          action: 'block',
          description: 'Direct detection of smuggled HTTP request in body',
          pattern: /^(GET|POST|PUT|DELETE|HEAD|OPTIONS|PATCH)\s+\/[^\s]*\s+HTTP\/[0-9.]+/im
        }], 25, userAgent);
        
        return response.status(403).send({
          error: 'Request blocked: HTTP request smuggling detected',
          status: 403,
          timestamp: new Date().toISOString(),
          threat_id: 'DIRECT_BODY_SMUGGLING'
        });
      }
    }
    
    // Skip client-side desync checks for authentication routes
    const isAuth = this.isAuthRoute(url)
    const isMainBePath = url === '/be' || url === '/be/'
    
    // Analyze request for threats
    const analysis = this.analyzeRequest(request)
    
    // Filter out client-side desync false positives for sensitive paths
    let filteredThreats = analysis.threats;
    const isMainAdminPath = url === '/admin' || url === '/admin/';
    // const isMainUserPath = url === '/user' || url === '/user/';
    const isProtectedPath = url.includes('/be/') || url.includes('/admin/');
    
    if (isProtectedPath && !isAuth && !isMainBePath && !isMainAdminPath) {
      // For /be/ paths, we need to be more careful with client-side desync detection
      // to avoid false positives while still maintaining security
      filteredThreats = analysis.threats.filter(threat => {
        // If it's a client-side desync threat, apply special handling
        if (threat.name.startsWith('CLIENT_DESYNC')) {
          // For CRLF injection, only block if it's in headers or body, not in URL parameters
          // which can have legitimate uses of encoded characters
          if (threat.name === 'CLIENT_DESYNC_CRLF_INJECTION') {
            const headers = request.headers();
            const headersString = JSON.stringify(headers);
            const bodyString = typeof request.body() === 'string' ? 
              request.body() : JSON.stringify(request.body());
              
            // Only block if CRLF is found in headers or body, not in URL
            return threat.pattern.test(headersString) || threat.pattern.test(bodyString);
          }
          
          // For path traversal, check if it's a legitimate nested path or an actual attack
          if (threat.name === 'CLIENT_DESYNC_PATH_TRAVERSAL') {
            // Block only if there are actual path traversal sequences, not just nested paths
            return /(\.\.\/)|(%2e%2e%2f)/i.test(url);
          }
        }
        
        // Keep all other threats
        return true;
      });
    }
    
    if (filteredThreats.length > 0) {
      // Update IP reputation
      for (const threat of filteredThreats) {
        this.updateIPReputation(ip, threat.name, this.getSeverityScore(threat.severity))
      }
      
      // Log the detection
      this.logThreatDetection(ip, url, method, filteredThreats, analysis.riskScore, userAgent)
      
      // Determine action based on highest severity threat
      const highestSeverityThreat = filteredThreats.reduce((prev, current) => {
        const severityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 }
        return severityOrder[current.severity] > severityOrder[prev.severity] ? current : prev
      })
      
      // Take action based on threat
      switch (highestSeverityThreat.action) {
        case 'block':
          return response.status(403).send({
            error: 'Request blocked by WAF',
            status: 403,
            timestamp: new Date().toISOString(),
            threat_id: highestSeverityThreat.name
          })
          
        case 'challenge':
          // In a real implementation, this could trigger CAPTCHA or other challenges
          response.header('X-WAF-Challenge', 'required')
          response.header('X-WAF-Threat-Level', highestSeverityThreat.severity)
          break
          
        case 'log':
          // Just log and continue
          response.header('X-WAF-Monitored', 'true')
          break
      }
    }
    
    // Add WAF headers to response
    response.header('X-WAF-Status', 'active')
    response.header('X-WAF-Request-ID', `waf-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`)
    
    // Add special protection headers for /admin paths
    if (url.includes('/admin/')) {
      // Add headers to prevent client-side desync
      response.header('X-Content-Type-Options', 'nosniff')
      
      // Prevent caching of sensitive content
      response.header('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
      response.header('Pragma', 'no-cache')
      
      // Set connection close to prevent keep-alive issues
      response.header('Connection', 'close')
      
      // Add explicit content length header to prevent chunked encoding confusion
      if (!response.getHeader('Transfer-Encoding')) {
        response.header('Transfer-Encoding-Policy', 'identity-only')
      }
    }
    
    await next()
  }
}
