import { HttpContext } from '@adonisjs/core/http'
import env from '#start/env'
import { B<PERSON><PERSON> } from 'buffer'

export default class HttpSecurityMiddleware {
  private readonly MAX_CONTENT_LENGTH = 100 * 1024 * 1024 // 8MB for regular requests
  private readonly API_MAX_LENGTH = 100 * 1024 * 1024 // 20MB for API requests
  private readonly MAX_FILE_SIZE = 100 * 1024 * 1024 // 10MB for file uploads
  
  // Pola untuk deteksi HTTP Request Smuggling yang lebih komprehensif
  private readonly SMUGGLING_PATTERNS = [
    // HTTP method patterns
    /^([A-Z]+)\s+\/[^\s]*\s+HTTP\/[0-9.]+/im,  // HTTP method pattern
    /\r\n\r\n([A-Z]+)\s+\/[^\s]*\s+HTTP\/[0-9.]+/im,  // Pattern after headers
    // Header smuggling patterns
    /Content-Length:\s*\d+\s*\r\n/im,  // Embedded Content-Length header
    /Transfer-Encoding:\s*chunked/im,  // Embedded Transfer-Encoding header
    /\r\n[a-zA-Z-]+:\s*[^\r\n]+\r\n/im, // Any header pattern in body
    // Chunked encoding patterns
    /\r\n[0-9a-fA-F]+\r\n/im, // Chunk size pattern
    /0\r\n\r\n[A-Z]+\s+\/[^\s]*\s+HTTP\/[0-9.]+/im, // Zero chunk followed by request
    // Advanced patterns
    /\bGET\b|\bPOST\b|\bHEAD\b|\bPUT\b|\bDELETE\b|\bOPTIONS\b|\bTRACE\b|\bCONNECT\b/im, // HTTP methods
    /HTTP\/[0-9.]+\s+[0-9]+\s+[a-zA-Z\s]+\r\n/im, // HTTP response line
  ]
  
  // Pola untuk deteksi Client-Side Desync di URL
  private readonly DESYNC_PATTERNS = [
    // CRLF injection
    /[\r\n]/,
    // URL encoded CRLF
    /%0[dD]|%0[aA]/,
    // Double-encoded CRLF
    /%25(0[dD]|0[aA])/,
    // Path traversal (could be used in desync)
    /\.\.\//,
    // URL encoded path traversal
    /%2e%2e\//i,
    // Double-encoded path traversal
    /%252e%252e\//i,
    // Null byte injection
    /%00/,
    // Header injection attempts
    /[:\r\n]/,
    // Suspicious URL encoding patterns
    /%[0-9a-fA-F]{2}%[0-9a-fA-F]{2}/,
    // HTTP protocol confusion
    /https?:\/\//i,
  ]

  // List of security-critical headers that could be used in request smuggling
  private readonly BLOCKED_HEADERS = new Set([
    'proxy-connection',
    'x-http-method-override',
    'x-method-override',
    'te',
    'trailer',
    'upgrade',
    'via',
    'expect',
    'keep-alive',
    'proxy-authenticate',
    'proxy-authorization',
    'content-transfer-encoding', // Additional dangerous header
    'x-forwarded-protocol',      // Additional dangerous header
    'x-forwarded-scheme',        // Additional dangerous header
    'x-forwarded-ssl',           // Additional dangerous header
  ]);

  // Headers that should only be accepted from trusted proxies
  private readonly PROXY_HEADERS = new Set([
    'x-forwarded-for',
    'x-forwarded-host',
    'x-forwarded-proto',
    'forwarded',
    'x-real-ip',
    'x-client-ip',
    'cf-connecting-ip',
    'true-client-ip',
    'x-cluster-client-ip',
  ])

  // Whitelist of allowed HTTP methods
  private readonly ALLOWED_METHODS = new Set(['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'])

  // Function to check if this is an auth route
  private isAuthRoute(url: string): boolean {
    return (
      /\/auth(\/|$)/.test(url) ||
      /\/login(\/|$)/.test(url) ||
      /\/register(\/|$)/.test(url) ||
      /\/admin(\/|$)/.test(url) ||
      /\/be(\/|$)/.test(url) ||
      /\/forgot-password(\/|$)/.test(url)
    );
  }

  private isProtectedPath(url: string): boolean {
    return url.startsWith('/be/') || url.startsWith('/admin/');
  }

  // Method to check for client-side desync patterns in URL
  private hasDesyncPatterns(url: string): boolean {
    // Decode URL untuk menangani encoding
    try {
      const decodedUrl = decodeURIComponent(url);
      
      // Periksa pola desync
      for (const pattern of this.DESYNC_PATTERNS) {
        if (pattern.test(decodedUrl)) {
          console.warn(`[SECURITY] Client-side desync pattern detected: ${pattern} in URL: ${url}`);
          return true;
        }
      }
      
      // Periksa karakter yang tidak valid dalam URL
      const invalidChars = decodedUrl.match(/[^\x20-\x7E]/g);
      if (invalidChars && invalidChars.length > 0) {
        console.warn(`[SECURITY] Invalid characters detected in URL: ${url}, chars: ${invalidChars.join(',')}`);
        return true;
      }
      
      // Periksa panjang URL yang tidak wajar
      if (url.length > 2000) {
        console.warn(`[SECURITY] URL length exceeds limit: ${url.length} chars`);
        return true;
      }
      
      return false;
    } catch (error) {
      // Jika URL tidak dapat di-decode, kemungkinan ada masalah dengan encoding
      console.error(`[SECURITY] Error decoding URL: ${url}`, error);
      return true;
    }
  }

  // Enhanced validation of request headers
  private validateRequestHeaders(headers: Record<string, any>, response: HttpContext['response']): boolean {
    // Normalize all header names to lowercase for consistent processing
    const normalizedHeaders: Record<string, string | string[]> = {};
    for (const [key, value] of Object.entries(headers)) {
      normalizedHeaders[key.toLowerCase()] = value;
    }
    
    // Check for conflicting Transfer-Encoding and Content-Length headers (CL.TE, TE.CL vulnerabilities)
    if ('transfer-encoding' in normalizedHeaders && 'content-length' in normalizedHeaders) {
      response.status(400).send({
        error: 'Invalid headers: Both Transfer-Encoding and Content-Length present',
        status: 400,
        timestamp: new Date().toISOString(),
      });
      return false;
    }
    
    // Validate Transfer-Encoding header if present
    if ('transfer-encoding' in normalizedHeaders) {
      const te = normalizedHeaders['transfer-encoding'];
      const teValue = Array.isArray(te) ? te[0] : te;
      
      // Only allow 'chunked' as the value, reject any other encoding
      if (typeof teValue !== 'string' || teValue.toLowerCase() !== 'chunked') {
        response.status(400).send({
          error: 'Invalid Transfer-Encoding header',
          status: 400,
          timestamp: new Date().toISOString(),
        });
        return false;
      }
      
      // Reject multiple Transfer-Encoding headers
      if (Array.isArray(te) && te.length > 1) {
        response.status(400).send({
          error: 'Multiple Transfer-Encoding headers not allowed',
          status: 400,
          timestamp: new Date().toISOString(),
        });
        return false;
      }
    }
    
    // Validate Content-Length header if present
    if ('content-length' in normalizedHeaders) {
      const cl = normalizedHeaders['content-length'];
      const clValue = Array.isArray(cl) ? cl[0] : cl;
      
      // Ensure Content-Length is a valid positive integer
      if (typeof clValue !== 'string' || !/^\d+$/.test(clValue) || parseInt(clValue, 10) < 0) {
        response.status(400).send({
          error: 'Invalid Content-Length header',
          status: 400,
          timestamp: new Date().toISOString(),
        });
        return false;
      }
      
      // Reject multiple Content-Length headers
      if (Array.isArray(cl) && cl.length > 1) {
        response.status(400).send({
          error: 'Multiple Content-Length headers not allowed',
          status: 400,
          timestamp: new Date().toISOString(),
        });
        return false;
      }
    }
    
    // Validate Connection header
    if ('connection' in normalizedHeaders) {
      const conn = normalizedHeaders['connection'];
      const connValue = Array.isArray(conn) ? conn[0] : conn;
      
      // Only allow 'close' or 'keep-alive' as values
      if (typeof connValue !== 'string' || !['close', 'keep-alive'].includes(connValue.toLowerCase())) {
        response.status(400).send({
          error: 'Invalid Connection header',
          status: 400,
          timestamp: new Date().toISOString(),
        });
        return false;
      }
    }
    
    // Validate Host header
    if (!('host' in normalizedHeaders)) {
      response.status(400).send({
        error: 'Host header is required',
        status: 400,
        timestamp: new Date().toISOString(),
      });
      return false;
    }
    
    return true;
  }

  private isApiRequest(url: string, method: string): boolean {
    const isApiPath = url.includes('/be/') || url.includes('/api/')
    const isApiMethod = this.ALLOWED_METHODS.has(method.toUpperCase())
    return isApiPath && isApiMethod
  }

  private isValidContentLength(contentLength: string | undefined, isApi: boolean): boolean {
    if (!contentLength) return true

    const length = Number.parseInt(contentLength, 10)
    if (Number.isNaN(length) || length < 0) return false

    // Different size limits for API vs regular requests
    const maxSize = isApi ? this.API_MAX_LENGTH : this.MAX_CONTENT_LENGTH
    return length <= maxSize
  }

  // Enhanced check for trusted proxies
  private isTrustedProxy(request: HttpContext['request']): boolean {
    const clientIp = request.ip()
    
    // List of trusted proxies (consider moving to environment config)
    const trustedProxies = env.get('TRUSTED_PROXIES', '127.0.0.1,::1').split(',')
    
    // Local network patterns
    const localNetworkPatterns = ['127.', '10.', '172.16.', '172.17.', '172.18.', '172.19.', '172.20.', '172.21.', '172.22.', '172.23.', '172.24.', '172.25.', '172.26.', '172.27.', '172.28.', '172.29.', '172.30.', '172.31.', '192.168.']
    
    // Trust all proxies in production by default when using HTTPS
    // This is a common practice when behind a properly configured Nginx server
    const isProduction = env.get('NODE_ENV') === 'production'
    const isSecure = request.protocol() === 'https'
    
    // Check if the client IP is in the trusted list or matches local network patterns
    const isInTrustedList = trustedProxies.includes(clientIp)
    const isLocalNetwork = localNetworkPatterns.some(pattern => clientIp.startsWith(pattern))
    
    return isInTrustedList || isLocalNetwork || (isProduction && isSecure)
  }
  

  // Enhanced CORS headers with strict origin validation
  private setCorsHeaders(response: HttpContext['response'], request: HttpContext['request']) {
    // Get allowed domains from environment with a fallback to prevent empty list
    const configuredDomains = env.get('APP_DOMAIN', '')
    const allowedDomains = configuredDomains ? 
      configuredDomains.split(',').map((domain) => domain.trim()).filter(Boolean) : 
      []
      
    // Always include the application's own domain
    const appHost = env.get('HOST', 'localhost')
    if (!allowedDomains.includes(appHost)) {
      allowedDomains.push(appHost)
    }
    
    const requestOrigin = request.header('Origin')

    // Only set CORS headers if origin is in the allowed list
    if (requestOrigin && allowedDomains.length > 0 && allowedDomains.includes(requestOrigin)) {
      response.header('Access-Control-Allow-Origin', requestOrigin)
      response.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS')
      response.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-CSRF-Token')
      response.header('Access-Control-Allow-Credentials', 'true')
      response.header('Vary', 'Origin')
    } else if (requestOrigin && allowedDomains.length === 0) {
      // If no domains are configured, default to same-origin policy
      response.header('Access-Control-Allow-Origin', appHost)
    }
  }

  // Enhanced check for smuggled HTTP requests in body
  private async checkForSmuggledRequests(body: string, url: string, headers: Record<string, string | string[]>): Promise<boolean> {
    // Skip check for auth routes
    if (this.isAuthRoute(url)) {
      return false;
    }
    
    // Only perform deep inspection on sensitive paths
    if (!this.isProtectedPath(url)) {
      return false;
    }
    
    // Check for Content-Length/Transfer-Encoding mismatch
    const contentLength = headers['content-length'];
    const transferEncoding = headers['transfer-encoding'];
    if (contentLength && transferEncoding) {
      return true; // CL.TE or TE.CL vulnerability
    }
    
    // Check for multiple differing Content-Length headers
    if (Array.isArray(headers['content-length'])) {
      const lengths = new Set(headers['content-length']);
      if (lengths.size > 1) return true;
    }
    
    // Check for multiple Transfer-Encoding headers
    if (Array.isArray(headers['transfer-encoding'])) {
      return true; // Multiple TE headers = potential desync
    }
    
    // Check for a complete HTTP request in the body
    const smuggledRequestPatterns = [
      // Complete HTTP request patterns
      /(?:GET|POST|PUT|DELETE|HEAD|OPTIONS|PATCH|CONNECT|TRACE)\s+\/[^\s]+\s+HTTP\/[\d\.]+[\s\S]*Host:\s*[^\r\n]+/i,
      
      // Suspicious header injections
      /\r?\n(?:Host|Content-Length|Transfer-Encoding|Connection):\s*[^\r\n]+/i,
      
      // HTTP request splitting
      /[\r\n](?:GET|POST|PUT|DELETE) [^\r\n]+/i,
      
      // Chunked encoding abuse
      /(?:0\r\n\r\n|\r\n0\r\n\r\n).*(?:GET|POST|PUT|DELETE) /i,
      
      // HTTP pipelining abuse
      /HTTP\/1\.1\r\n.*(?:GET|POST|PUT|DELETE) /is
    ];
    
    // Check body content
    if (typeof body === 'string') {
      // Check for oversized chunks in chunked encoding
      if (typeof transferEncoding === 'string' && transferEncoding.toLowerCase().includes('chunked')) {
        const chunks = body.split(/\r\n/);
        for (let i = 0; i < chunks.length; i += 2) {
          const size = parseInt(chunks[i], 16);
          if (size > 1024 * 1024) { // 1MB chunk size limit
            return true;
          }
        }
      }
      
      // Check for smuggled requests
      return smuggledRequestPatterns.some(pattern => pattern.test(body));
    }
    
    return false;
  }
  
  // Set enhanced security headers for /be paths to prevent client-side desync
  private setBePathSecurityHeaders(response: HttpContext['response'], url: string) {
    // Apply to all /be and /be/** paths (not just /be/)
    if (!(url === '/be' || url === '/be/' || url.startsWith('/be/'))) {
      return;
    }

    // Strict anti-caching headers
    response.header('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    response.header('Pragma', 'no-cache');
    response.header('Expires', '0');
    
    // Connection control headers
    response.header('Connection', 'close');
    response.header('Keep-Alive', 'timeout=1, max=1');
    
    // Content security headers
    response.header('Content-Security-Policy', "default-src 'self'");
    response.header('X-Content-Type-Options', 'nosniff');
    
    // Anti-desync headers
    response.header('Transfer-Encoding', 'chunked'); // Force chunked to prevent length confusion
    response.header('X-Frame-Options', 'DENY');
    response.header('X-XSS-Protection', '1; mode=block');
    response.header('Referrer-Policy', 'strict-origin-when-cross-origin');
    response.header('Surrogate-Control', 'no-store');

    // Remove Transfer-Encoding and enforce identity encoding
    response.removeHeader('Transfer-Encoding');
    response.header('Transfer-Encoding-Policy', 'identity-only');

    // Force content type for all /be responses
    response.header('Content-Type', 'application/json; charset=utf-8');

    // Prevent MIME sniffing and other browser attacks
    response.header('X-Content-Type-Options', 'nosniff');
    response.header('X-Frame-Options', 'DENY');
    response.header('X-XSS-Protection', '1; mode=block');
    response.header('Content-Security-Policy', "default-src 'self'");
    response.header('X-Download-Options', 'noopen');
    response.header('X-DNS-Prefetch-Control', 'off');

    // Explicitly close connection to prevent keep-alive issues
    response.header('Connection', 'close');

    // Set a safe Content-Length header if not present
    if (!response.getHeader('Content-Length')) {
      response.header('Content-Length', '0'); // fallback for static responses
    }
  }
  
  async handle(ctx: HttpContext, next: () => Promise<void>) {
    const { request, response } = ctx;
    const url = request.url();
    const method = request.method();

    // Early SQLi path check for /be/ and /admin/ subpaths
    if (this.isProtectedPath(url)) {
      // Dynamically import the SQLi middleware class to avoid circular dependency
      const { default: SqlInjectionProtectionMiddleware } = await import('./sql_injection_protection_middleware.js');
      // Use static method for early SQLi blocking
      if (SqlInjectionProtectionMiddleware.checkPathForSqlInjection(url)) {
        response.status(403).send('Blocked: Potential SQL injection detected in URL path.');
        return;
      }
    }

    // Enhanced header validation for desync/smuggling mitigation
    if (!this.validateRequestHeaders(request.headers(), response)) {
      // Response already sent, block further processing
      return;
    }

    if (this.isProtectedPath(url) && ['POST', 'PUT', 'PATCH'].includes(method)) {
      const contentType = request.header('content-type') || '';
      if (!contentType.includes('application/json')  && !contentType.includes('multipart/form-data')) {
        return response.status(415).send('Invalid Content-Type');
      }
    }

    // Block dangerous headers for protected paths (CSD/smuggling mitigation)
    if (this.isProtectedPath(url)) {
      // Allow x-method-override ONLY for authentication routes
      const isAuth = this.isAuthRoute(url);
      const dangerousHeaders = isAuth
        ? ['te', 'trailer', 'upgrade', 'proxy-connection', 'x-http-method-override', 'via']
        : ['te', 'trailer', 'upgrade', 'proxy-connection', 'x-http-method-override', 'x-method-override', 'via'];
      for (const h of dangerousHeaders) {
        if (h in request.headers()) {
          return response.status(400).send(`Blocked dangerous header: ${h}`);
        }
      }
      // Block both Content-Length and Transfer-Encoding present
      const headers = request.headers();
      if ('content-length' in headers && 'transfer-encoding' in headers) {
        return response.status(400).send('Conflicting Content-Length and Transfer-Encoding headers');
      }
    }
    
    // 1. Validate HTTP method - reject non-standard methods
    if (!this.ALLOWED_METHODS.has(method.toUpperCase())) {
      return response.status(405).send({
        error: 'Method not allowed',
        status: 405,
        timestamp: new Date().toISOString(),
      })
    }
    
    // 2. Check for blocked headers that could be used in request smuggling
    const headers = request.headers()
    for (const headerName of Object.keys(headers)) {
      const lowerHeaderName = headerName.toLowerCase()
      
      // Always block security-critical headers
      if (this.BLOCKED_HEADERS.has(lowerHeaderName)) {
        return response.status(400).send({
          error: 'Request contains blocked headers',
          status: 400,
          timestamp: new Date().toISOString(),
        })
      }
      
      // Allow proxy headers only from trusted sources
      if (this.PROXY_HEADERS.has(lowerHeaderName)) {
        const isTrustedProxy = this.isTrustedProxy(request)
        if (!isTrustedProxy) {
          return response.status(400).send({
            error: 'Proxy headers only allowed from trusted sources',
            status: 400,
            timestamp: new Date().toISOString(),
          })
        }
      }
    }
    
    const contentTypeHeader = request.header('content-type') || ''
    const rawBody = request.raw() || ''

    // 3. Strict validation of Transfer-Encoding and Content-Length headers
    const contentLengthHeader = request.header('content-length')
    const transferEncodingHeader = request.header('transfer-encoding')

    // Reject any form of Transfer-Encoding to prevent request smuggling
    if (transferEncodingHeader) {
      return response.status(400).send({
        error: 'Transfer-Encoding header is not allowed',
        status: 400,
        timestamp: new Date().toISOString(),
      })
    }

    // 4. Set CORS headers with strict origin validation
    this.setCorsHeaders(response, request)

    // 5. Check for request smuggling patterns in the body
    // Look for HTTP methods followed by a path and then an HTTP version
    const smugglingPatterns = [
      /^([A-Z]+)\s+\/[^\s]*\s+HTTP\/[0-9.]+/im,  // HTTP method pattern
      /\r\n\r\n([A-Z]+)\s+\/[^\s]*\s+HTTP\/[0-9.]+/im,  // Pattern after headers
      /Content-Length:\s*\d+\s*\r\n/im,  // Embedded Content-Length header
      /Transfer-Encoding:\s*chunked/im,  // Embedded Transfer-Encoding header
    ];
    
    for (const pattern of smugglingPatterns) {
      if (pattern.test(rawBody)) {
        return response.status(400).send({
          error: 'Potential HTTP request smuggling detected',
          status: 400,
          timestamp: new Date().toISOString(),
        })
      }
    }
    
    // Check for client-side desync patterns in URL for protected paths
    if (this.isProtectedPath(url) && this.hasDesyncPatterns(url)) {
      return response.status(400).send({
        error: 'Potential client-side desync attack detected',
        status: 400,
        timestamp: new Date().toISOString(),
      })
    }

    const isApi = this.isApiRequest(url, method)

    // 6. Set comprehensive security headers for all responses
    response.header('X-Content-Type-Options', 'nosniff')
    response.header('X-Frame-Options', 'DENY')
    response.header('X-XSS-Protection', '1; mode=block')
    response.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')
    response.header('Referrer-Policy', 'strict-origin-when-cross-origin')
    response.header('Permissions-Policy', 'camera=(), microphone=(), geolocation=(), interest-cohort=()')
    
    // Add Expect-CT header for Certificate Transparency monitoring
    response.header('Expect-CT', 'max-age=86400, enforce, report-uri="https://example.com/ct-report"')
    
    // Add additional security headers
    response.header('X-Permitted-Cross-Domain-Policies', 'none')
    response.header('Cross-Origin-Embedder-Policy', 'require-corp')
    response.header('Cross-Origin-Opener-Policy', 'same-origin')
    response.header('Cross-Origin-Resource-Policy', 'same-origin')
    
    // 7. Standardize connection handling to prevent desync
    response.header('Connection', 'close')
    
    // Apply enhanced security headers for /be paths to prevent client-side desync
    this.setBePathSecurityHeaders(response, url)
    
    // 8. Validate Content-Length
    if (contentLengthHeader) {
      if (!this.isValidContentLength(contentLengthHeader, isApi)) {
        return response.status(413).send({
          error: 'Content length exceeds limit',
          status: 413,
          timestamp: new Date().toISOString(),
        })
      }
      
      // Validate that Content-Length is a positive integer
      const length = Number.parseInt(contentLengthHeader, 10)
      if (isNaN(length) || length < 0 || length.toString() !== contentLengthHeader.trim()) {
        return response.status(400).send({
          error: 'Invalid Content-Length header',
          status: 400,
          timestamp: new Date().toISOString(),
        })
      }
    }

    // 9. Skip body validation for GET/HEAD/OPTIONS requests
    if (['GET', 'HEAD', 'OPTIONS'].includes(method.toUpperCase())) {
      await next()
      return
    }

    // 10. Allow empty bodies for DELETE requests
    if (method.toUpperCase() === 'DELETE' && !contentLengthHeader) {
      await next()
      return
    }

    // 11. Validate request body for write operations
    if (['PUT', 'PATCH', 'POST'].includes(method.toUpperCase())) {
      // Ensure Content-Type is set and valid
      if (!contentTypeHeader) {
        return response.status(415).send({
          error: 'Content-Type header is required',
          status: 415,
          timestamp: new Date().toISOString(),
        })
      }
      
      // Validate content type based on endpoint
      const validContentTypes = [
        'application/json',
        'application/x-www-form-urlencoded',
        'multipart/form-data',
        'text/plain'
      ]
      
      const hasValidContentType = validContentTypes.some(type => 
        contentTypeHeader.toLowerCase().startsWith(type.toLowerCase())
      )
      
      if (!hasValidContentType) {
        return response.status(415).send({
          error: 'Unsupported Media Type',
          status: 415,
          timestamp: new Date().toISOString(),
        })
      }
      
      // For non-multipart requests, validate body size
      if (!contentTypeHeader.toLowerCase().startsWith('multipart/form-data')) {
        const bodyLength = Buffer.byteLength(rawBody)
        const maxLength = isApi ? this.API_MAX_LENGTH : this.MAX_CONTENT_LENGTH

        if (bodyLength > maxLength) {
          return response.status(413).send({
            error: 'Payload too large',
            status: 413,
            timestamp: new Date().toISOString(),
          })
        }
        
        // Validate Content-Length against actual body size
        if (contentLengthHeader) {
          const declaredLength = Number.parseInt(contentLengthHeader, 10)
          // Allow for a small difference to account for encoding issues
          const allowedDiff = 128 // Strict 128 byte tolerance

          if (Math.abs(declaredLength - bodyLength) > allowedDiff) {
            return response.status(400).send({
              error: 'Content-Length mismatch with actual body size',
              status: 400,
              timestamp: new Date().toISOString(),
            })
          }
        }
      } else {
        // For multipart/form-data, we'll rely on the framework's built-in limits
        // but ensure a Content-Length header is present
        if (!contentLengthHeader) {
          return response.status(411).send({
            error: 'Length Required for multipart requests',
            status: 411,
            timestamp: new Date().toISOString(),
          })
        }
      }
    }

    await next()
  }
}
