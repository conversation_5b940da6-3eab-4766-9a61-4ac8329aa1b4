import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'

/**
 * This middleware ensures that ctx.route is always defined
 * to prevent errors in other middleware that depend on it,
 * particularly the Shield middleware's CSRF protection.
 * 
 * The error "Cannot read properties of undefined (reading 'pattern')"
 * occurs when adding data to link_terkait because ctx.route is undefined
 * in some cases.
 */
export default class RouteGuardMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    try {
      // Ensure route exists before other middleware that might need it
      if (!ctx.route) {
        const url = ctx.request.url()
        const method = ctx.request.method()
        
        // Create a minimal route object with the required properties
        Object.defineProperty(ctx, 'route', {
          value: {
            pattern: url,
            name: `${method} ${url}`,
            middleware: [],
          },
          writable: true,
          configurable: true
        })
        
        // Log that we had to create a route object
        console.debug(`[RouteGuard] Created route object for ${method} ${url}`)
      }
      
      await next()
    } catch (error) {
      // If there's still an error with ctx.route, log it and continue
      if (error.message && error.message.includes('undefined (reading \'pattern\')')) {
        console.error('[RouteGuard] Error with ctx.route despite middleware:', error.message)
        // Try to recover and continue
        await next()
      } else {
        // For other errors, rethrow
        throw error
      }
    }
  }
}
