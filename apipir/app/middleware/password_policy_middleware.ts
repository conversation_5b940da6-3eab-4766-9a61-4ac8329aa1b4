import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'

/**
 * Password Policy Middleware
 * 
 * This middleware enforces strong password policies for user registration
 * and password change operations. It validates passwords against a set of
 * rules to ensure they meet security requirements.
 */
export default class PasswordPolicyMiddleware {
  /**
   * Minimum password length
   */
  private minLength = 10
  
  /**
   * Password routes that should be validated
   */
  private passwordRoutes = [
    '/api/auth/register',
    '/api/auth/signup',
    '/api/users',
    '/api/auth/password/reset',
    '/api/auth/password/change',
    '/api/account/password',
  ]
  
  /**
   * Check if the route should have password validation
   */
  private shouldValidatePassword(url: string, method: string): boolean {
    if (!['POST', 'PUT', 'PATCH'].includes(method)) {
      return false
    }
    
    return this.passwordRoutes.some(route => url.startsWith(route))
  }
  
  /**
   * Validate password strength
   */
  private validatePassword(password: string): { valid: boolean; message: string } {
    if (!password) {
      return { valid: false, message: 'Password is required' }
    }
    
    if (password.length < this.minLength) {
      return { 
        valid: false, 
        message: `Password must be at least ${this.minLength} characters long` 
      }
    }
    
    // Check for uppercase letters
    if (!/[A-Z]/.test(password)) {
      return { 
        valid: false, 
        message: 'Password must contain at least one uppercase letter' 
      }
    }
    
    // Check for lowercase letters
    if (!/[a-z]/.test(password)) {
      return { 
        valid: false, 
        message: 'Password must contain at least one lowercase letter' 
      }
    }
    
    // Check for numbers
    if (!/\d/.test(password)) {
      return { 
        valid: false, 
        message: 'Password must contain at least one number' 
      }
    }
    
    // Check for special characters
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      return { 
        valid: false, 
        message: 'Password must contain at least one special character' 
      }
    }
    
    // Check for common passwords (simplified version)
    const commonPasswords = [
      'password', 'admin', '123456', 'qwerty', 'welcome',
      'letmein', 'monkey', 'password123', 'abc123', '111111'
    ]
    
    if (commonPasswords.includes(password.toLowerCase())) {
      return { 
        valid: false, 
        message: 'Password is too common and easily guessable' 
      }
    }
    
    // Check for sequential characters
    if (/(?:abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz|012|123|234|345|456|567|678|789)/.test(password.toLowerCase())) {
      return { 
        valid: false, 
        message: 'Password contains sequential characters' 
      }
    }
    
    // Check for repeated characters
    if (/(.)\1{2,}/.test(password)) {
      return { 
        valid: false, 
        message: 'Password contains too many repeated characters' 
      }
    }
    
    return { valid: true, message: 'Password meets all requirements' }
  }
  
  /**
   * Handle the request and validate password if needed
   */
  async handle(ctx: HttpContext, next: NextFn) {
    const { request, response } = ctx
    const method = request.method().toUpperCase()
    const url = request.url()
    
    if (this.shouldValidatePassword(url, method)) {
      const body = request.body()
      
      // Look for password field in various formats
      const password = body.password || body.user?.password || body.data?.password
      
      if (password) {
        const validation = this.validatePassword(password)
        
        if (!validation.valid) {
          return response.status(400).json({
            error: 'Password policy violation',
            message: validation.message,
            requirements: {
              minLength: this.minLength,
              requireUppercase: true,
              requireLowercase: true,
              requireNumbers: true,
              requireSpecialChars: true,
              preventCommonPasswords: true,
              preventSequentialChars: true,
              preventRepeatedChars: true
            }
          })
        }
      }
    }
    
    await next()
  }
}
