import { HttpContext } from '@adonisjs/core/http'
import { Buffer } from 'buffer'

/**
 * Middleware specifically designed to prevent client-side desync attacks
 * Implements strict validation of HTTP message framing to prevent request smuggling
 */
export default class DesyncProtectionMiddleware {
  /**
   * Maximum allowed difference between declared Content-Length and actual body size
   */
  private readonly CONTENT_LENGTH_TOLERANCE = 32 // bytes

  /**
   * Maximum number of headers allowed in a request
   */
  private readonly MAX_HEADERS_COUNT = 50

  /**
   * Patterns that indicate potential desync attacks
   */
  private readonly DESYNC_PATTERNS = [
    // HTTP pipelining attempts
    /HTTP\/[0-9.]+.*HTTP\/[0-9.]+/i,
    // Multiple requests in one
    /(?:GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS).*(?:GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)/i,
    // Newlines in suspicious places
    /[\r\n](?:GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)/i,
    // Common smuggling patterns
    /0\r\n\r\n(?:GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)/i,
    // Enhanced patterns for comprehensive detection
    /(?:GET|POST|PUT|DELETE|HEAD|OPTIONS|CONNECT|TRACE|PATCH) \/[^ ]+ HTTP\/[0-9.]+/,
    /^[0-9a-fA-F]+\r\n/m, // Chunked encoding markers
    /\r\n\r\n.*(?:GET|POST|PUT|DELETE|HEAD|OPTIONS|CONNECT|TRACE|PATCH)/s,
    /Content-Length:\s*\d+.*\r\n.*Content-Length:\s*\d+/is, // Duplicate Content-Length
    /Transfer-Encoding:\s*chunked.*Content-Length:/is, // Conflicting headers
    /\x00/, // Null bytes
    /%(?:25)?0[dDaA]/, // Encoded CRLF
    /%(?:25)?2[eE]%(?:25)?2[eE](?:%(?:25)?2[fF]|\/)/, // Path traversal
  ]

  /**
   * Suspicious header patterns that might indicate CSD attempts
   */
  private readonly SUSPICIOUS_HEADER_PATTERNS = [
    /transfer.{0,10}encoding/i,
    /content.{0,10}length/i,
    /host.{0,5}:/i,
    /connection.{0,5}:/i,
  ]

  /**
   * Headers that should never have multiple values
   */
  private readonly SINGLETON_HEADERS = new Set([
    'content-length',
    'transfer-encoding',
    'host',
    'connection',
    'content-type'
  ])

  /**
   * Detect multiple instances of singleton headers
   */
  private detectDuplicateHeaders(headers: Record<string, any>): boolean {
    const headerCounts = new Map<string, number>()

    // Count headers by normalizing keys to lowercase
    for (const key of Object.keys(headers)) {
      const lowerKey = key.toLowerCase()
      headerCounts.set(lowerKey, (headerCounts.get(lowerKey) || 0) + 1)
    }

    // Check for duplicates in singleton headers
    for (const [header, count] of headerCounts.entries()) {
      if (this.SINGLETON_HEADERS.has(header) && count > 1) {
        console.warn(`[SECURITY] Duplicate singleton header detected: ${header} (${count} instances)`)
        return true
      }
    }

    // Check for excessive headers count (potential DoS)
    if (headerCounts.size > this.MAX_HEADERS_COUNT) {
      console.warn(`[SECURITY] Excessive header count: ${headerCounts.size}`)
      return true
    }

    return false
  }

  /**
   * Validate Content-Length header format and value
   */
  private validateContentLength(contentLength: string | undefined): boolean {
    if (!contentLength) return true

    // Must be a valid positive integer with no leading zeros
    if (!/^[1-9][0-9]*$|^0$/.test(contentLength)) {
      console.warn(`[SECURITY] Invalid Content-Length format: ${contentLength}`)
      return false
    }

    // Parse and validate the value
    const length = parseInt(contentLength, 10)
    if (isNaN(length) || length < 0 || length > 100 * 1024 * 1024) { // 100MB max
      console.warn(`[SECURITY] Content-Length out of acceptable range: ${contentLength}`)
      return false
    }

    return true
  }

  /**
   * Validate Transfer-Encoding header
   */
  private validateTransferEncoding(transferEncoding: string | undefined): boolean {
    if (!transferEncoding) return true

    // Normalize and validate
    const normalized = transferEncoding.toLowerCase().trim()

    // Only allow simple "chunked" value, nothing else
    if (normalized !== 'chunked' && normalized !== 'identity') {
      console.warn(`[SECURITY] Invalid Transfer-Encoding value: ${transferEncoding}`)
      return false
    }

    return true
  }

  /**
   * Check for conflicting headers that could enable request smuggling
   */
  private detectConflictingHeaders(headers: Record<string, any>): boolean {
    const contentLength = headers['content-length']
    const transferEncoding = headers['transfer-encoding']

    // Both Content-Length and Transfer-Encoding present (CL.TE vulnerability)
    if (contentLength && transferEncoding) {
      console.warn('[SECURITY] Both Content-Length and Transfer-Encoding headers present')
      return true
    }

    return false
  }

  /**
   * Validate HTTP protocol version
   */
  private validateHttpProtocol(protocol: string): boolean {
    // Accept both lowercase 'http'/'https' (from AdonisJS) and standard HTTP/x.x format
    return protocol === 'http' || protocol === 'https' || /^HTTP\/(1\.0|1\.1|2\.0)$/.test(protocol)
  }

  /**
   * Detect HTTP request smuggling in URL
   */
  private detectUrlSmuggling(url: string): boolean {
    // Check for encoded/double-encoded newlines
    if (/%(?:25)?0[dDaA]/.test(url)) {
      return true
    }

    // Check for path traversal (could be used in desync)
    if (/%(?:25)?2[eE]%(?:25)?2[eE](?:%(?:25)?2[fF]|\/)/.test(url)) {
      return true
    }

    // Check for null bytes
    if (/%00/.test(url)) {
      return true
    }

    return false
  }

  /**
   * Validate header names for RFC compliance
   */
  private validateHeaderNames(headers: Record<string, any>): boolean {
    for (const key of Object.keys(headers)) {
      // Header names should only contain ASCII printable characters
      if (!/^[\x21-\x7E]+$/.test(key)) {
        return false
      }

      // Disallow spaces and control characters
      if (/[\s\x00-\x1F\x7F]/.test(key)) {
        return false
      }

      // Check for suspicious header patterns
      for (const pattern of this.SUSPICIOUS_HEADER_PATTERNS) {
        if (pattern.test(key) && !['transfer-encoding', 'content-length', 'host', 'connection'].includes(key.toLowerCase())) {
          console.warn(`[SECURITY] Suspicious header name detected: ${key}`)
          return false
        }
      }
    }

    return true
  }

  /**
   * Detect obfuscated or malformed headers
   */
  private detectObfuscatedHeaders(headers: Record<string, any>): boolean {
    // Skip header validation for certain production domains
    const host = headers['host'] || ''
    const isProductionDomain = typeof host === 'string' &&
      (host.includes('.com') || host.includes('.net') || host.includes('.org') ||
        host.includes('.io') || host.includes('.app') || host.includes('.dev'))

    // Less strict validation for production domains
    const headerValueMaxLength = isProductionDomain ? 8192 : 4096

    for (const key of Object.keys(headers)) {
      const value = headers[key]

      // Skip validation for certain headers in production
      if (isProductionDomain && ['x-forwarded-for', 'x-real-ip', 'forwarded', 'via'].includes(key.toLowerCase())) {
        continue
      }

      // Check for newlines in header values
      if (typeof value === 'string' && /[\r\n]/.test(value)) {
        console.warn(`[SECURITY] Newline character in header value: ${key}`)
        return true
      }

      // Check for extremely long header values (potential DoS)
      if (typeof value === 'string' && value.length > headerValueMaxLength) {
        console.warn(`[SECURITY] Excessively long header value: ${key} (${value.length} chars)`)
        return true
      }
    }

    return false
  }

  /**
   * Check for smuggled requests in the body
   */
  private detectSmuggledRequests(body: string | Buffer): boolean {
    if (!body) return false

    const bodyStr = typeof body === 'string' ? body : body.toString('utf-8', 0, 8192) // Check first 8KB

    // Check for HTTP request patterns in body
    for (const pattern of this.DESYNC_PATTERNS) {
      if (pattern.test(bodyStr)) {
        console.warn(`[SECURITY] Potential smuggled request detected: ${pattern}`)
        return true
      }
    }

    return false
  }

  /**
   * Set secure response headers to prevent desync
   */
  private setSecureResponseHeaders(response: HttpContext['response']): void {
    // Force connection closure to prevent connection reuse attacks
    response.header('Connection', 'close')

    // Remove headers that could be used in desync attacks
    response.removeHeader('Transfer-Encoding')
    response.removeHeader('Keep-Alive')
    response.removeHeader('Trailer')

    // Set security headers
    response.header('X-Content-Type-Options', 'nosniff')
  }

  /**
   * Set enhanced security headers for /be paths
   */
  private setBePathSecurityHeaders(response: HttpContext['response']): void {
    // Strict anti-caching headers
    response.header('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
    response.header('Pragma', 'no-cache')
    response.header('Expires', '0')
    response.header('Surrogate-Control', 'no-store')

    // Prevent chunked encoding confusion
    response.removeHeader('Transfer-Encoding')

    // Prevent MIME sniffing
    response.header('X-Content-Type-Options', 'nosniff')
  }

  /**
   * Validate Content-Length against actual body size
   */
  private validateBodySize(contentLength: string | undefined, body: Buffer | string): boolean {
    if (!contentLength) return true

    const declaredLength = parseInt(contentLength, 10)
    const actualLength = Buffer.byteLength(body)

    // Allow for a small difference to account for encoding issues
    if (Math.abs(declaredLength - actualLength) > this.CONTENT_LENGTH_TOLERANCE) {
      console.warn(`[SECURITY] Content-Length mismatch: declared=${declaredLength}, actual=${actualLength}`)
      return false
    }

    return true
  }

  /**
   * Main middleware handler
   */
  public async handle(ctx: HttpContext, next: () => Promise<void>): Promise<void> {
    const { request, response } = ctx
    const url = request.url()
    const method = request.method()
    const headers = request.headers()
    const rawBody = request.raw() || ''

    // 1. Validate HTTP protocol
    if (!this.validateHttpProtocol(request.protocol())) {
      return response.status(400).send({
        error: 'Invalid HTTP protocol',
        code: 'INVALID_HTTP_PROTOCOL',
        timestamp: new Date().toISOString()
      })
    }

    // 2. Check for URL smuggling patterns
    if (this.detectUrlSmuggling(url)) {
      return response.status(400).send({
        error: 'Invalid URL format',
        code: 'INVALID_URL_FORMAT',
        timestamp: new Date().toISOString()
      })
    }

    // 3. Validate header names
    if (!this.validateHeaderNames(headers)) {
      return response.status(400).send({
        error: 'Invalid header names',
        code: 'INVALID_HEADER_NAMES',
        timestamp: new Date().toISOString()
      })
    }

    // 4. Detect duplicate headers
    if (this.detectDuplicateHeaders(headers)) {
      return response.status(400).send({
        error: 'Invalid request headers',
        code: 'DUPLICATE_HEADERS',
        timestamp: new Date().toISOString()
      })
    }

    // 5. Validate Content-Length header
    const contentLength = headers['content-length']
    if (!this.validateContentLength(contentLength)) {
      return response.status(400).send({
        error: 'Invalid Content-Length header',
        code: 'INVALID_CONTENT_LENGTH',
        timestamp: new Date().toISOString()
      })
    }

    // 6. Validate Transfer-Encoding header
    const transferEncoding = headers['transfer-encoding']
    if (!this.validateTransferEncoding(transferEncoding)) {
      return response.status(400).send({
        error: 'Invalid Transfer-Encoding header',
        code: 'INVALID_TRANSFER_ENCODING',
        timestamp: new Date().toISOString()
      })
    }

    // 7. Check for conflicting headers
    if (this.detectConflictingHeaders(headers)) {
      return response.status(400).send({
        error: 'Conflicting headers detected',
        code: 'CONFLICTING_HEADERS',
        timestamp: new Date().toISOString()
      })
    }

    // 8. Check for obfuscated headers
    if (this.detectObfuscatedHeaders(headers)) {
      return response.status(400).send({
        error: 'Invalid header format',
        code: 'INVALID_HEADER_FORMAT',
        timestamp: new Date().toISOString()
      })
    }

    // 9. Check for request smuggling in body for write operations
    if (['POST', 'PUT', 'PATCH'].includes(method) && rawBody) {
      if (this.detectSmuggledRequests(rawBody)) {
        return response.status(400).send({
          error: 'Invalid request body',
          code: 'INVALID_REQUEST_BODY',
          timestamp: new Date().toISOString()
        })
      }

      // 10. Validate Content-Length against actual body size
      if (!this.validateBodySize(contentLength, rawBody)) {
        return response.status(400).send({
          error: 'Content-Length mismatch with actual body size',
          code: 'CONTENT_LENGTH_MISMATCH',
          timestamp: new Date().toISOString()
        })
      }
    }

    // 11. Set secure response headers
    this.setSecureResponseHeaders(response)

    // 12. Apply enhanced security for /be paths
    if (url.startsWith('/be/')) {
      this.setBePathSecurityHeaders(response)
    }

    // 13. Add request timestamp validation
    const requestTime = Date.now()
    response.header('X-Request-Time', requestTime.toString())

    // Continue to next middleware
    await next()

    // 14. Ensure Content-Length is set in response if possible
    const responseBody = response.getBody()
    if (responseBody && !response.getHeader('Content-Length')) {
      try {
        let bodySize = 0

        // Handle different types of response bodies
        if (typeof responseBody === 'string') {
          bodySize = Buffer.byteLength(responseBody)
        } else if (responseBody instanceof Buffer || responseBody instanceof ArrayBuffer) {
          bodySize = Buffer.byteLength(responseBody)
        } else if (typeof responseBody === 'object') {
          // Convert object to JSON string and get its size
          const jsonString = JSON.stringify(responseBody)
          bodySize = Buffer.byteLength(jsonString)
        }

        if (bodySize > 0) {
          response.header('Content-Length', bodySize.toString())
        }
      } catch (error) {
        console.error('Error calculating Content-Length:', error)
        // Continue without setting Content-Length if there's an error
      }
    }
  }
}
