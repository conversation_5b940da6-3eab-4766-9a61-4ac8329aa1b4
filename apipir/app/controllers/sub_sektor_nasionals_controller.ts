import type { HttpContext } from '@adonisjs/core/http'

import { configUpload, get_paginate, img_to_webp, upload, upload_img_to_webp } from '../helpers/global_helper.js';
import { createPeluangValidator } from '#validators/peluang';
import { createSektorNasionalValidator } from '#validators/sektor_nasional';
import { createSubSektorNasionalValidator, updateSubSektorNasionalValidator } from '#validators/sub_sektor_nasional';
import { aproveValidator } from '#validators/aprove';
import env from '#start/env';
import striptags from 'striptags';
import prisma from '../lib/prisma.js';
export default class SubSektorNasionalsController {

    /**
   * Display a list of resource
   */
  async index({}: HttpContext) {
    const data = await prisma.tb_sub_sektor_nasional.findMany({
        take: 100,
        include: {
            sub_sektor_nasional_value: {
                include: {
                    sub_sektor_nasional_value_detail: true,
                    sub_sektor_nasional_value_tr: true
                }
            }
        }
    });

    return {
        success : true,
        data : {data}
    }
  }

  async get_paginate({params,response,request}:HttpContext) {
    const parameters =  request.qs()
    let order = parameters.order
    const by = ['asc','desc'].includes(parameters.by) ? parameters.by : 'asc'
    let orderBy={}
    const paramList = ['status','nama_sektor','nama_sub_sektor']
    if (order != undefined && paramList.includes(order)) {
        if (order == 'nama_sektor') {
            orderBy = {sektor:{nama:by}}
        }else if (order == 'nama_sub_sektor') {
            orderBy = {sub_sektor_ref:{nama:by}}
        }else{
            orderBy = {[order]:by}
        }
    }
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          { deskripsi: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.judul
          { deskripsi_singkat: { contains: search, mode: 'insensitive' } }, // Search in tb_app_slider.deskripsi
          {
            sektor: {
              nama:  { contains: search, mode: 'insensitive' }, // Search in tb_app_slider_tr.deskripsi
            },
          },
        ],
      };
    }

    const req = request.qs()
    if (req.status) {
        (searchCondition.AND ??= []).push(
          { status: parseInt(req.status) }
  
        );
    }

    // Get total record count with the search condition applied
    const totalRecords = await prisma.tb_sub_sektor_nasional.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });

    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.tb_sub_sektor_nasional.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
      orderBy:orderBy,
      include: {
            sub_sektor_ref:true,
            sektor:true
        },
    });
    const data = datas.map(({sub_sektor_ref, sektor,...rest }) => {
    const status_text = rest.status === 99 ? "Approve" : "Delete";
    return {
            ...rest,
            status_text,
            nama_sektor: sektor.nama,
            nama_sub_sektor:sub_sektor_ref.nama,
            deskripsi : striptags(rest.deskripsi),

        };
    });


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };
  }

    /**
     * @store
     * @paramQuery id_sektor_nasional - id_sektor_nasional - @type(number) @required
     * @paramQuery id_sub_sektor - id_sub_sektor - @type(number) @required
     * @paramQuery deskripsi - id_sektor - @type(string)
     * @paramQuery deskripsi_singkat - tahun - @type(string)
     * @paramQuery potensi_pasar - id_sumber_data - @type(string)
     * @paramQuery cover - gambar cover - @type(file)
     * @paramQuery icon - gambar icon - @type(file)
     * @paramQuery id_sumber_data - id_prioritas - @type(number)
     * @paramQuery parameter_data - id_prioritas contoh - @type(JSON)
     * @paramQuery kontak - id_prioritas - @type(JSON)
     * @paramQuery infografis - gambar infografis - @type(file)
     * @paramQuery infografis_detail - id_prioritas - @type(JSON)
     */

    public async store({ request, response ,auth}: HttpContext) {

      const data = request.all();
      let datas = await request.validateUsing(createSubSektorNasionalValidator)
      let parameterData = request.input('parameter_data')
      let infografisDetail = request.input('infografis_detail')
      let sumberData = request.input('sumber_data')

      try {
          const configImg = await configUpload('img');

          let icon_name;
          let cover_name;
          let icon_name_tr;
          let id_sumber_data = datas.id_sumber_data;
          delete datas.id_sumber_data;

            const icon = request.files('icon', configImg);
            const iconTr = request.files('icon_tr', configImg);
            if (icon) {
                const uploadPromisesUpImg = icon.map(async (item) => {
                    const uploadPath = `uploads/sektor/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    icon_name = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }
            if (iconTr) {
                const uploadPromisesUpImg = iconTr.map(async (item) => {
                    const uploadPath = `uploads/sektor/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    icon_name_tr = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }

            const cover = request.files('cover', configImg);
            if (cover) {
                const uploadPromisesUpImg = cover.map(async (item) => {
                    const uploadPath = `uploads/sektor/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    cover_name = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }

            const potensi = request.files('potensi', configImg);
            const infografis = request.files('infografis', configImg);
            const infografisTr = request.files('infografis_tr', configImg);


            datas['file_icon'] = icon_name;
            datas['file_image'] = cover_name;

            // Jalankan semua operasi database dalam transaction Prisma
            await prisma.$transaction(async (prisma) => {
                // Insert pertama untuk tb_sub_sektor_nasional
                const dataPost = {
                    id_sektor_nasional : datas.id_sektor_nasional,
                    id_sub_sektor:datas.id_sub_sektor,
                    deskripsi : datas.deskripsi,
                    deskripsi_singkat : datas.deskripsi_singkat,
                    file_icon : icon_name,
                    file_image : cover_name,
                    status : datas.status || 0
                }
                const insert = await prisma.tb_sub_sektor_nasional.create({ data: dataPost });
                const dataPostTr = {
                    id_sub_sektor_nasional:insert.id_sub_sektor_nasional,
                    deskripsi : datas.deskripsi_tr || '-',
                    deskripsi_singkat : datas.deskripsi_singkat_tr,
                    file_icon : icon_name_tr,
                    file_image : 'fileImageEn',
                    kd_bahasa:'en'
                }
                const insertTr = await prisma.tb_sub_sektor_nasional_tr.create({ data: dataPostTr });
                const id_sub_sektor_nasional = insert.id_sub_sektor_nasional;
                if (typeof sumberData === 'string') {
                    sumberData = JSON.parse(sumberData);
                }

                if (Array.isArray(sumberData) && sumberData.length > 0) {
                    await Promise.all(
                        sumberData.map(id =>
                            prisma.tb_sub_sektor_nasional_sumber_data.create({
                                data: {
                                    id_sub_sektor_nasional: id_sub_sektor_nasional,
                                    id_sumber_data: parseInt(id),
                                },
                            })
                        )
                    );
                }

                if (typeof parameterData === 'string') {
                    parameterData = JSON.parse(parameterData);
                }

                for (const el of parameterData) {
                    const value = {
                        id_sub_sektor_nasional: id_sub_sektor_nasional,
                        nama: el.nama,
                        tipe: 1,
                        satuan: el.satuan,
                    };

                    const insertValue = await prisma.tb_sub_sektor_nasional_value.create({ data: value });
                    const value_tr = {
                        id_sub_sektor_nasional_value: insertValue?.id_sub_sektor_nasional_value,
                        nama: el.nama_tr,
                        satuan: el.satuan_tr,
                        kd_bahasa:"en"
                    };
                    await prisma.tb_sub_sektor_nasional_value_tr.create({ data: value_tr });

                    for (const el2 of el.detail) {
                        const value_detail = {
                            id_sub_sektor_nasional_value: insertValue?.id_sub_sektor_nasional_value,
                            tahun: parseInt(el2.tahun),
                            numeric_value: parseFloat(el2.numeric_value),
                        };

                        await prisma.tb_sub_sektor_nasional_value_detail.create({ data: value_detail });
                    }
                }

                if (typeof infografisDetail === 'string') {
                    infografisDetail = JSON.parse(infografisDetail);
                }
                if (infografis && Array.isArray(infografisDetail)) {
                    // Jika panjang array JSON dan file gambar tidak sesuai, kembalikan error
                    if (infografis.length !== infografisDetail.length) {
                        return response.badRequest('Jumlah gambar dan detail tidak sesuai');
                    }

                    const uploadPromisesUpImg = infografis.map(async (item, index) => {
                        const uploadPath = `uploads/sektor/`;
                        const upImg = await img_to_webp(item, uploadPath); // Mengupload gambar ke WebP
                        const fileName = upImg.data.filename; // Mendapatkan nama file gambar

                        // Menghubungkan gambar yang diupload dengan detail dari JSON
                        const detail = infografisDetail[index]; // Mendapatkan detail JSON berdasarkan index

                        // Lakukan proses insert ke database atau logika apapun yang kamu inginkan
                        const insertedFile = await prisma.tb_sub_sektor_nasional_file.create({
                            data: {
                                id_sub_sektor_nasional : id_sub_sektor_nasional,
                                tipe : 1,
                                jenis : 2,
                                nama: fileName, // Nama dari JSON
                                judul: detail.judul, // Judul dari JSON
                                keterangan: detail.keterangan, // Nama file yang sudah diupload
                            },
                        });

                        if (detail.tr && infografisTr) {

                            const trMatch = infografisTr.find(trItem => trItem.clientName === detail.tr.fileName);
                            if (trMatch) {
                                const trFileName = trMatch.fileName === detail.fileName
                                    ? fileName // Gunakan file utama jika sama
                                    : (await img_to_webp(trMatch, uploadPath)).data.filename; // Upload file baru jika berbeda

                                // Insert ke tabel translate
                                await prisma.tb_sub_sektor_nasional_file_tr.create({
                                    data: {
                                        id_sub_sektor_nasional_file: insertedFile.id_sub_sektor_nasional_file, // Relasi ke file utama
                                        kd_bahasa: "en", // Ganti dengan kode bahasa yang sesuai
                                        nama: trFileName,
                                        judul: detail.tr.judul,
                                        keterangan: detail.tr.keterangan || '-',
                                    },
                                });
                            } else {
                                console.log(`File translate tidak ditemukan untuk ${detail.tr.fileName}`);
                            }
                        }
                    });

                    await Promise.all(uploadPromisesUpImg); // Menjalankan semua upload dan insert secara paralel
                }
                response.status(201).json({ success: true,data:insert })
            });
        } catch (error) {
         response.status(500).json({ success: false, message: error.message })
        }
    }

    /**
   * Show individual record
   */
  async show({ params,response }: HttpContext) {
    const { id } = params
      const Id = parseInt(id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        const data = await prisma.tb_sub_sektor_nasional.findUnique({
        where: {
            id_sub_sektor_nasional: Id,
        },
        include: {
            sub_sektor_nasional_value:
            {
                include: {
                    sub_sektor_nasional_value_detail: true,
                    sub_sektor_nasional_value_tr: true,
                },
            },
            sub_sektor_nasional_file:{
                include:{
                    sub_sektor_nasional_file_tr:true
                }
            },
            sub_sektor_nasional_sumber_data:true,
            sub_sektor_nasional_tr:true
        }
        });

        if (data) {
            data.file_icon = `${env.get('APP_URL')}/uploads/sektor/${data.file_icon}`
            data.file_image = `${env.get('APP_URL')}/uploads/sektor/${data.file_image}`
        }

        if (data?.sub_sektor_nasional_file) {
            data.sub_sektor_nasional_file = data.sub_sektor_nasional_file.map((item) => {
                let tr = []
                if (item.sub_sektor_nasional_file_tr) {

                    tr = item.sub_sektor_nasional_file_tr.map((items) => {
                        return{
                            ...items,
                            nama: `${env.get('APP_URL')}/uploads/sektor/${items.nama}`
                        }
                    })
                }
                return {
                    ...item,
                    nama : `${env.get('APP_URL')}/uploads/sektor/${item.nama}`,
                    sub_sektor_nasional_file_tr : tr
                }
            })
            data.sub_sektor_nasional_tr = data.sub_sektor_nasional_tr.map((item) => {
                return {
                    ...item,
                    file_icon : `${env.get('APP_URL')}/uploads/sektor/${item.file_icon}`,
                }
            })
        }


        return {
            success : true,
            data : data
        }
  }

  public async update({ params,request, response ,auth}: HttpContext) {

    const Id = parseInt(params.id, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ error: 'Invalid ID provided' })
      }
      const data = request.all();

      let datas = await request.validateUsing(updateSubSektorNasionalValidator)
      let parameterData = request.input('parameter_data')
      let infografisDetail = request.input('infografis_detail')
      let sumberData = request.input('sumber_data')
      let tr = request.input('tr')

        // try {
             const configImg = await configUpload('img');

            let icon_name;
            let icon_name_tr;
            let id_sumber_data = datas.id_sumber_data;
            delete datas.id_sumber_data;

            const icon = request.files('icon', configImg);
            if (icon) {
                const uploadPromisesUpImg = icon.map(async (item) => {
                    const uploadPath = `uploads/sektor/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    icon_name = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }
            const iconTr = request.files('icon_tr', configImg);
            if (iconTr) {
                const uploadPromisesUpImg = iconTr.map(async (item) => {
                    const uploadPath = `uploads/sektor/`;
                    const upImg = await img_to_webp(item, uploadPath);
                    icon_name_tr = upImg.data.filename;
                });
                await Promise.all(uploadPromisesUpImg);
            }



            const infografis = request.files('infografis', configImg);
            const infografisTr = request.files('infografis_tr', configImg);

            const t = {icon,infografis}

            // return t
            datas['file_icon'] = icon_name;
            datas['status'] = 0;
            // Jalankan semua operasi database dalam transaction Prisma


            await prisma.$transaction(async (prisma) => {
                // update pertama untuk tb_sub_sektor_nasional
                const update = await prisma.tb_sub_sektor_nasional.update({where:{id_sub_sektor_nasional:Id}, data: datas });
                if (typeof tr === 'string') {
                    tr = JSON.parse(tr);
                }
                const dataPostTr = {
                    deskripsi : tr.deskripsi,
                    deskripsi_singkat : tr.deskripsi_singkat,
                    potensi_pasar : tr.potensi_pasar,
                    file_icon : icon_name_tr,
                }
                const updateTr = await prisma.tb_sub_sektor_nasional_tr.update({
                    where : {
                        id_sub_sektor_nasional_tr: parseInt(tr.id_sub_sektor_nasional_tr),
                    },
                    data: dataPostTr },

                );
                const id_sub_sektor_nasional = update.id_sub_sektor_nasional;
                if (typeof sumberData === 'string') {
                    sumberData = JSON.parse(sumberData);
                }
               if (Array.isArray(sumberData) && sumberData.length > 0) {
                    // 1. Ambil semua id_sumber_data yang sudah ada di database
                    const existingData = await prisma.tb_sub_sektor_nasional_sumber_data.findMany({
                        where: {
                            id_sub_sektor_nasional: id_sub_sektor_nasional,
                        },
                        select: {
                            id_sumber_data: true, // Ambil hanya kolom id_sumber_data
                        },
                    });

                    // 2. Buat array berisi id_sumber_data yang sudah ada
                    const existingIds = existingData.map(item => item.id_sumber_data);

                    // 4. Tambahkan data baru


                    // 5. Hapus data yang tidak ada di sumberData
                    if (existingIds.length > 0) {
                        await prisma.tb_sub_sektor_nasional_sumber_data.deleteMany({
                            where: {
                                id_sub_sektor_nasional: id_sub_sektor_nasional,
                                id_sumber_data: { in: existingIds },
                            },
                        });
                    }

                    if (sumberData.length > 0) {
                        await Promise.all(
                            sumberData.map(id =>
                                prisma.tb_sub_sektor_nasional_sumber_data.create({
                                    data: {
                                        id_sub_sektor_nasional: id_sub_sektor_nasional,
                                        id_sumber_data: parseInt(id),
                                    },
                                })
                            )
                        );
                    }
                }


                if (typeof parameterData === 'string') {
                    parameterData = JSON.parse(parameterData);
                }

                // Step 1: Ambil semua data terkait dari database
                const existingValues = await prisma.tb_sub_sektor_nasional_value.findMany({
                where: { id_sub_sektor_nasional: id_sub_sektor_nasional },
                select: { id_sub_sektor_nasional_value: true },
                });

                const existingValueTrs = await prisma.tb_sub_sektor_nasional_value_tr.findMany({
                where: {
                    id_sub_sektor_nasional_value: { in: existingValues.map(v => v.id_sub_sektor_nasional_value) }
                },
                select: { id_sub_sektor_nasional_value_tr: true },
                });

                const existingValueDetails = await prisma.tb_sub_sektor_nasional_value_detail.findMany({
                where: {
                    id_sub_sektor_nasional_value: { in: existingValues.map(v => v.id_sub_sektor_nasional_value) }
                },
                select: { id_sub_sektor_nasional_value_detail: true },
                });

                // Step 2: Ambil ID yang ada di parameterData
                const parameterDataIds = parameterData
                .map(el => parseInt(el.id_sub_sektor_nasional_value))
                .filter(id => id !== undefined); // Ambil hanya ID yang valid

                const parameterDataTrIds = parameterData
                .map(el => parseInt(el.tr.id_sub_Sektor_nasional_value_tr))
                .filter(id => id !== undefined); // Ambil hanya ID yang valid

                const parameterDataDetailIds = parameterData.flatMap(el => el.detail)
                .map(detail => parseInt(detail.id_sub_sektor_nasional_value))
                .filter(id => id !== undefined); // Ambil hanya ID yang valid

                // Step 3: Cari data di database yang tidak ada di JSON dan hapus
                const idsToDeleteValues = existingValues
                .filter(value => !parameterDataIds.includes(value.id_sub_sektor_nasional_value))
                .map(value => value.id_sub_sektor_nasional_value);

                const idsToDeleteValueTrs = existingValueTrs
                .filter(valueTr => !parameterDataTrIds.includes(valueTr.id_sub_sektor_nasional_value_tr))
                .map(valueTr => valueTr.id_sub_sektor_nasional_value_tr);

                const idsToDeleteValueDetails = existingValueDetails
                .filter(detail => !parameterDataDetailIds.includes(detail.id_sub_sektor_nasional_value_detail))
                .map(detail => detail.id_sub_sektor_nasional_value_detail);

                if (idsToDeleteValues.length > 0) {
                await prisma.tb_sub_sektor_nasional_value.deleteMany({
                    where: { id_sub_sektor_nasional_value: { in: idsToDeleteValues } },
                });
                }

                if (idsToDeleteValueTrs.length > 0) {
                await prisma.tb_sub_sektor_nasional_value_tr.deleteMany({
                    where: { id_sub_sektor_nasional_value_tr: { in: idsToDeleteValueTrs } },
                });
                }

                if (idsToDeleteValueDetails.length > 0) {
                await prisma.tb_sub_sektor_nasional_value_detail.deleteMany({
                    where: { id_sub_sektor_nasional_value_detail: { in: idsToDeleteValueDetails } },
                });
                }

                // Step 4: Lanjutkan ke proses update/insert seperti biasa
                for (const el of parameterData) {
                const value = {
                    id_sub_sektor_nasional: id_sub_sektor_nasional,
                    nama: el.nama,
                    tipe: 1,
                    satuan: el.satuan,
                };

                let uiValue;
                let value_tr = {};
                if (el.id_sub_sektor_nasional_value) {
                    uiValue = await prisma.tb_sub_sektor_nasional_value.update({
                    where: { id_sub_sektor_nasional_value: parseInt(el.id_sub_sektor_nasional_value) },
                    data: value,
                    });
                    value_tr = {
                    id_sub_sektor_nasional_value: uiValue?.id_sub_sektor_nasional_value,
                    nama: el.tr.nama_tr,
                    satuan: el.tr.satuan_tr,
                    kd_bahasa: "en",
                    };
                } else {
                    uiValue = await prisma.tb_sub_sektor_nasional_value.create({ data: value });
                    value_tr = {
                    id_sub_sektor_nasional_value: uiValue?.id_sub_sektor_nasional_value,
                    nama: el.tr.nama_tr,
                    satuan: el.tr.satuan_tr,
                    kd_bahasa: "en",
                    };
                }

                if (el.tr.id_sub_Sektor_nasional_value_tr) {
                    await prisma.tb_sub_sektor_nasional_value_tr.update({
                    where: { id_sub_sektor_nasional_value_tr: parseInt(el.tr.id_sub_Sektor_nasional_value_tr) },
                    data: value_tr,
                    });
                } else {
                    await prisma.tb_sub_sektor_nasional_value_tr.create({ data: value_tr });
                }

                for (const el2 of el.detail) {
                    const value_detail = {
                    id_sub_sektor_nasional_value: uiValue?.id_sub_sektor_nasional_value,
                    tahun: parseInt(el2.tahun),
                    numeric_value: parseFloat(el2.numeric_value),
                    };

                    if (el2.id_sub_sektor_nasional_value) {
                    await prisma.tb_sub_sektor_nasional_value_detail.update({
                        where: { id_sub_sektor_nasional_value_detail: parseInt(el2.id_sub_sektor_nasional_value_detail) },
                        data: value_detail,
                    });
                    } else {
                    await prisma.tb_sub_sektor_nasional_value_detail.create({ data: value_detail });
                    }
                }
                }


                if (typeof infografisDetail === 'string') {
                    infografisDetail = JSON.parse(infografisDetail);
                }
                if (infografis && Array.isArray(infografisDetail)) {
                    const existingFiles = await prisma.tb_sub_sektor_nasional_file.findMany({
                    where: { id_sub_sektor_nasional: id_sub_sektor_nasional },
                    select: { id_sub_sektor_nasional_file: true },
                    });

                    // Step 2: Ambil ID yang ada di infografisDetail
                    const infografisDetailIds = infografisDetail
                    .map(detail => parseInt(detail.id_sub_sektor_nasional_file))
                    .filter(id => id !== undefined); // Pastikan hanya mengambil ID yang ada

                    // Step 3: Cari ID di database yang tidak ada di JSON
                    const idsToDelete = existingFiles
                    .filter(file => !infografisDetailIds.includes(file.id_sub_sektor_nasional_file))
                    .map(file => file.id_sub_sektor_nasional_file);

                    // Step 4: Hapus entri di database yang tidak ada di JSON
                    if (idsToDelete.length > 0) {
                        await prisma.tb_sub_sektor_nasional_file.deleteMany({
                            where: {
                            id_sub_sektor_nasional_file: { in: idsToDelete },
                            },
                        });
                    }

                     const uploadPromisesUpImg = infografisDetail.map(async (detail) => {
                        // Cari padanan gambar di infografis berdasarkan nama file yang cocok
                        const item = infografis.find(infografisItem => infografisItem.clientName === detail.fileName);

                        let fileName = detail.fileName; // Default value if no matching image is found

                        // Jika ada gambar yang cocok di infografis, upload dan ambil nama file baru
                        let id_sub_sektor_nasional_file = 0;
                        if (item) {
                            const uploadPath = `uploads/sektor/`;
                            const upImg = await img_to_webp(item, uploadPath); // Mengupload gambar ke WebP
                            fileName = upImg.data.filename; // Mendapatkan nama file gambar yang diupload
                            if (detail.id_sub_sektor_nasional_file) {
                                id_sub_sektor_nasional_file=detail.id_sub_sektor_nasional_file
                                // Jika `id_sektor_nasional_file` ada, lakukan update
                                await prisma.tb_sub_sektor_nasional_file.update({
                                    where: {
                                        id_sub_sektor_nasional_file: parseInt(detail.id_sub_sektor_nasional_file),
                                    },
                                    data: {
                                        id_sub_sektor_nasional: id_sub_sektor_nasional,
                                        tipe: 1,
                                        jenis: 2,
                                        nama: fileName, // Nama file baru jika ada upload, atau dari detail jika tidak ada
                                        judul: detail.judul, // Judul dari JSON
                                        keterangan: detail.keterangan, // Keterangan dari JSON
                                    },
                                });
                            } else {
                                // Jika `id_sektor_nasional_file` tidak ada, lakukan insert
                                const insert = await prisma.tb_sub_sektor_nasional_file.create({
                                    data: {
                                        id_sub_sektor_nasional: id_sub_sektor_nasional,
                                        tipe: 1,
                                        jenis: 2,
                                        nama: fileName, // Nama file baru atau dari detail
                                        judul: detail.judul, // Judul dari JSON
                                        keterangan: detail.keterangan, // Keterangan dari JSON
                                    },
                                });
                                id_sub_sektor_nasional_file = insert.id_sub_sektor_nasional_file
                            }
                        }else{
                            if (detail.id_sub_sektor_nasional_file) {
                                id_sub_sektor_nasional_file = detail.id_sub_sektor_nasional_file
                                // Jika `id_sektor_nasional_file` ada, lakukan update
                                await prisma.tb_sub_sektor_nasional_file.update({
                                    where: {
                                        id_sub_sektor_nasional_file: parseInt(detail.id_sub_sektor_nasional_file),
                                    },
                                    data: {
                                        id_sub_sektor_nasional: id_sub_sektor_nasional,
                                        tipe: 1,
                                        jenis: 2,
                                        judul: detail.judul, // Judul dari JSON
                                        keterangan: detail.keterangan, // Keterangan dari JSON
                                    },
                                });
                            } else {
                                return response.status(500).json({ success: false, message: 'File Infografis Required!' })
                            }
                        }

                        if (detail.tr && infografisTr) {
                            const trMatch = infografisTr.find(trItem => trItem.clientName === detail.tr.fileName);

                            if (trMatch) {
                                const uploadPath = `uploads/sektor/`;
                                // Periksa apakah fileName sama atau upload file baru jika berbeda
                                const trFileName = trMatch.fileName === detail.fileName
                                ? trMatch.fileName // Gunakan file utama jika sama
                                : (await img_to_webp(trMatch, uploadPath)).data.filename; // Upload file baru jika berbeda

                                if (detail.tr.id_sub_sektor_nasional_file_tr) {
                                // UPDATE jika membawa id_sektor_nasional_file_tr
                                await prisma.tb_sub_sektor_nasional_file_tr.update({
                                    where: { id_sub_sektor_nasional_file_tr: parseInt(detail.tr.id_sub_sektor_nasional_file_tr) },
                                    data: {
                                    nama: trFileName,
                                    judul: detail.tr.judul,
                                    keterangan: detail.tr.keterangan,
                                    },
                                });
                                console.log(`File translate ID ${detail.tr.id_sub_sektor_nasional_file_tr} berhasil diupdate`);
                                } else {
                                // CREATE jika tidak membawa id_sub_sektor_nasional_file_tr
                                await prisma.tb_sub_sektor_nasional_file_tr.create({
                                    data: {
                                    id_sub_sektor_nasional_file: id_sub_sektor_nasional_file, // Relasi ke file utama
                                    kd_bahasa: "en", // Ganti dengan kode bahasa yang sesuai
                                    nama: trFileName,
                                    judul: detail.tr.judul,
                                    keterangan: detail.tr.keterangan,
                                    },
                                });
                                console.log("File translate baru berhasil ditambahkan");
                                }
                            } else {
                                if (detail.tr.id_sub_sektor_nasional_file_tr) {
                                // UPDATE jika membawa id_sektor_nasional_file_tr
                                    await prisma.tb_sub_sektor_nasional_file_tr.update({
                                        where: { id_sub_sektor_nasional_file_tr: parseInt(detail.tr.id_sub_sektor_nasional_file_tr) },
                                        data: {
                                        judul: detail.tr.judul,
                                        keterangan: detail.tr.keterangan,
                                        },
                                    });
                                }
                                console.log(`File translate tidak ditemukan untuk ${detail.tr.fileName}`);
                            }
                        }

                    });


                    await Promise.all(uploadPromisesUpImg); // Menjalankan semua upload dan insert secara paralel
                }
                response.status(201).json({ success: true,data:update })
            });
        // } catch (error) {
        //  response.status(500).json({ success: false, message: error.message })
        // }
    }

  async destroy({ params,response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_sub_sektor_nasional.delete({
        where: {
          id_sub_sektor_nasional: Id,
        },
      })
      return response.status(200).send({success:true,data:deletePost})
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }

  /**
     * @aprove
     * @paramQuery status - status - @type(number) @required
     * @paramQuery keterangan - keterangan - @type(string)  @required
     */
    async aprove({ params, request, response ,auth}: HttpContext) {
        // Ambil dan parse ID dari parameter URL
        const Id = parseInt(params.id_sub_sektor_nasional, 10);
        // Validasi ID
        if (isNaN(Id)) {
            return response.status(400).json({ error: 'Invalid ID provided' });
        }
        try {
            // Validasi input dengan validator yang telah dibuat
            const dataPost = await request.validateUsing(aproveValidator);

            // Update data di tb_sektor_nasional
            const update = await prisma.tb_sub_sektor_nasional.update({
            where: {
                id_sub_sektor_nasional: Id
            },
                data: {
                    status: dataPost.status
                },
            });
            // Insert ke tb_sektor_nasional_status
            await auth.check()
            const insert = await prisma.tb_sub_sektor_nasional_status.create({
                data: {
                    id_sub_sektor_nasional: Id,
                    status: dataPost.status,
                    status_proses: dataPost.status,
                    keterangan: dataPost.keterangan,
                    created_by:auth.user?.id,
                    updated_by:auth.user?.id
                },
            });
            // Berikan respon sukses dengan data update
            return response.status(201).json({ success: true, data: update });
        } catch (error) {
            // Tangani error dan kirimkan respon gagal
            return response.status(500).json({ success: false, message: error.message });
        }
    }

    /**
   * Show individual record
   */
  async get_by_sektor({ params,response }: HttpContext) {
      const Id = parseInt(params.id_sektor_nasional, 10)
      if (isNaN(Id)) {
        return response.status(400).send({ success:false,error: 'Invalid ID provided' })
      }
        const datas = await prisma.tb_sub_sektor_nasional.findMany({
            where: {
                id_sektor_nasional: Id,
            },
            select: {
                id_sub_sektor_nasional:true,
                id_sub_sektor:true,
                id_sektor_nasional:true,
                sub_sektor_ref:{
                    select:{
                        nama:true
                    }
                }

            }
        });

        const data = datas.map((item) =>
        {
            return {
                id_sub_sektor_nasional: item.id_sub_sektor_nasional,
                id_sub_sektor: item.id_sub_sektor,
                id_sektor: item.id_sektor_nasional,
                nama: item.sub_sektor_ref.nama
            }
        })
        return {
            success : true,
            data : data
        }

    }


    public async bySektorDaerah({ params,request, response}: HttpContext) {

        const idSektorDaerah = parseInt(params.id_sektor_daerah, 10)
        let data = {};

        if (request.input("is_simple") || request.input("is_simple") == "true") {
            data = await prisma.tb_sub_sektor_nasional.findMany({
                take: 100,
                where: {
                    sub_sektor_daerah: {
                        some: {
                            id_sektor_daerah: idSektorDaerah, // Pastikan ada data di `sub_sektor_daerah` sesuai kondisi ini
                        },
                    },
                },
                select: {
                    id_sub_sektor_nasional: true,
                    id_sektor_nasional: true,
                    id_sub_sektor: true,
                    sub_sektor_ref: {
                        select: {
                            nama: true
                        }
                    }
                }
            });
        } else {
            data = await prisma.tb_sub_sektor_nasional.findMany({
                take: 100,
                where: {
                    sub_sektor_daerah: {
                        some: {
                            id_sektor_daerah: idSektorDaerah, // Pastikan ada data di `sub_sektor_daerah` sesuai kondisi ini
                        },
                    },
                },
                include: {
                    sub_sektor_daerah: {
                        where: {
                            id_sektor_daerah: idSektorDaerah
                        }
                    },
                    sub_sektor_ref: true
                }
            });
        }

        return {
            success : true,
            data : data
        }
    }

    public async refSubSektorBySektorDaerah({ params,request, response}: HttpContext) {

        const idSektorDaerah = parseInt(params.id_sektor_daerah, 10)
        const datas = await prisma.tb_sektor_daerah.findFirst({
            take: 100,
            where:{
                id_sektor_daerah:idSektorDaerah
            },
            include: {
                sektor_nasional: {
                    include:{
                        sektor:{
                            include:{
                                sub_sektor_nasional:{
                                    include:{
                                        sub_sektor_ref:true
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });

        const data = datas?.sektor_nasional.sektor.sub_sektor_nasional.map((item) => {
            return {
                id_sektor_nasional:datas?.id_sektor_nasional,
                id_sub_sektor:item.id_sub_sektor,
                id_sub_sektor_nasional:item.id_sub_sektor_nasional,
                sub_sektor_ref:item.sub_sektor_ref


            }
        })

        return {
            success : true,
            data : data
        }

    }

}