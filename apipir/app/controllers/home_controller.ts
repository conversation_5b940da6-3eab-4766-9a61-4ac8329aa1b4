import type { HttpContext } from '@adonisjs/core/http'
import { checkFileOrUrl, decodeHtmlEntities, numberFormat, numberFormatLengkap, numberFormatLengkapEn, numberFormatRpLengkap, numberFormatRpLengkapEn, numberFormatRpSingkatan, rupiah, satuanNumber, satuanNumberEn } from '../helpers/global_helper.js'
import { Application } from '@adonisjs/core/app'
import { Env } from '@adonisjs/core/env'
import env from '#start/env'
import { isTemplateExpression } from 'typescript'
import { request } from 'node:http'
import prisma from '../lib/prisma.js'
import striptags from 'striptags'

export default class HomeController {
    /**
     * @index
     * @summary List Data Referensi Sektor Unggulan (Dropdown Sektor Unggulan Nasional)
     */
    public async sektor_nasional_ref({ params,response }:HttpContext) {
            
        try {
            const bahasa = params.bahasa || 'id'
            let data
            if (bahasa === 'id') {
                const data = await prisma.tb_sektor_nasional_ref.findMany({
                    where:{
                        sektor_nasional:{
                            status:99
                        }
                    }
                })
                const datas = data.map(({  ...data }) => ({
                    ...data,
                    icon :  `${env.get('APP_URL')}/uploads/icon-web/${data.iconmap}`,
                    icon_bawah : `${env.get('APP_URL')}/uploads/icon-web/${data.icon}`
                }));
                            
                    return { 
                        success : true,
                        data : {datas}
                    }
            }else{
                const data = await prisma.tb_sektor_nasional_ref.findMany({
                    include:{
                        sektor_nasional_tr: {
                            where: {
                                kd_bahasa: bahasa
                            }
                        }
                    }
                })
                const datas = data.map(({  sektor_nasional_tr,...data }) => ({
                    ...data,
                    nama:sektor_nasional_tr?.nama || data.nama,
                    icon : `${env.get('APP_URL')}/uploads/icon-web/${data.iconmap}`,
                    icon_bawah : `${env.get('APP_URL')}/uploads/icon-web/${data.icon}`
                }));
                            
                return { 
                    success : true,
                    data : {datas}
                }
            }
        } catch (error) {
            console.error(error);
            return response.status(500).json({
                status: 'error',
                message: 'Error fetching data',
            });
        }
    }
    public async peluang_sektor_ref({ response }:HttpContext) {
        try {
        const data = await prisma.tb_peluang_sektor.findMany({})
        const datas = data.map(({  ...data }) => ({
            ...data,
            icon : `${env.get('APP_URL')}/uploads/icon-web/${data.icon}`
        }));
                    
            return { 
                success : true,
                data : {datas}
            }
        } catch (error) {
        console.error(error);
        return response.status(500).json({
            status: 'error',
            message: 'Error fetching data',
        });
        }
    }
    /**
     * @sektor_nasional_detail
     * @summary List Detail Data Sektor Unggulan Nasional
     */
    public async sektor_nasional_detail({ params ,response}: HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }
        try {
            const bahasa = params.bahasa || 'id'
            let data
            if (bahasa === 'id') {
                const peluang = await prisma.tb_peluang_kabkot.findMany({
                    where: {
                        id_adm_kabkot:  {
                            gte: parseInt(`${Id}00`),
                            lt: parseInt(`${Id}99`) // Misalnya, mencari yang dimulai dengan '12'
                        },
                        status:'99'
                    },
                })

            
                const umr = await prisma.tb_umr_provinsi.findMany({
                    where: {
                        id_adm_provinsi: Id,
                    },
                    orderBy:{
                        tahun: 'desc',
                    }
                })

                    // get Penduduk
                    const maxTahun = await prisma.tb_demografi_provinsi.aggregate({
                        _max: {
                            tahun: true,
                        },
                        where:{
                            id_adm_provinsi: Id,
                        }
                    });
                    let jumlahPenduduk
                    if( maxTahun._max.tahun != null){
                        const records = await prisma.tb_demografi_provinsi.findMany({
                            where: {
                                id_adm_provinsi: Id,
                                tahun: maxTahun._max.tahun,
                            },
                            select: {
                                    jumlah_pria: true,
                                    jumlah_wanita: true,
                                },
                        });
                        
                        jumlahPenduduk = records.reduce((sum, record) => {
                            return sum + (record.jumlah_pria || 0) + (record.jumlah_wanita || 0);
                        }, 0);
                    }else{
                        jumlahPenduduk = 0
                    }


                    // get prdb
                    const maxThnPdrb = await prisma.tb_sektor_daerah_pdrb.aggregate({
                        _max: {
                            tahun_pdrb: true,
                        },
                        where:{
                            id_adm_provinsi: Id,
                        }
                    });
                    let pdrbNilai
                    if(maxThnPdrb._max.tahun_pdrb != null){

                        const pdrb = await prisma.tb_sektor_daerah_pdrb.findMany({
                            where: {
                                id_adm_provinsi: Id,
                                tahun_pdrb: maxThnPdrb._max.tahun_pdrb,
                            },
                            select: {
                                jumlah_pdrb: true,
                            },
                        });
                        
                        
                        pdrbNilai = pdrb.reduce((sum, record) => {
                            const jumlah = record.jumlah_pdrb ? record.jumlah_pdrb.toNumber() : 0;
                            return sum + jumlah;
                        }, 0);
                    }else{
                        pdrbNilai = 0
                    }
                        
                // get Realisasi
                const maxThnRealisasi = await prisma.tb_investasi_provinsi.aggregate({
                    _max: {
                        tahun: true,
                    },
                    where:{
                        id_adm_provinsi: Id,
                    }
                });
                let jumlahRealisasi
                if (maxThnRealisasi._max.tahun != null) {
                    
                    const investasiProv = await prisma.tb_investasi_provinsi.findMany({
                        where: {
                            id_adm_provinsi: Id,
                            tahun: maxThnRealisasi._max.tahun,
                        },
                        select: {
                            jumlah_investasi: true,
                        },
                    });
                

                    jumlahRealisasi = investasiProv.reduce((sum, record) => {
                        return sum + (record.jumlah_investasi || 0);
                    }, 0);
                }else{
                    jumlahRealisasi = 0
                }

                const hasilInvestasi = await prisma.tb_investasi_provinsi.groupBy({
                    by: ['id_jenis'], 
                    where: {
                        tahun: maxThnRealisasi._max.tahun,  
                        id_adm_provinsi: Id,
                    },
                    _sum: {
                        jumlah_investasi: true,  
                    },
                });
                let Investasi
                if (hasilInvestasi.length != 0) {
                    
                    Investasi = await Promise.all(
                        hasilInvestasi.map(async (investasi) => {
                            const jenis = await prisma.tb_investasi_jenis.findUnique({
                            where: { id_investasi_jenis: investasi.id_jenis },
                            select: { nama: true },
                            });

                            const totalInvestasi = investasi._sum.jumlah_investasi ?? 0;

                            const formattedTotalInvestasi = numberFormat(totalInvestasi);

                            return {
                                nilai: jenis?.nama == 'PMA' ? `PMA (US$ Thousand) : ${formattedTotalInvestasi}` : `PMDN (RP Million) : ${formattedTotalInvestasi}`,
                                // total_investasi: formattedTotalInvestasi,
                                // nama_jenis: jenis?.nama || 'Tidak diketahui', // Handle jika nama tidak ditemukan
                                // satuan : jenis?.nama == 'PMA' ? 'PMA (US$ Ribu)' : 'PMDN (RP Juta)'   
                            };
                        })
                    );
                }else{
                        Investasi = [
                            {
                                nilai : "PMA (US$) : 0"
                            },
                            {
                                nilai : "PMDN (RP) : 0"
                            }
                        ]
                }
                
                const kawasan = await prisma.tb_kawasan_industri.findMany({
                        where: {
                            id_adm_kabkot:  {
                                gte: parseInt(`${Id}00`),
                                lt: parseInt(`${Id}99`) // Misalnya, mencari yang dimulai dengan '12'
                            }
                        }
                });

                const data ={
                    peluang:`${peluang.length} Proyek`,
                    umr : {
                        nilai : umr.length > 0 ? rupiah(umr[0].nilai) : null,
                        tahun : umr.length > 0 ? umr[0].tahun : null
                    },
                    jumlah_penduduk:{
                        nilai : ` ${numberFormat(jumlahPenduduk)} Jiwa` ,
                        // satuan: 'Jiwa'
                    },
                    prdb:{
                        tahun :maxThnPdrb._max.tahun_pdrb == null ? '' : maxThnPdrb._max.tahun_pdrb,
                        // nilai :numberFormat(pdrbNilai),
                        nilai :pdrbNilai == 0 ? 0 : `${satuanNumber(pdrbNilai)} ${numberFormat(pdrbNilai)} `,
                        // satuan : "(Rp Juta)"
                    },
                    realisasi_investasi:Investasi,
                    kawasan: `${kawasan.length} Kawasan`

                }
                return { 
                    success : true,
                    data : data
                }
            }else{
                const peluang = await prisma.tb_peluang_kabkot.findMany({
                    where: {
                        id_adm_kabkot:  {
                            gte: parseInt(`${Id}00`),
                            lt: parseInt(`${Id}99`) // Misalnya, mencari yang dimulai dengan '12'
                        },
                        status:'99'
                    },
                })

            
                const umr = await prisma.tb_umr_provinsi.findMany({
                    where: {
                        id_adm_provinsi: Id,
                    },
                    orderBy:{
                        tahun: 'desc',
                    }
                })

                    // get Penduduk
                    const maxTahun = await prisma.tb_demografi_provinsi.aggregate({
                        _max: {
                            tahun: true,
                        },
                        where:{
                            id_adm_provinsi: Id,
                        }
                    });
                    let jumlahPenduduk
                    if( maxTahun._max.tahun != null){
                        const records = await prisma.tb_demografi_provinsi.findMany({
                            where: {
                                id_adm_provinsi: Id,
                                tahun: maxTahun._max.tahun,
                            },
                            select: {
                                    jumlah_pria: true,
                                    jumlah_wanita: true,
                                },
                        });
                        
                        jumlahPenduduk = records.reduce((sum, record) => {
                            return sum + (record.jumlah_pria || 0) + (record.jumlah_wanita || 0);
                        }, 0);
                    }else{
                        jumlahPenduduk = 0
                    }


                    // get prdb
                    const maxThnPdrb = await prisma.tb_sektor_daerah_pdrb.aggregate({
                        _max: {
                            tahun_pdrb: true,
                        },
                        where:{
                            id_adm_provinsi: Id,
                        }
                    });
                    let pdrbNilai
                    if(maxThnPdrb._max.tahun_pdrb != null){

                        const pdrb = await prisma.tb_sektor_daerah_pdrb.findMany({
                            where: {
                                id_adm_provinsi: Id,
                                tahun_pdrb: maxThnPdrb._max.tahun_pdrb,
                            },
                            select: {
                                jumlah_pdrb: true,
                            },
                        });
                        
                        
                        pdrbNilai = pdrb.reduce((sum, record) => {
                            const jumlah = record.jumlah_pdrb ? record.jumlah_pdrb.toNumber() : 0;
                            return sum + jumlah;
                        }, 0);
                    }else{
                        pdrbNilai = 0
                    }
                        
                // get Realisasi
                const maxThnRealisasi = await prisma.tb_investasi_provinsi.aggregate({
                    _max: {
                        tahun: true,
                    },
                    where:{
                        id_adm_provinsi: Id,
                    }
                });
                let jumlahRealisasi
                if (maxThnRealisasi._max.tahun != null) {
                    
                    const investasiProv = await prisma.tb_investasi_provinsi.findMany({
                        where: {
                            id_adm_provinsi: Id,
                            tahun: maxThnRealisasi._max.tahun,
                        },
                        select: {
                            jumlah_investasi: true,
                        },
                    });
                

                    jumlahRealisasi = investasiProv.reduce((sum, record) => {
                        return sum + (record.jumlah_investasi || 0);
                    }, 0);
                }else{
                    jumlahRealisasi = 0
                }

                const hasilInvestasi = await prisma.tb_investasi_provinsi.groupBy({
                    by: ['id_jenis'], 
                    where: {
                        tahun: maxThnRealisasi._max.tahun,  
                        id_adm_provinsi: Id,
                    },
                    _sum: {
                        jumlah_investasi: true,  
                    },
                });
                let Investasi
                if (hasilInvestasi.length != 0) {
                    
                    Investasi = await Promise.all(
                        hasilInvestasi.map(async (investasi) => {
                            const jenis = await prisma.tb_investasi_jenis.findUnique({
                            where: { id_investasi_jenis: investasi.id_jenis },
                            select: { nama: true },
                            });

                            const totalInvestasi = investasi._sum.jumlah_investasi ?? 0;

                            const formattedTotalInvestasi = numberFormat(totalInvestasi);

                            return {
                                nilai: jenis?.nama == 'PMA' ? `PMA (US$ Thousand) : ${formattedTotalInvestasi}` : `PMDN (RP Million) : ${formattedTotalInvestasi}`,
                                // total_investasi: formattedTotalInvestasi,
                                // nama_jenis: jenis?.nama || 'Tidak diketahui', // Handle jika nama tidak ditemukan
                                // satuan : jenis?.nama == 'PMA' ? 'PMA (US$ Ribu)' : 'PMDN (RP Juta)'   
                            };
                        })
                    );
                }else{
                        Investasi = [
                            {
                                nilai : "PMA (US$) : 0"
                            },
                            {
                                nilai : "PMDN (RP) : 0"
                            }
                        ]
                }
                
                const kawasan = await prisma.tb_kawasan_industri.findMany({
                        where: {
                            id_adm_kabkot:  {
                                gte: parseInt(`${Id}00`),
                                lt: parseInt(`${Id}99`) // Misalnya, mencari yang dimulai dengan '12'
                            }
                        }
                });

                const data ={
                    peluang:`${peluang.length} Project`,
                    umr : {
                        nilai : umr.length > 0 ? rupiah(umr[0].nilai) : null,
                        tahun : umr.length > 0 ? umr[0].tahun : null
                    },
                    jumlah_penduduk:{
                        nilai : ` ${numberFormat(jumlahPenduduk)} Population` ,
                        // satuan: 'Jiwa'
                    },
                    prdb:{
                        tahun :maxThnPdrb._max.tahun_pdrb == null ? '' : maxThnPdrb._max.tahun_pdrb,
                        // nilai :numberFormat(pdrbNilai),
                        nilai :pdrbNilai == 0 ? 0 : `${satuanNumberEn(pdrbNilai)} ${numberFormat(pdrbNilai)} `,

                        // satuan : "(Rp Juta)"
                    },
                    realisasi_investasi:Investasi,
                    kawasan: `${kawasan.length} Area`

                }
                return { 
                    success : true,
                    data : data
                }
            }
        } catch (error) {
            
        }
        
    }

     /**
     * @provinsi_ref
     * @summary List Provinsi 
     */
    async provinsi_ref({params}: HttpContext) {
        try {
            const bahasa = params.bahasa || 'id'
            let data
            if (bahasa === 'id') {
                const data = await prisma.tb_adm_provinsi.findMany({
                    select:{
                        id_adm_provinsi:true,
                        nama:true
                    },
                    orderBy:{
                        id_adm_provinsi:'asc'
                    }
                    });

                return { 
                    success : true,
                    data : {data}
                }
            }else{
                const data = await prisma.tb_adm_provinsi_tr.findMany({
                    select:{
                        id_adm_provinsi:true,
                        nama:true
                    },
                    orderBy:{
                        id_adm_provinsi:'asc'
                    },
                    where:{
                        kd_bahasa:bahasa
                    }
                });

                return { 
                    success : true,
                    data : {data}
                }
            }
        } catch (error) {
            
        }
        
    }
    /**
     * @slider
     * @summary List skider 
     */
    async slider({params}: HttpContext) {
        const bahasa = params.bahasa || 'id'
        let data
        if (bahasa === 'id') {
            
            const datas = await prisma.tb_app_slider.findMany({            
                orderBy:{
                    ordering: 'asc',
                },
                where:{
                    is_background:false
                }
            });

            data = datas.map(({ ...item }) => ({
                ...item,
                nama_file_image: `${env.get('APP_URL')}/uploads/slider/${item.nama_file_image}`
            }));
        }else{
            const datas = await prisma.tb_app_slider.findMany({            
                orderBy:{
                    ordering: 'asc',
                },
                where:{
                    is_background:false,
                },
                include:{
                    tb_app_slider_tr:{
                        where:{
                            kd_bahasa:bahasa
                        }
                    }
                }
            });
            data = datas.map(({ tb_app_slider_tr,...item }) => ({
                ...item,
                deskripsi : tb_app_slider_tr[0]?.deskripsi || item.deskripsi,
                judul : tb_app_slider_tr[0]?.judul || item.judul,
                nama_file_image: tb_app_slider_tr[0]?.nama_file_image ? `${env.get('APP_URL')}/uploads/slider/${tb_app_slider_tr[0]?.nama_file_image}` : `${env.get('APP_URL')}/uploads/slider/${item.nama_file_image}`,
            }));

        }

        return { 
            success : true,
            data : {data}
        }
    }
    async slider_background({request}: HttpContext) {
        const parameters =  request.qs()

        const datas = await prisma.tb_app_slider.findFirst({            
            orderBy:{
                ordering: 'asc',
            },
            where:{
                is_background:true
            },
            include:{
                tb_app_slider_tr:true
            }
        });
        // return datas

        if (datas) {
            datas.nama_file_image= `${env.get('APP_URL')}/uploads/slider/${datas.nama_file_image}`
            if (parameters.en) {
                datas.deskripsi = datas.tb_app_slider_tr[0]?.deskripsi || datas.deskripsi
                datas.judul = datas.tb_app_slider_tr[0]?.judul || datas.judul
                datas.nama_file_image=datas.tb_app_slider_tr[0].nama_file_image != undefined ?  `${env.get('APP_URL')}/uploads/slider/${datas.tb_app_slider_tr[0].nama_file_image}` :`${env.get('APP_URL')}/uploads/slider/${datas.nama_file_image}`
            }
            delete datas?.tb_app_slider_tr
        }


        return { 
            success : true,
            data : datas
        }
    }

    /**
     * @slider
     * @summary Detail Sektor unggulan Nasional By Prov 
     */
    async sektor_unggulan_nasionals({params,response}: HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }
        const datas = await prisma.tb_sektor_daerah.findFirst({            
            include:{
                sektor_nasional:true
            },
             where: {
                id_adm_provinsi: Id,
            }
        });
      
        const data = {
           id_sektor_nasional:datas?.sektor_nasional.id_sektor_nasional,
           id_adm_provinsi:datas?.id_adm_provinsi,
           deskripsi:datas?.sektor_nasional.deskripsi,
           deskripsi_singkat:datas?.sektor_nasional.deskripsi_singkat,
           file_icon:datas?.sektor_nasional.file_icon,
           file_image:datas?.sektor_nasional.file_image,
           potensi_pasar:datas?.sektor_nasional.potensi_pasar,
           status:datas?.sektor_nasional.status
        }

        return { 
            success : true,
            data : data
        }
    }

    async peluang_investasi_wilayah({params,response,request}: HttpContext) {
        const Id = parseInt(params.id_sektor, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const parameters =  request.qs()

       

        const datas = await prisma.tb_peluang_kabkot.findMany({
            where: {
                    // id_sektor:Id,
                    tb_peluang_sektor:{
                        id_kategori_sektor:Id
                    },
                    status:"99",
                    id_prioritas:1,
            },
            include:{
                tb_peluang_sektor:{
                    include:{
                        tb_peluang_sektor_tr:true
                    }
                },
                tb_peluang_kabkot_tr:true,
            }

        });

        let data
        
        if (!parameters.en) {
            data = await Promise.all(datas.map(async ({tb_peluang_sektor,...data}) => ({
                ...data,
                deskripsi:data.deskripsi != null ? striptags(data.deskripsi).replaceAll("\u003Cp\u003E","\n") : '',
                deskripsi_singkat:data.deskripsi_singkat != null ? striptags(data.deskripsi_singkat).replaceAll("\u003Cp\u003E","\n") : '',
                nilai_irr: `${data.nilai_irr}%`,
                nilai_investasi: data.nilai_investasi != null ? numberFormatRpSingkatan(data.nilai_investasi) : '',
                nilai_npv: data.nilai_npv != null ? numberFormatRpSingkatan(data.nilai_npv) : '',
                nilai_pp : `${data.nilai_pp} Tahun`,
                nama_sektor : tb_peluang_sektor.nama,
                icon:`${env.get('APP_URL')}/uploads/icon-web/${tb_peluang_sektor.icon}` ,
                iconmap:tb_peluang_sektor.iconmap
            })));
        }else{
            data = await Promise.all(datas.map(async ({tb_peluang_sektor,tb_peluang_kabkot_tr,...data}) => ({
                ...data,
                nilai_irr: `${data.nilai_irr}%`,
                nilai_investasi: data.nilai_investasi != null ? numberFormatRpSingkatan(data.nilai_investasi) : '',
                nilai_npv: data.nilai_npv != null ? numberFormatRpSingkatan(data.nilai_npv) : '',
                nilai_pp : `${data.nilai_pp} Year`,
                nama_sektor : tb_peluang_sektor.tb_peluang_sektor_tr[0]?.nama || tb_peluang_sektor.nama,
                icon:`${env.get('APP_URL')}/uploads/icon-web/${tb_peluang_sektor.icon}` ,
                iconmap:tb_peluang_sektor.iconmap,
                keterangan : tb_peluang_kabkot_tr[0]?.keterangan || data.keterangan,
                nama : tb_peluang_kabkot_tr[0]?.nama || data.nama,
                deskripsi : tb_peluang_kabkot_tr[0]?.deskripsi || data.deskripsi,
                deskripsi_singkat : tb_peluang_kabkot_tr[0]?.deskripsi_singkat || data.deskripsi_singkat,
                lokasi_kawasan : tb_peluang_kabkot_tr[0]?.lokasi_kawasan || data.lokasi_kawasan,
            })));
        }

        

        return { 
            success : true,
            data : {data}
        }
    }

    async sektor_unggulan_wilayah({params,response}: HttpContext) {
        const Id = parseInt(params.id_adm_provinsi, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }
        const bahasa = params.bahasa || 'id'
            let data
            try {
                
            
                if (bahasa === 'id') {
                    const datas = await prisma.tb_sektor_daerah.findMany({
                        where: {
                            id_adm_provinsi: Id,
                            status: 99
                        },
                        include: {
                            sektor_nasional:{
                                include:{
                                    sektor:true
                                }
                            },
                            tb_adm_provinsi: true
                        },
                    });


                    const sektor_unggulan = datas.
                    filter((data, index, self) => 
                        index === self.findIndex((d) => d.id_sektor_nasional === data.id_sektor_nasional)
                    ).map(({ tb_adm_provinsi, ...data }) => ({
                        ...data,
                        deskripsi_singkat:decodeHtmlEntities(striptags(data.deskripsi_singkat)).replaceAll("\u003Cp\u003E","\n"),
                        deskripsi: decodeHtmlEntities(striptags(data.deskripsi).replaceAll("\u003Cp\u003E","\n")),                        
                        nama_sektor: decodeHtmlEntities(striptags(data.sektor_nasional.sektor.nama).replaceAll("\u003Cp\u003E","\n")),
                        nama_provinsi: tb_adm_provinsi?.nama,
                        icon_path : `${env.get('APP_URL')}/uploads/icon-web/${data.sektor_nasional.sektor.icon}`,
                    }));
                    
                    // const peluang = await prisma.tb_peluang_kabkot.findMany({
                    //     where: {
                    //         // id_prioritas: 1,
                    //         id_adm_kabkot:  {
                    //             gte: parseInt(`${Id}00`),
                    //             lt: parseInt(`${Id}99`) 
                    //         },
                    //         status:"99"
                    //     },
                    //     include:{
                    //         tb_adm_kabkot:true,
                    //         tb_peluang_kabkot_file:{
                    //             where: {
                    //                 tipe:1,                    
                    //             },
                    //         }
                    //     }
                    // });

                    // const peluang_investasi = peluang.map(({ tb_adm_kabkot, ...data }) => ({
                    //     ...data,
                    //     nilai_investasi: numberFormatRpLengkap(data.nilai_investasi),
                    //     nama_kabkot: tb_adm_kabkot.nama,
                    //     image : `${env.get('APP_URL')}/uploads/peluang/${data.id_adm_kabkot}/peluang/${data.tahun}/1/${data.tb_peluang_kabkot_file[0]?.nama}`,
                    //     tb_peluang_kabkot_file : ''
                    // }));

                    const [ppi, pid] = await Promise.all([
                            prisma.tb_peluang_kabkot.findMany({
                                select:{
                                    id_adm_kabkot:true,
                                    id_peluang_kabkot:true,
                                    nama:true,
                                    nilai_investasi:true,
                                    nilai_irr:true,
                                    nilai_npv:true,
                                    nilai_pp:true,
                                    id_prioritas:true,
                                    project_status_enum:true,
                                    is_ipro:true,
                                    tahun:true,
                                    tb_peluang_sektor:{
                                        select:{
                                            kategori_sektor:{
                                                select:{
                                                    nama:true,
                                                    id_kategori_sektor:true
                                                }
                                            },
                                            nama:true,
                                            icon:true
                                        }
                                    },
                                    tb_peluang_kabkot_file:{
                                        select:{
                                            nama:true
                                        },
                                        where:{
                                            tipe:1
                                        }
                                    },
                                    tb_adm_kabkot:{
                                        select:{
                                            nama:true,
                                            tb_adm_provinsi:{
                                                select:{
                                                    nama:true,
                                                    tb_adm_provinsi_tr:{
                                                        select:{
                                                            nama:true
                                                        }
                                                    }
                                                }
                                            },
                                            tb_adm_kabkot_tr:{
                                                select:{
                                                    nama:true
                                                }
                                            }
                                        }
                                    },
                                    tb_peluang_kabkot_tr:{
                                        select:{
                                            nama:true
                                        }
                                    }
                                },
                                where: {
                                        status:"99",
                                        id_prioritas:1,
                                        id_adm_kabkot: {
                                            gte: parseInt(`${Id}00`), // Id00 (misalnya, 3500)
                                            lt: parseInt(`${Id}99`),  // Id99 (misalnya, 3599)
                                        },
                                },
                                orderBy:{
                                    nilai_investasi:'desc'
                                }
                            }),
                            // prisma.$queryRaw`
                            //     SELECT 
                            //                     p.id_peluang_daerah,
                            //                     p.id_adm_provinsi,
                            //                     p.id_adm_kabkot,
                            //                     p.judul as nama, 
                            //                     p.tahun,
                            //                     pt.judul as nama_tr, 
                            //                     k.initial_invesment as nilai_investasi, 
                            //                     k.irr as nilai_irr, 
                            //                     k.npv as nilai_npv, 
                            //                     k.pp as nilai_pp,
                            //                     skn.id_kategori_sektor as id_kategori_sektor,
                            //                     skn.nama as nama_sektor,
                            //                     snf.nama as nama_sektor_peluang,
                            //                     snf.icon as icon,
                            //                     pdf.nama as images,
                            //                     p.status,
                            //                     ak.nama as nama_kabkot,
                            //                     akt.nama as nama_kabkot_tr,
                            //                     ap.nama as nama_provinsi,
                            //                     apt.nama as nama_provinsi_tr,
                            //                     p.project_status_enum,
                            //                     ps.nama as status_proyek
                            //                 FROM tb_peluang_daerah p
                            //                 LEFT JOIN tb_peluang_daerah_tr pt
                            //                     ON p.id_peluang_daerah = pt.id_peluang_daerah
                            //                 LEFT JOIN tb_peluang_daerah_kelayakan k
                            //                     ON p.id_peluang_daerah = k.id_peluang_daerah
                            //                 LEFT JOIN tb_sub_sektor_daerah ssd 
                            //                     ON p.id_sub_sektor_daerah = ssd.id_sub_sektor_daerah
                            //                 LEFT JOIN tb_sub_sektor_nasional ssn
                            //                     ON ssd.id_sub_sektor_nasional = ssn.id_sub_sektor_nasional
                            //                 LEFT JOIN tb_sektor_nasional sn
                            //                     ON sn.id_sektor_nasional = ssn.id_sektor_nasional
                            //                 LEFT JOIN tb_sektor_nasional_ref snf
                            //                     ON sn.id_sektor_nasional = snf.id_sektor
                            //                 LEFT JOIN tb_kategori_sektor skn
                            //                     ON skn.id_kategori_sektor = snf.id_kategori_sektor
                            //                 LEFT JOIN tb_peluang_daerah_file pdf
                            //                     ON pdf.id_peluang_daerah = p.id_peluang_daerah AND pdf.tipe = 1
                            //                 LEFT JOIN tb_adm_kabkot ak 
                            //                     ON ak.id_adm_kabkot = p.id_adm_kabkot
                            //                 LEFT JOIN tb_adm_kabkot_tr akt
                            //                     ON ak.id_adm_kabkot = akt.id_adm_kabkot
                            //                 LEFT JOIN tb_adm_provinsi ap 
                            //                     ON ap.id_adm_provinsi = p.id_adm_provinsi
                            //                 LEFT JOIN tb_adm_provinsi_tr apt 
                            //                     ON apt.id_adm_provinsi = ap.id_adm_provinsi
                            //                 LEFT JOIN tb_peluang_status ps 
                            //                     ON ps.id_Peluang_status = p.project_status_enum
                            //                 WHERE p.status = 99
                            //                 and (p.id_adm_provinsi = ${Id} 
                            //                     OR LEFT(p.id_adm_kabkot::text, 2) = ${Id}::text)
                            //     ORDER BY k.initial_invesment desc;
                            // `
                            
                            prisma.$queryRaw`SELECT 
                                p.id_peluang_daerah,
                                p.id_adm_provinsi,
                                p.id_adm_kabkot,
                                p.judul as nama, 
                                p.tahun,
                                pt.judul as nama_tr, 
                                k.initial_invesment as nilai_investasi, 
                                k.irr as nilai_irr, 
                                k.npv as nilai_npv, 
                                k.pp as nilai_pp,
                                skn.id_kategori_sektor as id_kategori_sektor,
                                skn.nama as nama_sektor,
                                snf.nama as nama_sektor_peluang,
                                snf.icon as icon,
                                pdf.nama as images,
                                p.status,
                                ak.nama as nama_kabkot,
                                akt.nama as nama_kabkot_tr,
                                ap.nama as nama_provinsi,
                                apt.nama as nama_provinsi_tr,
                                p.project_status_enum,
                                ps.nama as status_proyek
                            FROM tb_peluang_daerah p
                            LEFT JOIN tb_peluang_daerah_tr pt
                                ON p.id_peluang_daerah = pt.id_peluang_daerah
                            LEFT JOIN tb_peluang_daerah_kelayakan k
                                ON p.id_peluang_daerah = k.id_peluang_daerah
                            LEFT JOIN tb_sub_sektor_daerah ssd 
                                ON p.id_sub_sektor_daerah = ssd.id_sub_sektor_daerah
                            LEFT JOIN tb_sub_sektor_nasional ssn
                                ON ssd.id_sub_sektor_nasional = ssn.id_sub_sektor_nasional
                            LEFT JOIN tb_sektor_nasional sn
                                ON sn.id_sektor_nasional = ssn.id_sektor_nasional
                            LEFT JOIN tb_sektor_nasional_ref snf
                                ON sn.id_sektor_nasional = snf.id_sektor
                            LEFT JOIN tb_kategori_sektor skn
                                ON skn.id_kategori_sektor = snf.id_kategori_sektor
                            LEFT JOIN (
                                SELECT id_peluang_daerah, nama
                                FROM (
                                    SELECT 
                                        id_peluang_daerah, 
                                        nama, 
                                        ROW_NUMBER() OVER (PARTITION BY id_peluang_daerah ORDER BY id_peluang_daerah_file ASC) as rn
                                    FROM tb_peluang_daerah_file
                                    WHERE tipe = 1
                                ) t
                                WHERE rn = 1
                            ) pdf
                                ON pdf.id_peluang_daerah = p.id_peluang_daerah
                            LEFT JOIN tb_adm_kabkot ak 
                                ON ak.id_adm_kabkot = p.id_adm_kabkot
                            LEFT JOIN tb_adm_kabkot_tr akt
                                ON ak.id_adm_kabkot = akt.id_adm_kabkot
                            LEFT JOIN tb_adm_provinsi ap 
                                ON ap.id_adm_provinsi = p.id_adm_provinsi
                            LEFT JOIN tb_adm_provinsi_tr apt 
                                ON apt.id_adm_provinsi = ap.id_adm_provinsi
                            LEFT JOIN tb_peluang_status ps 
                                ON ps.id_Peluang_status = p.project_status_enum
                            WHERE p.status = 99
                                AND (p.id_adm_provinsi = ${Id} 
                                    OR LEFT(p.id_adm_kabkot::text, 2) = ${Id}::text)
                            ORDER BY k.initial_invesment DESC;`
                    
                        ]);
                            // Mapping data dengan keterangan masing-masing
                            const proyeks = [
                                
                            ...ppi.map(item => {
                                    const status = {
                                        2: 'DIMINATI',
                                        7: 'SOLD',
                                    };
                    
                                    let status_proyek = '';
                    
                                    if (item.project_status_enum !== '') {
                                        status_proyek = status[item.project_status_enum];
                                    }
                                    let kabkot = item.tb_adm_kabkot.nama
                                    let prov = item.tb_adm_kabkot.tb_adm_provinsi.nama
                                    let nama = item.nama
                                    if (bahasa=='en') {
                                        kabkot = item.tb_adm_kabkot.tb_adm_kabkot_tr[0]?.nama || item.tb_adm_kabkot.nama
                                        prov = item.tb_adm_kabkot.tb_adm_provinsi.tb_adm_provinsi_tr[0]?.nama || item.tb_adm_kabkot.tb_adm_provinsi.nama
                                        nama = item.tb_peluang_kabkot_tr[0]?.nama || item.nama
                                    }
                    
                                    return {
                                        id_peluang: item.id_peluang_kabkot,
                                        id_adm_provinsi: parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                                        id_adm_kabkot: item.id_adm_kabkot,
                                        nama_kabkot: kabkot,
                                        nama_provinsi: prov,
                                        nama: nama,
                                        tahun:item.tahun,
                                        nilai_irr: `${item.nilai_irr}%`,
                                        nilai_investasi: numberFormatRpSingkatan(item.nilai_investasi),
                                        nilai_npv: numberFormatRpSingkatan(item.nilai_npv),
                                        nilai_pp: item.nilai_pp,
                                        id_kategori_sektor: item.tb_peluang_sektor.kategori_sektor?.id_kategori_sektor,
                                        nama_sektor: item.tb_peluang_sektor.kategori_sektor?.nama,
                                        nama_sektor_peluang: item.tb_peluang_sektor.nama,
                                        project_status_enum: item.project_status_enum,
                                        status_proyek, // Properti status_proyek ditambahkan (opsional, jika dibutuhkan)
                                        status: item.is_ipro ? 'IPRO' : 'PPI',
                                        icon: `${env.get('APP_URL')}/uploads/icon-web/${item.tb_peluang_sektor.icon}`,
                                        image: `${env.get('APP_URL')}/uploads/peluang/${item.id_adm_kabkot}/peluang/${item.tahun}/${item.id_prioritas}/${item.tb_peluang_kabkot_file[0]?.nama || 'default.jpg'}`, // Fallback untuk nama file
                                    };
                                }),
                            ...pid.map(item => {
                                    let kabkot = item.nama_kabkot
                                    let prov = item.nama_provinsi
                                    let nama = item.nama
                                    if (bahasa == 'en') {
                                        kabkot = item.nama_kabkot_tr || kabkot
                                        prov =item.nama_provinsi_tr || prov
                                        nama =item.nama_tr || nama
                                    }
                                    
                                return {
                                    id_peluang:item.id_peluang_daerah,
                                    id_adm_provinsi:item.id_adm_provinsi,
                                    id_adm_kabkot:item.id_adm_kabkot,
                                    nama_kabkot:kabkot,
                                    nama_provinsi:prov,
                                    nama:nama,
                                    tahun:item.tahun,
                                    nilai_irr: `${item.nilai_irr}%`,
                                    nilai_investasi: numberFormatRpSingkatan(item.nilai_investasi),
                                    nilai_npv: numberFormatRpSingkatan(item.nilai_npv),
                                    nilai_pp:item.nilai_pp,
                                    id_kategori_sektor:item.id_kategori_sektor,
                                    nama_sektor:item.nama_sektor,
                                    nama_sektor_peluang:item.nama_sektor_peluang,
                                    project_status_enum:item.project_status_enum == null ? 0 : item.project_status_enum,
                                    status_proyek:item.status_proyek,
                                    status: 'PID',
                                    icon: `${env.get('APP_URL')}/uploads/icon-web/${item.icon}`,
                                    image:`${env.get('APP_URL')}/uploads/peluang_daerah/${item.id_peluang_daerah}/${item.images}`
                                    }
                            })
                        ]

                        const uniqueProyeks = proyeks.reduce((unique, current) => {
                            if (!unique.some(item => item.nama === current.nama)) {
                              unique.push(current);
                            }
                            return unique;
                          }, []);


                    return { 
                        success : true,
                        data : {sektor_unggulan,peluang_investasi:uniqueProyeks}
                    }
                }else{
                    const datas = await prisma.tb_sektor_daerah.findMany({
                        where: {
                            id_adm_provinsi: Id,
                            status: 99
                        },
                        include: {
                            sektor_nasional: {
                                include:{
                                    sektor_nasional_tr:true,
                                    sektor:{
                                        include:{
                                            sektor_nasional_tr:true
                                        }
                                    }
                                }
                            },
                            tb_adm_provinsi: {
                                include:{
                                    tb_adm_provinsi_tr:true
                                }
                            },
                            tb_sektor_daerah_tr:true
                        },
                    });


                    const sektor_unggulan = datas.map(({ tb_sektor_daerah_tr,tb_adm_provinsi,sektor_nasional, ...data }) => ({
                        ...data,
                        deskripsi_singkat: tb_sektor_daerah_tr?.deskripsi_singkat ? decodeHtmlEntities(striptags(tb_sektor_daerah_tr.deskripsi_singkat)) : decodeHtmlEntities(striptags(data.deskripsi_singkat)),
                        deskripsi : tb_sektor_daerah_tr?.deskripsi ? decodeHtmlEntities(    striptags(tb_sektor_daerah_tr?.deskripsi)) : decodeHtmlEntities(striptags(data.deskripsi)),
                        nama_sektor: sektor_nasional.sektor.sektor_nasional_tr?.nama ? sektor_nasional.sektor.sektor_nasional_tr?.nama : sektor_nasional.sektor.nama,
                        nama_provinsi: tb_adm_provinsi?.tb_adm_provinsi_tr[0]?.nama ? tb_adm_provinsi?.tb_adm_provinsi_tr[0]?.nama : tb_adm_provinsi?.nama ,
                        icon_path : `${env.get('APP_URL')}/uploads/icon-web/${sektor_nasional.sektor.icon}`
                    }));
                    
                    
                    // const peluang = await prisma.tb_peluang_kabkot.findMany({
                    //     where: {
                    //         id_prioritas: 1,
                    //         id_adm_kabkot:  {
                    //             gte: parseInt(`${Id}00`),
                    //             lt: parseInt(`${Id}99`) 
                    //         },
                    //         status:"99"
                    //     },
                    //     include:{
                    //         tb_adm_kabkot:{
                    //             include:{
                    //                 tb_adm_kabkot_tr:true
                    //             }
                    //         },
                    //         tb_peluang_kabkot_file:{
                    //             where: {
                    //                 tipe:1,                    
                    //             },
                    //         },
                    //         tb_peluang_kabkot_tr:true
                    //     }
                    // });

                    // const peluang_investasi = peluang.map(({ tb_adm_kabkot, ...data }) => ({
                    //     ...data,
                    //     nama: data.tb_peluang_kabkot_tr[0]?.nama,
                    //     nama_singkat: data.tb_peluang_kabkot_tr[0]?.nama_singkat,
                    //     keterangan: data.tb_peluang_kabkot_tr[0]?.keterangan,
                    //     nilai_investasi: numberFormatRpLengkap(data.nilai_investasi),
                    //     nama_kabkot: tb_adm_kabkot.tb_adm_kabkot_tr[0]?.nama ? tb_adm_kabkot.tb_adm_kabkot_tr[0]?.nama : tb_adm_kabkot.nama,
                    //     image : `${env.get('APP_URL')}/uploads/peluang/${data.id_adm_kabkot}/peluang/${data.tahun}/1/${data.tb_peluang_kabkot_file[0]?.nama}`,
                    //     tb_peluang_kabkot_file : ''
                    // }));

                    const [ppi, pid] = await Promise.all([
                            prisma.tb_peluang_kabkot.findMany({
                                select:{
                                    id_adm_kabkot:true,
                                    id_peluang_kabkot:true,
                                    nama:true,
                                    nilai_investasi:true,
                                    nilai_irr:true,
                                    nilai_npv:true,
                                    nilai_pp:true,
                                    id_prioritas:true,
                                    project_status_enum:true,
                                    is_ipro:true,
                                    tahun:true,
                                    tb_peluang_sektor:{
                                        select:{
                                            kategori_sektor:{
                                                select:{
                                                    nama:true,
                                                    id_kategori_sektor:true
                                                }
                                            },
                                            nama:true,
                                            icon:true
                                        }
                                    },
                                    tb_peluang_kabkot_file:{
                                        select:{
                                            nama:true
                                        },
                                        where:{
                                            tipe:1
                                        }
                                    },
                                    tb_adm_kabkot:{
                                        select:{
                                            nama:true,
                                            tb_adm_provinsi:{
                                                select:{
                                                    nama:true,
                                                    tb_adm_provinsi_tr:{
                                                        select:{
                                                            nama:true
                                                        }
                                                    }
                                                }
                                            },
                                            tb_adm_kabkot_tr:{
                                                select:{
                                                    nama:true
                                                }
                                            }
                                        }
                                    },
                                    tb_peluang_kabkot_tr:{
                                        select:{
                                            nama:true
                                        }
                                    }
                                },
                                where: {
                                        status:"99",
                                        // id_prioritas:1,
                                        is_ikn:{
                                            not:null
                                        },
                                        id_adm_kabkot: {
                                            gte: parseInt(`${Id}00`), // Id00 (misalnya, 3500)
                                            lt: parseInt(`${Id}99`),  // Id99 (misalnya, 3599)
                                        },
                                },
                                orderBy:{
                                    nilai_investasi:'desc'
                                }
                            }),
                            prisma.$queryRaw`
                                SELECT 
                                                p.id_peluang_daerah,
                                                p.id_adm_provinsi,
                                                p.id_adm_kabkot,
                                                p.judul as nama, 
                                                p.tahun,
                                                pt.judul as nama_tr, 
                                                k.initial_invesment as nilai_investasi, 
                                                k.irr as nilai_irr, 
                                                k.npv as nilai_npv, 
                                                k.pp as nilai_pp,
                                                skn.id_kategori_sektor as id_kategori_sektor,
                                                skn.nama as nama_sektor,
                                                snf.nama as nama_sektor_peluang,
                                                snf.icon as icon,
                                                pdf.nama as images,
                                                p.status,
                                                ak.nama as nama_kabkot,
                                                akt.nama as nama_kabkot_tr,
                                                ap.nama as nama_provinsi,
                                                apt.nama as nama_provinsi_tr,
                                                p.project_status_enum,
                                                ps.nama as status_proyek
                                            FROM tb_peluang_daerah p
                                            LEFT JOIN tb_peluang_daerah_tr pt
                                                ON p.id_peluang_daerah = pt.id_peluang_daerah
                                            LEFT JOIN tb_peluang_daerah_kelayakan k
                                                ON p.id_peluang_daerah = k.id_peluang_daerah
                                            LEFT JOIN tb_sub_sektor_daerah ssd 
                                                ON p.id_sub_sektor_daerah = ssd.id_sub_sektor_daerah
                                            LEFT JOIN tb_sub_sektor_nasional ssn
                                                ON ssd.id_sub_sektor_nasional = ssn.id_sub_sektor_nasional
                                            LEFT JOIN tb_sektor_nasional sn
                                                ON sn.id_sektor_nasional = ssn.id_sektor_nasional
                                            LEFT JOIN tb_sektor_nasional_ref snf
                                                ON sn.id_sektor_nasional = snf.id_sektor
                                            LEFT JOIN tb_kategori_sektor skn
                                                ON skn.id_kategori_sektor = snf.id_kategori_sektor
                                            LEFT JOIN tb_peluang_daerah_file pdf
                                                ON pdf.id_peluang_daerah = p.id_peluang_daerah AND pdf.tipe = 1
                                            LEFT JOIN tb_adm_kabkot ak 
                                                ON ak.id_adm_kabkot = p.id_adm_kabkot
                                            LEFT JOIN tb_adm_kabkot_tr akt
                                                ON ak.id_adm_kabkot = akt.id_adm_kabkot
                                            LEFT JOIN tb_adm_provinsi ap 
                                                ON ap.id_adm_provinsi = p.id_adm_provinsi
                                            LEFT JOIN tb_adm_provinsi_tr apt 
                                                ON apt.id_adm_provinsi = ap.id_adm_provinsi
                                            LEFT JOIN tb_peluang_status ps 
                                                ON ps.id_Peluang_status = p.project_status_enum
                                            WHERE p.status = 99
                                            and p.id_adm_provinsi = ${Id} 
                                            --and (p.id_adm_provinsi = ${Id} 
                                                --OR LEFT(p.id_adm_kabkot::text, 2) = ${Id}::text)
                                ORDER BY k.initial_invesment desc;
                            `
                            ]);
                            // Mapping data dengan keterangan masing-masing
                            const proyeks = [
                                
                            ...ppi.map(item => {
                                    const status = {
                                        2: 'DIMINATI',
                                        7: 'SOLD',
                                    };
                    
                                    let status_proyek = '';
                    
                                    if (item.project_status_enum !== '') {
                                        status_proyek = status[item.project_status_enum];
                                    }
                                    let kabkot = item.tb_adm_kabkot.nama
                                    let prov = item.tb_adm_kabkot.tb_adm_provinsi.nama
                                    let nama = item.nama
                                    if (bahasa == 'en') {
                                        kabkot = item.tb_adm_kabkot.tb_adm_kabkot_tr[0]?.nama || item.tb_adm_kabkot.nama
                                        prov = item.tb_adm_kabkot.tb_adm_provinsi.tb_adm_provinsi_tr[0]?.nama || item.tb_adm_kabkot.tb_adm_provinsi.nama
                                        nama = item.tb_peluang_kabkot_tr[0]?.nama || item.nama
                                    }
                    
                                    return {
                                        id_peluang: item.id_peluang_kabkot,
                                        id_adm_provinsi: parseInt(item.id_adm_kabkot.toString().slice(0, 2)),
                                        id_adm_kabkot: item.id_adm_kabkot,
                                        nama_kabkot: kabkot,
                                        nama_provinsi: prov,
                                        nama: nama,
                                        tahun:item.tahun,
                                        nilai_irr: `${item.nilai_irr}%`,
                                        nilai_investasi: numberFormatRpSingkatan(item.nilai_investasi),
                                        nilai_npv: numberFormatRpSingkatan(item.nilai_npv),
                                        nilai_pp: item.nilai_pp,
                                        id_kategori_sektor: item.tb_peluang_sektor.kategori_sektor?.id_kategori_sektor,
                                        nama_sektor: item.tb_peluang_sektor.kategori_sektor?.nama,
                                        nama_sektor_peluang: item.tb_peluang_sektor.nama,
                                        project_status_enum: item.project_status_enum,
                                        status_proyek, // Properti status_proyek ditambahkan (opsional, jika dibutuhkan)
                                        status: item.is_ipro ? 'IPRO' : 'PPI',
                                        icon: `${env.get('APP_URL')}/uploads/icon-web/${item.tb_peluang_sektor.icon}`,
                                        image: `${env.get('APP_URL')}/uploads/peluang/${item.id_adm_kabkot}/peluang/${item.tahun}/${item.id_prioritas}/${item.tb_peluang_kabkot_file[0]?.nama || 'default.jpg'}`, // Fallback untuk nama file
                                    };
                                }),
                            ...pid.map(item => {
                                    let kabkot = item.nama_kabkot
                                    let prov = item.nama_provinsi
                                    let nama = item.nama
                                    if (bahasa == 'en') {
                                        kabkot = item.nama_kabkot_tr || kabkot
                                        prov =item.nama_provinsi_tr || prov
                                        nama =item.nama_tr || nama
                                    }
                                    
                                return {
                                    id_peluang:item.id_peluang_daerah,
                                    id_adm_provinsi:item.id_adm_provinsi,
                                    id_adm_kabkot:item.id_adm_kabkot,
                                    nama_kabkot:kabkot,
                                    nama_provinsi:prov,
                                    nama:nama,
                                    tahun:item.tahun,
                                    nilai_irr: `${item.nilai_irr}%`,
                                    nilai_investasi: numberFormatRpSingkatan(item.nilai_investasi),
                                    nilai_npv: numberFormatRpSingkatan(item.nilai_npv),
                                    nilai_pp:item.nilai_pp,
                                    id_kategori_sektor:item.id_kategori_sektor,
                                    nama_sektor:item.nama_sektor,
                                    nama_sektor_peluang:item.nama_sektor_peluang,
                                    project_status_enum:item.project_status_enum == null ? 0 : item.project_status_enum,
                                    status_proyek:item.status_proyek,
                                    status: 'PID',
                                    icon: `${env.get('APP_URL')}/uploads/icon-web/${item.icon}`,
                                    image:`${env.get('APP_URL')}/uploads/peluang_daerah/${item.id_peluang_daerah}/${item.images}`
                                    }
                            })
                        ]
                        const uniqueProyeks = proyeks.reduce((unique, current) => {
                            if (!unique.some(item => item.nama === current.nama)) {
                              unique.push(current);
                            }
                            return unique;
                          }, []);


                    return { 
                        success : true,
                        data : {sektor_unggulan,peluang_investasi:uniqueProyeks}
                    }
                }

            } catch (error) {
                
            }
    }

     async sektor_unggulan_nasional({params,response}: HttpContext) {
        const Id = parseInt(params.id_sektor, 10)
        if (isNaN(Id)) {
            return response.status(400).send({ error: 'Invalid ID provided' })
        }

        const bahasa = params.bahasa || 'id'
            let data
            if (bahasa === 'id') {
                const sektor_nasional = await prisma.tb_sektor_nasional.findFirst({
                    where:{
                        id_sektor_nasional:Id
                    }
                })

                const data = await prisma.$queryRaw`
                    WITH RankedData AS (
                        SELECT 
                        nas.id_sektor,
                        nas.nama AS nama_sektor,
                        nv.nama AS nama_detail,
                        nvd.tahun,
                        nvd.numeric_value,
                        nv.satuan,
                        ROW_NUMBER() OVER (PARTITION BY nv.nama ORDER BY nvd.tahun DESC) AS rn
                        FROM tb_sektor_nasional_ref nas
                        JOIN tb_sektor_nasional_value nv 
                        ON nv.id_sektor_nasional = nas.id_sektor
                        JOIN tb_sektor_nasional_value_detail nvd 
                        ON nvd.id_sektor_nasional_value = nv.id_sektor_nasional_value
                        WHERE nas.id_sektor = ${Id}
                    )
                    SELECT 
                        id_sektor, 
                        nama_sektor, 
                        nama_detail, 
                        tahun, 
                        numeric_value,
                        satuan
                    FROM RankedData
                    WHERE rn = 1;
                    `;

                const datas = data.map(item => {
                    let nilai = numberFormat(item.numeric_value)
                    if(item.satuan == 'USD'){
                        nilai = numberFormatLengkap(item.numeric_value)
                    }else if(item.satuan == 'rupiah'){
                        nilai = numberFormatLengkap(item.numeric_value)
                        
                    }else if(item.satuan == '%'){
                        nilai = item.numeric_value
                    }
                    return {
                        id_sektor: item.id_sektor,
                        nama_sektor: item.nama_sektor,
                        nama_detail: item.nama_detail,
                        tahun: item.tahun,
                        numeric_value: nilai,
                        satuan: item.satuan
                    }
                })

                return { 
                    success : true,
                    data : {
                        deskripsi : sektor_nasional?.deskripsi_singkat,
                        data :datas
                    }
                } 
                
            }else{
                const sektor_nasional = await prisma.tb_sektor_nasional_tr.findFirst({
                    where:{
                        id_sektor_nasional:Id
                    }
                })

                const data = await prisma.$queryRaw`
                     WITH RankedData AS (
                        SELECT 
                        nas.id_sektor,
                        nast.nama AS nama_sektor,
                        nvt.nama AS nama_detail,
                        nvd.tahun,
                        nvd.numeric_value,
                        nvt.satuan,
                        ROW_NUMBER() OVER (PARTITION BY nvt.nama ORDER BY nvd.tahun DESC) AS rn
                        FROM tb_sektor_nasional_ref nas
                        JOIN tb_sektor_nasional_value nv 
                        ON nv.id_sektor_nasional = nas.id_sektor
                        join tb_sektor_nasional_value_tr nvt
                        on nvt.id_sektor_nasional_value = nv.id_sektor_nasional_value
                        JOIN tb_sektor_nasional_value_detail nvd 
                        ON nvd.id_sektor_nasional_value = nv.id_sektor_nasional_value
                        JOIN tb_sektor_nasional_ref_tr nast 
                        ON nast.id_sektor=nas.id_sektor
                        WHERE nas.id_sektor =${Id}
                    )
                    SELECT 
                        id_sektor, 
                        nama_sektor, 
                        nama_detail, 
                        tahun, 
                        numeric_value,
                        satuan
                    FROM RankedData
                    WHERE rn = 1
                    `;

                const datas = data.map(item => {
                    let nilai = numberFormat(item.numeric_value)
                    if(item.satuan == 'USD'){
                        nilai = numberFormatLengkapEn(item.numeric_value)
                    }else if(item.satuan == 'rupiah'){
                        nilai = numberFormatLengkapEn(item.numeric_value)
                        
                    }else if(item.satuan == '%'){
                        nilai = item.numeric_value
                    }
                    return {
                        id_sektor: item.id_sektor,
                        nama_sektor: item.nama_sektor,
                        nama_detail: item.nama_detail,
                        tahun: item.tahun,
                        numeric_value: nilai,
                        satuan: item.satuan
                    }
                })

                return { 
                    success : true,
                    data : {
                        deskripsi : sektor_nasional?.deskripsi_singkat,
                        data :datas
                    }
                } 

            }

           
                
            
    }
    public async umkm({ response }:HttpContext) {
        try {
            const data = await prisma.tb_umkm.groupBy({
                        by: ['bidang_usaha'],
                        _max: {
                            id_umkm: true,
                            nama: true,
                            image: true,
                            nama_kabkot:true,
                            nama_provinsi:true,
                            email:true
                        },
                        take: 5,
                        orderBy: {
                            bidang_usaha: 'asc', // Atur urutan hasil berdasarkan 'bidang_usaha'
                        },
                        });

                const datas = data.map(item => {
                    return {
                        id_umkm: item._max.id_umkm,
                        nama: item._max.nama,
                        nama_kabkot: item._max.nama_kabkot,
                        nama_provinsi: item._max.nama_provinsi,
                        email: item._max.email,
                        bidang_usaha: item.bidang_usaha,
                        image : `${env.get('APP_URL')}/uploads/umkm/${item._max.image}`
                    };
                });
            return { 
                success : true,
                data : {datas}
            }
        } catch (error) {
        console.error(error);
        return response.status(500).json({
            status: 'error',
            message: 'Error fetching data',
        });
        }
    }

    public async link_terkait({ response }:HttpContext) {
        try {
            const data = await prisma.tb_link_terkait.findMany({
            where:{
                status:99
            },
        })
        const datas = data.map(({  ...data }) => ({
            ...data,
            file_logo : `${env.get('APP_URL')}/uploads/icon-web/${data.file_logo}`,
            file_image :   data.file_image ? `${env.get('APP_URL')}/uploads/link_terkait/${data.file_image}` : null
        }));
                    
            return { 
                success : true,
                data : datas
            }
        } catch (error) {
        console.error(error);
        return response.status(500).json({
            status: 'error',
            message: 'Error fetching data',
        });
        }
    }
    
    public async ikn({ response ,request}:HttpContext) {
        const ikns = await prisma.tb_ikn.findMany({
            include:{
                tb_ikn_tr:true
            }
        })

        const parameters =  request.qs()
        let ikn
        if (!parameters.en) {            
            ikn = await Promise.all(ikns.map(async (item) => {
                let vidio = '';
                const check = await checkFileOrUrl(item.file_video);
                
                if (check == 'file') {
                    vidio = `${env.get('APP_URL')}/uploads/ikn/${item.file_video}`;
                } else {
                    vidio = item.file_video;
                }

                return {
                    ...item,
                    file_image: `${env.get('APP_URL')}/uploads/ikn/${item.file_image}`,
                    file_logo: `${env.get('APP_URL')}/uploads/ikn/${item.file_logo}`,
                    file_video: vidio,
                };
            }));
        }else{
            ikn = await Promise.all(ikns.map(async (item) => {
                let vidio = '';
                const check = await checkFileOrUrl(item.file_video);
                
                if (check == 'file') {
                    vidio = `${env.get('APP_URL')}/uploads/ikn/${item.file_video}`;
                } else {
                    vidio = item.file_video;
                }

                return {
                    nama : item.tb_ikn_tr[0]?.nama || item.nama,
                    deskripsi : item.tb_ikn_tr[0]?.deskripsi || item.deskripsi,
                    file_image: `${env.get('APP_URL')}/uploads/ikn/${item.file_image}`,
                    file_logo: `${env.get('APP_URL')}/uploads/ikn/${item.file_logo}`,
                    file_video: vidio,
                };
            }));
        }
                
        return {
                success :true,
                data:{
                    ikn : ikn
                }
        }
    }

    public async news({ response }:HttpContext) {
        const data = await prisma.tb_news.findMany({
            take : 5
        })
        const datas = data.map(({  ...data }) => ({
            ...data,
            file_cover : `${env.get('APP_URL')}/uploads/ikn/${data.file_cover}`
        }));
        
        return {
                success :true,
                data:datas
        }
    }
    public async vidio({ response }:HttpContext) {
        const data = await prisma.tb_vidio_beranda.findMany({
            take : 1
        })
  
        return {
                success :true,
                data:data[0]
        }
    }

    public async search({ request, params }: HttpContext) {
        const search = params.search.replace('%20',' ');

        // Ambil parameter page dan pageSize dari query string
        const page = parseInt(params.page) || 1; // Default halaman pertama
        const pageSize = parseInt(params.pageSize) || 10; // Default 5 item per halaman
        const isNumber = !isNaN(Number(search));
        const parameter = request.qs()
        const searchCondition = {
            AND: [ // Gunakan AND untuk menggabungkan kondisi lain dengan OR
                { status: "99" }, // Kondisi untuk status = 99
                { id_prioritas: 1 }, // Kondisi untuk id_prioritas = 1
                {
                    OR: [
                        { nama: { contains: search, mode: 'insensitive' } }, 
                        { keterangan: { contains: search, mode: 'insensitive' } }, 
                        { deskripsi: { contains: search, mode: 'insensitive' } }, 
                        { deskripsi_singkat: { contains: search, mode: 'insensitive' } }, 
                        { lokasi_kawasan: { contains: search, mode: 'insensitive' } }, 
                        ...(isNumber
                            ? [{ tahun: { equals: Number(search) } }] // Jika search adalah angka, gunakan equals
                            : []),
                        {
                            tb_adm_kabkot: {
                                nama: { contains: search, mode: 'insensitive' }, 
                            },
                        },
                        {
                            tb_peluang_sektor: {
                                nama: { contains: search, mode: 'insensitive' }, 
                            },
                        },
                    ],
                }
            ]
        };
        // const searchConditionKebijakan = {
        //     OR: [
        //         { judul: { contains: search, mode: 'insensitive' } }, 
        //     ],
        // };

        const totalData = await prisma.tb_peluang_kabkot.count({
            where: searchCondition,
        });

        const data = await prisma.tb_peluang_kabkot.findMany({
            where: searchCondition,
            include: {
                tb_adm_kabkot: {
                    include:{
                        tb_adm_kabkot_tr:true
                    }
                },
                tb_peluang_sektor: {
                    include:{
                        tb_peluang_sektor_tr:true
                    }
                },
                tb_peluang_kabkot_kebijakan_mapping:{
                    include:{
                        tb_kebijakan:true
                    }
                },
                tb_peluang_kabkot_tr:true
            },
            skip: (page - 1) * pageSize, // Mulai dari item ke berapa
            take: pageSize, // Ambil jumlah item sesuai pageSize
        });

        // const dataKebijakan = await prisma.tb_kebijakan.findMany({
        //     where: searchConditionKebijakan,
        //     // skip: (page - 1) * pageSize, 
        //     // take: pageSize, 
        // });

        // const mappedKebijakan = dataKebijakan.map(item => ({
        //     ...item,
        //     nama_file: `${env.get('APP_URL')}/uploads/kebijakan/${item?.nama_file }`,
        // }));
        // return data
        let mappedData
        if (!parameter.en) {
            
            mappedData = data.map(item => ({
                id_peluangan_kabkot: item.id_peluang_kabkot,
                nama_peluang: item.nama,
                deskripsi: item.deskripsi,
                deskripsi_singkat: item.deskripsi_singkat,
                lokasi_kawasan: item.lokasi_kawasan,
                nama_kabkot: item.tb_adm_kabkot?.nama || 'N/A',
                nama_sektor: item.tb_peluang_sektor?.nama || 'N/A',
                tahun: item.tahun,
                status: item.is_ipro == true ? 'IPRO': 'PPI',
                icon: `${env.get('APP_URL')}/uploads/icon-web/${item.tb_peluang_sektor?.icon }`,
                kebijakan:item.tb_peluang_kabkot_kebijakan_mapping.map(i => ({
                    file :`${env.get('APP_URL')}/uploads/kebijakan/${i.tb_kebijakan.nama_file }`
                }))
            }));
        }else{
            mappedData = data.map(item => ({
                id_peluangan_kabkot: item.id_peluang_kabkot,
                nama_peluang: item.tb_peluang_kabkot_tr[0].nama || item.nama,
                deskripsi: item.tb_peluang_kabkot_tr[0].deskripsi || item.deskripsi,
                deskripsi_singkat: item.tb_peluang_kabkot_tr[0].deskripsi_singkat || item.deskripsi_singkat,
                lokasi_kawasan: item.tb_peluang_kabkot_tr[0].lokasi_kawasan || item.lokasi_kawasan,
                nama_kabkot: item.tb_adm_kabkot.tb_adm_kabkot_tr[0].nama ||  item.tb_adm_kabkot?.nama || 'N/A',
                nama_sektor: item.tb_peluang_sektor.tb_peluang_sektor_tr[0]?.nama || item.tb_peluang_sektor?.nama || 'N/A',
                tahun: item.tahun,
                status: item.is_ipro == true ? 'IPRO': 'PPI',
                icon: `${env.get('APP_URL')}/uploads/icon-web/${item.tb_peluang_sektor?.icon }`,
                kebijakan:item.tb_peluang_kabkot_kebijakan_mapping.map(i => ({
                    file :`${env.get('APP_URL')}/uploads/kebijakan/${i.tb_kebijakan.nama_file }`
                }))
            }));
        }

        return {
            success: true,
            totalRecords: totalData,
            totalPage:Math.ceil(totalData / pageSize),
            page,
            pageSize,
            data:mappedData 
            
            };
    }

    public async searchPID({ request, params }: HttpContext) {
        const search = params.search.replace('%20',' ');

        // Ambil parameter page dan pageSize dari query string
        const page = parseInt(params.page) || 1; // Default halaman pertama
        const pageSize = parseInt(params.pageSize) || 10; // Default 5 item per halaman
        const isNumber = !isNaN(Number(search));
        const parameter = request.qs()
        const searchCondition = {
            AND: [ // Gunakan AND untuk menggabungkan kondisi lain dengan OR
                { status: 99 }, // Kondisi untuk status = 99
                // { id_prioritas: 1 }, // Kondisi untuk id_prioritas = 1
                {
                    OR: [
                        { judul: { contains: search, mode: 'insensitive' } }, 
                        { keterangan: { contains: search, mode: 'insensitive' } }, 
                        { aspek_pasar: { contains: search, mode: 'insensitive' } }, 
                        { aspek_teknis: { contains: search, mode: 'insensitive' } }, 
                        { lokasi: { contains: search, mode: 'insensitive' } }, 
                        ...(isNumber
                            ? [{ tahun: { equals: Number(search) } }] // Jika search adalah angka, gunakan equals
                            : []),
                        {
                            tb_adm_kabkot: {
                                nama: { contains: search, mode: 'insensitive' }, 
                            },
                        },
                        {
                            tb_adm_kabkot: {
                                tb_adm_provinsi:{
                                    nama: { contains: search, mode: 'insensitive' }, 
                                }
                            },
                        },
                        {
                            tb_sub_sektor_daerah:{
                                tb_sub_sektor_nasional:{
                                    sub_sektor_ref:{
                                        sektor_nasional_ref:{
                                            nama: { contains: search, mode: 'insensitive' }, 
                                        }
                                    }
                                }
                            },
                        },
                    ],
                }
            ]
        };
        // const searchConditionKebijakan = {
        //     OR: [
        //         { judul: { contains: search, mode: 'insensitive' } }, 
        //     ],
        // };

        const totalData = await prisma.tb_peluang_daerah.count({
            where: searchCondition,
        });

        const data = await prisma.tb_peluang_daerah.findMany({
            where: searchCondition,
            include: {
                tb_adm_kabkot: {
                    include:{
                        tb_adm_kabkot_tr:true,
                        tb_adm_provinsi:{
                            include:{
                                tb_adm_provinsi_tr:true
                            }
                        }
                    },
                },
                tb_adm_provinsi:{
                    include:{
                        tb_adm_provinsi_tr:true
                    }
                },
                tb_sub_sektor_daerah:{
                    include:{
                        tb_sub_sektor_nasional:{
                            include:{
                                sub_sektor_ref:{
                                    include:{
                                        sektor_nasional_ref:{
                                            include:{
                                                sektor_nasional_tr:true
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                tb_peluang_daerah_tr:true
            },
            skip: (page - 1) * pageSize, // Mulai dari item ke berapa
            take: pageSize, // Ambil jumlah item sesuai pageSize
        });
        // return data
       
        let mappedData
        if (!parameter.en) {
            
            mappedData = data.map(item => ({
                id_peluangan_kabkot: item.id_peluang_daerah,
                judul: item.judul,
                aspek_teknis: item.aspek_teknis,
                aspek_pasar: item.aspek_pasar,
                lokasi: item.lokasi,
                nama_kabkot: item.tb_adm_kabkot?.nama || 'N/A',
                nama_prov: item.tb_adm_kabkot?.tb_adm_provinsi?.nama || item.tb_adm_provinsi?.nama || 'N/A',
                nama_sektor: item.tb_sub_sektor_daerah?.tb_sub_sektor_nasional?.sub_sektor_ref?.sektor_nasional_ref?.sektor_nasional_ref_tr?.nama || item.tb_sub_sektor_daerah?.tb_sub_sektor_nasional?.sub_sektor_ref?.sektor_nasional_ref?.nama || 'N/A',
                tahun: item.tahun,
                icon: `${env.get('APP_URL')}/uploads/icon-web/${item.tb_sub_sektor_daerah?.tb_sub_sektor_nasional?.sub_sektor_ref?.sektor_nasional_ref?.icon}`,
            }));
        }else{
            mappedData = data.map(item => ({
                id_peluangan_kabkot: item.id_peluang_daerah,
                judul: item.tb_peluang_daerah_tr[0]?.judul || item.judul,
                aspek_teknis: item.tb_peluang_daerah_tr[0]?.aspek_teknis || item.aspek_teknis,
                aspek_pasar: item.tb_peluang_daerah_tr[0]?.aspek_pasar || item.aspek_pasar,
                lokasi: item.tb_peluang_daerah_tr[0]?.lokasi || item.lokasi,
                nama_kabkot: item.tb_adm_kabkot?.tb_adm_kabkot_tr[0]?.nama  || item.tb_adm_kabkot?.nama || 'N/A',
                nama_prov: item.tb_adm_kabkot?.tb_adm_provinsi?.tb_adm_provinsi_tr[0]?.nama || item.tb_adm_kabkot?.tb_adm_provinsi?.tb_adm_provinsi[0]?.nama || item.tb_adm_kabkot?.tb_adm_provinsi?.nama || item.tb_adm_provinsi?.nama || 'N/A',
                nama_sektor: item.tb_sub_sektor_daerah?.tb_sub_sektor_nasional?.sub_sektor_ref?.sektor_nasional_ref?.sektor_nasional_tr?.nama || item.tb_sub_sektor_daerah?.tb_sub_sektor_nasional?.sub_sektor_ref?.sektor_nasional_ref?.nama || 'N/A',
                tahun: item.tahun,
                icon: `${env.get('APP_URL')}/uploads/icon-web/${item.tb_sub_sektor_daerah?.tb_sub_sektor_nasional?.sub_sektor_ref?.sektor_nasional_ref?.icon}`,
               
            }));
        }

        return {
            success: true,
            totalRecords: totalData,
            totalPage:Math.ceil(totalData / pageSize),
            page,
            pageSize,
            data:mappedData 
            
            };
    }
    public async searchRoadmap({ response,request,params }:HttpContext) {

        const search = params.search.replace('%20',' ');
        const searchCondition = {
            AND: [ // Gunakan AND untuk menggabungkan kondisi lain dengan OR
                { status: 99 }, // Kondisi untuk status = 99
                // { id_prioritas: 1 }, // Kondisi untuk id_prioritas = 1
                {
                    OR: [
                        { judul: { contains: search, mode: 'insensitive' } }, 
                        { deskripsi: { contains: search, mode: 'insensitive' } }, 
                        { tb_komoditi_nasional_ref:{ nama: { contains: search, mode: 'insensitive' } }}, 
                        { tb_komoditi_nasional_ref:{ tb_komoditi_nasional_ref_tr:{nama: { contains: search, mode: 'insensitive' } }}}, 
                        
                    ],
                }
            ]
        };
        const datas = await prisma.tb_roadmap.findMany({
                    include:{
                        tb_komoditi_nasional_ref:{
                            include:{
                                tb_komoditi_daerah:true,
                                tb_komoditi_nasional_ref_tr:true
                            }
                        },
                        tb_roadmap_file:true
                    },
                    where:searchCondition
                })
                const parameters = request.qs()
    
                const data = datas
                    .map((item) => {
                        let nama_komoditi = item.tb_komoditi_nasional_ref.nama
                        if (parameters.en) {
                            nama_komoditi = item.tb_komoditi_nasional_ref.tb_komoditi_nasional_ref_tr?.nama || nama_komoditi
                        }
                        console.log(item.tb_komoditi_nasional_ref.tb_komoditi_nasional_ref_tr);
                        
                        return {
    
                            id_komoditi:item.id_komoditi,
                            deskripsi:item.deskripsi,
                            id_roadmap: item.id_roadmap,
                            image_komoditi : `${env.get('APP_URL')}/uploads/sektor/${item.tb_komoditi_nasional_ref.tb_komoditi_daerah[0]?.file_icon}`,
                            nama_komoditi : nama_komoditi,
                            judul: item.judul,
                            image: item.tb_roadmap_file.map((items) => 
                                `${env.get('APP_URL')}/uploads/roadmap/${items.nama}`
                            ),
                        }
                    })
                     .reduce((acc, current) => {
                        const existingKomoditi = acc.find(
                        (item) => item.nama_komoditi === current.nama_komoditi
                        );
    
                        if (existingKomoditi) {
                        // Tambahkan id_roadmap, judul, dan image ke array existingKomoditi
                        existingKomoditi.roadmaps.push({
                            id_roadmap: current.id_roadmap,
                            judul: current.judul,
                            deskripsi:current.deskripsi,
                            image: current.image[0], // gunakan hanya satu image jika diperlukan
                        });
                        } else {
                        // Jika komoditi belum ada, tambahkan objek baru ke accumulator
                        acc.push({
                            id_komoditi: current.id_komoditi,
                            image_komoditi: current.image_komoditi,
                            deskripsi:current.deskripsi,
                            nama_komoditi: current.nama_komoditi,
                            roadmaps: [
                            {
                                id_roadmap: current.id_roadmap,
                                judul: current.judul,
                                deskripsi: current.deskripsi,
                                image: current.image[0], // gunakan hanya satu image jika diperlukan
                            },
                            ],
                        });
                        }
    
                        return acc;
                    }, []);
                return {
                    success:true,
                    data : data
                }
    }
    public async searchKajian({ response,request,params }:HttpContext) {

        const search = params.search.replace('%20',' ');
        const searchCondition = {
            AND: [ // Gunakan AND untuk menggabungkan kondisi lain dengan OR
                { status: 99 }, // Kondisi untuk status = 99
                { id_news_kategori: 5 }, 
                {
                    OR: [
                        { judul: { contains: search, mode: 'insensitive' } }, 
                        { deskripsi: { contains: search, mode: 'insensitive' } }, 
                        { deskripsi_singkat: { contains: search, mode: 'insensitive' } }, 
                    ],
                }
            ]
        };
        const datas = await prisma.tb_news.findMany({
                    include:{
                        files:true,
                        translations:true,
                        tb_komoditi_nasional_ref:true
                    },
                    where:searchCondition
                })
                const parameters = request.qs()
    
                const data = datas
                    .map((item) => {
                        return {
                            id_news:item.id,            
                            komoditi:item.tb_komoditi_nasional_ref?.nama,
                            deskripsi:item.deskripsi,
                            deskripsi_singkat:item.deskripsi_singkat,
                            cover: `${env.get('APP_URL')}/uploads/kajian/cover/${item?.file_cover }`,
                            file: item.files.map((items) => 
                                `${env.get('APP_URL')}/uploads/kajian/3/${items.nama}`
                            ),
                        }
                    })
                return {
                    success:true,
                    data : data
                }
    }
    public async searchKawasan({ response,request,params }:HttpContext) {
        const search = params.search.replace('%20',' ');
        const page = parseInt(params.page) || 1; // Default halaman pertama
        const pageSize = parseInt(params.pageSize) || 10; // Default 5 item per halaman
        const isNumber = !isNaN(Number(search));
        const parameters = request.qs()
        if (!parameters.en) {   
            const searchCondition = {
                AND: [ // Gunakan AND untuk menggabungkan kondisi lain dengan OR
                    { status: "99" }, // Kondisi untuk status = 99
                    {
                        OR: [
                            { nama: { contains: search, mode: 'insensitive' } }, 
                            { keterangan: { contains: search, mode: 'insensitive' } }, 
                            { tb_kawasan_industri_kategori: { nama: { contains: search, mode: 'insensitive' } } }, 
                        ],
                    }
                ]
            };

            const datas = await prisma.tb_kawasan_industri.findMany({
                        where:searchCondition,
                        include:{
                            tb_kawasan_industri_kategori:true
                        }
                        // take: pageSize,
                        // skip: (page - 1) * pageSize,
                        
                    })
                            
                        const data = datas
                            .map((item) => {
                                return {
                                    id_kawasan_industri:item.id_kawasan_industri,            
                                    nama:item.nama,
                                    keterangan:item.keterangan,
                                    
                                }
                            })
                        return {
                            success:true,
                            data : data
                        
                        }
        }else{
            const searchCondition = {
                AND: [
                  { status: "99" },
                  {
                    OR: [
                      { 
                        tb_kawasan_industri_tr: {
                          some: {
                            nama: { contains: search, mode: 'insensitive' }
                          }
                        }
                      },
                      { 
                        tb_kawasan_industri_tr: {
                          some: {
                            keterangan: { contains: search, mode: 'insensitive' }
                          }
                        }
                      }
                    ]
                  }
                ]
              };

            const datas = await prisma.tb_kawasan_industri.findMany({
                        include:{
                            tb_kawasan_industri_tr:true,
                        },
                        where:searchCondition,
                        // skip: (page - 1) * pageSize,
                        // take: pageSize,

                    })
                        const data = datas
                            .map((item) => {
                                return {
                                    id_kawasan_industri:item.id_kawasan_industri,            
                                    nama:item?.tb_kawasan_industri_tr[0]?.nama,
                                    keterangan:item?.tb_kawasan_industri_tr[0]?.keterangan,
                                    
                                }
                            })
                        return {
                            success:true,
                            data : data
                        
                        }
        }
    }

    public async running_text({ response,request }:HttpContext) {
            const today = new Date();
            today.setUTCHours(0, 0, 0, 0); // Set waktu ke 00:00:00.000 untuk memastikan hanya tanggal yang dicocokkan
            const tomorrow = new Date(today);
            tomorrow.setUTCDate(today.getUTCDate() + 1); // Batas akhir adalah hari berikutnya pada pukul 00:00:00.000
            const datas = await prisma.tb_running_text.findMany({
                where: {
                    date: {
                    gte: today.toISOString(), // Lebih besar atau sama dengan awal hari ini
                    lt: tomorrow.toISOString(), // Kurang dari awal hari esok
                    },
                },
            });
            const parameter = request.qs()
            let data
            if (!parameter.en) {
                data = datas.map((item) => {
                    return {

                        keterangan : item.keterangan
                    }
                })                
            }else{
                data = datas.map((item) => {
                    return {
                        keterangan : item.keterangan_tr ||item.keterangan
                    }
                })                

            }

  
        return {
                success :true,
                data:data
        }
    }

    public async get_peluang_kategori_sektor({ response,request }:HttpContext) {
        const datas = await prisma.tb_peluang_kabkot.findMany({
            where: {
                status:"99",
                id_prioritas:1,
            },
            include:{
                tb_peluang_sektor:{
                    include:{
                        kategori_sektor:true,
                    }
                },
                tb_peluang_kabkot_tr:true
            },
            orderBy:{
                nilai_investasi:'desc'
            }
        });
        const parameter = request.qs()
       
        interface CategoryData {
            primer: string[];
            sekunder: string[];
            tersier: string[];
        }
        
        interface EnglishCategoryData {
            primary: string[];
            secondary: string[];
            Tertiary: string[];
        }
        
        // Initialize with the correct structure
        let data: CategoryData | EnglishCategoryData;
        
        if (!parameter.en) {
            data = {
                primer: [],
                sekunder: [],
                tersier: []
            };
            
            datas.forEach((item) => {
                if (item.tb_peluang_sektor.id_kategori_sektor == 1) {
                    (data as CategoryData).primer.push(item.nama);
                } else if (item.tb_peluang_sektor.id_kategori_sektor == 2) {
                    (data as CategoryData).sekunder.push(item.nama);
                } else {
                    (data as CategoryData).tersier.push(item.nama);
                }
            });
        } else {
            data = {
                primary: [],
                secondary: [],
                Tertiary: []
            };
            
            datas.forEach((item) => {
                if (item.tb_peluang_sektor.id_kategori_sektor == 1) {
                    (data as EnglishCategoryData).primary.push(item.tb_peluang_kabkot_tr[0]?.nama || item.nama);
                } else if (item.tb_peluang_sektor.id_kategori_sektor == 2) {
                    (data as EnglishCategoryData).secondary.push(item.tb_peluang_kabkot_tr[0]?.nama || item.nama);
                } else {
                    (data as EnglishCategoryData).Tertiary.push(item.tb_peluang_kabkot_tr[0]?.nama || item.nama);
                }
            });
        }


        return {
                success :true,
                data:data
        }
    }

}
