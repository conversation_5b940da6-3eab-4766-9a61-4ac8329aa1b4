import { HttpContext } from '@adonisjs/core/http'
import prisma from '../lib/prisma.js'
import { emailBlastValidator } from '#validators/email_blast_validator'
import env from '#start/env'
import { send_mail } from '../helpers/global_helper.js'

export default class EmailBlastController {
  /**
   * Get all users with email addresses
   */
  async getEmailUsers({ response }: HttpContext) {
    try {
      const users = await prisma.users.findMany({
        where: {
          email: {
            not: null,
          },
        },
        select: {
          id: true,
          email: true,
          full_name: true,
          first_name: true,
          last_name: true,
          status: true,
        },
        orderBy: {
          full_name: 'asc',
        },
      })

      return response.status(200).json({
        status: 'success',
        data: users,
      })
    } catch (error) {
      console.error('Error fetching email users:', error)
      return response.status(500).json({
        status: 'error',
        message: 'Failed to fetch users with email addresses',
        error: error.message,
      })
    }
  }

  /**
   * Send email blast to selected users
   */
  async sendEmailBlast({ request, response }: HttpContext) {
    try {
      const payload = await request.validateUsing(emailBlastValidator)

      const { subject, content, recipients } = payload
      // Get user emails if userIds are provided, otherwise get all users with emails
      let users
      if (recipients && recipients.length > 0) {
        users = await prisma.users.findMany({
          where: {
            id: {
              in: recipients,
            },
            email: {
              not: null,
            },
          },
          select: {
            id: true,
            email: true,
            full_name: true,
          },
        })
      }
      if (users.length === 0) {
        return response.status(400).json({
          status: 'error',
          message: 'No valid email recipients found',
        })
      }
      // Send emails to all users
      const emailPromises = users.map(async (user) => {
        if (!user.email) return null

        // Replace placeholders in content with user data
        let personalizedContent = content
        if (user.full_name) {
          personalizedContent = personalizedContent.replace(/\{\{name\}\}/g, user.full_name)
          personalizedContent = personalizedContent.replace(/\{name\}/g, user.full_name)
        }
        personalizedContent = personalizedContent.replace(/\{\{email\}\}/g, user.email || '')
        personalizedContent = personalizedContent.replace(/\{email\}/g, user.email || '')

        // Use the send_mail helper function instead of nodemailer directly
        return send_mail(user.email, subject, personalizedContent)
      })

      // Wait for all emails to be sent
      const results = await Promise.allSettled(emailPromises)

      // Log email blast
      await prisma.email_blast_logs.create({
        data: {
          subject,
          content,
          total_recipients: users.length,
          sent_at: new Date(),
        },
      })

      // Count successful and failed emails
      const successful = results.filter((result) => result.status === 'fulfilled').length
      const failed = results.filter((result) => result.status === 'rejected').length

      return response.status(200).json({
        status: 'success',
        message: `Email blast sent successfully to ${successful} recipients. Failed: ${failed}`,
        data: {
          total: users.length,
          successful,
          failed,
        },
      })
    } catch (error) {
      console.error('Error sending email blast:', error)
      return response.status(500).json({
        status: 'error',
        message: 'Failed to send email blast',
        error: error.message,
      })
    }
  }

  /**
   * Get email blast logs
   */
  async getEmailBlastLogs({ response }: HttpContext) {
    try {
      const logs = await prisma.email_blast_logs.findMany({
        orderBy: {
          sent_at: 'desc',
        },
      })

      return response.status(200).json({
        status: 'success',
        data: logs,
      })
    } catch (error) {
      console.error('Error fetching email blast logs:', error)
      return response.status(500).json({
        status: 'error',
        message: 'Failed to fetch email blast logs',
        error: error.message,
      })
    }
  }
}
