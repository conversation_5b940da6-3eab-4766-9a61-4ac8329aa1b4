import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../helpers/model_helper.js';
import uploadFile from '../../helpers/file_uploader.js';

import prisma from '../../lib/prisma.js';


// komoditi_layers_controller
export default class KawasanIndustriPeluangFileController {
  private async schemaData() {
    const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
      id_kawasan_industri_peluang_file: {column: 'id_kawasan_industri_peluang_file', alias: 'id_kawasan_industri_peluang_file', type: 'int'},
      id_kawasan_industri_peluang: {column: 'id_kawasan_industri_peluang', alias: 'id_kawasan_industri_peluang', type: 'int'},
      tipe: {column: 'tipe', alias: 'tipe', type: 'int'},
      jenis: {column: 'jenis', alias: 'jenis', type: 'int'},
      nama: {column: 'nama', alias: 'nama', type: 'string'},
      judul: {column: 'judul', alias: 'judul', type: 'string'},
      keterangan: {column: 'keterangan', alias: 'keterangan', type: 'string'}
    };
  
    const joins = {};
  
    const where = {};
  
    return {
      columns: columnMappings,
      join: joins,
      where: where,
    };
  }

  public async get({ request, params, response }: HttpContext) {
    const query = request.qs(); // Ini akan mengembalikan objek query string

    console.log(query); // { id_kawasan_industri_peluang: '201' }
    let isAll = false;

    interface Options {
      skip: number; // Keep it non-optional
      take: number;
      select?: { [key: string]: boolean };
    }

    if (query['all'] || query['all'] == 'true') {
      isAll = true
      delete query['all'];
    }

    const page = parseInt(request.input('page', 1));
    const perPage = parseInt(request.input('per_page', 10));
    delete query['page'];
    delete query['per_page'];

    let options: Options = {
      skip: (page - 1) * perPage,
      take: perPage,
    };

    if (isAll) {
      delete options['skip'];
      delete options['take'];
    }

    if (query['select']) {
      options['select'] = modelSelect((await this.schemaData()).columns, query['select']);
      delete query['select'];
    }

    options['where'] = modelWhereAnd((await this.schemaData()).columns, query);

    let data = await prisma.tb_kawasan_industri_peluang_file.findMany(options);

    data = data.map((item) => {
      return {
        ...item, // Spread item untuk mempertahankan properti yang ada
        path: `${process.env.APP_URL}/uploads/peluang_daerah/${item.tipe}/${item.nama}`,
      };
    });

    if (isAll) {
      return response.status(200).json({
        success: true,
        data: data
      });      
    }

    const totalCount = await prisma.tb_kawasan_industri_peluang_file.count();
    
    return response.status(200).json({
      success: true,
      data: data,
      pagination: {
        page: page,
        per_page: perPage,
        total_count: totalCount,
        total_pages: Math.ceil(totalCount / perPage),
      },
    });
  }

  public async getById({ request, params, response }: HttpContext) {
    interface Options {
      select?: { [key: string]: boolean };
      where?: { [key: string]: any; };
    }

    let options: Options = {};

    if (params['select']) {
      options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
      delete params['select'];
    }

    options['where'] = {
      'id_kawasan_industri_peluang_file': parseInt(params.id)
    }

    const data = await prisma.tb_kawasan_industri_peluang_file.findFirst(options);

    return response.status(200).json({
      success: true,
      data: data
    });      
  }

  public async createOrUpdate({ request, response }: HttpContext) {
    let params = request.all()

    try {
      const result = await prisma.$transaction(async (prisma) => {
        if (params.id_kawasan_industri_peluang_file) {
          // Jika `id` ada, lakukan operasi update
          const update = await prisma.tb_kawasan_industri_peluang_file.update({
            where: { id_kawasan_industri_peluang_file: params.id_kawasan_industri_peluang_file },
            data: params,
          })

          return {
            status: 'success',
            message: 'Successfully Updated Data',
            data: update,
          }
        } else {
          console.log("Sync tb_kawasan_industri_peluang_file id sequence",
            await prisma.$executeRaw`
            SELECT setval((
                SELECT PG_GET_SERIAL_SEQUENCE('"tb_kawasan_industri_peluang_file"', 'id_kawasan_industri_peluang_file')),
                (SELECT (MAX("id_kawasan_industri_peluang_file") + 1) FROM "tb_kawasan_industri_peluang_file"),
                false) FROM "tb_kawasan_industri_peluang_file";
            `);

            if (params && typeof params === 'object') {
              const model = "peluang_investasi_daerah";
              for (const key in params) {
                if (params.hasOwnProperty(key)) {
                  let filename: string = "";
                  const value = params[key];
                  const file = request.file(`[${key}]file`);

                  value['id_kawasan_industri_peluang_file'] = parseInt(value['id_kawasan_industri_peluang_file']);
                  value['id_kawasan_industri_peluang'] = parseInt(value['id_kawasan_industri_peluang']);
                  value['tipe'] = parseInt(value['tipe']);
                  value['jenis'] = parseInt(value['jenis']);

                  if (file) {
                    let uploadFileToServer = await uploadFile(file, model, value['tipe']);
                    let filenameFromServer = '';
                    if (uploadFileToServer) {
                      filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                      filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                    }
                    filename = filenameFromServer; // Set fileName if file exists
                    value['nama'] = filename;
                  }

                  if (value['id_kawasan_industri_peluang_file'] > 1) {
                    const update = await prisma.tb_kawasan_industri_peluang_file.update({
                      where: { id_kawasan_industri_peluang_file: value['id_kawasan_industri_peluang_file'] },
                      data: value,
                    })
                  } else {
                    delete value['id_kawasan_industri_peluang_file'];
                    await prisma.tb_kawasan_industri_peluang_file.create({
                      data: value,
                    });
                  }
                  // console.log(`Key: ${key}`);
                  // console.log(`Value:`, value);
                  // console.log(`Judul: ${value.judul}, Keterangan: ${value.keterangan}`);
                }
              }
  
              return {
                status: 'success',
                message: 'Successfully Upload Image',
                data: null,
              }
            } else if (Array.isArray(params)) {
              // for (const item of params) {
              //   console.log(item)
              //   await prisma.tb_kawasan_industri_peluang_file.create({
              //     data: item,
              //   });
              // }
  
              return {
                status: 'success',
                message: 'Successfully Added Data',
                data: null,
              }
            } else {
              const save = await prisma.tb_kawasan_industri_peluang_file.create({
                data: params,
              })
    
              return {
                status: 'success',
                message: 'Successfully Added Data',
                data: save,
              }
            }
        }
      })

      return response.json(result)

    } catch (error) {
      // Handle error jika ada kesalahan dalam transaksi
      return response.status(500).json({
        status: 'error',
        message: 'An error occurred while processing data',
        error: error.message,
      })
    }
  }

  public async deleteById({ request, params, response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_kawasan_industri_peluang_file.delete({
        where: {
          id_kawasan_industri_peluang_file: Id,
        },
      })

      return response.status(200).json({
        status: 'success',
        message: 'Success Delete Data',
        data: deletePost,
      })
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }  
  }
}