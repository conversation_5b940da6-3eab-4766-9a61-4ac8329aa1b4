import type { HttpContext } from '@adonisjs/core/http'
import { loginValidator, registerValidator, updateUserValidator, updateUserPasswordValidator, forgotPasswordUserValidator, resetPasswordValidator } from '#validators/auth'
import User from '#models/user'
import hash from '@adonisjs/core/services/hash'
import prisma from '../lib/prisma.js'
import RequestValidator from '../utils/RequestValidator.js'
import { send_mail } from '../helpers/global_helper.js'
import env from '#start/env'
import crypto from 'crypto'
import IamUser from '#models/iam_user'
import db from '@adonisjs/lucid/services/db'

export default class AuthController {


  /**
     * @get_paginate
     * @summary Get a list of users with pagination
     */
  async get_paginate({ params, response }: HttpContext) {
    const page = parseInt(params.page) || 1;
    const pageSize = parseInt(params.pageSize) || 10;
    const search = decodeURIComponent(params.search) == "undefined" ? '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided

    // Define the search conditions (optional)
    let searchCondition = {}; // Initialize as an empty object

    if (search !== ',' && search !== '') {
      searchCondition = {
        OR: [
          {
            first_name: { contains: search, mode: 'insensitive' },
          },
          {
            middle_name: { contains: search, mode: 'insensitive' },
          },
          {
            last_name: { contains: search, mode: 'insensitive' },
          },
          {
            login_name: { contains: search, mode: 'insensitive' },
          },
        ],
      };


    }
    // Get total record count with the search condition applied
    const totalRecords = await prisma.users.count({
      where: searchCondition, // Apply searchCondition only if it's not empty
    });
    // Calculate total pages
    const totalPage = Math.ceil(totalRecords / pageSize); // Total pages calculation

    // Fetch records with pagination and search filtering
    const datas = await prisma.users.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where: searchCondition, // Apply searchCondition only if it's not empty
      include: {
        roles: true
      }
    });
    const data = datas.map(({ ...rest }) => {
      let full_name = `${rest.first_name} ${rest.middle_name} ${rest.last_name}`
      full_name = full_name.replaceAll('null', '')

      return {
        id: rest.id,
        full_name: full_name,
        login_name: rest.login_name,
        email: rest.email,
        role_name: rest.roles.role_name
      };
    });


    return {
      success: true,
      totalRecords: totalRecords, // Total number of records in the database
      totalPage: totalPage, // Total number of pages
      page: page,
      pageSize: pageSize,
      data: data, // Data for the current page
    };
  }


  async index({ }: HttpContext) {
    const data = await prisma.users.findMany({})
    return {
      success: true,
      data
    }
  }


  async show({ params, response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }
    const data = await prisma.users.findUnique({
      where: {
        id: Id,
      },
      include: {
        tb_kawasan_user: true,
        // tb_user_internal_kabkot:true,
        tb_user_internal:true,
        tb_user_internal_provinsi:true,
        tb_user_internal_kawasan_industri:true,
        tb_adm_user: true
      }
    })

    if(data && data?.role_id == 14){
      if (data && data.tb_user_internal_provinsi.length > 0 ) {
        data[' '] = data.tb_user_internal_provinsi.map((item) => item.id_adm_provinsi)
      }else{
        data['pic_prov'] = []
      }
      if (data && data.tb_user_internal_kawasan_industri.length > 0) {
        data['kawasan_pic'] = data.tb_user_internal_kawasan_industri.map((item) => item.id_kawasan_industri)
      }else{
        data['kawasan_pic'] = []
      }
      if (data && data.tb_user_internal.length > 0) {
        data['id_jabatan'] = data.tb_user_internal[0]?.id_jabatan
      }else{
        data['id_jabatan'] = null
      }
      // delete data?.tb_user_internal
      // delete data?.tb_user_internal_provinsi
      // delete data?.tb_user_internal_kawasan_industri
      
    }


    return {
      success: true,
      data:data || []
    }
  }

  async getByEmail({ params, response }: HttpContext) {
    const data = await prisma.users.findUnique({
      where: {
        email: params.email,
      },
    })
    return {
      success: true,
      data:data || []
    }
  }


  /**
    * @store
    * @summary Insert User
    * @paramQuery login_name - Masukan Username Anda - @type(string) @required
    * @paramQuery password - Masukan Password Anda - @type(string) @required
    * @paramQuery email - Masukan Email Anda - @type(string) @required
    * @paramQuery full_name - Masukan Nama Anda - @type(string) @required
    * @paramQuery role_id - Masukan Role Anda - @type(number) @required
    */
  async store({ request }: HttpContext) {
    const data = await request.validateUsing(registerValidator)
    const prov = data.id_adm_provinsi
    const kabkot = data.id_adm_kabkot
    const kawasan = data.id_kawasan_industri
    delete data.id_adm_provinsi
    delete data.id_adm_kabkot
    delete data.id_kawasan_industri
    const pic_prov = data.pic_prov
    const kawasan_pic = data.kawasan_pic
    const jabatan = data.id_jabatan
    delete data.pic_prov
    delete data.kawasan_pic
    delete data.id_jabatan
    const user = await User.create(data)


    if (data.role_id == 15) {
      await prisma.tb_adm_user.create({
        data: {
          id_user: user.id,
          id_adm_provinsi: prov
        }
      })
    }
    if (data.role_id == 3) {
      await prisma.tb_adm_user.create({
        data: {
          id_user: user.id,
          id_adm_provinsi: prov
        }
      })
    }
    if (data.role_id == 4) {
      await prisma.tb_adm_user.create({
        data: {
          id_user: user.id,
          id_adm_kabkot: kabkot
        }
      })
    }
    if (data.role_id == 5) {
      await prisma.tb_kawasan_user.create({
        data: {
          id_user: user.id,
          id_kawasan_industri: kawasan
        }
      })
    }

    if (data.role_id == 14) {
      if (pic_prov) {
        const insData = pic_prov.map((item) => {
          return {
            id_user: user.id,
            id_adm_provinsi: item
          }
        })
        await prisma.tb_user_internal_provinsi.createMany({
          data: insData
        })
      }
      if (kawasan_pic) {
        const insData = kawasan_pic.map((item) => {
          return {
            id_user: user.id,
            id_kawasan_industri: item
          }
        })
        await prisma.tb_user_internal_kawasan_industri.createMany({
          data: insData
        })
      }

      if (jabatan) {
        await prisma.tb_user_internal.create({
          data: {
            id_user: user.id,
            id_jabatan: jabatan
          }
        })
      }
    }

    return User.accessTokens.create(user)
  }
  /**
     * @login
     * @paramQuery login_name - Masukan Username Anda - @type(string) @required
     * @paramQuery password - Masukan Password Anda - @type(string) @required
     */
  async login({ request, auth, response }: HttpContext) {
    // Additional security validation beyond middleware
    const validation = RequestValidator.validate({ request, response } as any)
    if (!validation.valid) {
      return response.status(400).send({
        success: false,
        error: validation.error,
        code: 'SECURITY_VALIDATION_FAILED'
      })
    }

    // Sanitize input parameters
    const rawData = request.only(['login_name', 'password'])
    const sanitizedData = RequestValidator.sanitizeParams(rawData)

    // Validate using AdonisJS validator with sanitized data
    const { login_name, password } = await request.validateUsing(loginValidator)

    // Additional validation for login attempts
    if (!login_name || !password || login_name.length < 3 || password.length < 6) {
      return response.status(400).send({
        success: false,
        error: 'Invalid login credentials format',
        code: 'INVALID_CREDENTIALS_FORMAT'
      })
    }

    try {
      const user = await User.verifyCredentials(login_name, password)
      const token = await User.accessTokens.create(user)

      // Log successful login for security monitoring
      console.log(`[SECURITY] Successful login for user: ${login_name} at ${new Date().toISOString()}`)

      return token
    } catch (error) {
      // Log failed login attempt for security monitoring
      console.warn(`[SECURITY] Failed login attempt for user: ${login_name} at ${new Date().toISOString()}`)

      return response.status(401).send({
        success: false,
        error: 'Invalid credentials',
        code: 'AUTHENTICATION_FAILED'
      })
    }
  }
  // async login({ request, response }: HttpContext) {
  //   try {
  //     // Validate credentials from request body
  //     const { login_name, password } = await request.validateUsing(loginValidator)

  //     try {
  //       // Find and verify user credentials
  //       const user = await User.verifyCredentials(login_name, password)
  //       if (!user) {
  //         return response.status(401).send({
  //           success: false,
  //           message: 'Invalid credentials'
  //         })
  //       }

  //       try {
  //         // Create access token using the configured provider
  //         const token = await User.accessTokens.create(user, ['api'])
  //         console.log('Created token:', token) // Debug log

  //         // Update last login time and token
  //         await prisma.users.update({
  //           where: { id: user.id },
  //           data: { 
  //             last_login: new Date(),
  //             user_token: token.identifier.toString()
  //           }
  //         })

  //         return response.json({
  //           success: true,
  //           data: {
  //             type: 'bearer',
  //             token: token.identifier.toString(),
  //             expires_at: token.expiresAt,
  //             user: {
  //               id: user.id,
  //               login_name: user.login_name,
  //               email: user.email,
  //               first_name: user.first_name,
  //               middle_name: user.middle_name,
  //               last_name: user.last_name,
  //               role_id: user.role_id
  //             }
  //           }
  //         })
  //       } catch (error) {
  //         console.error('Error creating token:', error)
  //         return response.status(500).json({
  //           success: false,
  //           message: 'Error creating access token'
  //         })
  //       }

  //     } catch (error) {
  //       console.error('Login error:', error)
  //       return response.status(401).send({
  //         success: false,
  //         message: 'Invalid credentials'
  //       })
  //     }
  //   } catch (error) {
  //     console.error('Authentication error:', error)
  //     return response.status(500).send({
  //       success: false,
  //       message: 'An error occurred during authentication',
  //       details: process.env.NODE_ENV === 'development' ? error.message : undefined
  //     })
  //   }
  // }

  async logout({ auth }: HttpContext) {
    const user = auth.user
    await User.accessTokens.delete(user, user.currentAccessToken.identifier)

    return {
      success: true,
      message: 'Logout Success'
    }
  }

  async me({ auth }: HttpContext) {
    await auth.check()
    const user = auth.user!
    console.log(user.$attributes.id);

    const dataUser = await prisma.users.findFirst({
      where: {
        id: user.$attributes.id
      },
      include: {
        tb_kawasan_user: true,
        tb_user_internal: true,
        tb_user_internal_provinsi: true,
        tb_user_internal_kabkot: true,
        roles: true,
        tb_adm_user: true
      }
    })

    const data = {
      id: dataUser?.id,
      email: dataUser?.email,
      roleId: dataUser?.role_id,
      address: dataUser?.address,
      // file_image:dataUser?.file_image,
      first_name: dataUser?.first_name,
      middle_name: dataUser?.middle_name,
      last_name: dataUser?.last_name,
      instansi: dataUser?.instansi,
      jabatan: dataUser?.jabatan,
      keterangan: dataUser?.keterangan,
    }
    data['role_name'] = dataUser?.roles.role_name
    delete data.password
    if (dataUser?.tb_kawasan_user.length > 0) {
      data['id_kawasan'] = dataUser?.tb_kawasan_user[0]?.id_kawasan_user
    }
    if (dataUser?.tb_adm_user.length > 0) {
      if (dataUser?.tb_adm_user[0]?.id_adm_provinsi) {
        data['id_adm_provinsi'] = dataUser?.tb_adm_user[0]?.id_adm_provinsi
      }
      if (dataUser?.tb_adm_user[0]?.id_adm_kabkot) {
        data['id_adm_kabkot'] = dataUser?.tb_adm_user[0]?.id_adm_kabkot
      }
    }
    if (dataUser?.tb_user_internal_provinsi.length > 0) {
      data['pic_prov_arr'] = dataUser?.tb_user_internal_provinsi.map((item) => {
        return item.id_adm_provinsi
      })
      data['pic_prov'] =''
      dataUser?.tb_user_internal_provinsi.map((item) => {
        data['pic_prov'] += item.id_adm_provinsi + ','
      })
      data['pic_prov'] = parseFloat(data['pic_prov'].slice(0, -1))

    }
    // if (dataUser?.tb_user_internal_provinsi.length > 0) {
    //   data['id_adm_provinsi'] = dataUser?.tb_user_internal_provinsi[0]?.id_adm_provinsi
    // }
    // if (dataUser?.tb_user_internal_kabkot.length > 0) {
    //   data['id_adm_kabkot'] = dataUser?.tb_user_internal_kabkot[0]?.id_adm_kabkot
    // }

    return {
      success: true,
      data: data,
    }
  }


  /**
       * @update
       * @paramQuery login_name - Masukan Username Anda - @type(string)
       * @paramQuery password - Masukan Password Anda - @type(string)
       * @paramQuery email - Masukan Email Anda - @type(string)
       * @paramQuery full_name - Masukan Nama Anda - @type(string)
       * @paramQuery role_id - Masukan Role Anda - @type(number)
       */
  async update({ request, response, params }: HttpContext) {
    const { id } = params
    const Id = parseInt(id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ success: false, error: 'Invalid ID provided' })
    }
    const data = await request.validateUsing(updateUserValidator)
    let Password
    if (data.password) {
      Password = await hash.make(data.password);
    }
    // return data.user_kabkot.id_adm_kabkot
    // return data
    if (data.role_id == 15) {
      await prisma.tb_adm_user.update({
        data: {
          id_adm_provinsi: data.user_provinsi?.id_adm_provinsi
        },
        where: {
          id_adm_user: data.user_provinsi?.id_adm_user
        }
      })
    }
    if (data.role_id == 3) {
      await prisma.tb_adm_user.update({
        data: {
          id_adm_provinsi: data.user_provinsi?.id_adm_provinsi
        },
        where: {
          id_adm_user: data.user_provinsi?.id_adm_user
        }
      })
    }
    if (data.role_id == 4) {
      await prisma.tb_adm_user.update({
        data: {
          id_adm_kabkot: data.user_kabkot?.id_adm_kabkot
        },
        where: {
          id_adm_user: data.user_kabkot?.id_adm_user
        }
      })
    }
    // return data.user_kawasan
    if (data.role_id == 5) {
      await prisma.tb_kawasan_user.update({
        data: {
          id_kawasan_industri: data.user_kawasan?.id_kawasan_industri
        },
        where: {
          id_kawasan_user: data.user_kawasan?.id_kawasan_user
        }

      })
    }
    if (data.role_id == 14) {
      await prisma.tb_user_internal_provinsi.deleteMany({
        where: {
          id_user: Id
        }
      })
      await prisma.tb_user_internal_kawasan_industri.deleteMany({
        where: {
          id_user: Id
        }
      })
      await prisma.tb_user_internal.deleteMany({
        where: {
          id_user: Id
        }
      })
      if (data.pic_prov) {
        const insData = data.pic_prov.map((item) => {
          return {
            id_user: Id,
            id_adm_provinsi: item
          }
        })
        await prisma.tb_user_internal_provinsi.createMany({
          data: insData
        })
      }
      
      if (data.kawasan_pic) {
        const insData = data.kawasan_pic.map((item) => {
          return {
            id_user: Id,
            id_kawasan_industri: item
          }
        })
        await prisma.tb_user_internal_kawasan_industri.createMany({
          data: insData
        })
      }
      
      if (data.id_jabatan) {
        await prisma.tb_user_internal.create({
          data: {
            id_user: Id,
            id_jabatan: data.id_jabatan
          }
        })
      }
    }


    try {
      const updatePost = await prisma.users.update({
        where: {
          id: Id,
        },
        data: {
          login_name: data.login_name,
          email: data.email,
          password: Password,
          role_id: data.role_id,
          first_name: data.first_name,
          middle_name: data.middle_name,
          last_name: data.last_name,
          phone_number: data.phone_number,
          mobile_number: data.mobile_number,
        },
      })
      let gpUser = await IamUser.findBy('login_name', data.login_name)
        if (gpUser && data.password) {
          await db.connection('gp')
            .rawQuery(
              "UPDATE iam_users SET pwd_hash = crypt(?, gen_salt('bf')) WHERE user_uid = ?",
              [data.password, gpUser.user_uid]
            )
            gpUser = await IamUser.findBy('login_name', data.login_name)
          console.log('GP user password updated successfully')
        }
        
      return response.status(200).json({ success: true, data: updatePost,gp:gpUser })
    } catch (error) {
      return response.status(500).send({ error: 'Error updating data' ,message:error.message})
    }
  }

  async update_password({ request, response, params }: HttpContext) {
    const { id } = params
    const Id = parseInt(id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ success: false, error: 'Invalid ID provided' })
    }
    const data = await request.validateUsing(updateUserPasswordValidator)
    let Password
    if (data.password) {
      Password = await hash.make(data.password);
    }
    try {
      const updatePost = await prisma.users.update({
        where: {
          id: Id,
        },
        data: {
          password: Password,
        },
      })
      return response.status(200).json({ success: true, data: updatePost })
    } catch (error) {
      return response.status(500).send({ error: 'Error updating data' })
    }
  }


  async delete({ params, response }: HttpContext) {
    const { id } = params
    const Id = parseInt(id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.post.delete({
        where: {
          id: Id,
        },
      })
      return response.status(200).send({
        success: true,
        message: 'Detele Success',
        data: deletePost
      })
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }

  /**
     * @forgot_password
     * @summary Forgot Password
     * @paramQuery email - Masukan Email Anda - @type(string) @required
     */
  async forgot_password({ request, response }: HttpContext) {
    try {
      const { email } = await request.validateUsing(forgotPasswordUserValidator)

      // Rate limiting for password reset requests
      const now = Date.now()
      const recentResetRequests = await prisma.password_reset.count({
        where: {
          email: email,
          created_at: {
            gte: new Date(now - 15 * 60 * 1000) // 15 minutes window
          }
        }
      })

      if (recentResetRequests >= 3) {
        return response.status(429).send({
          success: false,
          message: 'Too many password reset requests. Please try again later.'
        })
      }

      // Always return success even if email doesn't exist to prevent email enumeration
      const user = await prisma.users.findUnique({
        where: {
          email: email,
        },
      })

      if (!user) {
        // Log the attempt but don't reveal that the email doesn't exist
       

        // Return generic success message to prevent email enumeration
        return {
          success: true,
          message: 'If your email is registered, you will receive password reset instructions.'
        }
      }

      // Generate a secure token with high entropy
      const tokenBuffer = await new Promise<Buffer>((resolve, reject) => {
        crypto.randomBytes(32, (err, buffer) => {
          if (err) reject(err)
          else resolve(buffer)
        })
      })

      const token = tokenBuffer.toString('hex')
      const expiresAt = new Date(Date.now() + 3600 * 1000) // 1 hour expiry
      // Store the reset token with IP address for rate limiting and auditing
      const res_data = {
          email,
          token,
          expires_at: expiresAt,
          updated_at: expiresAt
      }
      await prisma.password_reset.create({
        data: res_data
      })

      // Create a secure reset URL with HTTPS
      const baseUrl = env.get('URL_LINK_EMAIL')
      const resetUrl = `${baseUrl}?token=${encodeURIComponent(token)}`

      // Improved email template with security instructions
      const bodyMail = `
        <p>Dear ${user.first_name || 'User'},</p>
        
        <p>We received a request to reset the password for your account. To proceed with resetting your password, please follow the link below:</p>
        
        <p><a href="${resetUrl}">Reset Your Password</a></p>
        
        <p>For security reasons:</p>
        <ul>
          <li>This link will expire in 1 hour</li>
          <li>If you didn't request this password reset, please ignore this email or contact our support team immediately</li>
          <li>After resetting your password, you will be logged out of all devices</li>
        </ul>
        
        <p>Thank you,</p>
        <p>PIR BKPM Security Team</p>
      `

      await send_mail(email, 'Reset Your Password Request', bodyMail)

    
      return {
        success: true,
        message: 'If your email is registered, you will receive password reset instructions.'
      }
    } catch (error) {
      // Log the error but don't expose details
      console.error('Password reset error:', error)

      return response.status(500).send({
        success: false,
        message: 'An error occurred while processing your request.',
        error:error.message
      })
    }
  }

  /**
     * @reset_password
     * @summary Reset Password
     * @paramQuery token - Masukan Token - @type(string) @required
     * @paramQuery password - Masukan Password  - @type(string)
     */
  async reset_password({ request, response }: HttpContext) {
    try {
      // Validate request data
      const { token, password } = await request.validateUsing(resetPasswordValidator)

      // Find the reset record
      const resetRecord = await prisma.password_reset.findUnique({
        where: { token },
      })


      // Check if token exists and is valid
      if (!resetRecord) {
        
        return response.status(400).send({
          success: false,
          message: 'Password reset link is invalid'
        })
      }

      // Check if token has expired
      if (resetRecord.expires_at < new Date()) {
       

        return response.status(400).send({
          success: false,
          message: 'Password reset link has expired. Please request a new one.'
        })
      }

      // Check for password complexity
      const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
      if (!passwordRegex.test(password)) {
        return response.status(400).send({
          success: false,
          message: 'Password must be at least 8 characters long and include uppercase, lowercase, number and special character'
        })
      }

      // Hash the new password
      const passwordHash = await hash.make(password)

      // Get user email from reset record
      const email = resetRecord.email
      
      // Find the user
      const user = await prisma.users.findUnique({
        where: { email }
      })
      
      if (!user) {
        return response.status(400).send({
          success: false,
          message: 'User account not found'
        })
      }
      // Update the user's password
      const up = await prisma.users.update({
        where: { email },
        data: { password: passwordHash }
      })
      let gpUser = await IamUser.findBy('login_name', user.login_name)
      if (up) {
        let edited = 'no'
          if (gpUser && user.password) {
            edited='yes'
            await db.connection('gp')
              .rawQuery(
                "UPDATE iam_users SET pwd_hash = crypt(?, gen_salt('bf')) WHERE user_uid = ?",
                [password, gpUser.user_uid]
              )
              gpUser = await IamUser.findBy('login_name', user.login_name)
            console.log('GP user password updated successfully')
          }  
      }

      // Invalidate the reset token by setting expiry to now
      await prisma.password_reset.update({
        where: { token },
        data: { expires_at: new Date() }
      })

      // Invalidate all existing sessions for this user
      // await prisma.auth_access_tokens.deleteMany({
      //   where: { user_id: user.id }
      // })


      // Send notification email about password change
      const notificationEmail = `
        <p>Dear ${user.first_name || 'User'},</p>
        
        <p>Your password has been successfully reset.</p>
        
        <p>If you did not make this change, please contact our support team immediately as your account may be compromised.</p>
        
        <p>Thank you,</p>
        <p>PIR BKPM Security Team</p>
      `

      await send_mail(email, 'Your Password Has Been Reset', notificationEmail)

      return response.status(200).send({
        success: true,
        message: 'Password has been reset successfullypppp',
        data:{user:up,gp:gpUser},
        token:token
      })
    } catch (error) {
      console.error('Password reset error:', error)

      return response.status(500).send({
        success: false,
        message: 'An error occurred while resetting your password',
        error:error.message
      })
    }
  }

  async destroy({ params, response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.users.delete({
        where: {
          id: Id,
        },
      })
      return response.status(200).send(deletePost)
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }
  }

  /**
   * @get_gp_users
   * @summary Get users from GP database
   */
  async getGpUsers({ request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 10)

      const users = await IamUser.query()
        .orderBy('login_name')
        .paginate(page, limit)

      return response.status(200).send({
        success: true,
        data: users
      })
    } catch (error) {
      console.error('Error fetching GP users:', error)
      return response.status(500).send({
        success: false,
        message: 'An error occurred while fetching users',
        error: error.message
      })
    }
  }

  async getGpUserByUid({ params, response }: HttpContext) {
    try {
      const { uid } = params
      const user = await IamUser.findBy('user_uid', uid)

      if (!user) {
        return response.status(404).send({
          success: false,
          message: 'User not found'
        })
      }

      return response.status(200).send({
        success: true,
        data: user
      })
    } catch (error) {
      console.error('Error fetching GP user:', error)
      return response.status(500).send({
        success: false,
        message: 'An error occurred while fetching user',
        error: error.message
      })
    }
  }

  async getGpUserByUsers({ params, response }: HttpContext) {
    try {
      const { login_name } = params
      const user = await IamUser.findBy('login_name', login_name)

      if (!user) {
        return response.status(404).send({
          success: false,
          message: 'User not found'
        })
      }

      return response.status(200).send({
        success: true,
        data: user
      })
    } catch (error) {
      console.error('Error fetching GP user:', error)
      return response.status(500).send({
        success: false,
        message: 'An error occurred while fetching user',
        error: error.message
      })
    }
  }

  async updateGpUserPassword({ request, response, params }: HttpContext) {
    try {
      const { login_name } = params
      const { password } = await request.validateUsing(updateUserPasswordValidator)

      // Find user first to verify existence
      const gpUser = await IamUser.findBy('login_name', login_name)
      if (!gpUser) {
        return response.status(404).send({
          success: false,
          message: 'User not found'
        })
      }

      // Using db service to execute raw query
      await db.connection('gp')
        .rawQuery(
          "UPDATE public.iam_users SET pwd_hash = crypt(?, gen_salt('bf')) WHERE login_name = ?",
          [password, login_name]
        )

      return response.status(200).send({
        success: true,
        message: 'Password updated successfully'
      })
    } catch (error) {
      console.error('Error updating GP user password:', error)
      return response.status(500).send({
        success: false,
        message: 'An error occurred while updating password',
        error: error.message
      })
    }
  }

  async deleteGpUser({params,response}:HttpContext){
    try {
      const { login_name } = params
      const user = await IamUser.findBy('login_name', login_name)

      if (!user) {
        return response.status(404).send({
          success: false,
          message: 'User not found'
        })
      }

      return response.status(200).send({
        success: true,
        data: user
      })
    } catch (error) {
      console.error('Error fetching GP user:', error)
      return response.status(500).send({
        success: false,
        message: 'An error occurred while fetching user',
        error: error.message
      })
    }
  }

}
