import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../../helpers/model_helper.js';
import uploadFile from '../../../helpers/file_uploader.js';

import prisma from '../../../lib/prisma.js';
import { createKawasanfValidator } from '#validators/daerah';
import { isArray } from '@sindresorhus/is';



export default class KawasanIndustriController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_kawasan_industri: { column: 'id_kawasan_industri', alias: 'id_kawasan_industri', type: 'int' },
                        id_adm_kabkot: { column: 'id_adm_kabkot', alias: 'id_adm_kabkot', type: 'int' },
                        id_sumber_data: { column: 'id_sumber_data', alias: 'id_sumber_data', type: 'int' },
                        id_kategori: { column: 'id_kategori', alias: 'id_kategori', type: 'int' },
                        nama: { column: 'nama', alias: 'nama', type: 'string' },
                        keterangan: { column: 'keterangan', alias: 'keterangan', type: 'string' },
                        alamat: { column: 'alamat', alias: 'alamat', type: 'string' },
                        luas: { column: 'luas', alias: 'luas', type: 'string' },
                        luas_satuan: { column: 'luas_satuan', alias: 'luas_satuan', type: 'string' },
                        id_bandara_terdekat: { column: 'id_bandara_terdekat', alias: 'id_bandara_terdekat', type: 'int' },
                        jarak_bandara_terdekat: { column: 'jarak_bandara_terdekat', alias: 'jarak_bandara_terdekat', type: 'string' },
                        id_pelabuhan_terdekat: { column: 'id_pelabuhan_terdekat', alias: 'id_pelabuhan_terdekat', type: 'int' },
                        jarak_pelabuhan_terdekat: { column: 'jarak_pelabuhan_terdekat', alias: 'jarak_pelabuhan_terdekat', type: 'int' },
                        jarak_ibukota: { column: 'jarak_ibukota', alias: 'jarak_ibukota', type: 'string' },
                        url_web: { column: 'url_web', alias: 'url_web', type: 'string' },
                        no_telp: { column: 'no_telp', alias: 'no_telp', type: 'string' },
                        no_fax: { column: 'no_fax', alias: 'no_fax', type: 'string' },
                        email: { column: 'email', alias: 'email', type: 'string' },
                        cp: { column: 'cp', alias: 'cp', type: 'string' },
                        ketersediaan: { column: 'ketersediaan', alias: 'ketersediaan', type: 'string' },
                        status: { column: 'status', alias: 'status', type: 'string' },
                        lon: { column: 'lon', alias: 'lon', type: 'string' },
                        lat: { column: 'lat', alias: 'lat', type: 'string' },
                        shape: { column: 'shape', alias: 'shape', type: 'string' },
                        is_ikn: { column: 'is_ikn', alias: 'is_ikn', type: 'int' },
                        id_kawasan_industri_ref_range: { column: 'id_kawasan_industri_ref_range', alias: 'id_kawasan_industri_ref_range', type: 'string' },
                        major_tenants: { column: 'major_tenants', alias: 'major_tenants', type: 'string' },
                        id_kawasan_industri_occupancy: { column: 'id_kawasan_industri_occupancy', alias: 'id_kawasan_industri_occupancy', type: 'string' },
                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                let isAll = false;
                
                let q = request.input('q')?.trim() || '';

                let whereDefault = {
                    OR: [
                        {
                                nama: {
                                    contains: q,
                                    mode: 'insensitive',
                                },
                            },
                            {
                                no_telp: {
                                    contains: q,
                                    mode: 'insensitive',
                                },
                            },
                            {
                                alamat: {
                                    contains: q,
                                    mode: 'insensitive',
                                },
                            },
                    ],
                };

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        where?: any

                }

                if (params['all'] || params['all'] == 'true') {
                        isAll = true
                        delete params['all'];
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));
                delete params['page'];
                delete params['per_page'];
                const req = request.qs()
                

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        orderBy: {
                                id_kawasan_industri: 'desc', 
                        },
                        where: {
                                ...(q ? whereDefault : {}),
                        }
                };
                
                // return options
                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                // Apply the where conditions
                let where = modelWhereAnd((await this.schemaData()).columns, q);

                if (Object.keys(where).length !== 0) {
                        options['where'] = {
                                AND: [
                                        whereDefault, 
                                        where,
                                ],
                        };
                }
                // if (Object.keys(where).length !== 0) {
                //         // Membuat array untuk menyimpan kondisi
                //         const andConditions = [whereDefault]; // Mulai dengan kondisi default
                    
                //         // Menambahkan kondisi tambahan jika ada
                //         if (Object.keys(where).length !== 0) {
                //             andConditions.push(where);
                //         }
                    
                //         // Jika andConditions tidak kosong, set options['where']
                //         options['where'] = andConditions.length > 0 ? { AND: andConditions } : {};
                // } else {
                //         // Jika `where` kosong, set options['where'] menjadi objek kosong
                //         options['where'] = {};
                // }

                let order = req.order 
                const by = ['asc','desc'].includes(req.by) ? req.by : 'asc'
                const paramList = ['status','nama','alamat']
                if (order != undefined && paramList.includes(order)) {
                        // if (order == 'nama_sektor') {
                        //         orderBy = {sub_sektor:{sektor:{nama:by}}}
                        // }else if (order == 'nama_sub_sektor') {
                        //         orderBy = {sub_sektor:{sub_sektor_ref:{nama:by}}}
                        // }else if (order == 'nama_komoditi') {
                        //         orderBy = {komoditi_nasional_ref:{nama:by}}
                        // }else{
                                options.orderBy = {[order]:by}
                        // }
                }
                if (req.id_kawasan) {
                        // Ensure options.where is initialized as an object if it doesn't exist
                        options.where = options.where || {};
                        
                        // Add the id_kawasan_industri condition to the existing where conditions
                        options.where.id_kawasan_industri = parseInt(req.id_kawasan);
                    }
                 if (req.pic) {
                      const picProv = await prisma.tb_user_internal_kawasan_industri.findMany({
                              where:{
                              id_user:parseInt(req.pic)
                              }
                            })
                            if (picProv.length > 0) {
                              const provinsiIds = picProv.map((item) => item.id_kawasan_industri);
                              options.where.id_kawasan_industri = {
                                      in: provinsiIds,
                              }
                            }
                    }
                const data = await prisma.tb_kawasan_industri.findMany(options);

                if (isAll) {
                        return response.status(200).json({
                                success: true,
                                data: data
                        });
                }

                const totalCount = await prisma.tb_kawasan_industri.count({where:options.where});

                return response.status(200).json({
                        success: true,
                        pagination: {
                                page: page,
                                per_page: perPage,
                                total_count: totalCount,
                                total_pages: Math.ceil(totalCount / perPage),
                        },
                        data: data,
                });
        }

        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                }

                let options: Options = {
                        include: {
                                tb_adm_kabkot: true,
                                tb_bandara: true,
                                tb_kawasan_industri_kategori: true,
                                tb_kawasan_industri_occupancy: true,
                                tb_kawasan_industri_ref_range: true,
                                tb_pelabuhan: true,
                                tb_kawasan_industri_blok: true,
                                tb_kawasan_industri_file: true,
                                tb_kawasan_industri_peluang: true,
                                tb_kawasan_layers: true,
                                tb_kawasan_industri_tr: true,
                        }
                };

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_kawasan_industri': parseInt(params.id)
                }

                const data = await prisma.tb_kawasan_industri.findFirst(options);

                data['tb_kawasan_industri_file'] = data['tb_kawasan_industri_file'].map((item) => {
                        return {
                                ...item, // Spread item untuk mempertahankan properti yang ada
                                path: `${process.env.APP_URL}/uploads/kawasan_industri/${item.tipe}/${item.nama}`,
                        };
                });

                return response.status(200).json({
                        success: true,
                        data: data
                });
        }

        public async createOrUpdate({ request, params, response }: HttpContext) {
                let reqBody: any = request.body();

                let paramID = parseInt(reqBody.id_kawasan_industri || params.id);

                const uploadFotos = request.files('upload_foto');
                const uploadVideo = request.file('upload_video');
                const uploadDocument = request.file('upload_document');

                const model = "kawasan_potensi_investasi";

                if (!paramID) {
                    await request.validateUsing(createKawasanfValidator)
                }

                try {
                        const result = await prisma.$transaction(async (prisma) => {
                                if (paramID) {
                                        // Jika `id` ada, lakukan operasi update
                                        const update = await prisma.tb_kawasan_industri.update({
                                                where: { id_kawasan_industri: paramID },
                                                data: {
                                                        id_adm_kabkot: request.input('id_adm_kabkot') ? parseInt(request.input('id_adm_kabkot')) : 0,
                                                        id_sumber_data: request.input('id_sumber_data') ? parseInt(request.input('id_sumber_data')) : 0,
                                                        id_kategori: request.input('id_kategori') ? parseInt(request.input('id_kategori')) : 0,
                                                        nama: request.input('nama') ? request.input('nama') : '',
                                                        keterangan: request.input('keterangan') ? request.input('keterangan') : '',
                                                        alamat: request.input('alamat') ? request.input('alamat') : '',
                                                        luas: request.input('luas') ? parseFloat(request.input('luas')) : 0,
                                                        luas_satuan: request.input('luas_satuan') ? request.input('luas_satuan') : '',
                                                        id_bandara_terdekat: request.input('id_bandara_terdekat') ? parseInt(request.input('id_bandara_terdekat')) : 0,
                                                        jarak_bandara_terdekat: request.input('jarak_bandara_terdekat') ? parseFloat(request.input('jarak_bandara_terdekat')) : 0,
                                                        id_pelabuhan_terdekat: request.input('id_pelabuhan_terdekat') ? parseInt(request.input('id_pelabuhan_terdekat')) : 0,
                                                        jarak_pelabuhan_terdekat: request.input('jarak_pelabuhan_terdekat') ? parseFloat(request.input('jarak_pelabuhan_terdekat')) : 0,
                                                        jarak_ibukota: request.input('jarak_ibukota') ? parseFloat(request.input('jarak_ibukota')) : 0,
                                                        url_web: request.input('url_web') ? request.input('url_web') : '',
                                                        no_telp: request.input('no_telp') ? request.input('no_telp') : '',
                                                        no_fax: request.input('no_fax') ? request.input('no_fax') : '',
                                                        email: request.input('email') ? request.input('email') : '',
                                                        cp: request.input('cp') ? request.input('cp') : '',
                                                        ketersediaan: request.input('ketersediaan') ? request.input('ketersediaan') : '',
                                                        status: request.input('status') ? request.input('status') : '',
                                                        lon: request.input('lon') ? parseFloat(request.input('lon')) : 0,
                                                        lat: request.input('lat') ? parseFloat(request.input('lat')) : 0,
                                                        shape: request.input('shape') ? request.input('shape') : '',
                                                        is_ikn: request.input('is_ikn') ? true : false,
                                                        id_kawasan_industri_ref_range: request.input('id_kawasan_industri_ref_range') ? parseInt(request.input('id_kawasan_industri_ref_range')) : 0,
                                                        major_tenants: request.input('major_tenants') ? request.input('major_tenants') : '',
                                                        id_kawasan_industri_occupancy: request.input('id_kawasan_industri_occupancy') ? parseInt(request.input('id_kawasan_industri_occupancy')) : 0,
                                                },
                                        })

                                        await prisma.tb_kawasan_layers.deleteMany({
                                                where: {
                                                        id_kawasan_industri: paramID
                                                }
                                        });
                                        let mapService = request.input('map_service')
                                        if (typeof mapService === 'string') {
                                        mapService = JSON.parse(mapService);
                                        }
                                        if (Array.isArray(mapService) && mapService.length > 0) {
                                                const mapService = request.input("map_service")
                                                if (Array.isArray(mapService) && mapService.length > 0) {
                                                        const kawasanLayer= mapService.map(async (item)=> {
                                                                await prisma.tb_kawasan_layers.create({ 
                                                                        data: {
                                                                                id_kawasan_industri: paramID,
                                                                                layeruid: item.layer,
                                                                                keterangan: item.nama_layer,
                                                                                kategori: parseInt(item.type),
                                                                                is_active:true
                                                                        }
                                                                });
                                                        })
                                                        await Promise.all(kawasanLayer);
                                                }
                                        }

                                        await prisma.tb_kawasan_industri_tr.deleteMany({
                                                where: {
                                                        id_kawasan_industri: paramID
                                                }
                                        });

                                        console.log("Sync tb_kawasan_industri_tr id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_kawasan_industri_tr"', 'id_kawasan_industri_tr')),
                                                (SELECT (MAX("id_kawasan_industri_tr") + 1) FROM "tb_kawasan_industri_tr"),
                                                false) FROM "tb_kawasan_industri_tr";
                                         `);

                                        await prisma.tb_kawasan_industri_tr.create({ 
                                                data: {
                                                        id_kawasan_industri: paramID,
                                                        kd_bahasa: request.input("kd_bahasa") || 'en',
                                                        keterangan: request.input("keterangan_tr") || '-',
                                                        nama: request.input("nama_tr") || ''
                                                }
                                        });
                                        await prisma.tb_kawasan_industri_file.deleteMany({
                                                where: {
                                                        id_kawasan_industri: paramID,
                                                        tipe:2
                                                }
                                        });

                                        if (uploadVideo) {
                                                let filename: string = "";
                                                let videoParams : any = {};

                                                videoParams['id_kawasan_industri'] = paramID;
                                                videoParams['judul'] = '-';
                                                videoParams['keterangan'] = '-';
                                                videoParams['tipe'] = 2;

                                                if (uploadVideo) {
                                                        let uploadFileToServer = await uploadFile(uploadVideo, model, paramID);
                                                        let filenameFromServer = '';
                                                        if (uploadFileToServer) {
                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                        }
                                                        filename = filenameFromServer; // Set fileName if file exists
                                                        videoParams['nama'] = filename;
                                                }

                                                await prisma.tb_kawasan_industri_file.create({
                                                        data: videoParams,
                                                });
                                        }

                                        await prisma.tb_kawasan_industri_file.deleteMany({
                                                where: {
                                                        id_kawasan_industri: paramID,
                                                        tipe:3
                                                }
                                        });
                                        if (uploadDocument) {
                                                let filename: string = "";
                                                let documentParams : any = {};

                                                documentParams['id_kawasan_industri'] = paramID;
                                                documentParams['judul'] = '-';
                                                documentParams['keterangan'] = '-';
                                                documentParams['tipe'] = 3;

                                                if (uploadDocument) {
                                                        let uploadFileToServer = await uploadFile(uploadDocument, model, paramID);
                                                        let filenameFromServer = '';
                                                        if (uploadFileToServer) {
                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                        }
                                                        filename = filenameFromServer; // Set fileName if file exists
                                                        documentParams['nama'] = filename;
                                                }

                                                await prisma.tb_kawasan_industri_file.create({
                                                        data: documentParams,
                                                });
                                        }
                                        await prisma.tb_kawasan_industri_file.deleteMany({
                                                where: {
                                                        id_kawasan_industri: paramID,
                                                        tipe:1
                                                }
                                        });
                                        for (const key in uploadFotos) {
                                                if (uploadFotos.hasOwnProperty(key)) {
                                                        let filename: string = "";
                                                        const value = uploadFotos[key];
                                                        let fileParams : any = {};

                                                        fileParams['id_kawasan_industri'] = paramID;
                                                        fileParams['judul'] = '-';
                                                        fileParams['keterangan'] = '-';
                                                        fileParams['tipe'] = 1;

                                                        if (value) {
                                                                let uploadFileToServer = await uploadFile(value, model, paramID);
                                                                let filenameFromServer = '';
                                                                if (uploadFileToServer) {
                                                                        filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                        filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                                }
                                                                filename = filenameFromServer; // Set fileName if file exists
                                                                fileParams['nama'] = filename;
                                                        }

                                                        await prisma.tb_kawasan_industri_file.create({
                                                                data: fileParams,
                                                        });
                                                }
                                        }

                                        await prisma.tb_kawasan_industri_layer_spasial.deleteMany({
                                                where: {
                                                        id_kawasan_industri: paramID
                                                }
                                        });

                                        console.log("Sync tb_kawasan_industri_layer_spasial id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_kawasan_industri_layer_spasial"', 'id_kawasan_industri_layer_spasial')),
                                                (SELECT (MAX("id_kawasan_industri_layer_spasial") + 1) FROM "tb_kawasan_industri_layer_spasial"),
                                                false) FROM "tb_kawasan_industri_layer_spasial";
                                         `);

                                        for (const key in reqBody['map_services']) {
                                                if (reqBody['map_services'].hasOwnProperty(key)) {
                                                        const value = reqBody['map_services'][key];

                                                        await prisma.tb_kawasan_industri_layer_spasial.create({ 
                                                                data: {
                                                                        id_kawasan_industri: paramID,
                                                                        nama_layer: value["nama_layer"] || '-',
                                                                        tipe: parseInt(value["tipe"]) || 1,
                                                                        url_service: value["url_service"] || '',
                                                                        status: parseInt(value["status"]) || 1,
                                                                        is_active: value["is_active"] ? true : false,
                                                                }
                                                        });
                                                }
                                        }

                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update,
                                        }
                                } else {
                                        console.log("Sync tb_kawasan_industri id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_kawasan_industri"', 'id_kawasan_industri')),
                                                        (SELECT (MAX("id_kawasan_industri") + 1) FROM "tb_kawasan_industri"),
                                                        false) FROM "tb_kawasan_industri";
                                                `);
                                        
                                        const save = await prisma.tb_kawasan_industri.create({
                                                data: {
                                                        id_adm_kabkot: request.input('id_adm_kabkot') ? parseInt(request.input('id_adm_kabkot')) : 0,
                                                        id_sumber_data: request.input('id_sumber_data') ? parseInt(request.input('id_sumber_data')) : 0,
                                                        id_kategori: request.input('id_kategori') ? parseInt(request.input('id_kategori')) : 0,
                                                        nama: request.input('nama') ? request.input('nama') : '',
                                                        keterangan: request.input('keterangan') ? request.input('keterangan') : '',
                                                        alamat: request.input('alamat') ? request.input('alamat') : '',
                                                        luas: request.input('luas') ? parseFloat(request.input('luas')) : 0,
                                                        luas_satuan: request.input('luas_satuan') ? request.input('luas_satuan') : '',
                                                        id_bandara_terdekat: request.input('id_bandara_terdekat') ? parseInt(request.input('id_bandara_terdekat')) : 0,
                                                        jarak_bandara_terdekat: request.input('jarak_bandara_terdekat') ? parseFloat(request.input('jarak_bandara_terdekat')) : 0,
                                                        id_pelabuhan_terdekat: request.input('id_pelabuhan_terdekat') ? parseInt(request.input('id_pelabuhan_terdekat')) : 0,
                                                        jarak_pelabuhan_terdekat: request.input('jarak_pelabuhan_terdekat') ? parseFloat(request.input('jarak_pelabuhan_terdekat')) : 0,
                                                        jarak_ibukota: request.input('jarak_ibukota') ? parseFloat(request.input('jarak_ibukota')) : 0,
                                                        url_web: request.input('url_web') ? request.input('url_web') : '',
                                                        no_telp: request.input('no_telp') ? request.input('no_telp') : '',
                                                        no_fax: request.input('no_fax') ? request.input('no_fax') : '',
                                                        email: request.input('email') ? request.input('email') : '',
                                                        cp: request.input('cp') ? request.input('cp') : '',
                                                        ketersediaan: request.input('ketersediaan') ? request.input('ketersediaan') : '',
                                                        status:'0',
                                                        lon: request.input('lon') ? parseFloat(request.input('lon')) : 0,
                                                        lat: request.input('lat') ? parseFloat(request.input('lat')) : 0,
                                                        shape: request.input('shape') ? request.input('shape') : '',
                                                        is_ikn: request.input('is_ikn') ? true : false,
                                                        id_kawasan_industri_ref_range: request.input('id_kawasan_industri_ref_range') ? parseInt(request.input('id_kawasan_industri_ref_range')) : 0,
                                                        major_tenants: request.input('major_tenants') ? request.input('major_tenants') : '',
                                                        id_kawasan_industri_occupancy: request.input('id_kawasan_industri_occupancy') ? parseInt(request.input('id_kawasan_industri_occupancy')) : 0,
                                                },
                                        });

                                        const mapService = request.input("map_service")
                                        if (Array.isArray(mapService) && mapService.length > 0) {
                                                const kawasanLayer= mapService.map(async (item)=> {
                                                        if (item.layer != null && item.type != 'NaN' && item.nama_layer != null) {
                                                                await prisma.tb_kawasan_layers.create({ 
                                                                        data: {
                                                                                id_kawasan_industri: save.id_kawasan_industri,
                                                                                layeruid: item.layer,
                                                                                keterangan: item.nama_layer,
                                                                                kategori: parseInt(item.type),
                                                                                is_active:true
                                                                        }
                                                                });
                                                        }
                                                })
                                                await Promise.all(kawasanLayer);
                                        }
                                        await prisma.tb_kawasan_industri_tr.deleteMany({
                                                where: {
                                                        id_kawasan_industri: save.id_kawasan_industri
                                                }
                                        });

                                        console.log("Sync tb_kawasan_industri_tr id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_kawasan_industri_tr"', 'id_kawasan_industri_tr')),
                                                (SELECT (MAX("id_kawasan_industri_tr") + 1) FROM "tb_kawasan_industri_tr"),
                                                false) FROM "tb_kawasan_industri_tr";
                                         `);

                                        await prisma.tb_kawasan_industri_tr.create({ 
                                                data: {
                                                        id_kawasan_industri: save.id_kawasan_industri,
                                                        kd_bahasa: request.input("kd_bahasa") || 'en',
                                                        keterangan: request.input("keterangan_tr") || '-',
                                                        nama: request.input("nama_tr") || ''
                                                }
                                        });

                                        await prisma.tb_kawasan_industri_file.deleteMany({
                                                where: {
                                                        id_kawasan_industri: save.id_kawasan_industri
                                                }
                                        });

                                        console.log("Sync tb_kawasan_industri_file id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_kawasan_industri_file"', 'id_kawasan_industri_file')),
                                                (SELECT (MAX("id_kawasan_industri_file") + 1) FROM "tb_kawasan_industri_file"),
                                                false) FROM "tb_kawasan_industri_file";
                                         `);

                                        if (uploadVideo) {
                                                let filename: string = "";
                                                let videoParams : any = {};

                                                videoParams['id_kawasan_industri'] = save.id_kawasan_industri;
                                                videoParams['judul'] = '-';
                                                videoParams['keterangan'] = '-';
                                                videoParams['tipe'] = 2;

                                                if (uploadVideo) {
                                                        let uploadFileToServer = await uploadFile(uploadVideo, model, save.id_kawasan_industri);
                                                        let filenameFromServer = '';
                                                        if (uploadFileToServer) {
                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                        }
                                                        filename = filenameFromServer; // Set fileName if file exists
                                                        videoParams['nama'] = filename;
                                                }

                                                await prisma.tb_kawasan_industri_file.create({
                                                        data: videoParams,
                                                });
                                        }

                                        if (uploadDocument) {
                                                let filename: string = "";
                                                let documentParams : any = {};

                                                documentParams['id_kawasan_industri'] = save.id_kawasan_industri;
                                                documentParams['judul'] = '-';
                                                documentParams['keterangan'] = '-';
                                                documentParams['tipe'] = 3;

                                                if (uploadDocument) {
                                                        let uploadFileToServer = await uploadFile(uploadDocument, model, save.id_kawasan_industri);
                                                        let filenameFromServer = '';
                                                        if (uploadFileToServer) {
                                                                filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                        }
                                                        filename = filenameFromServer; // Set fileName if file exists
                                                        documentParams['nama'] = filename;
                                                }

                                                await prisma.tb_kawasan_industri_file.create({
                                                        data: documentParams,
                                                });
                                        }

                                        for (const key in uploadFotos) {
                                                if (uploadFotos.hasOwnProperty(key)) {
                                                        let filename: string = "";
                                                        const value = uploadFotos[key];
                                                        let fileParams : any = {};

                                                        fileParams['id_kawasan_industri'] = save.id_kawasan_industri;
                                                        fileParams['judul'] = '-';
                                                        fileParams['keterangan'] = '-';
                                                        fileParams['tipe'] = 1;

                                                        if (value) {
                                                                let uploadFileToServer = await uploadFile(value, model, save.id_kawasan_industri);
                                                                let filenameFromServer = '';
                                                                if (uploadFileToServer) {
                                                                        filenameFromServer = uploadFileToServer.split("/").pop() ?? '';
                                                                        filenameFromServer = filenameFromServer.split("\\").pop() ?? '';
                                                                }
                                                                filename = filenameFromServer; // Set fileName if file exists
                                                                fileParams['nama'] = filename;
                                                        }

                                                        await prisma.tb_kawasan_industri_file.create({
                                                                data: fileParams,
                                                        });
                                                }
                                        }

                                        await prisma.tb_kawasan_industri_layer_spasial.deleteMany({
                                                where: {
                                                        id_kawasan_industri: save.id_kawasan_industri
                                                }
                                        });

                                        console.log("Sync tb_kawasan_industri_layer_spasial id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_kawasan_industri_layer_spasial"', 'id_kawasan_industri_layer_spasial')),
                                                (SELECT (MAX("id_kawasan_industri_layer_spasial") + 1) FROM "tb_kawasan_industri_layer_spasial"),
                                                false) FROM "tb_kawasan_industri_layer_spasial";
                                         `);

                                        for (const key in reqBody['map_services']) {
                                                if (reqBody['map_services'].hasOwnProperty(key)) {
                                                        const value = reqBody['map_services'][key];

                                                        await prisma.tb_kawasan_industri_layer_spasial.create({ 
                                                                data: {
                                                                        id_kawasan_industri: save.id_kawasan_industri,
                                                                        nama_layer: value["nama_layer"] || '-',
                                                                        tipe: parseInt(value["tipe"]) || 1,
                                                                        url_service: value["url_service"] || '',
                                                                        status: parseInt(value["status"]) || 1,
                                                                        is_active: value["is_active"] ? true : false,
                                                                }
                                                        });
                                                }
                                        }

                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: save,
                                        }
                                }
                        })

                        return response.json(result)

                } catch (error) {
                        console.log(error)
                        // Handle error jika ada kesalahan dalam transaksi
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error.message,
                        })
                }
        }

        public async deleteById({ params, response }: HttpContext) {
                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_kawasan_industri.delete({
                                where: {
                                        id_kawasan_industri: Id,
                                },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

        public async toggleStatus({ request, params, response }: HttpContext) {
                const Id = parseInt(params.id, 10)
                const statusId = parseInt(request.input('status') ?? 0);

                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                if (isNaN(statusId)) {
                        return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                }

                try {
                        const toggleStatus = await prisma.tb_kawasan_industri.update({
                                where: {
                                        id_kawasan_industri: Id,
                                },
                                data: {
                                        status: String(request.input('status')) ?? '0'
                                },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Toggle Status',
                                data: toggleStatus,
                        })
                } catch (error) {
                        console.log(error);
                        return response.status(500).send({ error: 'Error Toggle Status' })
                }
        }
}