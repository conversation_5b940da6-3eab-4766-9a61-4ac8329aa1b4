import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../../helpers/model_helper.js';

import prisma from '../../../lib/prisma.js';
import uploadFile from '../../../helpers/file_uploader.js';



export default class SUDSektorDaerahInsentifController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_sektor_daerah_insentif: { column: 'id_sektor_daerah_insentif', alias: 'id_sektor_daerah_insentif', type: 'int' },
                        id_sektor_daerah: { column: 'id_sektor_daerah', alias: 'id_sektor_daerah', type: 'int' },
                        nama: { column: 'nama', alias: 'nama', type: 'string' },
                        deskripsi: { column: 'deskripsi', alias: 'deskripsi', type: 'string' },
                        status: { column: 'status', alias: 'status', type: 'int' }
                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                if (request.input("is_get_table_admin") || request.input("is_get_table_admin") == "true") {
                        const page = parseInt(request.input("page", "1")) || 1;
                        const perPage = parseInt(request.input("per_page", "10")) || 10;
                        const offset = (page - 1) * perPage;

                        const searchQuery = request.input("q")?.trim() || request.input("filter")?.trim();
                        let totalCount = 0;
                        let data = [];
                        let Wilayah = ""; // Awalnya valid
                        const params = request.qs();
                        let IdAdmProvinsi = params['id_adm_provinsi'];
                        let IdAdmKabkot = params['id_adm_kabkot'];
                        let status = params['status'];
                        let pic = parseInt(params['pic']);
                        try {
                                if (searchQuery) {
                                        if (IdAdmKabkot) {
                                                Wilayah += ` AND tak.id_adm_kabkot = ${IdAdmKabkot}`;
                                        }
                                        if (IdAdmProvinsi){
                                                Wilayah += ` AND (tap.id_adm_provinsi = ${IdAdmProvinsi} 
                                                                OR LEFT(tak.id_adm_kabkot::TEXT, 2) = LEFT(${IdAdmProvinsi}::TEXT, 2))`;
                                        }
                                        if (pic) {
                                                const picProv = await prisma.tb_user_internal_provinsi.findMany({
                                                        where:{
                                                        id_user:pic
                                                        }
                                                })
                                                if (picProv.length > 0) {
                                                        const provinsiIds = picProv.map((item) => item.id_adm_provinsi);
                                                        const provinsiIdsStr = provinsiIds.join(",");
                                                        const kabkotConditions = provinsiIds.map(id => `LEFT(tak.id_adm_kabkot::TEXT, 2) = LPAD(${id}::TEXT, 2, '0')`).join(' OR ');
                                                        Wilayah += ` AND (tap.id_adm_provinsi IN (${provinsiIdsStr}) OR (${kabkotConditions}))`;
                                                }
                                        }
                                        if (status != undefined  && !isNaN(status)){
                                                Wilayah += ` AND tsdi.status = ${status}` 
                                        }
                                        // Query untuk menghitung total data dengan filter `q`
                                        const totalCountResult = await prisma.$queryRawUnsafe(`
                                                SELECT COUNT(*) as count
                                                FROM tb_sektor_daerah_insentif tsdi
                                                LEFT JOIN tb_sektor_daerah tsd ON tsd.id_sektor_daerah = tsdi.id_sektor_daerah
                                                LEFT JOIN tb_sektor_nasional tsn ON tsd.id_sektor_nasional = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sektor_nasional_ref tsnr ON tsnr.id_sektor = tsn.id_sektor_nasional
                                                LEFT JOIN tb_adm_kabkot tak ON tsd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tsd.id_adm_provinsi
                                                WHERE 
                                                        (LOWER(tsnr.nama) ILIKE $1 OR
                                                        LOWER(tap.nama) ILIKE $1 OR
                                                        LOWER(tak.nama) ILIKE $1 OR
                                                        LOWER(tsdi.nama) ILIKE $1 OR
                                                        tsdi.id_sektor_daerah_insentif::TEXT ILIKE $1)
                                                ${Wilayah}
                                                `, `%${searchQuery}%`);

                                        totalCount = Number(totalCountResult[0]?.count || 0);

                                        // Query data dengan filter `q`, limit, dan offset
                                        data = await prisma.$queryRawUnsafe(`
                                                SELECT 
                                                        tsdi.id_sektor_daerah_insentif AS id_sektor_daerah_insentif,
                                                        tsnr.nama AS sektor,
                                                        case 
                                                                when tap.nama is null then tap2.nama
                                                                else 
                                                                tap.nama 
                                                        end AS provinsi,
                                                        tak.nama AS kabkota,
                                                        tsdi.nama AS nama_sektor_daerah,
                                                        tsdi.status AS status
                                                FROM tb_sektor_daerah_insentif tsdi
                                                LEFT JOIN tb_sektor_daerah tsd ON tsd.id_sektor_daerah = tsdi.id_sektor_daerah
                                                LEFT JOIN tb_sektor_nasional tsn ON tsd.id_sektor_nasional = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sektor_nasional_ref tsnr ON tsnr.id_sektor = tsn.id_sektor_nasional
                                                LEFT JOIN tb_adm_kabkot tak ON tsd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tsd.id_adm_provinsi
                                                LEFT JOIN tb_adm_provinsi tap2 ON tap2.id_adm_provinsi = tak.id_adm_provinsi
                                                WHERE 
                                                        (LOWER(tsnr.nama) ILIKE $1 OR
                                                        LOWER(tap.nama) ILIKE $1 OR
                                                        LOWER(tak.nama) ILIKE $1 OR
                                                        LOWER(tsdi.nama) ILIKE $1 OR
                                                        tsdi.id_sektor_daerah_insentif::TEXT ILIKE $1)
                                                ${Wilayah}
                                                ORDER BY tsdi.id_sektor_daerah_insentif DESC
                                                LIMIT $2 OFFSET $3
                                        `, `%${searchQuery}%`, perPage, offset);

                                        
                                } else {
                                        // Query untuk menghitung total data tanpa filter `q`
                                        // const totalCountResult = await prisma.$queryRaw<{ count: number }[]>`
                                        if (IdAdmKabkot) {
                                                Wilayah += ` where tak.id_adm_kabkot = ${IdAdmKabkot}`;
                                                if (status != undefined  && !isNaN(status)){
                                                        Wilayah += ` AND tsd.status = ${status}` 
                                                }
                                        }
                                        if (IdAdmProvinsi){
                                                Wilayah += ` where (tap.id_adm_provinsi = ${IdAdmProvinsi} 
                                                                      OR LEFT(tak.id_adm_kabkot::TEXT, 2) = LEFT(${IdAdmProvinsi}::TEXT, 2))`;
                                                if (status != undefined  && !isNaN(status)){
                                                        Wilayah += ` AND tsd.status = ${status}` 
                                                }
                                        }
                                        if (pic) {
                                                const picProv = await prisma.tb_user_internal_provinsi.findMany({
                                                        where:{
                                                        id_user:pic
                                                        }
                                                })
                                                if (picProv.length > 0) {
                                                        const provinsiIds = picProv.map((item) => item.id_adm_provinsi);
                                                        const provinsiIdsStr = provinsiIds.join(",");
                                                        const kabkotConditions = provinsiIds.map(id => `LEFT(tak.id_adm_kabkot::TEXT, 2) = LPAD(${id}::TEXT, 2, '0')`).join(' OR ');
                                                        Wilayah += ` where (tap.id_adm_provinsi IN (${provinsiIdsStr}) OR (${kabkotConditions}))`;
                                                }
                                        }
                                        if (status != undefined && Wilayah == ''){
                                                Wilayah += ` where tsd.status = ${status}` 
                                        }


                                        // Query untuk menghitung total data
                                        const totalCountResult =  await prisma.$queryRawUnsafe(`
                                                SELECT COUNT(*) as count
                                                FROM tb_sektor_daerah_insentif tsdi
                                                LEFT JOIN tb_sektor_daerah tsd ON tsd.id_sektor_daerah = tsdi.id_sektor_daerah
                                                LEFT JOIN tb_sektor_nasional tsn ON tsd.id_sektor_nasional = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sektor_nasional_ref tsnr ON tsnr.id_sektor = tsn.id_sektor_nasional
                                                LEFT JOIN tb_adm_kabkot tak ON tsd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tsd.id_adm_provinsi
                                                ${Wilayah}
                                        `);
                                        // return totalCountResult

                                        totalCount = Number(totalCountResult[0]?.count || 0);

                                        // Query data tanpa filter `q`, limit, dan offset
                                        data = await await prisma.$queryRawUnsafe(`
                                                SELECT 
                                                        tsdi.id_sektor_daerah_insentif AS id_sektor_daerah_insentif,
                                                        tsnr.nama AS sektor,
                                                        case 
                                                                when tap.nama is null then tap2.nama
                                                                else 
                                                                tap.nama 
                                                        end AS provinsi,
                                                        tak.nama AS kabkota,
                                                        tsdi.nama AS nama_sektor_daerah,
                                                        tsdi.status AS status
                                                FROM tb_sektor_daerah_insentif tsdi
                                                LEFT JOIN tb_sektor_daerah tsd ON tsd.id_sektor_daerah = tsdi.id_sektor_daerah
                                                LEFT JOIN tb_sektor_nasional tsn ON tsd.id_sektor_nasional = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sektor_nasional_ref tsnr ON tsnr.id_sektor = tsn.id_sektor_nasional
                                                LEFT JOIN tb_adm_kabkot tak ON tsd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tsd.id_adm_provinsi
                                                LEFT JOIN tb_adm_provinsi tap2 ON tap2.id_adm_provinsi = tak.id_adm_provinsi
                                                ${Wilayah}
                                                ORDER BY tsdi.id_sektor_daerah_insentif DESC
                                                LIMIT ${perPage} OFFSET ${offset}
                                        `);
                                }
                                // return data
                                // Format response
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: perPage,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / perPage),
                                        },
                                        data: data,
                                });
                        } catch (error) {
                                console.error(error);
                                return response.status(500).json({
                                        success: false,
                                        message: "An error occurred while fetching data",
                                        error: error.message,
                                });
                        }
                }

                let isAll = false;

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        include: {
                                tb_sektor_daerah: true
                        }
                }


                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                options['where'] = modelWhereAnd((await this.schemaData()).columns, queryParams);

                try {
                        const data = await prisma.tb_sektor_daerah_insentif.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_sektor_daerah_insentif.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }

        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {

                };
                


                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_sektor_daerah_insentif': parseInt(params.id)
                }

                let data = await prisma.tb_sektor_daerah_insentif.findFirst({
                        where: { id_sektor_daerah_insentif: parseInt(params.id) },
                        include: {
                                tb_sektor_daerah: {
                                        include: {
                                                tb_adm_provinsi: { select: { id_adm_provinsi: true, nama: true } },
                                                tb_adm_kabkot: { select: { id_adm_kabkot: true, nama: true } },
                                        }
                                },
                                tb_sektor_daerah_insentif_file: true
                        }
                });

                if (data?.tb_sektor_daerah_insentif_file?.length != undefined && data?.tb_sektor_daerah_insentif_file?.length > 0) {
                        data?.tb_sektor_daerah_insentif_file.forEach((i,k) => {
                                data.tb_sektor_daerah_insentif_file[k]['type'] = i.nama.split('.').pop()
                        });
                }

                const sanitizeFileName = (fileName: string): string => {
                        const fileBaseName = fileName.replace(/\s+/g, "_").replace(/\.[^/.]+$/, ""); // Remove spaces and extension
                        const fileExtension = fileName.split('.').pop()?.toLowerCase() || "";
                        return fileExtension === "pdf" ? `${fileBaseName}.pdf` : `${fileBaseName}.webp`; // Replace non-PDF extensions with .webp
                };
                const getFoto = data?.tb_sektor_daerah_insentif_file?.map((file: { nama: string }) =>
                        `${process.env.APP_URL}/uploads/sektor/${sanitizeFileName(file.nama)}`
                ) || [];

                // const pathFoto = getFoto.join(',');


                let optionTr: Options = {
                        where: {
                                'id_sektor_daerah_insentif': parseInt(params.id)
                        }
                };


                const dataTr = await prisma.tb_sektor_daerah_insentif_tr.findFirst(optionTr)

                if (optionTr['where'] && (!data || data.length === 0)) {
                        return response.status(404).json({
                                success: false,
                                message: "Data yang kamu cari tidak ada"
                        });
                }

                data['translate'] = dataTr;


                return response.status(200).json({
                        success: true,
                        data: { 
                                ...data,
                                 pathFoto: getFoto
                                }
                });
        }



        public async createOrUpdate({ request, response, params }: HttpContext) {
                const paramID = parseInt(params.id);
                // const model = "sud_sektor_daerah_insentif";

                try {
                        const result = await prisma.$transaction(async (prisma) => {
                                // let fileName: string | undefined;
                                // let resID: number | undefined;

                                // const file = request.file("file");

                                if (!paramID) {
                                        console.log("Sync tb_sektor_daerah_insentif id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_sektor_daerah_insentif"', 'id_sektor_daerah_insentif')),
                                                (SELECT (MAX("id_sektor_daerah_insentif") + 1) FROM "tb_sektor_daerah_insentif"),
                                                false) FROM "tb_sektor_daerah_insentif";
                                         `);

                                        const res = await prisma.tb_sektor_daerah_insentif.create({
                                                data: {
                                                        id_sektor_daerah: request.input("id_sektor_daerah") ? parseInt(request.input("id_sektor_daerah")) : 0,
                                                        nama: request.input("nama") || "",
                                                        deskripsi: request.input("deskripsi") || "",
                                                        status: request.input("status") ? parseInt(request.input("status")) : 0,
                                                }
                                        });

                                        // resID = res.id_sektor_daerah_insentif

                                        // if (file) {
                                        //         await uploadFile(file, model, resID);
                                        //         fileName = file.fileName ?? ""; // Set fileName if file exists
                                        // }

                                        // console.log("Sync tb_sektor_daerah_insentif_file id sequence",
                                        //         await prisma.$executeRaw`
                                        //         SELECT setval((
                                        //         SELECT PG_GET_SERIAL_SEQUENCE('"tb_sektor_daerah_insentif_file"', 'id_sektor_daerah_insentif_file')),
                                        //         (SELECT (MAX("id_sektor_daerah_insentif_file") + 1) FROM "tb_sektor_daerah_insentif_file"),
                                        //         false) FROM "tb_sektor_daerah_insentif_file";
                                        //  `);

                                        // const data_sektor_daerah_insentif_file: any = {
                                        //         id_sektor_daerah_insentif: res.id_sektor_daerah_insentif,
                                        //         tipe: request.input("tipe") ? parseInt(request.input("tipe")) : 3,
                                        //         jenis: request.input("jenis") ? parseInt(request.input("jenis")) : 0,
                                        //         nama: fileName || "",
                                        //         judul: request.input("judul_file") || "",
                                        //         keterangan: request.input("keterangan_file") || "",
                                        // }

                                        // const res_data_sektor_daerah_insentif_file = await prisma.tb_sektor_daerah_insentif_file.create({ data: data_sektor_daerah_insentif_file });

                                        console.log("Sync tb_sektor_daerah_insentif_tr id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_sektor_daerah_insentif_tr"', 'id_sektor_daerah_insentif_tr')),
                                                (SELECT (MAX("id_sektor_daerah_insentif_tr") + 1) FROM "tb_sektor_daerah_insentif_tr"),
                                                false) FROM "tb_sektor_daerah_insentif_tr";
                                         `);

                                        const data_sektor_daerah_insentif_tr: any = {
                                                id_sektor_daerah_insentif: res.id_sektor_daerah_insentif,
                                                kd_bahasa: request.input("kd_bahasa") || "en",
                                                nama: request.input("tr_nama") || "",
                                                deskripsi: request.input("tr_deskripsi") || "",
                                        }

                                        const res_data_sektor_daerah_insentif_tr = await prisma.tb_sektor_daerah_insentif_tr.create({ data: data_sektor_daerah_insentif_tr });

                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: res,
                                                // data_file: res_data_sektor_daerah_insentif_file,
                                                data_tr: res_data_sektor_daerah_insentif_tr,
                                        };


                                } else {
                                        // console.log("Sync tb_sektor_daerah_insentif id sequence",
                                        //         await prisma.$executeRaw`
                                        //         SELECT setval((
                                        //         SELECT PG_GET_SERIAL_SEQUENCE('"tb_sektor_daerah_insentif"', 'id_sektor_daerah_insentif')),
                                        //         (SELECT (MAX("id_sektor_daerah_insentif") + 1) FROM "tb_sektor_daerah_insentif"),
                                        //         false) FROM "tb_sektor_daerah_insentif";
                                        //  `);
                                        const update = await prisma.tb_sektor_daerah_insentif.update({
                                                where: { id_sektor_daerah_insentif: paramID },
                                                data: {
                                                        id_sektor_daerah: request.input("id_sektor_daerah") ? parseInt(request.input("id_sektor_daerah")) : 0,
                                                        nama: request.input("nama") || "",
                                                        deskripsi: request.input("deskripsi") || "",
                                                        status: request.input("status") ? parseInt(request.input("status")) : 0,
                                                }
                                        });

                                        // await prisma.tb_sektor_daerah_insentif_tr.deleteMany({
                                        //         where: {
                                        //                 id_sektor_daerah_insentif: paramID
                                        //         }
                                        // });

                                        // console.log("Sync tb_sektor_daerah_insentif_tr id sequence",
                                        //         await prisma.$executeRaw`
                                        //         SELECT setval((
                                        //         SELECT PG_GET_SERIAL_SEQUENCE('"tb_sektor_daerah_insentif_tr"', 'id_sektor_daerah_insentif_tr')),
                                        //         (SELECT (MAX("id_sektor_daerah_insentif_tr") + 1) FROM "tb_sektor_daerah_insentif_tr"),
                                        //         false) FROM "tb_sektor_daerah_insentif_tr";
                                        //  `);

                                        const data_sektor_daerah_insentif_tr: any = {
                                                id_sektor_daerah_insentif: paramID,
                                                kd_bahasa: request.input("kd_bahasa") || 'en',
                                                deskripsi: request.input("deskripsi") || '-',
                                                nama: request.input("tr_nama") || ''
                                        }

                                        const existingTr = await prisma.tb_sektor_daerah_insentif_tr.findFirst({
                                                where: { id_sektor_daerah_insentif: paramID }
                                        })

                                        let resTr: any
                                        if (existingTr) {
                                                resTr = await prisma.tb_sektor_daerah_insentif_tr.update({
                                                        where: { id_sektor_daerah_insentif_tr: existingTr.id_sektor_daerah_insentif_tr },
                                                        data: data_sektor_daerah_insentif_tr,
                                                });
                                        }
                                        // await prisma.tb_sektor_daerah_insentif_tr.update({ data: data_sub_sektor_daerah_tr });

                                        // await prisma.tb_sektor_daerah_insentif_status.deleteMany({
                                        //         where: {
                                        //                 id_sektor_daerah_insentif: paramID
                                        //         }
                                        // });

                                        // const data_sektor_daerah_insentif_status: any = {
                                        //         id_sektor_daerah_insentif: update.id_sektor_daerah_insentif,
                                        //         status: request.input("sdi_status") ? parseInt(request.input("sdi_status")) : 0,
                                        //         status_proses: request.input("status_proses") ? parseInt(request.input("status_proses")) : 0,
                                        //         keterangan: request.input("sdi_keterangan") ? request.input("sdi_keterangan") : "",
                                        //         created_by: request.input("created_by") ? parseInt(request.input("created_by")) : 0,
                                        //         created_date: request.input("created_date") ? new Date(request.input("created_date")) : new Date(),
                                        //         updated_by: request.input("updated_by") ? parseInt(request.input("updated_by")) : 0,
                                        //         updated_date: request.input("updated_date") ? new Date(request.input("updated_date")) : new Date(),
                                        // }

                                        // await prisma.tb_sektor_daerah_insentif_status.create({ data: data_sektor_daerah_insentif_status });

                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update,
                                                data_tr: resTr
                                        };
                                }
                        });

                        return response.json(result);
                } catch (error) {
                        console.log(error);
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error,
                        });
                }
        }

        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        // Find related translations before deleting the parent record
                        const relatedRecords = await prisma.tb_sektor_daerah_insentif_tr.findMany({
                                where: { id_sektor_daerah_insentif: Id },
                        });

                        // Delete related translations
                        await prisma.tb_sektor_daerah_insentif_tr.deleteMany({
                                where: { id_sektor_daerah_insentif: Id },
                        });

                        // Delete the parent record
                        const deletePost = await prisma.tb_sektor_daerah_insentif.delete({
                                where: { id_sektor_daerah_insentif: Id },
                        });

                        return response.status(200).json({
                                status: 'success',
                                message: 'Successfully deleted data and its related records',
                                data: deletePost,
                                deletedTranslations: relatedRecords,
                        });
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

        public async toggleStatus({ request, params, response }: HttpContext) {
                const Id = parseInt(params.id, 10)
                const statusId = parseInt(request.input('status') ?? 0);

                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                if (isNaN(statusId)) {
                        return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                }

                try {
                        const toggleStatus = await prisma.tb_sektor_daerah_insentif.update({
                                where: {
                                        id_sektor_daerah_insentif: Id,
                                },
                                data: {
                                        status: request.input('status') ?? 0
                                },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Toggle Status',
                                data: toggleStatus,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error Toggle Status' })
                }
        }
}