import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../../helpers/model_helper.js';

import prisma from '../../../lib/prisma.js';
import uploadFile from '../../../helpers/file_uploader.js';
import { exit } from 'process';



export default class SUDSektorDaerahController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_sektor_daerah: { column: 'id_sektor_daerah', alias: 'id_sektor_daerah', type: 'int' },
                        tipe: { column: 'tipe', alias: 'tipe', type: 'int' },
                        id_sektor_nasional: { column: 'id_sektor_nasional', alias: 'id_sektor_nasional', type: 'int' },
                        id_adm_kabkot: { column: 'id_adm_kabkot', alias: 'id_adm_kabkot', type: 'int' },
                        id_adm_provinsi: { column: 'id_adm_provinsi', alias: 'id_adm_provinsi', type: 'int' },
                        deskripsi_singkat: { column: 'deskripsi_singkat', alias: 'deskripsi_singkat', type: 'string' },
                        deskripsi: { column: 'deskripsi', alias: 'deskripsi', type: 'string' },
                        potensi_pasar: { column: 'potensi_pasar', alias: 'potensi_pasar', type: 'string' },
                        no_dokumen: { column: 'no_dokumen', alias: 'no_dokumen', type: 'string' },
                        perihal: { column: 'perihal', alias: 'perihal', type: 'string' },
                        file_dokumen: { column: 'file_dokumen', alias: 'file_dokumen', type: 'string' },
                        status: { column: 'status', alias: 'status', type: 'int' }
                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                if (request.input('is_simple') || request.input('is_simple') == 'true') {
                        const params = request.qs();
                        let IdAdmProvinsi = params['id_adm_provinsi'];
                        let IdAdmKabkot = params['id_adm_kabkot'];
                        let whereOpts = {
                                id_adm_provinsi: ((parseInt(IdAdmKabkot ?? 0) > 0) ? null : parseInt(IdAdmProvinsi)),
                                id_adm_kabkot:null
                        }
                        if (parseInt(IdAdmKabkot ?? 0) > 0) {
                                delete whereOpts['id_adm_provinsi'];
                                whereOpts['id_adm_kabkot'] = ((parseInt(IdAdmKabkot ?? 0) > 0) ? parseInt(IdAdmKabkot) : null);
                        }
                        const data = await prisma.tb_sektor_daerah.findMany({
                                where: whereOpts,
                                select: {
                                  sektor_nasional: {
                                        select: {
                                                id_sektor_nasional: true,
                                                sektor: {
                                                        select: {
                                                                nama: true
                                                        }
                                                }
                                        },
                                  },
                                  id_sektor_daerah: true, // Ambil id dari tb_sektor_daerah
                                  tb_sektor_daerah_lq: {
                                        select: {
                                                tahun_pdrb: true
                                        }
                                  },
                                },
                              })

                
                        
                        
                        // Modifikasi array dengan menambahkan sektor_nasional_ref
                        const modifiedData = data.map((item) => {
                                return {
                                        ...item,
                                        sektor_nasional_ref: {
                                                nama: item.sektor_nasional?.sektor?.nama || null
                                        }, // Ambil nama dari sektor atau null jika tidak ada
                                };
                        });

                        // const modifiedData = data.map((item) => ({
                        //         id_sektor_nasional: item.sektor_nasional?.id_sektor_nasional || null,
                        //         nama: item.sektor_nasional?.sektor?.nama || null,
                        // })).filter(item => item.id_sektor_nasional !== null); // Hapus yang id null
                        
                        // // Group berdasarkan id_sektor_nasional
                        // const groupedData = modifiedData.reduce((acc, item) => {
                        // if (!acc.some(entry => entry.id_sektor_nasional === item.id_sektor_nasional)) {
                        //         acc.push(item);
                        // }
                        // return acc;
                        // }, []);

                        
                              
                        return response.status(200).json({
                                success: true,
                                message: 'Success Retrieve Data',
                                data: modifiedData,
                        })
                }

                if (
                        request.input("is_get_table_admin") ||
                        request.input("is_get_table_admin") == "true"
                ) {
                        const page = parseInt(request.input("page", "1")) || 1;
                        const perPage = parseInt(request.input("per_page", "10")) || 10;
                        const offset = (page - 1) * perPage;
                
                        const searchQuery = request.input("q")?.trim() || request.input("filter")?.trim(); // Trim untuk menghapus spasi ekstra
                        let Wilayah = ""; // Awalnya valid

                        const params = request.qs();
                        let IdAdmProvinsi = params['id_adm_provinsi'];
                        let IdAdmKabkot = params['id_adm_kabkot'];
                        let status = parseInt(params['status']);
                        let pic = parseInt(params['pic']) || null;
                        let data = [];
                        let totalCount = 0
                        // return IdAdmKabkot
                        try {
                                // Jika `searchQuery` kosong, maka `WHERE` dihilangkan
                                let order = params.order 
                                const by = ['asc','desc'].includes(params.by) ? params.by : 'asc'
                                let orderBy=`ORDER BY tsd.id_sektor_daerah DESC`
                                const paramList = ['status','tahun']
                                if (order != undefined && paramList.includes(order)) {
                                        if (order == 'tahun') {
                                                orderBy = `order by tsdl.tahun_pdrb ${by}`
                                        }else if (order == 'status') {
                                                orderBy = `order by tsd.status ${by}`
                                        }
                                }
                                if (searchQuery) {
                                        if (IdAdmKabkot) {
                                                Wilayah += ` AND tak.id_adm_kabkot = ${IdAdmKabkot}`;
                                        }
                                        if (IdAdmProvinsi){
                                                Wilayah += ` AND (tap.id_adm_provinsi = ${IdAdmProvinsi} 
                                                                OR LEFT(tak.id_adm_kabkot::TEXT, 2) = LEFT(${IdAdmProvinsi}::TEXT, 2))`;
                                        }
                                        if (pic) {
                                                const picProv = await prisma.tb_user_internal_provinsi.findMany({
                                                        where:{
                                                        id_user:pic
                                                        }
                                                })
                                                if (picProv.length > 0) {
                                                        const provinsiIds = picProv.map((item) => item.id_adm_provinsi);
                                                        const provinsiIdsStr = provinsiIds.join(",");
                                                        const kabkotConditions = provinsiIds.map(id => `LEFT(tak.id_adm_kabkot::TEXT, 2) = LPAD(${id}::TEXT, 2, '0')`).join(' OR ');
                                                        Wilayah += ` where (tap.id_adm_provinsi IN (${provinsiIdsStr}) OR (${kabkotConditions}))`;
                                                }
                                        }
                                        if (status != undefined  && !isNaN(status)){
                                                Wilayah += ` AND tsd.status = ${status}` 
                                        }
                                        // Query untuk menghitung total data
                                        const totalCountResult = await prisma.$queryRawUnsafe(`
                                                SELECT COUNT(*) as count
                                                FROM tb_sektor_daerah tsd
                                                LEFT JOIN tb_adm_kabkot tak ON tsd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap ON tsd.id_adm_provinsi = tap.id_adm_provinsi
                                                LEFT JOIN tb_sektor_nasional_ref tsnr ON tsd.id_sektor_nasional = tsnr.id_sektor
                                                LEFT JOIN tb_sektor_daerah_lq tsdl ON tsdl.id_sektor_daerah = tsd.id_sektor_daerah
                                                WHERE 
                                                (LOWER(tap.nama) ILIKE $1 OR
                                                LOWER(tak.nama) ILIKE $1 OR
                                                LOWER(tsnr.nama) ILIKE $1 OR
                                                tsdl.tahun_pdrb::TEXT ILIKE $1 OR
                                                tsdl.nilai_lq::TEXT ILIKE $1 OR
                                                tsdl.pdb_sektor::TEXT ILIKE $1 OR
                                                tsdl.pdb_total::TEXT ILIKE $1 OR
                                                tsdl.pdrb_sektor::TEXT ILIKE $1 OR
                                                tsdl.pdrb_total::TEXT ILIKE $1 OR
                                                tsd.id_sektor_daerah::TEXT ILIKE $1)
                                                ${Wilayah}
                                        `, `%${searchQuery}%`);

                                        totalCount = Number(totalCountResult[0]?.count || 0);
                        
                                        // Query data dengan filter q, limit, offset
                                        data = await prisma.$queryRawUnsafe(`
                                                SELECT
                                                        tsnr.nama AS nama_sektor,
                                                        case 
                                                                when tap.nama is null then tap2.nama
                                                                else 
                                                                tap.nama 
                                                        end AS nama_provinsi,
                                                        tak.nama AS nama_kabkot,
                                                        tsdl.tahun_pdrb AS tahun,
                                                        tsdl.nilai_lq,
                                                        tsdl.pdb_sektor,
                                                        tsdl.pdb_total,
                                                        tsdl.pdrb_sektor,
                                                        tsdl.pdrb_total,
                                                        tsd.*,
                                                         regexp_replace(tsd.potensi_pasar, '<[^>]*>', '', 'g') AS potensi_pasar,
                                                        regexp_replace(tsd.deskripsi, '<[^>]*>', '', 'g') AS deskripsi
                                                FROM tb_sektor_daerah tsd
                                                LEFT JOIN tb_adm_kabkot tak ON tsd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap ON tsd.id_adm_provinsi = tap.id_adm_provinsi
                                                LEFT JOIN tb_adm_provinsi tap2 ON tak.id_adm_provinsi = tap2.id_adm_provinsi
                                                LEFT JOIN tb_sektor_nasional_ref tsnr ON tsd.id_sektor_nasional = tsnr.id_sektor
                                                LEFT JOIN tb_sektor_daerah_lq tsdl ON tsdl.id_sektor_daerah = tsd.id_sektor_daerah
                                                WHERE 
                                                        (LOWER(tap.nama) ILIKE $1 OR
                                                        LOWER(tak.nama) ILIKE $1 OR
                                                        LOWER(tsnr.nama) ILIKE $1 OR
                                                        tsdl.tahun_pdrb::TEXT ILIKE $1 OR
                                                        tsdl.nilai_lq::TEXT ILIKE $1 OR
                                                        tsdl.pdb_sektor::TEXT ILIKE $1 OR
                                                        tsdl.pdb_total::TEXT ILIKE $1 OR
                                                        tsdl.pdrb_sektor::TEXT ILIKE $1 OR
                                                        tsdl.pdrb_total::TEXT ILIKE $1 OR
                                                        tsd.id_sektor_daerah::TEXT ILIKE $1)
                                                        ${Wilayah}
                                                        ${orderBy}
                                                LIMIT $2 OFFSET $3
                                        `, `%${searchQuery}%`, perPage, offset);
                                } else {
                                        if (IdAdmKabkot) {
                                                Wilayah += ` where tak.id_adm_kabkot = ${IdAdmKabkot}`;
                                                if (status != undefined  && !isNaN(status)){
                                                        Wilayah += ` AND tsd.status = ${status}` 
                                                }
                                        }
                                        if (IdAdmProvinsi){
                                                Wilayah += ` where (tap.id_adm_provinsi = ${IdAdmProvinsi} 
                                                                      OR LEFT(tak.id_adm_kabkot::TEXT, 2) = LEFT(${IdAdmProvinsi}::TEXT, 2))`;
                                                if (status != undefined  && !isNaN(status)){
                                                        Wilayah += ` AND tsd.status = ${status}` 
                                                }
                                        }

                                        if (pic) {
                                                const picProv = await prisma.tb_user_internal_provinsi.findMany({
                                                        where:{
                                                        id_user:pic
                                                        }
                                                })
                                                if (picProv.length > 0) {
                                                        const provinsiIds = picProv.map((item) => item.id_adm_provinsi);
                                                        const provinsiIdsStr = provinsiIds.join(",");
                                                        const kabkotConditions = provinsiIds.map(id => `LEFT(tak.id_adm_kabkot::TEXT, 2) = LPAD(${id}::TEXT, 2, '0')`).join(' OR ');
                                                        Wilayah += ` where (tap.id_adm_provinsi IN (${provinsiIdsStr}) OR (${kabkotConditions}))`;
                                                }
                                        }
                                        if (status != undefined && Wilayah == '' && !isNaN(status)){
                                                Wilayah += ` where tsd.status = ${status}` 
                                        }
                                        // Query untuk menghitung total data
                                        const totalCountResult =  await prisma.$queryRawUnsafe(`
                                                SELECT COUNT(*) as count
                                                FROM tb_sektor_daerah tsd
                                                LEFT JOIN tb_adm_kabkot tak ON tsd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap ON tsd.id_adm_provinsi = tap.id_adm_provinsi
                                                LEFT JOIN tb_sektor_nasional_ref tsnr ON tsd.id_sektor_nasional = tsnr.id_sektor
                                                LEFT JOIN tb_sektor_daerah_lq tsdl ON tsdl.id_sektor_daerah = tsd.id_sektor_daerah
                                                ${Wilayah}
                                        `);

                                        totalCount = Number(totalCountResult[0]?.count || 0);
                        
                                        // Query data dengan filter q, limit, offset
                                        data = await prisma.$queryRawUnsafe(`
                                                SELECT
                                                        tsnr.nama AS nama_sektor,
                                                        case 
                                                                when tap.nama is null then tap2.nama
                                                                else 
                                                                tap.nama 
                                                        end AS nama_provinsi,
                                                        tak.nama AS nama_kabkot,
                                                        tsdl.tahun_pdrb AS tahun,
                                                        tsdl.nilai_lq,
                                                        tsdl.pdb_sektor,
                                                        tsdl.pdb_total,
                                                        tsdl.pdrb_sektor,
                                                        tsdl.pdrb_total,
                                                        tsd.*,
                                                        regexp_replace(tsd.potensi_pasar, '<[^>]*>', '', 'g') AS potensi_pasar,
                                                        regexp_replace(tsd.deskripsi, '<[^>]*>', '', 'g') AS deskripsi
                                                FROM tb_sektor_daerah tsd
                                                LEFT JOIN tb_adm_kabkot tak ON tsd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap ON tsd.id_adm_provinsi = tap.id_adm_provinsi
                                                LEFT JOIN tb_adm_provinsi tap2 ON tak.id_adm_provinsi = tap2.id_adm_provinsi
                                                LEFT JOIN tb_sektor_nasional_ref tsnr ON tsd.id_sektor_nasional = tsnr.id_sektor
                                                LEFT JOIN tb_sektor_daerah_lq tsdl ON tsdl.id_sektor_daerah = tsd.id_sektor_daerah
                                                ${Wilayah}                                                
                                                ${orderBy}
                                                LIMIT ${perPage} OFFSET ${offset}
                                        `);
                                }
                                // Format response
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: perPage,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / perPage),
                                        },
                                        data: data,
                                });
                        } catch (error) {
                                console.error(error);
                                return response.status(500).json({
                                        success: false,
                                        message: "An error occurred while fetching data",
                                        error: error.message,
                                });
                        }
                }

                let isAll = false;

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        orderBy: {
                                id_sektor_daerah: 'desc', 
                        },
                        include: {
                                sektor_nasional: {
                                        include: {
                                                sektor: true
                                        }
                                },
                                // sektor_nasional_ref: true,
                                tb_adm_provinsi: true,
                                tb_sektor_daerah_file: true,
                                tb_sektor_daerah_insentif: true,
                                tb_sub_sektor_daerah: true,
                                // tb_sektor_daerah_tr: true,
                                tb_adm_kabkot: true,
                                tb_sektor_daerah_lq: true,
                                tb_sektor_daerah_tr: true,
                                tb_sektor_daerah_sumber_data:{
                                        include:{
                                                tb_sumber_data:{
                                                        include:{
                                                                tb_sumber_data_judul:{
                                                                        include:{
                                                                                tb_sumber_data_instansi:true
                                                                        }
                                                                }
                                                        }
                                                }
                                        }
                                },
                        }
                }


                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                options['where'] = modelWhereAnd((await this.schemaData()).columns, queryParams);
                try {
                        let data = await prisma.tb_sektor_daerah.findMany(options);

                        data = data.map((item) => {
                                return {
                                        ...item, // Spread item untuk mempertahankan properti yang ada
                                        sektor_nasional_ref: item['sektor_nasional'] ? item['sektor_nasional']['sektor'] : [],
                                };
                        });
                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_sektor_daerah.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }

        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {
                        include: {
                                sektor_nasional: {
                                        include: {
                                                sektor: true
                                        }
                                },
                                tb_adm_provinsi: true,
                                tb_sektor_daerah_file: {
                                        include:{
                                                tb_sektor_daerah_file_tr:true
                                        }
                                },
                                tb_sektor_daerah_insentif: {
                                        include:{
                                                tb_sektor_daerah_insentif_file:{
                                                        include: {
                                                                tb_sektor_daerah_insentif_file_tr:true
                                                        }
                                                },
                                                tb_sektor_daerah_insentif_tr:true
                                        }
                                },
                                tb_sub_sektor_daerah: true,
                                tb_sektor_daerah_tr: true,
                                tb_adm_kabkot: true,
                                tb_sektor_daerah_lq: true,
                                tb_sektor_daerah_value_detail: true,
                                tb_sektor_daerah_sumber_data:{
                                        include:{
                                                tb_sumber_data:{
                                                        include:{
                                                                tb_sumber_data_judul:{
                                                                        include:{
                                                                                tb_sumber_data_instansi:true
                                                                        }
                                                                }
                                                        }
                                                }
                                        }
                                },
                        }
                };

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_sektor_daerah': parseInt(params.id)
                }

                const data = await prisma.tb_sektor_daerah.findFirst(options);

                data['tb_sektor_daerah_file'] = data['tb_sektor_daerah_file'].map((item) => {
                        const tb_sektor_daerah_file_tr = item.tb_sektor_daerah_file_tr.map((items) => {
                                return {
                                        ...items,
                                        path: `${process.env.APP_URL}/uploads/sektor/${items.nama}`,

                                }
                        }) 
                        return {
                                ...item, // Spread item untuk mempertahankan properti yang ada
                                path: `${process.env.APP_URL}/uploads/sektor/${item.nama}`,
                                tb_sektor_daerah_file_tr
                        };
                });
                data['tb_sektor_daerah_insentif'] = data['tb_sektor_daerah_insentif'].map((item) => {
                        const tb_sektor_daerah_insentif_file = item.tb_sektor_daerah_insentif_file.map((item1) => {
                                const tb_sektor_daerah_insentif_file_tr = item1.tb_sektor_daerah_insentif_file_tr.map((item2) => {
                                        return {
                                                ...item2,
                                                path: `${process.env.APP_URL}/uploads/sektor/${item2.nama}`,

                                        }
                                }) 
                                return {
                                        ...item1, // Spread item untuk mempertahankan properti yang ada
                                        path: `${process.env.APP_URL}/uploads/sektor/${item1.nama}`,
                                        tb_sektor_daerah_insentif_file_tr
                                };
                        })
                        return tb_sektor_daerah_insentif_file
                });

                const modifiedData = {
                        ...data,
                        sektor_nasional_ref: {
                                nama: data?.sektor_nasional?.sektor?.nama || null,
                        },
                };
                

                return response.status(200).json({
                        success: true,
                        data: modifiedData
                });
        }



        public async createOrUpdate({ request, response, params }: HttpContext) {
                const paramID = parseInt(params.id);
                const model = "sud_sektor_daerah";

                try {
                        const result = await prisma.$transaction(async (prisma) => {
                                let fileName1: string = "";
                                let fileName2: string = "";

                                let resID: number | undefined;

                                const file1 = request.file("file_dokumen");
                                const file2 = request.file("file_nama");

                                if (!paramID) {
                                        
                                        const tahun = parseInt(request.input("tahun_pdrb")) || 0
                                        const idProv = parseInt(request.input("id_adm_provinsi")) || 0
                                        const idKabkot = request.input("id_adm_kabkot") // bisa undefined atau string kosong

                                        const whereCondition: any = {
                                        tahun_pdrb: tahun,
                                        id_adm_provinsi: idProv,
                                        }

                                        if (idKabkot) {
                                        whereCondition.id_adm_kabkot = parseInt(idKabkot)
                                        }

                                        const checkExist = await prisma.tb_sektor_daerah_pdrb.findFirst({
                                        where: whereCondition,
                                        })

                                        console.log('checkExist')
                                        console.log(checkExist)
                                        console.log(whereCondition)
                                        console.log(parseInt(request.input("tahun_pdrb")))
                                        console.log(parseInt(request.input("id_adm_kabkot")) )
                                        console.log(parseInt(request.input("id_adm_provinsi")) || 0)
                                        if (!checkExist) {
                                                return {
                                                        status: 'error',
                                                        message: 'Sektor daerah PDRB Belum Dibuat Pada Daerah Ini!',
                                                        // error: null,
                                                };
                                        }

                                        const checkUnique = await prisma.tb_sektor_daerah.findFirst({
                                                where: {
                                                        id_sektor_nasional: parseInt(request.input("id_sektor_nasional")) || 0,
                                                        id_adm_kabkot: parseInt(request.input("id_adm_kabkot")) || undefined,
                                                        id_adm_provinsi: parseInt(request.input("id_adm_provinsi")) || 0,
                                                }
                                        })

                                        if (checkUnique) {
                                                const checkUniqueYear = await prisma.tb_sektor_daerah_lq.findFirst({
                                                        where: {
                                                                id_sektor_daerah: checkUnique.id_sektor_daerah,
                                                                tahun_pdb: parseInt(request.input("tahun_pdb")) || 0,
                                                        }
                                                });

                                                if (checkUniqueYear) {
                                                        return {
                                                                status: 'error',
                                                                message: 'Data Duplikat! Sektor daerah Sudah Pernah Dibuat Pada Daerah dan Tahun Yang Sama!',
                                                                // error: null,
                                                        };
                                                }
                                                return {
                                                        status: 'error',
                                                        message: 'Data Duplikat! Sektor daerah Sudah Pernah Dibuat Pada Daerah dan Tahun Yang Sama!',
                                                        // error: null,
                                                };
                                        }

                                        console.log("Sync tb_sektor_daerah id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_sektor_daerah"', 'id_sektor_daerah')),
                                                (SELECT (MAX("id_sektor_daerah") + 1) FROM "tb_sektor_daerah"),
                                                false) FROM "tb_sektor_daerah";
                                         `);

                                         const res = await prisma.tb_sektor_daerah.create({
                                                data: {
                                                        tipe: parseInt(request.input("tipe")) || 0,
                                                        id_sektor_nasional: parseInt(request.input("id_sektor_nasional")) || 0,
                                                        id_adm_kabkot: parseInt(request.input("id_adm_kabkot")) || undefined,
                                                        id_adm_provinsi: parseInt(request.input("id_adm_provinsi")) || 0,
                                                        deskripsi_singkat: request.input("deskripsi_singkat") || "",
                                                        deskripsi: request.input("deskripsi") || "",
                                                        potensi_pasar: request.input("potensi_pasar") || "",
                                                        no_dokumen: request.input("no_dokumen") || "",
                                                        perihal: request.input("perihal") || "",
                                                        file_dokumen: fileName1 || "",
                                                        status: parseInt(request.input("status")) || 0,
                                                }
                                        });

                                        console.log("Sync tb_sektor_daerah_sumber_data id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_sektor_daerah_sumber_data"', 'id_sektor_daerah_sumber_data')),
                                                (SELECT (MAX("id_sektor_daerah_sumber_data") + 1) FROM "tb_sektor_daerah_sumber_data"),
                                                false) FROM "tb_sektor_daerah_sumber_data";
                                         `);
                                        
                                        if (parseInt(request.input("id_sumber_data")) || 0 > 0) {
                                                await prisma.tb_sektor_daerah_sumber_data.create({
                                                        data: {
                                                                id_sektor_daerah: res.id_sektor_daerah,
                                                                id_sumber_data: parseInt(request.input("id_sumber_data")) || 0
                                                        }
                                                });
                                        }

                                        await prisma.tb_sektor_daerah_lq.create({
                                                data: {
                                                        id_sektor_daerah: res.id_sektor_daerah,
                                                        nilai_lq: parseFloat(request.input("nilai_lq")) || 0,
                                                        pdb_sektor: parseFloat(request.input("pdb_sektor")) || 0,
                                                        pdb_total: parseFloat(request.input("pdb_total")) || 0,
                                                        pdrb_sektor: parseFloat(request.input("pdrb_sektor")) || 0,
                                                        pdrb_total: parseFloat(request.input("pdrb_total")) || 0,
                                                        tahun_pdb: parseInt(request.input("tahun_pdb")) || 0,
                                                        tahun_pdrb: parseInt(request.input("tahun_pdrb")) || 0
                                                }
                                        });

                                        if (Array.isArray(request.input("parameter_data"))) {
                                                for (const item of request.input("parameter_data")) {
                                                        item['id_sektor_daerah'] = res.id_sektor_daerah;
                                                        item['id_sektor_nasional_value'] = parseInt(item['id_sektor_nasional_value']);
                                                        item['tahun'] = parseInt(item['tahun'] ?? 0);
                                                        item['numeric_value'] = parseFloat(item['numeric_value'] ?? 0);
                                                        await prisma.tb_sektor_daerah_value_detail.create({
                                                                data: item,
                                                        });
                                                }
                                        }

                                        await prisma.tb_sektor_daerah_tr.create({
                                                data: {
                                                        id_sektor_daerah: res.id_sektor_daerah,
                                                        kd_bahasa: request.input("kd_bahasa") || 'en',
                                                        deskripsi_singkat: request.input("translate_deskripsi_singkat") || '',
                                                        deskripsi: request.input("translate_deskripsi") || '',
                                                        potensi_pasar: request.input("translate_potensi_pasar") || '',
                                                        perihal: request.input("perihal") || '',
                                                }
                                        });

                                        // console.log("Sync tb_sektor_daerah_file id sequence",
                                        //         await prisma.$executeRaw`
                                        //         SELECT setval((
                                        //         SELECT PG_GET_SERIAL_SEQUENCE('"tb_sektor_daerah_file"', 'id_sektor_daerah_file')),
                                        //         (SELECT (MAX("id_sektor_daerah_file") + 1) FROM "tb_sektor_daerah_file"),
                                        //         false) FROM "tb_sektor_daerah_file";
                                        //  `);

                                        // const data_sektor_daerah_file: any = {
                                        //         id_sektor_daerah: res.id_sektor_daerah,
                                        //         tipe: parseInt(request.input("file_tipe")) || 0,
                                        //         jenis: parseInt(request.input("jenis")) || 0,
                                        //         nama: fileName2 || "",
                                        //         judul: request.input("judul") || "",
                                        //         keterangan: request.input("keterangan") || "",
                                        // }

                                        // const res_data_sektor_daerah_file = await prisma.tb_sektor_daerah_file.create({ data: data_sektor_daerah_file });

                                        // resID = res_data_sektor_daerah_file.jenis
                                        // if (file1) {
                                        //         await uploadFile(file1, model, resID);
                                        //         fileName1 = file1.fileName ?? ""; // Set fileName if file exists
                                        // }

                                        // if (file2) {
                                        //         await uploadFile(file2, model, resID);
                                        //         fileName2 = file2.fileName ?? ""; // Set fileName if file exists
                                        // }

                                        // console.log("Sync tb_sektor_daerah_insentif id sequence",
                                        //         await prisma.$executeRaw`
                                        //         SELECT setval((
                                        //         SELECT PG_GET_SERIAL_SEQUENCE('"tb_sektor_daerah_insentif"', 'id_sektor_daerah_insentif')),
                                        //         (SELECT (MAX("id_sektor_daerah_insentif") + 1) FROM "tb_sektor_daerah_insentif"),
                                        //         false) FROM "tb_sektor_daerah_insentif";
                                        //  `);


                                        // const data_sektor_daerah_insentif: any = {
                                        //         id_sektor_daerah: res.id_sektor_daerah,
                                        //         nama: request.input("insentif_nama") || "",
                                        //         deskripsi: request.input("insentif_deskripsi") || "",
                                        //         status: parseInt(request.input("insentif_status")) || 0,
                                        // }

                                        // const res_data_sektor_daerah_insentif = await prisma.tb_sektor_daerah_insentif.create({ data: data_sektor_daerah_insentif });

                                        // console.log("Sync tb_sub_sektor_daerah id sequence",
                                        //         await prisma.$executeRaw`
                                        //         SELECT setval((
                                        //         SELECT PG_GET_SERIAL_SEQUENCE('"tb_sub_sektor_daerah"', 'id_sub_sektor_daerah')),
                                        //         (SELECT (MAX("id_sub_sektor_daerah") + 1) FROM "tb_sub_sektor_daerah"),
                                        //         false) FROM "tb_sub_sektor_daerah";
                                        //  `);

                                        // const data_sub_sektor_daerah: any = {
                                        //         id_sektor_daerah: res.id_sektor_daerah,
                                        //         id_sub_sektor_nasional: parseInt(request.input("id_sub_sektor_nasional")) || 0,
                                        //         deskripsi_singkat: request.input("ssd_deskripsi_singkat") || "",
                                        //         deskripsi: request.input("ssd_deskripsi") || "",
                                        //         status: parseInt(request.input("ssd_status")) || 0,
                                        // }

                                        // const res_data_sub_sektor_daerah = await prisma.tb_sub_sektor_daerah.create({ data: data_sub_sektor_daerah });

                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: res,
                                                // data_sektor_daerah_insentif: res_data_sektor_daerah_insentif,
                                                // data_sektor_daerah_file: res_data_sektor_daerah_file,
                                                // data_sub_sektor_daerah: res_data_sub_sektor_daerah,
                                        };
                                } else {
                                        // console.log(paramID);

                                        const update = await prisma.tb_sektor_daerah.update({
                                                where: { id_sektor_daerah: paramID },
                                                data: {
                                                        tipe: parseInt(request.input("tipe")) || 0,
                                                        id_sektor_nasional: parseInt(request.input("id_sektor_nasional")) || 0,
                                                        id_adm_kabkot: parseInt(request.input("id_adm_kabkot")) || undefined,
                                                        id_adm_provinsi: parseInt(request.input("id_adm_provinsi")) || 0,
                                                        deskripsi_singkat: request.input("deskripsi_singkat") || "",
                                                        deskripsi: request.input("deskripsi") || "",
                                                        potensi_pasar: request.input("potensi_pasar") || "",
                                                        no_dokumen: request.input("no_dokumen") || "",
                                                        perihal: request.input("perihal") || "",
                                                        file_dokumen: fileName1 || "",
                                                        status: parseInt(request.input("status")) || 0,
                                                }
                                        });

                                        await prisma.tb_sektor_daerah_sumber_data.deleteMany({
                                                where: {
                                                        id_sektor_daerah: paramID
                                                }
                                        });

                                        await prisma.tb_sektor_daerah_sumber_data.create({
                                                data: {
                                                        id_sektor_daerah: paramID,
                                                        id_sumber_data: parseInt(request.input("id_sumber_data")) || 0
                                                }
                                        });

                                        await prisma.tb_sektor_daerah_lq.deleteMany({
                                                where: {
                                                        id_sektor_daerah: paramID
                                                }
                                        });

                                        await prisma.tb_sektor_daerah_lq.create({
                                                data: {
                                                        id_sektor_daerah: paramID,
                                                        nilai_lq: parseFloat(request.input("nilai_lq")) || 0,
                                                        pdb_sektor: parseFloat(request.input("pdb_sektor")) || 0,
                                                        pdb_total: parseFloat(request.input("pdb_total")) || 0,
                                                        pdrb_sektor: parseFloat(request.input("pdrb_sektor")) || 0,
                                                        pdrb_total: parseFloat(request.input("pdrb_total")) || 0,
                                                        tahun_pdb: parseInt(request.input("tahun_pdb")) || 0,
                                                        tahun_pdrb: parseInt(request.input("tahun_pdrb")) || 0
                                                }
                                        });

                                        await prisma.tb_sektor_daerah_value_detail.deleteMany({
                                                where: {
                                                        id_sektor_daerah: paramID
                                                }
                                        });

                                        if (Array.isArray(request.input("parameter_data"))) {
                                                for (const item of request.input("parameter_data")) {
                                                        item['id_sektor_daerah'] = paramID;
                                                        item['id_sektor_nasional_value'] = parseInt(item['id_sektor_nasional_value']);
                                                        item['tahun'] = parseInt(item['tahun']);
                                                        item['numeric_value'] = parseFloat(item['numeric_value']);
                                                        await prisma.tb_sektor_daerah_value_detail.create({
                                                                data: item,
                                                        });
                                                }
                                        }

                                        await prisma.tb_sektor_daerah_tr.deleteMany({
                                                where: {
                                                        id_sektor_daerah: paramID
                                                }
                                        });

                                        await prisma.tb_sektor_daerah_tr.create({
                                                data: {
                                                        id_sektor_daerah: paramID,
                                                        kd_bahasa: request.input("kd_bahasa") || 'en',
                                                        deskripsi_singkat: request.input("translate_deskripsi_singkat") || '',
                                                        deskripsi: request.input("translate_deskripsi") || '',
                                                        potensi_pasar: request.input("translate_potensi_pasar") || '',
                                                        perihal: request.input("perihal") || '',
                                                }
                                        });

                                        // const data_sektor_daerah_file: any = {
                                        //         id_sektor_daerah: update.id_sektor_daerah,
                                        //         tipe: parseInt(request.input("file_tipe")) || 0,
                                        //         jenis: parseInt(request.input("jenis")) || 0,
                                        //         nama: fileName2 || "",
                                        //         judul: request.input("judul") || "",
                                        //         keterangan: request.input("keterangan") || "",
                                        // }

                                        // const existing_data_sektor_daerah_file = await prisma.tb_sektor_daerah_file.findFirst({
                                        //         where: { id_sektor_daerah: update.id_sektor_daerah }
                                        // });

                                        // let res_data_sektor_daerah_file: any;
                                        // if (existing_data_sektor_daerah_file) {
                                        //         res_data_sektor_daerah_file = await prisma.tb_sektor_daerah_file.update({
                                        //                 where: { id_sektor_daerah_file: existing_data_sektor_daerah_file.id_sektor_daerah },
                                        //                 data: data_sektor_daerah_file,
                                        //         });
                                        // }

                                        // const data_sektor_daerah_insentif: any = {
                                        //         id_sektor_daerah: update.id_sektor_daerah,
                                        //         nama: request.input("insentif_nama") || "",
                                        //         deskripsi: request.input("insentif_deskripsi") || "",
                                        //         status: parseInt(request.input("insentif_status")) || 0,
                                        // }

                                        // const existing_data_sektor_daerah_insentif = await prisma.tb_sektor_daerah_insentif.findFirst({
                                        //         where: { id_sektor_daerah: update.id_sektor_daerah }
                                        // });

                                        // let res_data_sektor_daerah_insentif: any
                                        // if (existing_data_sektor_daerah_insentif) {
                                        //         res_data_sektor_daerah_insentif = await prisma.tb_sektor_daerah_insentif.update({
                                        //                 where: { id_sektor_daerah_insentif: existing_data_sektor_daerah_insentif.id_sektor_daerah_insentif },
                                        //                 data: data_sektor_daerah_insentif,
                                        //         });
                                        // }

                                        // const data_sub_sektor_daerah: any = {
                                        //         id_sektor_daerah: update.id_sektor_daerah,
                                        //         id_sub_sektor_nasional: parseInt(request.input("id_sub_sektor_nasional")) || 0,
                                        //         deskripsi_singkat: request.input("ssd_deskripsi_singkat") || "",
                                        //         deskripsi: request.input("ssd_deskripsi") || "",
                                        //         status: parseInt(request.input("ssd_status")) || 0,
                                        // }

                                        // const existing_data_sub_sektor_daerah = await prisma.tb_sub_sektor_daerah.findFirst({
                                        //         where: { id_sektor_daerah: update.id_sektor_daerah }
                                        // });

                                        // let res_data_sub_sektor_daerah;
                                        // if (existing_data_sub_sektor_daerah) {
                                        //         res_data_sub_sektor_daerah = await prisma.tb_sub_sektor_daerah.update({
                                        //                 where: { id_sub_sektor_daerah: existing_data_sub_sektor_daerah.id_sub_sektor_daerah },
                                        //                 data: data_sub_sektor_daerah,
                                        //         });
                                        // }

                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update,
                                                // data_sektor_daerah_insentif: res_data_sektor_daerah_insentif,
                                                // data_sektor_daerah_file: res_data_sektor_daerah_file,
                                                // data_sub_sektor_daerah: res_data_sub_sektor_daerah,
                                        };
                                }
                        });

                        return response.json(result);
                } catch (error) {
                        console.log(error);
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error.message,
                        });
                }
        }

        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_sektor_daerah.deleteMany({
                                where: { id_sektor_daerah: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        console.log(error)
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

        public async toggleStatus({ request, params, response }: HttpContext) {
                const Id = parseInt(params.id, 10)
                const statusId = parseInt(request.input('status') ?? 0);

                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                if (isNaN(statusId)) {
                        return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                }

                try {
                        const toggleStatus = await prisma.tb_sektor_daerah.update({
                                where: {
                                        id_sektor_daerah: Id,
                                },
                                data: {
                                        status: request.input('status') ?? 0
                                },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Toggle Status',
                                data: toggleStatus,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error Toggle Status' })
                }
        }

        public async getPdrbLq({request, response}: HttpContext) {
                const params = request.qs();

                let IdAdmProvinsi = params['id_adm_provinsi'];
                let IdAdmKabkot = params['id_adm_kabkot'];
                let IdSektorNasional = params['id_sektor_nasional'];
                let TahunPdb = params['tahun_pdb'];

                const pdrbSektor = await prisma.tb_sektor_daerah_pdrb.aggregate({
                        _sum: {
                          jumlah_pdrb: true,
                        },
                        where: {
                          id_sektor_nasional: parseInt(IdSektorNasional),
                          id_adm_provinsi: (parseInt(IdAdmKabkot ?? 0) > 0) ? null : parseInt(IdAdmProvinsi),
                          id_adm_kabkot: (parseInt(IdAdmKabkot ?? 0) > 0) ? parseInt(IdAdmKabkot) : null,
                          tahun_pdrb: parseInt(TahunPdb),
                        },
                      });

                //       console.log(parseInt(IdSektorNasional));
                //       console.log((parseInt(IdAdmKabkot ?? 0) > 0) ? null : parseInt(IdAdmProvinsi));
                //       console.log((parseInt(IdAdmKabkot ?? 0) > 0) ? parseInt(IdAdmKabkot) : null);
                //       console.log(parseInt(TahunPdb));
                      
                      let amountPdrbSektor = pdrbSektor._sum.jumlah_pdrb ?? 0;

                      const pdrbTotal = await prisma.tb_sektor_daerah_pdrb.aggregate({
                        _sum: {
                          jumlah_pdrb: true,
                        },
                        where: {
                        //  id_sektor_nasional: parseInt(IdSektorNasional),
                         id_adm_provinsi: (parseInt(IdAdmKabkot ?? 0) > 0) ? null : parseInt(IdAdmProvinsi),
                         id_adm_kabkot: (parseInt(IdAdmKabkot ?? 0) > 0) ? parseInt(IdAdmKabkot) : null,
                         tahun_pdrb: parseInt(TahunPdb),
                        },
                      });
                      
                      let amountPdrbTotal = pdrbTotal._sum.jumlah_pdrb ?? 0;

                      const pdbSektor = await prisma.tb_sektor_nasional_pdb.aggregate({
                        _sum: {
                          jumlah_pdb: true,
                        },
                        where: {
                          tahun_pdb: parseInt(TahunPdb),
                          id_sektor_nasional: parseInt(IdSektorNasional),
                        },
                      });
                      
                      let amountPdbSektor = pdbSektor._sum.jumlah_pdb ?? 0;

                      const pdbTotal = await prisma.tb_sektor_nasional_pdb.aggregate({
                        _sum: {
                          jumlah_pdb: true,
                        },
                        where: {
                          tahun_pdb: parseInt(TahunPdb)
                        },
                      });
                      
                      let amountPdbTotal = pdbTotal._sum.jumlah_pdb ?? 0;

                      let lqValue = (amountPdrbSektor / amountPdrbTotal) / (amountPdbSektor / amountPdbTotal);

                      if (!lqValue) {
                        lqValue = 0;
                      }

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Retrieve LQ',
                                data: {
                                        pdrb_sektor: amountPdbSektor,
                                        pdrb_total: amountPdrbTotal,
                                        pdb_sektor: amountPdbSektor,
                                        pdb_total: amountPdbTotal,
                                        nilai_lq: lqValue
                                },
                        })
        }
}
