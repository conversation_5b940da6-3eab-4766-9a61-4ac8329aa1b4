import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../../helpers/model_helper.js';

import prisma from '../../../lib/prisma.js';
import uploadFile from '../../../helpers/file_uploader.js';



export default class SUDSubSektorDaerahController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_sub_sektor_daerah: { column: 'id_sub_sektor_daerah', alias: 'id_sub_sektor_daerah', type: 'int' },
                        id_sektor_daerah: { column: 'id_sektor_daerah', alias: 'id_sektor_daerah', type: 'int' },
                        id_sub_sektor_nasional: { column: 'id_sub_sektor_nasional', alias: 'id_sub_sektor_nasional', type: 'int' },
                        deskripsi_singkat: { column: 'deskripsi_singkat', alias: 'deskripsi_singkat', type: 'string' },
                        deskripsi: { column: 'deskripsi', alias: 'deskripsi', type: 'string' },
                        status: { column: 'status', alias: 'status', type: 'int' }
                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                if (request.input('is_simple') || request.input('is_simple') == 'true') {
                        const params = request.qs();
                        let idSektorDaerah = params['id_sektor_daerah'];

                        const data = await prisma.tb_sub_sektor_daerah.findMany({
                                where: {
                                        id_sektor_daerah: parseInt(idSektorDaerah)
                                },
                                select: {
                                        id_sektor_daerah: true, 
                                        id_sub_sektor_daerah: true,
                                        tb_sub_sektor_nasional: {
                                                select: {
                                                        id_sub_sektor_nasional: true,
                                                        id_sub_sektor:true,
                                                        sub_sektor_ref: {
                                                                select: {
                                                                        nama: true,
                                                                },
                                                        }
                                                },
                                        },
                                },
                        });

                        return response.status(200).json({
                                success: true,
                                message: 'Success Retrieve Data',
                                data: data,
                        })
                }

                if (request.input("is_get_table_admin") || request.input("is_get_table_admin") == "true") {
                        const page = parseInt(request.input("page", "1")) || 1;
                        const perPage = parseInt(request.input("per_page", "10")) || 10;
                        const offset = (page - 1) * perPage;
                
                        const searchQuery = request.input("q")?.trim() || request.input("filter")?.trim();
                        let totalCount = 0;
                        let data = [];
                        let Wilayah = "";
                        const params = request.qs();
                        let IdAdmProvinsi = params['id_adm_provinsi'];
                        let IdAdmKabkot = params['id_adm_kabkot'];
                        let status = params['status'];
                        let pic = parseInt(params['pic']) || null;
                        try {
                                let order = params.order 
                                const by = ['asc','desc'].includes(params.by) ? params.by : 'asc'
                                let orderBy=`ORDER BY tssd.id_sub_sektor_daerah DESC`
                                const paramList = ['status','tahun']
                                if (order != undefined && paramList.includes(order)) {
                                        // if (order == 'tahun') {
                                        //         orderBy = `order by tsdl.tahun_pdrb ${by}`
                                        // }else 
                                        if (order == 'status') {
                                                orderBy = `order by tssd.status ${by}`
                                        }
                                }
                                if (searchQuery) {
                                        
                                        if (IdAdmKabkot) {
                                                Wilayah += ` and tak.id_adm_kabkot = ${IdAdmKabkot}`;
                                        }
                                        if (IdAdmProvinsi){
                                                        Wilayah += ` and (tap.id_adm_provinsi = ${IdAdmProvinsi} 
                                                                OR LEFT(tak.id_adm_kabkot::TEXT, 2) = LEFT(${IdAdmProvinsi}::TEXT, 2))`;
                                        }
                                        
                                        if (pic) {
                                                const picProv = await prisma.tb_user_internal_provinsi.findMany({
                                                        where:{
                                                        id_user:pic
                                                        }
                                                })
                                                if (picProv.length > 0) {
                                                        const provinsiIds = picProv.map((item) => item.id_adm_provinsi);
                                                        const provinsiIdsStr = provinsiIds.join(",");
                                                        const kabkotConditions = provinsiIds.map(id => `LEFT(tak.id_adm_kabkot::TEXT, 2) = LPAD(${id}::TEXT, 2, '0')`).join(' OR ');
                                                        Wilayah += ` where (tap.id_adm_provinsi IN (${provinsiIdsStr}) OR (${kabkotConditions}))`;
                                                }
                                        }
                                        if (status != undefined  && !isNaN(status)){
                                                Wilayah += ` AND tsd.status = ${status}` 
                                        }
                                        // Query untuk menghitung total data dengan filter `q`
                                        const totalCountResult = await prisma.$queryRawUnsafe(`
                                                SELECT COUNT(*) as count
                                                FROM tb_sub_sektor_daerah tssd
                                                LEFT JOIN tb_sektor_daerah tsd ON tsd.id_sektor_daerah = tssd.id_sektor_daerah
                                                LEFT JOIN tb_sektor_nasional tsn ON tsd.id_sektor_nasional = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sektor_nasional_ref tsnr ON tsnr.id_sektor = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sub_sektor_nasional tssn ON tssn.id_sub_sektor_nasional = tssd.id_sub_sektor_nasional
                                                LEFT JOIN tb_sub_sektor_nasional_ref tssnr ON tssnr.id_sub_sektor = tssn.id_sub_sektor
                                                LEFT JOIN tb_adm_kabkot tak ON tsd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tsd.id_adm_provinsi
                                                WHERE 
                                                        (LOWER(tsnr.nama) ILIKE $1 OR
                                                        LOWER(tssnr.nama) ILIKE $1 OR
                                                        LOWER(tap.nama) ILIKE $1 OR
                                                        LOWER(tak.nama) ILIKE $1 OR
                                                        tssd.id_sub_sektor_daerah::TEXT ILIKE $1)
                                                        ${Wilayah}
                                                        `, `%${searchQuery}% `);
                                                
                                        totalCount = Number(totalCountResult[0]?.count || 0);
                
                                        // Query data dengan filter `q`, limit, dan offset
                                        data = await prisma.$queryRawUnsafe(`
                                                SELECT 
                                                        tsnr.nama AS sektor,
                                                        tssnr.nama AS sub_sektor,
                                                        case 
                                                                when tap.nama is null then tap2.nama
                                                                else 
                                                                tap.nama 
                                                        end AS provinsi,
                                                        tak.nama AS kabkota,
                                                        tssd.*,
                                                        regexp_replace(tssd.deskripsi_singkat, '<[^>]*>', '', 'g') AS deskripsi_singkat,
                                                        regexp_replace(tssd.deskripsi, '<[^>]*>', '', 'g') AS deskripsi
                                                FROM tb_sub_sektor_daerah tssd
                                                LEFT JOIN tb_sektor_daerah tsd ON tsd.id_sektor_daerah = tssd.id_sektor_daerah
                                                LEFT JOIN tb_sektor_nasional tsn ON tsd.id_sektor_nasional = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sektor_nasional_ref tsnr ON tsnr.id_sektor = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sub_sektor_nasional tssn ON tssn.id_sub_sektor_nasional = tssd.id_sub_sektor_nasional
                                                LEFT JOIN tb_sub_sektor_nasional_ref tssnr ON tssnr.id_sub_sektor = tssn.id_sub_sektor
                                                LEFT JOIN tb_adm_kabkot tak ON tsd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tsd.id_adm_provinsi
                                                LEFT JOIN tb_adm_provinsi tap2 ON tap2.id_adm_provinsi = tak.id_adm_provinsi
                                                WHERE 
                                                        (LOWER(tsnr.nama) ILIKE $1 OR
                                                        LOWER(tssnr.nama) ILIKE $1 OR
                                                        LOWER(tap.nama) ILIKE $1 OR
                                                        LOWER(tak.nama) ILIKE $1 OR
                                                        tssd.id_sub_sektor_daerah::TEXT ILIKE $1)
                                                        ${Wilayah}
                                                        ${orderBy}                                               
                                                LIMIT $2 OFFSET $3
                                        `, `%${searchQuery}%`, perPage, offset);
                                } else {
                                        if (IdAdmKabkot) {
                                                Wilayah += ` where tak.id_adm_kabkot = ${IdAdmKabkot}`;
                                                if (status != undefined  && !isNaN(status)){
                                                        Wilayah += ` AND tsd.status = ${status}` 
                                                }
                                        }
                                        if (IdAdmProvinsi){
                                                Wilayah += ` where (tap.id_adm_provinsi = ${IdAdmProvinsi} 
                                                                      OR LEFT(tak.id_adm_kabkot::TEXT, 2) = LEFT(${IdAdmProvinsi}::TEXT, 2))`;
                                                if (status != undefined  && !isNaN(status)){
                                                        Wilayah += ` AND tsd.status = ${status}` 
                                                }
                                        }
                                       
                                        if (pic) {
                                                const picProv = await prisma.tb_user_internal_provinsi.findMany({
                                                        where:{
                                                        id_user:pic
                                                        }
                                                })
                                                if (picProv.length > 0) {
                                                        const provinsiIds = picProv.map((item) => item.id_adm_provinsi);
                                                        const provinsiIdsStr = provinsiIds.join(",");
                                                        const kabkotConditions = provinsiIds.map(id => `LEFT(tak.id_adm_kabkot::TEXT, 2) = LPAD(${id}::TEXT, 2, '0')`).join(' OR ');
                                                        Wilayah += ` where (tap.id_adm_provinsi IN (${provinsiIdsStr}) OR (${kabkotConditions}))`;
                                                }
                                        }
                                        if (status != undefined && Wilayah == ''){
                                                Wilayah += ` where tsd.status = ${status}` 
                                        }
                                        // Query untuk menghitung total data tanpa filter `q`
                                        const totalCountResult = await prisma.$queryRawUnsafe(`
                                                SELECT COUNT(*) as count
                                                FROM tb_sub_sektor_daerah tssd
                                                LEFT JOIN tb_sektor_daerah tsd ON tsd.id_sektor_daerah = tssd.id_sektor_daerah
                                                LEFT JOIN tb_sektor_nasional tsn ON tsd.id_sektor_nasional = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sektor_nasional_ref tsnr ON tsnr.id_sektor = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sub_sektor_nasional tssn ON tssn.id_sub_sektor_nasional = tssd.id_sub_sektor_nasional
                                                LEFT JOIN tb_sub_sektor_nasional_ref tssnr ON tssnr.id_sub_sektor = tssn.id_sub_sektor
                                                LEFT JOIN tb_adm_kabkot tak ON tsd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tsd.id_adm_provinsi
                                                ${Wilayah}
                                        `);
                
                                        totalCount = Number(totalCountResult[0]?.count || 0);

                                        // Query data tanpa filter `q`, limit, dan offset
                                        data = await prisma.$queryRawUnsafe(`
                                                SELECT 
                                                        tsnr.nama AS sektor,
                                                        tssnr.nama AS sub_sektor,
                                                        case 
                                                                when tap.nama  is null then tap2.nama
                                                                else 
                                                                tap.nama 
                                                        end AS provinsi,
                                                        tak.nama AS kabkota,
                                                        tssd.*,
                                                        regexp_replace(tssd.deskripsi_singkat, '<[^>]*>', '', 'g') AS deskripsi_singkat,
                                                        regexp_replace(tssd.deskripsi, '<[^>]*>', '', 'g') AS deskripsi
                                                FROM tb_sub_sektor_daerah tssd
                                                LEFT JOIN tb_sektor_daerah tsd ON tsd.id_sektor_daerah = tssd.id_sektor_daerah
                                                LEFT JOIN tb_sektor_nasional tsn ON tsd.id_sektor_nasional = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sektor_nasional_ref tsnr ON tsnr.id_sektor = tsn.id_sektor_nasional
                                                LEFT JOIN tb_sub_sektor_nasional tssn ON tssn.id_sub_sektor_nasional = tssd.id_sub_sektor_nasional
                                                LEFT JOIN tb_sub_sektor_nasional_ref tssnr ON tssnr.id_sub_sektor = tssn.id_sub_sektor
                                                LEFT JOIN tb_adm_kabkot tak ON tsd.id_adm_kabkot = tak.id_adm_kabkot
                                                LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tsd.id_adm_provinsi
                                                LEFT JOIN tb_adm_provinsi tap2 ON tap2.id_adm_provinsi = tak.id_adm_provinsi
                                                ${Wilayah}
                                                ${orderBy}
                                                LIMIT ${perPage} OFFSET ${offset}
                                        `);
                                }
                
                                // Format response
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: perPage,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / perPage),
                                        },
                                        data: data,
                                });
                        } catch (error) {
                                console.error(error);
                                return response.status(500).json({
                                        success: false,
                                        message: "An error occurred while fetching data",
                                        error: error.message,
                                });
                        }
                }

                let isAll = false;

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        orderBy: {
                                id_sub_sektor_daerah: 'desc', 
                        },
                        include: {
                                tb_komoditi_daerah: true,
                                tb_sektor_daerah: true,
                                tb_sub_sektor_nasional: true,
                                tb_peluang_daerah: true,
                        }
                }


                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                options['where'] = modelWhereAnd((await this.schemaData()).columns, queryParams);

                try {
                        const data = await prisma.tb_sub_sektor_daerah.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_sub_sektor_daerah.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }

        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = { 
                        include: {
                                tb_komoditi_daerah: true,
                                tb_sektor_daerah: true,
                                tb_sub_sektor_nasional: {
                                        include: {
                                                sub_sektor_daerah: {
                                                        where: {
                                                                id_sub_sektor_daerah: parseInt(params.id)
                                                        }
                                                },
                                        }
                                },
                                tb_peluang_daerah: true,
                                tb_sub_sektor_daerah_sumber_data: {
                                        include: {
                                                tb_sumber_data: {
                                                        include: {
                                                                tb_sumber_data_judul: true
                                                        }
                                                }
                                        }
                                },
                                tb_sub_sektor_daerah_tr: true,
                                tb_sub_sektor_daerah_value_detail: {
                                        // include: {
                                        //         sub_sektor_nasional_value: true
                                        // }
                                },
                                tb_sub_sektor_daerah_file: true
                        } 
                        };

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_sub_sektor_daerah': parseInt(params.id)
                }

                const data = await prisma.tb_sub_sektor_daerah.findFirst(options);

                data['tb_sub_sektor_daerah_file'] = data['tb_sub_sektor_daerah_file'].map((item) => {
                        return {
                                ...item, // Spread item untuk mempertahankan properti yang ada
                                path: `${process.env.APP_URL}/uploads/sektor//${item.nama}`,
                        };
                });

                return response.status(200).json({
                        success: true,
                        data: data
                });
        }



        public async createOrUpdate({ request, response, params }: HttpContext) {
                const paramID = parseInt(params.id);
                const model = "sud_sub_sektor_daerah";

                try {
                        const result = await prisma.$transaction(async (prisma) => {
                                let resID: number | undefined;

                                if (!paramID) {
                                        const checkUnique = await prisma.tb_sub_sektor_daerah.count({
                                                where: {
                                                        id_sektor_daerah: request.input("id_sektor_daerah") ? parseInt(request.input("id_sektor_daerah")) : 0,
                                                        id_sub_sektor_nasional: request.input("id_sub_sektor_nasional") ? parseInt(request.input("id_sub_sektor_nasional")) : 0,
                                                }
                                        })

                                        if (checkUnique > 0) {
                                                return {
                                                        status: 'error',
                                                        message: 'Data Duplikat! Sektor daerah dan Sub Sektor Nasional Sudah Pernah Dibuat Pada Daerah Yang Sama!',
                                                        error: null,
                                                };
                                        }
                                        console.log("Sync tb_sub_sektor_daerah id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_sub_sektor_daerah"', 'id_sub_sektor_daerah')),
                                                (SELECT (MAX("id_sub_sektor_daerah") + 1) FROM "tb_sub_sektor_daerah"),
                                                false) FROM "tb_sub_sektor_daerah";
                                         `);

                                        const res = await prisma.tb_sub_sektor_daerah.create({
                                                data: {
                                                        id_sektor_daerah: request.input("id_sektor_daerah") ? parseInt(request.input("id_sektor_daerah")) : 0,
                                                        id_sub_sektor_nasional: request.input("id_sub_sektor_nasional") ? parseInt(request.input("id_sub_sektor_nasional")) : 0,
                                                        deskripsi_singkat: request.input("deskripsi_singkat") || "",
                                                        deskripsi: request.input("deskripsi") || "",
                                                        status:  0,
                                                }
                                        });

                                        resID = res.id_sub_sektor_daerah;

                                        console.log("Sync tb_sub_sektor_daerah_sumber_data id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_sub_sektor_daerah_sumber_data"', 'id_sub_sektor_daerah_daerah_sumber_data')),
                                                (SELECT (MAX("id_sub_sektor_daerah_daerah_sumber_data") + 1) FROM "tb_sub_sektor_daerah_sumber_data"),
                                                false) FROM "tb_sub_sektor_daerah_sumber_data";
                                         `);

                                        if (parseInt(request.input("id_sumber_data")) || 0 > 0) {
                                                const data_sub_sektor_daerah_sumber_data: any = {
                                                        id_sub_sektor_daerah: res.id_sub_sektor_daerah,
                                                        id_sumber_data: request.input("id_sumber_data") ? parseInt(request.input("id_sumber_data")) : 0,
                                                }

                                                await prisma.tb_sub_sektor_daerah_sumber_data.create({ data: data_sub_sektor_daerah_sumber_data });
                                        }

                                        console.log("Sync tb_sub_sektor_daerah_status id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_sub_sektor_daerah_status"', 'id_sub_sektor_daerah_status')),
                                                (SELECT (MAX("id_sub_sektor_daerah_status") + 1) FROM "tb_sub_sektor_daerah_status"),
                                                false) FROM "tb_sub_sektor_daerah_status";
                                         `);

                                        const data_sub_sektor_daerah_status: any = {
                                                id_sub_sektor_daerah: res.id_sub_sektor_daerah,
                                                status: request.input("status") ? parseInt(request.input("status")) : 0,
                                                status_proses: request.input("status_proses") ? parseInt(request.input("status_proses")) : 0,
                                                keterangan: request.input("keterangan") || "",
                                                created_by: request.input("created_by") ? parseInt(request.input("created_by")) : 0,
                                                created_date: request.input("created_date") ? new Date(request.input("created_date")) : new Date(),
                                                updated_by: request.input("updated_by") ? parseInt(request.input("updated_by")) : 0,
                                                updated_date: request.input("updated_date") ? new Date(request.input("updated_date")) : new Date(),
                                        }

                                        console.log("Sync tb_sub_sektor_daerah_tr id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_sub_sektor_daerah_tr"', 'id_sub_sektor_daerah_tr')),
                                                (SELECT (MAX("id_sub_sektor_daerah_tr") + 1) FROM "tb_sub_sektor_daerah_tr"),
                                                false) FROM "tb_sub_sektor_daerah_tr";
                                         `);

                                        const data_sub_sektor_daerah_tr: any = {
                                                id_sub_sektor_daerah: res.id_sub_sektor_daerah,
                                                kd_bahasa: request.input("kd_bahasa") || 'en',
                                                deskripsi_singkat: request.input("deskripsi_singkat") || '',
                                                deskripsi: request.input("deskripsi") || ''
                                        }

                                        await prisma.tb_sub_sektor_daerah_status.create({ data: data_sub_sektor_daerah_status });
                                        await prisma.tb_sub_sektor_daerah_tr.create({ data: data_sub_sektor_daerah_tr });

                                        if (Array.isArray(request.input("parameter_data"))) {
                                                for (const item of request.input("parameter_data")) {
                                                        if (parseInt(item['id_sub_sektor_nasional_value'])) {
                                                                item['id_sub_sektor_daerah'] = res.id_sub_sektor_daerah;
                                                                item['id_sub_sektor_nasional_value'] = parseInt(item['id_sub_sektor_nasional_value']);
                                                                item['tahun'] = parseInt(item['tahun']);
                                                                item['numeric_value'] = parseFloat(item['numeric_value']);
                                                                await prisma.tb_sub_sektor_daerah_value_detail.create({
                                                                        data: item,
                                                                });
                                                        }
                                                }
                                        }

                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: res
                                        };
                                } else {
                                        const update = await prisma.tb_sub_sektor_daerah.update({
                                                where: { id_sub_sektor_daerah: paramID },
                                                data: {
                                                        id_sektor_daerah: request.input("id_sektor_daerah") ? parseInt(request.input("id_sektor_daerah")) : 0,
                                                        id_sub_sektor_nasional: request.input("id_sub_sektor_nasional") ? parseInt(request.input("id_sub_sektor_nasional")) : 0,
                                                        deskripsi_singkat: request.input("deskripsi_singkat") || "",
                                                        deskripsi: request.input("deskripsi") || "",
                                                        status:  0,
                                                }
                                        });

                                        const data_sub_sektor_daerah_sumber_data: any = {
                                                id_sub_sektor_daerah: update.id_sub_sektor_daerah,
                                                id_sumber_data: request.input("id_sumber_data") ? parseInt(request.input("id_sumber_data")) : 0,
                                        }

                                        const existing_data_sub_sektor_daerah_sumber_data = await prisma.tb_sub_sektor_daerah_sumber_data.findFirst({
                                                where: { id_sub_sektor_daerah: update.id_sub_sektor_daerah }
                                        });

                                        let res_data_sub_sektor_daerah_sumber_data;
                                        if (existing_data_sub_sektor_daerah_sumber_data) {
                                                res_data_sub_sektor_daerah_sumber_data = await prisma.tb_sub_sektor_daerah_sumber_data.update({
                                                        where: { id_sub_sektor_daerah_daerah_sumber_data: existing_data_sub_sektor_daerah_sumber_data.id_sub_sektor_daerah_daerah_sumber_data },
                                                        data: data_sub_sektor_daerah_sumber_data,
                                                });
                                        }

                                        const data_sub_sektor_daerah_status: any = {
                                                id_sub_sektor_daerah: update.id_sub_sektor_daerah,
                                                status: request.input("status") ? parseInt(request.input("status")) : 0,
                                                status_proses: request.input("status_proses") ? parseInt(request.input("status_proses")) : 0,
                                                keterangan: request.input("keterangan") || "",
                                                created_by: request.input("created_by") ? parseInt(request.input("created_by")) : 0,
                                                created_date: request.input("created_date") ? new Date(request.input("created_date")) : new Date(),
                                                updated_by: request.input("updated_by") ? parseInt(request.input("updated_by")) : 0,
                                                updated_date: request.input("updated_date") ? new Date(request.input("updated_date")) : new Date(),
                                        }

                                        const existing_data_sub_sektor_daerah_status = await prisma.tb_sub_sektor_daerah_status.findFirst({
                                                where: { id_sub_sektor_daerah: update.id_sub_sektor_daerah }
                                        });

                                        let res_data_sub_sektor_daerah_status: any | undefined;
                                        if (existing_data_sub_sektor_daerah_status) {
                                                res_data_sub_sektor_daerah_status = await prisma.tb_sub_sektor_daerah_status.update({
                                                        where: { id_sub_sektor_daerah_status: existing_data_sub_sektor_daerah_status.id_sub_sektor_daerah_status },
                                                        data: data_sub_sektor_daerah_status,
                                                });
                                        }

                                        await prisma.tb_sub_sektor_daerah_tr.deleteMany({
                                                where: {
                                                        id_sub_sektor_daerah: paramID
                                                }
                                        });

                                        console.log("Sync tb_sub_sektor_daerah_tr id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_sub_sektor_daerah_tr"', 'id_sub_sektor_daerah_tr')),
                                                (SELECT (MAX("id_sub_sektor_daerah_tr") + 1) FROM "tb_sub_sektor_daerah_tr"),
                                                false) FROM "tb_sub_sektor_daerah_tr";
                                         `);

                                        const data_sub_sektor_daerah_tr: any = {
                                                id_sub_sektor_daerah: paramID,
                                                kd_bahasa: request.input("kd_bahasa") || 'en',
                                                deskripsi_singkat: request.input("deskripsi_singkat") || '',
                                                deskripsi: request.input("deskripsi") || ''
                                        }
                                        await prisma.tb_sub_sektor_daerah_tr.create({ data: data_sub_sektor_daerah_tr });
                                        
                                        await prisma.tb_sub_sektor_daerah_value_detail.deleteMany({
                                                where: {
                                                        id_sub_sektor_daerah: paramID
                                                }
                                        });

                                        if (Array.isArray(request.input("parameter_data"))) {
                                                for (const item of request.input("parameter_data")) {
                                                        if (parseInt(item['id_sub_sektor_nasional_value'])) {
                                                                item['id_sub_sektor_daerah'] = paramID;
                                                                item['id_sub_sektor_nasional_value'] = parseInt(item['id_sub_sektor_nasional_value']);
                                                                item['tahun'] = parseInt(item['tahun']);
                                                                item['numeric_value'] = parseFloat(item['numeric_value']);
                                                                await prisma.tb_sub_sektor_daerah_value_detail.create({
                                                                        data: item,
                                                                });
                                                        }
                                                }
                                        }

                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update
                                        };
                                }
                        });

                        return response.json(result);
                } catch (error) {
                        console.log(error);
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error,
                        });
                }
        }

        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_sub_sektor_daerah.delete({
                                where: { id_sub_sektor_daerah: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

        public async toggleStatus({ request, params, response }: HttpContext) {
                const Id = parseInt(params.id, 10)
                const statusId = parseInt(request.input('status') ?? 0);

                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                if (isNaN(statusId)) {
                        return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                }

                try {
                        const toggleStatus = await prisma.tb_sub_sektor_daerah.update({
                                where: {
                                        id_sub_sektor_daerah: Id,
                                },
                                data: {
                                        status: request.input('status') ?? 0
                                },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Toggle Status',
                                data: toggleStatus,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error Toggle Status' })
                }
        }
}