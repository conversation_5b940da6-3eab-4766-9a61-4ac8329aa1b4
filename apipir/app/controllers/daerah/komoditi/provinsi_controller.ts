import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../../helpers/model_helper.js';

import prisma from '../../../lib/prisma.js';
// tb_komoditi_provinsi
export default class KomoditiProvinsiController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_komoditi_provinsi: { column: 'id_komoditi_provinsi', alias: 'id_komoditi_provinsi', type: 'int' },
                        id_adm_provinsi: { column: 'id_adm_provinsi', alias: 'id_adm_provinsi', type: 'int' },
                        id_komoditi: { column: 'id_komoditi', alias: 'id_komoditi', type: 'int' },
                        tahun: { column: 'tahun', alias: 'tahun', type: 'int' },
                        id_sumber_data: { column: 'id_sumber_data', alias: 'id_sumber_data', type: 'int' },
                        status: { column: 'status', alias: 'status', type: 'int' },
                        lon: { column: 'lon', alias: 'lon', type: 'float' },
                        lat: { column: 'lat', alias: 'lat', type: 'float' },
                        id_satuan: { column: 'id_satuan', alias: 'id_satuan', type: 'int' },
                        luas_lahan: { column: 'luas_lahan', alias: 'luas_lahan', type: 'float' },
                        nama: { column: 'nama', alias: 'nama', type: 'string' },
                        nilai_produksi: { column: 'nilai_produksi', alias: 'nilai_produksi', type: 'float' },
                        bijih_hipotik: { column: 'bijih_hipotik', alias: 'bijih_hipotik', type: 'float' },
                        logam_hipotik: { column: 'logam_hipotik', alias: 'logam_hipotik', type: 'float' },
                        bijih_tereka: { column: 'bijih_tereka', alias: 'bijih_tereka', type: 'float' },
                        logam_tereka: { column: 'logam_tereka', alias: 'logam_tereka', type: 'float' },
                        bijih_tertunjuk: { column: 'bijih_tertunjuk', alias: 'bijih_tertunjuk', type: 'float' },
                        logam_tertunjuk: { column: 'logam_tertunjuk', alias: 'logam_tertunjuk', type: 'float' },
                        bijih_terukur: { column: 'bijih_terukur', alias: 'bijih_terukur', type: 'float' },
                        logam_terukur: { column: 'logam_terukur', alias: 'logam_terukur', type: 'float' },
                        bijih_terkira: { column: 'bijih_terkira', alias: 'bijih_terkira', type: 'float' },
                        logam_terkira: { column: 'logam_terkira', alias: 'logam_terkira', type: 'float' },
                        bijih_terbukti: { column: 'bijih_terbukti', alias: 'bijih_terbukti', type: 'float' },
                        logam_terbukti: { column: 'logam_terbukti', alias: 'logam_terbukti', type: 'float' },
                        logam_status: { column: 'logam_status', alias: 'logam_status', type: 'string' },
                        non_logam_hipotik: { column: 'non_logam_hipotik', alias: 'non_logam_hipotik', type: 'float' },
                        non_logam_tereka: { column: 'non_logam_tereka', alias: 'non_logam_tereka', type: 'float' },
                        non_logam_tertunjuk: { column: 'non_logam_tertunjuk', alias: 'non_logam_tertunjuk', type: 'float' },
                        non_logam_terukur: { column: 'non_logam_terukur', alias: 'non_logam_terukur', type: 'float' },
                        non_logam_terkira: { column: 'non_logam_terkira', alias: 'non_logam_terkira', type: 'float' },
                        non_logam_terbukti: { column: 'non_logam_terbukti', alias: 'non_logam_terbukti', type: 'float' },
                        non_logam_status: { column: 'non_logam_status', alias: 'non_logam_status', type: 'string' },
                        panas_spekulasi: { column: 'panas_spekulasi', alias: 'panas_spekulasi', type: 'float' },
                        panas_hipotik: { column: 'panas_hipotik', alias: 'panas_hipotik', type: 'float' },
                        panas_terduga: { column: 'panas_terduga', alias: 'panas_terduga', type: 'float' },
                        panas_mungkin: { column: 'panas_mungkin', alias: 'panas_mungkin', type: 'float' },
                        panas_terbukti: { column: 'panas_terbukti', alias: 'panas_terbukti', type: 'float' },
                        panas_terpasang: { column: 'panas_terpasang', alias: 'panas_terpasang', type: 'float' },
                        panas_temperatur: { column: 'panas_temperatur', alias: 'panas_temperatur', type: 'float' },
                        panas_klasifikasi: { column: 'panas_klasifikasi', alias: 'panas_klasifikasi', type: 'string' }
                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                let isAll = false;
                let q = '';

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        orderBy?: { [key: string]: string };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                if (request.input('q')) {
                        q = request.input('q');
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let whereDefault = {}

                if (q) {
                        whereDefault = {
                                OR: [
                                    {
                                        tb_komoditi_nasional_ref: {
                                            nama: {
                                                contains: q,
                                                mode: 'insensitive', // Mengabaikan besar kecil huruf
                                            },
                                        },
                                    },
                                    {
                                        tb_komoditi_nasional_ref: {
                                            tb_sub_sektor_nasional_ref: {
                                                nama: {
                                                    contains: q,
                                                    mode: 'insensitive',
                                                },
                                            },
                                        },
                                    },
                                    {
                                        tb_komoditi_nasional_ref: {
                                            tb_sub_sektor_nasional_ref: {
                                                sektor_nasional_ref: {
                                                    nama: {
                                                        contains: q,
                                                        mode: 'insensitive',
                                                    },
                                                },
                                            },
                                        },
                                    },
                                    {
                                        tb_komoditi_satuan: {
                                            nama: {
                                                contains: q,
                                                mode: 'insensitive',
                                            },
                                        },
                                    },
                                    !isNaN(Number(q)) && {
                                        tahun: {
                                          equals: Number(q),
                                        },
                                      },
                                    ].filter(Boolean),
                            };
                }

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        orderBy: {
                            id_komoditi_provinsi: 'desc',
                        },
                        include: {
                            tb_komoditi_nasional_ref: {
                                include: {
                                    tb_sub_sektor_nasional_ref: {
                                        include: {
                                            sektor_nasional_ref: true,
                                        },
                                    },
                                },
                            },
                            tb_komoditi_satuan: true,
                            tb_adm_provinsi: true,
                        },
                        where: whereDefault,
                    };

                    let order = queryParams.order
                const by = ['asc','desc'].includes(queryParams.by) ? queryParams.by : 'asc'
                const paramList = ['status','tahun','provinsi']
                if (order != undefined && paramList.includes(order)) {
                        if (order == 'provinsi') {
                
                                options.orderBy = {tb_adm_provinsi:{nama:by}}
                        } else {
                
                                options.orderBy = {[order]:by}
                        }
                }

                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                let where = modelWhereAnd((await this.schemaData()).columns, queryParams);
                if (queryParams.pic) {
                        const picProv = await prisma.tb_user_internal_provinsi.findMany({
                                where:{
                                id_user:parseInt(queryParams.pic)
                                }
                                })
                                if (picProv.length > 0) {
                                const provinsiIds = picProv.map((item) => item.id_adm_provinsi);
                                whereDefault.id_adm_provinsi = {
                                        in: provinsiIds,
                                }
                                }
                }
                if (Object.keys(where).length !== 0) {
                        options['where'] = {
                            AND: [
                                whereDefault, // Kondisi pencarian `OR`
                                where,        // Kondisi tambahan dari `modelWhereAnd`
                            ],
                        };
                }
                try {
                        const data = await prisma.tb_komoditi_provinsi.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                // return response.status(404).json({
                                //         success: false,
                                //         message: "Data yang kamu cari tidak ada"
                                // });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_komoditi_provinsi.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        status_text,
                                }
                        })

                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }

        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {
                        include: {
                                tb_komoditi_nasional_ref:{
                                        include:{
                                                tb_sub_sektor_nasional_ref:{
                                                        include:{
                                                                sektor_nasional_ref:true
                                                        }
                                                }
                                        }
                                },
                                tb_komoditi_satuan: true,
                                tb_adm_provinsi: true,
                                tb_komoditi_layers : true
                        }
                        // include: {
                        //         tb_adm_kabkot: {
                        //                 select: {
                        //                         nama: true,
                        //                         // jumlah_penduduk: true,
                        //                 }
                        //         },
                        // }
                };

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_komoditi_provinsi': parseInt(params.id)
                }

                const data = await prisma.tb_komoditi_provinsi.findFirst(options);

                return response.status(200).json({
                        success: true,
                        data: data
                });
        }

        public async createOrUpdate({ request, response, params }: HttpContext) {
                let reqBody: any = request.all();
                const paramID = parseInt(reqBody.id_komoditi_provinsi || params.id)
                try {
                        const result = await prisma.$transaction(async (prisma) => {
                                if (paramID) {
                                        const update = await prisma.tb_komoditi_provinsi.update({
                                                where: { id_komoditi_provinsi: paramID },
                                                data: {
                                                        id_komoditi: request.input('id_komoditi') ? parseInt(request.input('id_komoditi')) : 0,
                                                        id_adm_provinsi: request.input('id_adm_provinsi') ? parseInt(request.input('id_adm_provinsi')) : 0,
                                                        tahun: request.input('tahun') ? parseInt(request.input('tahun')) : 0,
                                                        nilai_produksi: request.input('nilai_produksi') ? parseFloat(request.input('nilai_produksi')) : 0,
                                                        id_satuan: request.input('id_satuan') ? parseInt(request.input('id_satuan')) : 0,
                                                        luas_lahan: request.input('luas_lahan') ? parseFloat(request.input('luas_lahan')) : 0,
                                                        id_sumber_data: request.input('id_sumber_data') ? parseInt(request.input('id_sumber_data')) : 0,
                                                        lon: request.input('lon') ? parseFloat(request.input('lon')) : 0,
                                                        lat: request.input('lat') ? parseFloat(request.input('lat')) : 0,
                                                        status: request.input('status') ? parseInt(request.input('status')) : 0,
                                                }
                                        });

                                        // await prisma.tb_komoditi_layers.deleteMany({
                                        //         where: {
                                        //                 id_komoditi: paramID
                                        //         }
                                        // });

                                        await prisma.tb_komoditi_layers.update({
                                                where:{
                                                        id_komoditi_provinsi:paramID
                                                },
                                                data: {
                                                        layeruid: request.input('layeruid') ? request.input('layeruid') : '',
                                                        kategori: 1,
                                                        keterangan: request.input('keterangan') ? request.input('keterangan') : '',
                                                        is_active: true,
                                                }
                                        });

                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update,
                                        }
                                } else {

                                        // 1 provinsi
                                        // 2 kabkota
                                        // tb_komoditi_layers

                                        // console.log("create")
                                        console.log("Sync tb_komoditi_provinsi id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_komoditi_provinsi"', 'id_komoditi_provinsi')),
                                                        (SELECT (MAX("id_komoditi_provinsi") + 1) FROM "tb_komoditi_provinsi"),
                                                        false) FROM "tb_komoditi_provinsi";
                                                `);

                                        if (!reqBody['status']) {
                                                reqBody['status'] = 0;
                                        }


                                        const save = await prisma.tb_komoditi_provinsi.create({
                                                data: {
                                                        id_komoditi: request.input('id_komoditi') ? parseInt(request.input('id_komoditi')) : 0,
                                                        id_adm_provinsi: request.input('id_adm_provinsi') ? parseInt(request.input('id_adm_provinsi')) : 0,
                                                        tahun: request.input('tahun') ? parseInt(request.input('tahun')) : 0,
                                                        nilai_produksi: request.input('nilai_produksi') ? parseFloat(request.input('nilai_produksi')) : 0,
                                                        id_satuan: request.input('id_satuan') ? parseInt(request.input('id_satuan')) : 0,
                                                        luas_lahan: request.input('luas_lahan') ? parseFloat(request.input('luas_lahan')) : 0,
                                                        id_sumber_data: request.input('id_sumber_data') ? parseInt(request.input('id_sumber_data')) : 0,
                                                        lon: request.input('lon') ? parseFloat(request.input('lon')) : 0,
                                                        lat: request.input('lat') ? parseFloat(request.input('lat')) : 0,
                                                        status: request.input('status') ? parseInt(request.input('status')) : 0,
                                                }
                                        });

                                        // console.log("Sync tb_komoditi_layers id sequence",
                                        //         await prisma.$executeRaw`
                                        //         SELECT setval((
                                        //                 SELECT PG_GET_SERIAL_SEQUENCE('"tb_komoditi_layers"', 'id_kl')),
                                        //                 (SELECT (MAX("id_kl") + 1) FROM "tb_komoditi_layers"),
                                        //                 false) FROM "tb_komoditi_layers";
                                        //         `);

                                        await prisma.tb_komoditi_layers.create({
                                                data: {
                                                        id_komoditi_provinsi: save.id_komoditi_provinsi,
                                                        layeruid: request.input('layeruid') ? request.input('layeruid') : '',
                                                        kategori: 1,
                                                        keterangan: request.input('keterangan') ? request.input('keterangan') : '',
                                                        is_active: true,
                                                }
                                        });

                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: save,
                                        }
                                }
                        })

                        return response.json(result)

                } catch (error) {
                        // Handle error jika ada kesalahan dalam transaksi
                        console.log(error)
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error.message,
                        })
                }
        }

        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_komoditi_provinsi.delete({
                                where: { id_komoditi_provinsi: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

        public async toggleStatus({ request, params, response }: HttpContext) {
                const Id = parseInt(params.id, 10)
                const statusId = parseInt(request.input('status') ?? 0);

                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                if (isNaN(statusId)) {
                        return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                }

                try {
                        const toggleStatus = await prisma.tb_komoditi_provinsi.update({
                                where: {
                                        id_komoditi_provinsi: Id,
                                },
                                data: {
                                        status: request.input('status') ?? 0
                                },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Toggle Status',
                                data: toggleStatus,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error Toggle Status' })
                }
        }
}