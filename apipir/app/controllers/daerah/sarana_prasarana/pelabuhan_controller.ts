import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../../helpers/model_helper.js';

import prisma from '../../../lib/prisma.js';

export default class SarprasPelabuhanController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_pelabuhan: { column: 'id_pelabuhan', alias: 'id_pelabuhan', type: 'int' },
                        nama: { column: 'nama', alias: 'nama', type: 'string' },
                        keterangan: { column: 'keterangan', alias: 'keterangan', type: 'string' },
                        id_adm_kabkot: { column: 'id_adm_kabkot', alias: 'id_adm_kabkot', type: 'int' },
                        id_sumber_data: { column: 'id_sumber_data', alias: 'id_sumber_data', type: 'int' },
                        alamat: { column: 'alamat', alias: 'alamat', type: 'string' },
                        no_telp: { column: 'no_telp', alias: 'no_telp', type: 'string' },
                        no_fax: { column: 'no_fax', alias: 'no_fax', type: 'string' },
                        url_web: { column: 'url_web', alias: 'url_web', type: 'string' },
                        panjang_dermaga: { column: 'panjang_dermaga', alias: 'panjang_dermaga', type: 'float' },
                        kedalaman: { column: 'kedalaman', alias: 'kedalaman', type: 'float' },
                        id_fungsi: { column: 'id_fungsi', alias: 'id_fungsi', type: 'int' },
                        id_kelas: { column: 'id_kelas', alias: 'id_kelas', type: 'int' },
                        status: { column: 'status', alias: 'status', type: 'int' },
                        lon: { column: 'lon', alias: 'lon', type: 'float' },
                        lat: { column: 'lat', alias: 'lat', type: 'float' },
                        is_ikn: { column: 'is_ikn', alias: 'is_ikn', type: 'boolean' },
                        id_kategori_infrastruktur: { column: 'id_kategori_infrastruktur', alias: 'id_kategori_infrastruktur', type: 'int' },

                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                let isAll = false;
                let isSimple = false;
                let q = '';

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                if (request.input('is_simple') || request.input('is_simple') == 'true') {
                        isSimple = true
                }

                if (request.input('q')) {
                        q = request.input('q');
                }

                let whereDefault = {};

                if (q) {
                        whereDefault = {
                                OR: [
                                    {
                                        nama: {
                                                contains: q,
                                                mode: 'insensitive', // Mengabaikan besar kecil huruf
                                        },
                                    },
                                    {
                                        tb_adm_kabkot: {
                                            nama: {
                                                contains: q,
                                                mode: 'insensitive',
                                            },
                                        },
                                    },
                                    {
                                        tb_adm_kabkot: {
                                                tb_adm_provinsi:{

                                                        nama: {
                                                                contains: q,
                                                                mode: 'insensitive',
                                                        },
                                                }
                                        },
                                    },
                                    {
                                        tb_kategori_infrastruktur: {
                                            nama: {
                                                contains: q,
                                                mode: 'insensitive',
                                            },
                                        },
                                    },
                                    {
                                        tb_pelabuhan_fungsi: {
                                            nama: {
                                                contains: q,
                                                mode: 'insensitive',
                                            },
                                        },
                                    },
                                ],
                            };
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        orderBy: {
                                id_pelabuhan: 'desc',
                        },
                        include: {
                                tb_adm_kabkot: {
                                        include:{
                                                tb_adm_provinsi:true
                                        }
                                },
                                tb_sumber_data: true,
                                tb_kategori_infrastruktur: true,
                                tb_pelabuhan_fungsi: true
                        },
                        where: whereDefault,
                }


                if (isAll) {
                        delete options['skip'];
                        delete options['take'];

                        if (isSimple) {
                                delete options['include'];
                                options.select = {
                                        id_pelabuhan: true,
                                        nama: true
                                }
                        }
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                let where = modelWhereAnd((await this.schemaData()).columns, queryParams);
                
                const p = request.qs();
                let IdAdmProvinsi = p['id_adm_provinsi'];
                if (IdAdmProvinsi) {

                        where['id_adm_kabkot']  = {
                                gte: parseInt(`${IdAdmProvinsi}00`),
                                lt: parseInt(`${IdAdmProvinsi}99`)
                            }
                }
                if (Object.keys(where).length !== 0) {
                        options['where'] = {
                                AND: [
                                whereDefault,
                                where,
                                ],
                        };
                }
                const req = request.qs()
                let order = req.order
                const by = ['asc','desc'].includes(req.by) ? req.by : 'asc'
                const paramList = ['nama','status','provinsi','kabkot']
                if (order != undefined && paramList.includes(order)) {
                        if (order == 'provinsi') {
                                options.orderBy = {tb_adm_kabkot:{tb_adm_provinsi:{nama:by}}}
                        }else if (order == 'kabkot') {
                                options.orderBy = {tb_adm_kabkot:{nama:by}}
                        }else  {
                                options.orderBy = {[order]:by}
                        }
                }

                try {
                        const data = await prisma.tb_pelabuhan.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(200).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada",
                                        pagination: {
                                                page: 1,
                                                per_page: 10,
                                                total_count: 0,
                                                total_pages: 0
                                                },
                                                data: []
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_pelabuhan.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }
        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {
                        include: {
                                tb_adm_kabkot: true,
                                tb_sumber_data: true,
                        }
                };

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_pelabuhan': parseInt(params.id)
                }

                const data = await prisma.tb_pelabuhan.findFirst(options);

                return response.status(200).json({
                        success: true,
                        data: data
                });
        }

        public async createOrUpdate({ request, response, params,auth }: HttpContext) {
                const paramID = parseInt(params.id);


                try {
                        const result = await prisma.$transaction(async (prisma) => {

                                if (paramID) {
                                        // console.log(paramID);

                                        const update = await prisma.tb_pelabuhan.update({
                                                where: { id_pelabuhan: paramID },
                                                data: {
                                                        nama: request.input("nama") || "",
                                                        keterangan: request.input("keterangan") || "",
                                                        id_adm_kabkot: parseInt(request.input("id_adm_kabkot")) || 0,
                                                        id_sumber_data: parseInt(request.input("id_sumber_data")) || 0,
                                                        alamat: request.input("alamat") || "",
                                                        no_telp: request.input("no_telp") || "",
                                                        no_fax: request.input("no_fax") || "",
                                                        url_web: request.input("url_web") || "",
                                                        panjang_dermaga: parseFloat(request.input("panjang_dermaga")) || 0,
                                                        kedalaman: parseFloat(request.input("kedalaman")) || 0,
                                                        id_fungsi: parseInt(request.input("id_fungsi")) || 0,
                                                        id_kelas: parseInt(request.input("id_kelas")) || 0,
                                                        status: parseInt(request.input("status")) || 0,
                                                        lon: parseFloat(request.input("lon")) || 0,
                                                        lat: parseFloat(request.input("lat")) || 0,
                                                        is_ikn: request.input("is_ikn") === "true",
                                                        // id_kategori_infrastruktur: parseFloat(request.input("id_kategori_infrastruktur")) || 0,
                                                }
                                        });

                                        const data_pelabuhan_tr: any = {
                                                id_pelabuhan: update.id_pelabuhan,
                                                kd_bahasa: request.input("kd_bahasa") || "",
                                                nama: request.input("nama") || "",
                                                keterangan: request.input("keterangan") || "",
                                        }

                                        const existing_data_pelabuhan_tr = await prisma.tb_pelabuhan_tr.findFirst({
                                                where: { id_pelabuhan: update.id_pelabuhan }
                                        });

                                        let res_data_pelabuhan_tr: any
                                        if (existing_data_pelabuhan_tr) {
                                                res_data_pelabuhan_tr = await prisma.tb_pelabuhan_tr.update({
                                                        where: { id_pelabuhan_tr: existing_data_pelabuhan_tr.id_pelabuhan_tr },
                                                        data: data_pelabuhan_tr,
                                                });
                                        }

                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update,
                                                data_pelabuhan_tr: res_data_pelabuhan_tr,
                                        };

                                } else {
                                        console.log("Sync tb_pelabuhan id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_pelabuhan"', 'id_pelabuhan')),
                                                (SELECT (MAX("id_pelabuhan") + 1) FROM "tb_pelabuhan"),
                                                false) FROM "tb_pelabuhan";
                                         `);

                                        const res = await prisma.tb_pelabuhan.create({
                                                data: {
                                                        nama: request.input("nama") || "",
                                                        keterangan: request.input("keterangan") || "",
                                                        id_adm_kabkot: parseInt(request.input("id_adm_kabkot")) || 0,
                                                        id_sumber_data: parseInt(request.input("id_sumber_data")) || 0,
                                                        alamat: request.input("alamat") || "",
                                                        no_telp: request.input("no_telp") || "",
                                                        no_fax: request.input("no_fax") || "",
                                                        url_web: request.input("url_web") || "",
                                                        panjang_dermaga: parseFloat(request.input("panjang_dermaga")) || 0,
                                                        kedalaman: parseFloat(request.input("kedalaman")) || 0,
                                                        id_fungsi: parseInt(request.input("id_fungsi")) || 0,
                                                        id_kelas: parseInt(request.input("id_kelas")) || 0,
                                                        status: parseInt(request.input("status")) || 0,
                                                        lon: parseFloat(request.input("lon")) || 0,
                                                        lat: parseFloat(request.input("lat")) || 0,
                                                        is_ikn: request.input("is_ikn") === "true",
                                                        // id_kategori_infrastruktur: parseFloat(request.input("id_kategori_infrastruktur")) || 0,
                                                }
                                        });

                                        console.log("Sync tb_pelabuhan_tr id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_pelabuhan_tr"', 'id_pelabuhan_tr')),
                                                (SELECT (MAX("id_pelabuhan_tr") + 1) FROM "tb_pelabuhan_tr"),
                                                false) FROM "tb_pelabuhan_tr";
                                         `);


                                        const data_pelabuhan_tr: any = {
                                                id_pelabuhan: res.id_pelabuhan,
                                                kd_bahasa: request.input("kd_bahasa") || "",
                                                nama: request.input("nama") || "",
                                                keterangan: request.input("keterangan") || "",
                                        }

                                        await auth.check()
                                        await prisma.tb_pelabuhan_status.create({
                                                data: {
                                                id_pelabuhan: res.id_pelabuhan,
                                                status: 0,
                                                status_proses: 0,
                                                keterangan: 'Dokumen baru',
                                                created_by:auth.user?.id,
                                                updated_by:auth.user?.id,
                                                created_date:new Date()
                                                },
                                        });

                                        const res_data_pelabuhan_tr = await prisma.tb_pelabuhan_tr.create({ data: data_pelabuhan_tr });

                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: res,
                                                data_pelabuhan_tr: res_data_pelabuhan_tr,
                                        };
                                }
                        });

                        return response.json(result);
                } catch (error) {
                        console.log(error);
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error,
                        });
                }
        }

        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_pelabuhan.delete({
                                where: { id_pelabuhan: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

        public async toggleStatus({ request, params, response,auth }: HttpContext) {
                const Id = parseInt(params.id, 10)
                const statusId = parseInt(request.input('status') ?? 0);

                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                if (isNaN(statusId)) {
                        return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                }

                try {
                        const toggleStatus = await prisma.tb_pelabuhan.update({
                                where: {
                                        id_pelabuhan: Id,
                                },
                                data: {
                                        status: parseInt(request.input('status')) ?? 0
                                },
                        })

                        await auth.check()
                        const insert = await prisma.tb_pelabuhan_status.create({
                                data: {
                                    id_pelabuhan: Id,
                                    status: parseInt(request.input('status')),
                                    status_proses: parseInt(request.input('status')),
                                    keterangan: request.input('keterangan'),
                                    created_by:auth.user?.id,
                                    updated_by:auth.user?.id,
                                    created_date:new Date()
                                },
                            });



                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Toggle Status',
                                data: toggleStatus,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error Toggle Status' })
                }
        }
}