import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../../helpers/model_helper.js';

import prisma from '../../../lib/prisma.js';

export default class SarprasBandaraController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_bandara: { column: 'id_bandara', alias: 'id_bandara', type: 'int' },
                        id_adm_kabkot: { column: 'id_adm_kabkot', alias: 'id_adm_kabkot', type: 'int' },
                        id_sumber_data: { column: 'id_sumber_data', alias: 'id_sumber_data', type: 'int' },
                        id_kelas: { column: 'id_kelas', alias: 'id_kelas', type: 'int' },
                        id_kategori: { column: 'id_kategori', alias: 'id_kategori', type: 'int' },
                        nama: { column: 'nama', alias: 'nama', type: 'string' },
                        keterangan: { column: 'keterangan', alias: 'keterangan', type: 'string' },
                        jarak_ibu_kota_provinsi: { column: 'jarak_ibu_kota_provinsi', alias: 'jarak_ibu_kota_provinsi', type: 'float' },
                        iata: { column: 'iata', alias: 'iata', type: 'string' },
                        alamat: { column: 'alamat', alias: 'alamat', type: 'string' },
                        no_telp: { column: 'no_telp', alias: 'no_telp', type: 'string' },
                        no_fax: { column: 'no_fax', alias: 'no_fax', type: 'string' },
                        url_web: { column: 'url_web', alias: 'url_web', type: 'string' },
                        jam_operasional_awal: { column: 'jam_operasional_awal', alias: 'jam_operasional_awal', type: 'datetime' },
                        jam_operasional_akhir: { column: 'jam_operasional_akhir', alias: 'jam_operasional_akhir', type: 'datetime' },
                        id_zona_waktu: { column: 'id_zona_waktu', alias: 'id_zona_waktu', type: 'int' },
                        jenis_pesawat: { column: 'jenis_pesawat', alias: 'jenis_pesawat', type: 'string' },
                        maskapai: { column: 'maskapai', alias: 'maskapai', type: 'string' },
                        lon: { column: 'lon', alias: 'lon', type: 'float' },
                        lat: { column: 'lat', alias: 'lat', type: 'float' },
                        status: { column: 'status', alias: 'status', type: 'int' },
                        is_ikn: { column: 'is_ikn', alias: 'is_ikn', type: 'boolean' },
                        id_kategori_infrastruktur: { column: 'id_kategori_infrastruktur', alias: 'id_kategori_infrastruktur', type: 'int' },
                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                let isAll = false;
                let isSimple = false;
                let q = '';

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        orderBy?: { [key: string]: string };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                if (request.input('is_simple') || request.input('is_simple') == 'true') {
                        isSimple = true
                }

                if (request.input('q')) {
                        q = request.input('q');
                }

                let whereDefault = {};

                if (q) {
                        whereDefault = {
                                OR: [
                                    {
                                        nama: {
                                                contains: q,
                                                mode: 'insensitive', // Mengabaikan besar kecil huruf
                                        },
                                    },
                                    {
                                        tb_adm_kabkot: {
                                            nama: {
                                                contains: q,
                                                mode: 'insensitive',
                                            },
                                        },
                                    },
                                    {
                                        tb_adm_kabkot: {
                                                tb_adm_provinsi:{

                                                        nama: {
                                                                contains: q,
                                                                mode: 'insensitive',
                                                        },
                                                }
                                        },
                                    },
                                    {
                                        tb_bandara_kelas: {
                                            nama: {
                                                contains: q,
                                                mode: 'insensitive',
                                            },
                                        },
                                    },
                                    {
                                        tb_bandara_kategori: {
                                            nama: {
                                                contains: q,
                                                mode: 'insensitive',
                                            },
                                        },
                                    },
                                ],
                            };
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        orderBy: {
                                id_bandara: 'desc',
                        },
                        include: {
                                tb_adm_kabkot: {
                                        include:{
                                                tb_adm_provinsi:true
                                        }
                                },
                                tb_sumber_data: true,
                                tb_bandara_kelas: true,
                                tb_bandara_kategori: true
                        },
                        where: whereDefault,
                }




                if (isAll) {
                        delete options['skip'];
                        delete options['take'];

                        if (isSimple) {
                                delete options['include'];
                                options.select = {
                                        id_bandara: true,
                                        nama: true
                                }
                        }

                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                let where = modelWhereAnd((await this.schemaData()).columns, queryParams);
                if (queryParams.pic) {
                        const picProv = await prisma.tb_user_internal_provinsi.findMany({
                                where:{
                                id_user:parseInt(queryParams.pic)
                                }
                                })
                                if (picProv.length > 0) {
                                const kabkotRanges = picProv.map(provId => ({
                                        id_adm_kabkot: {
                                                gte: parseInt(`${provId?.id_adm_provinsi}00`),
                                                lt: parseInt(`${provId?.id_adm_provinsi}99`)
                                        }
                                        }));
                                        (whereDefault.AND ??= []).push({
                                        OR: [
                                                ...kabkotRanges
                                        ]
                                        });
                                }
                }
                const p = request.qs();
                let IdAdmProvinsi = p['id_adm_provinsi'];
                if (IdAdmProvinsi) {

                        where['id_adm_kabkot']  = {
                                gte: parseInt(`${IdAdmProvinsi}00`),
                                lt: parseInt(`${IdAdmProvinsi}99`)
                            }
                }
                // return IdAdmProvinsi
                if (Object.keys(where).length !== 0 ) {
                        options['where'] = {
                                AND: [
                                        whereDefault,
                                        where,
                                ],
                        };
                }
                const req = request.qs()
                let order = req.order
                const by = ['asc','desc'].includes(req.by) ? req.by : 'asc'
                const paramList = ['nama','status','provinsi','kabkot']
                if (order != undefined && paramList.includes(order)) {
                        if (order == 'provinsi') {
                                options.orderBy = {tb_adm_kabkot:{tb_adm_provinsi:{nama:by}}}
                        }else if (order == 'kabkot') {
                                options.orderBy = {tb_adm_kabkot:{nama:by}}
                        }else  {
                                options.orderBy = {[order]:by}
                        }
                }
                // return options

                try {
                        const data = await prisma.tb_bandara.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(200).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada",
                                        pagination: {
                                        page: 1,
                                        per_page: 10,
                                        total_count: 0,
                                        total_pages: 0
                                        },
                                        data: []
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_bandara.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }

        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {
                        include: {
                                tb_adm_kabkot: true,
                                tb_sumber_data: true,
                                tb_bandara_kelas: true,
                                tb_bandara_kategori: true
                        }
                };

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_bandara': parseInt(params.id)
                }

                const data = await prisma.tb_bandara.findFirst(options);

                return response.status(200).json({
                        success: true,
                        data: data
                });
        }

        public async createOrUpdate({ request, response, params,auth }: HttpContext) {
                const paramID = parseInt(params.id);

                try {
                        const result = await prisma.$transaction(async (prisma) => {
                                if (paramID) {
                                        // console.log(paramID);

                                        const update = await prisma.tb_bandara.update({
                                                where: { id_bandara: paramID },
                                                data: {
                                                        id_adm_kabkot: parseInt(request.input("id_adm_kabkot")) || 0,
                                                        id_sumber_data: parseInt(request.input("id_sumber_data")) || 0,
                                                        id_kelas: parseInt(request.input("id_kelas")) || 0,
                                                        id_kategori: parseInt(request.input("id_kategori")) || 0,
                                                        nama: request.input("nama") || "",
                                                        keterangan: request.input("keterangan") || "",
                                                        jarak_ibu_kota_provinsi: parseFloat(request.input("jarak_ibu_kota_provinsi")) || 0,
                                                        iata: request.input("iata") || "",
                                                        alamat: request.input("alamat") || "",
                                                        no_telp: request.input("no_telp") || "",
                                                        no_fax: request.input("no_fax") || "",
                                                        url_web: request.input("url_web") || "",
                                                        jam_operasional_awal: new Date(request.input("jam_operasional_awal")) || null,
                                                        jam_operasional_akhir: new Date(request.input("jam_operasional_akhir")) || null,
                                                        id_zona_waktu: parseInt(request.input("id_zona_waktu")) || 0,
                                                        jenis_pesawat: request.input("jenis_pesawat") || "",
                                                        maskapai: request.input("maskapai") || "",
                                                        lon: parseFloat(request.input("lon")) || 0,
                                                        lat: parseFloat(request.input("lat")) || 0,
                                                        status: parseInt(request.input("status")) || 0,
                                                        is_ikn: request.input("is_ikn") === "true",
                                                        // id_kategori_infrastruktur: parseInt(request.input("id_kategori_infrastruktur")) || 0,
                                                }
                                        });

                                        const data_bandara_tr: any = {
                                                id_bandara: update.id_bandara,
                                                kd_bahasa: request.input("kd_bahasa") || "",
                                                nama: request.input("nama") || "",
                                                keterangan: request.input("keterangan") || "",
                                        }

                                        const existing_data_bandara_tr = await prisma.tb_bandara_tr.findFirst({
                                                where: { id_bandara: update.id_bandara }
                                        });

                                        let res_data_bandara_tr: any
                                        if (existing_data_bandara_tr) {
                                                res_data_bandara_tr = await prisma.tb_bandara_tr.update({
                                                        where: { id_bandara_tr: existing_data_bandara_tr.id_bandara_tr },
                                                        data: data_bandara_tr,
                                                });
                                        }

                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update,
                                                data_bandara_tr: res_data_bandara_tr,
                                        };

                                } else {
                                        console.log("Sync tb_bandara id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_bandara"', 'id_bandara')),
                                                (SELECT (MAX("id_bandara") + 1) FROM "tb_bandara"),
                                                false) FROM "tb_bandara";
                                         `);

                                        const res = await prisma.tb_bandara.create({
                                                data: {
                                                        id_adm_kabkot: parseInt(request.input("id_adm_kabkot")) || 0,
                                                        id_sumber_data: parseInt(request.input("id_sumber_data")) || 0,
                                                        id_kelas: parseInt(request.input("id_kelas")) || 0,
                                                        id_kategori: parseInt(request.input("id_kategori")) || 0,
                                                        nama: request.input("nama") || "",
                                                        keterangan: request.input("keterangan") || "",
                                                        jarak_ibu_kota_provinsi: parseFloat(request.input("jarak_ibu_kota_provinsi")) || 0,
                                                        iata: request.input("iata") || "",
                                                        alamat: request.input("alamat") || "",
                                                        no_telp: request.input("no_telp") || "",
                                                        no_fax: request.input("no_fax") || "",
                                                        url_web: request.input("url_web") || "",
                                                        jam_operasional_awal: new Date(request.input("jam_operasional_awal")) || null,
                                                        jam_operasional_akhir: new Date(request.input("jam_operasional_akhir")) || null,
                                                        id_zona_waktu: parseInt(request.input("id_zona_waktu")) || 0,
                                                        jenis_pesawat: request.input("jenis_pesawat") || "",
                                                        maskapai: request.input("maskapai") || "",
                                                        lon: parseFloat(request.input("lon")) || 0,
                                                        lat: parseFloat(request.input("lat")) || 0,
                                                        status: parseInt(request.input("status")) || 0,
                                                        is_ikn: request.input("is_ikn") === "true",
                                                        // id_kategori_infrastruktur: parseInt(request.input("id_kategori_infrastruktur")) || 0,

                                                }
                                        });

                                        console.log("Sync tb_bandara_tr id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_bandara_tr"', 'id_bandara_tr')),
                                                (SELECT (MAX("id_bandara_tr") + 1) FROM "tb_bandara_tr"),
                                                false) FROM "tb_bandara_tr";
                                         `);


                                        const data_bandara_tr: any = {
                                                id_bandara: res.id_bandara,
                                                kd_bahasa: request.input("kd_bahasa") || "",
                                                nama: request.input("nama") || "",
                                                keterangan: request.input("keterangan") || "",
                                        }

                                        const res_data_bandara_tr = await prisma.tb_bandara_tr.create({ data: data_bandara_tr });

                                        await auth.check()
                                        await prisma.tb_bandara_status.create({
                                                data: {
                                                id_bandara: res.id_bandara,
                                                status: 0,
                                                status_proses: 0,
                                                keterangan: 'Dokumen baru',
                                                created_by:auth.user?.id,
                                                updated_by:auth.user?.id,
                                                created_date:new Date()
                                                },
                                        });

                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: res,
                                                data_bandara_tr: res_data_bandara_tr,
                                        };
                                }
                        });

                        return response.json(result);
                } catch (error) {
                        console.log(error);
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error,
                        });
                }
        }

        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_bandara.delete({
                                where: { id_bandara: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

        public async toggleStatus({ request, params, response ,auth}: HttpContext) {
                const Id = parseInt(params.id, 10)
                const statusId = parseInt(request.input('status') ?? 0);

                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                if (isNaN(statusId)) {
                        return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                }

                try {
                        const toggleStatus = await prisma.tb_bandara.update({
                                where: {
                                        id_bandara: Id,
                                },
                                data: {
                                        status: parseInt(request.input('status')) ?? 0
                                },
                        })

                        await auth.check()
                        const insert = await prisma.tb_bandara_status.create({
                                data: {
                                    id_bandara: Id,
                                    status: parseInt(request.input('status')),
                                    status_proses: parseInt(request.input('status')),
                                    keterangan: request.input('keterangan'),
                                    created_by:auth.user?.id,
                                    updated_by:auth.user?.id,
                                    created_date:new Date()
                                },
                            });



                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Toggle Status',
                                data: toggleStatus,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error Toggle Status' ,message : error.message})
                }
        }
}