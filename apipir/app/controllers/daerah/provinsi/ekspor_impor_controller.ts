import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../../helpers/model_helper.js';

import TranslateText from '../../../helpers/translator.js';
import { equal } from 'node:assert';

import prisma from '../../../lib/prisma.js'

export default class ProvinsiEksporImporController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_ekspor_provinsi: { column: 'id_ekspor_provinsi', alias: 'id_ekspor_provinsi', type: 'int' },
                        id_adm_provinsi: { column: 'id_adm_provinsi', alias: 'id_adm_provinsi', type: 'int' },
                        id_sumber_data: { column: 'id_sumber_data', alias: 'id_sumber_data', type: 'int' },
                        tahun: { column: 'tahun', alias: 'tahun', type: 'int' },
                        keterangan: { column: 'keterangan', alias: 'keterangan', type: 'string' },
                        nilai_ekspor: { column: 'nilai_ekspor', alias: 'nilai_ekspor', type: 'int' },
                        status: { column: 'status', alias: 'status', type: 'int' },
                        nilai_impor: { column: 'nilai_impor', alias: 'nilai_impor', type: 'int' }
                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                let isAll = false;
                let q = '';

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        orderBy?: { [key: string]: string };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                if (request.input('q')) {
                        q = request.input('q');
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let whereDefault = {}

                if (q) {
                        whereDefault = {
                                OR: [
                                        {
                                                tb_adm_provinsi: {
                                                    nama: {
                                                        contains: q,
                                                        mode: 'insensitive', // Mengabaikan besar kecil huruf
                                                    },
                                                },
                                        },
                                        {
                                                tahun: {
                                                        equals: isNaN(q) ? undefined : parseInt(q),
                                                },
                                         },
                                ],
                            };
                }

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                        // where: {
                        //         tb_adm_provinsi: {
                        //                 nama: {
                        //                         equals: queryParams
                        //                 },
                        //         }
                        // },
                        include: {
                                 tb_ekspor_provinsi_tr: true ,
                                 tb_adm_provinsi: true
                        },
                        where: whereDefault,
                }

                let order = queryParams.order
                const by = ['asc','desc'].includes(queryParams.by) ? queryParams.by : 'asc'
                const paramList = ['status','tahun']
                if (order != undefined && paramList.includes(order)) {
                        options.orderBy = {[order]:by}
                }

                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                let where = modelWhereAnd((await this.schemaData()).columns, queryParams);
                if (queryParams.pic) {
                        const picProv = await prisma.tb_user_internal_provinsi.findMany({
                                where:{
                                id_user:parseInt(queryParams.pic)
                                }
                              })
                              if (picProv.length > 0) {
                                const provinsiIds = picProv.map((item) => item.id_adm_provinsi);
                                whereDefault.id_adm_provinsi = {
                                        in: provinsiIds,
                                }
                              }
                }
                if (Object.keys(where).length !== 0) {
                        options['where'] = {
                            AND: [
                                whereDefault, // Kondisi pencarian `OR`
                                where,        // Kondisi tambahan dari `modelWhereAnd`
                            ],
                        };
                }

                try {
                        const data = await prisma.tb_ekspor_provinsi.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                // return response.status(404).json({
                                //         success: false,
                                //         message: "Data yang kamu cari tidak ada"
                                // });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_ekspor_provinsi.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        const dataMapped = data.map((item) => {
                                const status_text = item.status == 99 ? 'Approve' : 'Delete'
                                return {
                                        ...item,
                                        status_text,
                                }
                        })


                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: dataMapped,
                                });
                        }


                        console.log(params);
                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: dataMapped,
                        });

                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }

        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = { include: { tb_ekspor_provinsi_tr: true } };

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_ekspor_provinsi': parseInt(params.id)
                }

                const data = await prisma.tb_ekspor_provinsi.findFirst(options);

                return response.status(200).json({
                        success: true,
                        data: data
                });
        }

        public async createOrUpdate({ request, response, params }: HttpContext) {
                let reqBody: any = request.all();
                const paramID = parseInt(params.id)
                try {

                        const result = await prisma.$transaction(async (prisma) => {
                                if (params.id) {
                                        // Jika `id` ada, lakukan operasi update
                                        const update = await prisma.tb_ekspor_provinsi.update({
                                                where: { id_ekspor_provinsi: paramID },
                                                data: reqBody,
                                        })
                                        // Prepare the data for `tb_ekspor_provinsi_tr`
                                        const dataTr = {
                                                id_ekspor_provinsi: update.id_ekspor_provinsi,
                                                kd_bahasa: "en",
                                                keterangan: update.keterangan, // You can modify this if you need translation
                                        };

                                        // Check if a translation record exists
                                        const existingTranslation = await prisma.tb_ekspor_provinsi_tr.findFirst({
                                                where: {
                                                        id_ekspor_provinsi: paramID,
                                                        kd_bahasa: "en",
                                                },
                                        });

                                        let createOrUpdateTr;
                                        if (existingTranslation) {
                                                // If the translation exists, update it
                                                createOrUpdateTr = await prisma.tb_ekspor_provinsi_tr.update({
                                                        where: { id_ekspor_provinsi_tr: existingTranslation.id_ekspor_provinsi_tr },
                                                        data: dataTr,
                                                });
                                        } else {
                                                // If no translation record exists, create a new one
                                                createOrUpdateTr = await prisma.tb_ekspor_provinsi_tr.create({
                                                        data: dataTr,
                                                });
                                        }
                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update,
                                                data_translated: createOrUpdateTr,
                                        }
                                } else {
                                        // console.log("create")
                                        console.log("Sync tb_ekspor_provinsi id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_ekspor_provinsi"', 'id_ekspor_provinsi')),
                                                        (SELECT (MAX("id_ekspor_provinsi") + 1) FROM "tb_ekspor_provinsi"),
                                                        false) FROM "tb_ekspor_provinsi";
                                                `);
                                        const save = await prisma.tb_ekspor_provinsi.create({ data: reqBody })
                                        // const translatedRes = await TranslateText(save.keterangan, 'en');

                                        const dataTr = {
                                                id_ekspor_provinsi: save.id_ekspor_provinsi,
                                                kd_bahasa: "en",
                                                keterangan: save.keterangan
                                        }

                                        const createTr = await prisma.tb_ekspor_provinsi_tr.create({ data: dataTr })
                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: save,
                                                data_translated: createTr
                                        }
                                }
                        })

                        return response.json(result)

                } catch (error) {
                        // Handle error jika ada kesalahan dalam transaksi
                        console.log(error)
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error,
                        })
                }
        }

        public async deleteById({ params, response }: HttpContext) {

                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_ekspor_provinsi.delete({
                                where: { id_ekspor_provinsi: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

        public async toggleStatus({ request, params, response,auth }: HttpContext) {
                const Id = parseInt(params.id, 10)
                const statusId = parseInt(request.input('status') ?? 0);

                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                if (isNaN(statusId)) {
                        return response.status(400).send({ error: 'Pastikan Status ID adalah angka' })
                }

                try {
                        const toggleStatus = await prisma.tb_ekspor_provinsi.update({
                                where: {
                                        id_ekspor_provinsi: Id,
                                },
                                data: {
                                        status: request.input('status') ?? 0
                                },
                        })
                        await auth.check()
                        const insert = await prisma.tb_ekspor_provinsi_status.create({
                                data: {
                                    id_ekspor_provinsi: Id,
                                    status: parseInt(request.input('status')),
                                    status_proses: parseInt(request.input('status')),
                                    keterangan: request.input('keterangan'),
                                    created_by:auth.user?.id,
                                    updated_by:auth.user?.id,
                                    created_date:new Date()
                                },
                            });
                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Toggle Status',
                                data: toggleStatus,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error Toggle Status' })
                }
        }
}