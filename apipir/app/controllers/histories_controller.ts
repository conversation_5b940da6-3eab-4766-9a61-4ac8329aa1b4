// import type { HttpContext } from '@adonisjs/core/http'
import type { HttpContext } from '@adonisjs/core/http'
import env from '#start/env'
import { PrismaClient } from '@prisma/client'
const prisma = new PrismaClient()


export default class HistoriesController {
    
    public async get_hitory({params,response}:HttpContext) {
        const datas = await prisma.tb_halaman_pengunjung.findMany({
            where:{
                total_pengunjung:{
                    not:0
                }
            },
            orderBy:{
                id_halaman_pengunjung:'asc'
            }
        })

        const result= datas.map((data) => {
            return {
                id_halaman_pengunjung:data.id_halaman_pengunjung,
                nama:data.nama,
                jumlah:data.total_pengunjung
            }
        })

        const groupedData = {};

        // Iterate through the datas array
        // for (const entry of datas) {
        // const idHalamanPengunjung = entry.id_halaman_pengunjung;
        // const nama = entry.halaman_pengunjung.nama;

        // if (groupedData[idHalamanPengunjung]) {
        //     groupedData[idHalamanPengunjung].jumlah += 1;
        // } else {
        //     groupedData[idHalamanPengunjung] = {
        //     nama: nama,
        //     jumlah: 1
        //     };
        // }
        // }

        // Convert the dictionary to a list of dictionaries
        // const result = Object.keys(groupedData).map(key => ({
        // id_halaman_pengunjung: parseInt(key),
        // ...groupedData[key]
        // }));

    return response.json({success:true,data:result});
    }

    public async get_home_dt({request,params,response}:HttpContext) {
        const page = parseInt(params.page) || 1;
        const pageSize = parseInt(params.pageSize) || 10;
        const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided
        const { 
            startDate :startDateRaw, 
            endDate :endDateRaw 
          } = request.qs();
       
          const now = new Date();
          now.setDate(now.getDate() + 1)
          const defaultStartDate = new Date(2020, 0, 1);
          const defaultEndDate = now;
          
          const startDate = startDateRaw || defaultStartDate.toISOString().split('T')[0];
          const endDate = endDateRaw || defaultEndDate.toISOString().split('T')[0];
        const count = await prisma.$queryRawUnsafe(`SELECT 
                count(*) as jml
                FROM 
                tb_halaman_pengunjung_det                   
                WHERE 
                    id_halaman_pengunjung = 1
                    and created_date BETWEEN $2::date AND $3::date
                AND (
                    ip_pengunjung ILIKE '%' || $1 || '%'
                )`,search,startDate, endDate)
                const totalRecords = Number(count[0].jml)
        const totalPage = Math.ceil(Number(totalRecords / pageSize)); 

        const limit = pageSize; // Number of records to retrieve
        const offset = (page - 1) * pageSize; // Number of records to skip
        
        const data = await prisma.$queryRawUnsafe(`
            SELECT 
                ip_pengunjung,                   
                asal_negara,     
                TRIM(TO_CHAR(created_date, 'FMDD FMMonth YYYY')) AS created_date
            FROM 
                tb_halaman_pengunjung_det   
                WHERE 
                    id_halaman_pengunjung = 1
                    and created_date BETWEEN $4::date AND $5::date
                AND (
                    ip_pengunjung::text LIKE '%' || $3 || '%'
                )
            ORDER BY 
               id_halaman_pengunjung_det desc
            LIMIT $1 OFFSET $2;
        `, limit, offset, search,startDate, endDate);
        
        

        return {
            success: true,
            totalRecords: totalRecords, // Total number of records in the database
            totalPage: totalPage, // Total number of pages
            page: page,
            pageSize: pageSize,
            data: data, // Data for the current page
        };

    }
    public async get_peluang_dt({request,params,response}:HttpContext) {
        const page = parseInt(params.page) || 1;
        const pageSize = parseInt(params.pageSize) || 10;
        const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided
        const { 
            startDate :startDateRaw, 
            endDate :endDateRaw 
          } = request.qs();
       
          const now = new Date();
          now.setDate(now.getDate() + 1)
          const defaultStartDate = new Date(2020, 0, 1);
          const defaultEndDate = now;
          
          const startDate = startDateRaw || defaultStartDate.toISOString().split('T')[0];
          const endDate = endDateRaw || defaultEndDate.toISOString().split('T')[0];
        const count = await prisma.$queryRawUnsafe(`SELECT 
                count(*) as jml
                FROM 
                tb_halaman_pengunjung_det                   
                WHERE 
                    id_halaman_pengunjung = 6
                    and created_date BETWEEN $2::date AND $3::date                    
                AND (
                    ip_pengunjung ILIKE '%' || $1 || '%'
                )`,search,startDate, endDate)
                const totalRecords = Number(count[0].jml)
        const totalPage = Math.ceil(Number(totalRecords / pageSize)); 

        const limit = pageSize; // Number of records to retrieve
        const offset = (page - 1) * pageSize; // Number of records to skip
        
        const data = await prisma.$queryRawUnsafe(`
            SELECT 
                ip_pengunjung,                   
                asal_negara,     
                TRIM(TO_CHAR(created_date, 'FMDD FMMonth YYYY')) AS created_date
            FROM 
                tb_halaman_pengunjung_det   
                WHERE 
                    id_halaman_pengunjung = 6
                    and created_date BETWEEN $4::date AND $5::date
                AND (
                    ip_pengunjung::text LIKE '%' || $3 || '%'
                )
            ORDER BY 
               id_halaman_pengunjung_det desc
            LIMIT $1 OFFSET $2;
        `, limit, offset, search,startDate, endDate);
        
        

        return {
            success: true,
            totalRecords: totalRecords, // Total number of records in the database
            totalPage: totalPage, // Total number of pages
            page: page,
            pageSize: pageSize,
            data: data, // Data for the current page
        };

    }
    public async get_kawasan_dt({request,params,response}:HttpContext) {
        const page = parseInt(params.page) || 1;
        const pageSize = parseInt(params.pageSize) || 10;
        const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided
        const { 
            startDate :startDateRaw, 
            endDate :endDateRaw 
          } = request.qs();
       
          const now = new Date();
          now.setDate(now.getDate() + 1)
          const defaultStartDate = new Date(2020, 0, 1);
          const defaultEndDate = now;
          
          const startDate = startDateRaw || defaultStartDate.toISOString().split('T')[0];
          const endDate = endDateRaw || defaultEndDate.toISOString().split('T')[0];
        const count = await prisma.$queryRawUnsafe(`SELECT 
                count(*) as jml
                FROM 
                tb_halaman_pengunjung_det                   
                WHERE 
                    id_halaman_pengunjung = 2
                    and created_date BETWEEN $2::date AND $3::date
                AND (
                    ip_pengunjung ILIKE '%' || $1 || '%'
                )`,search,startDate, endDate)
                const totalRecords = Number(count[0].jml)
        const totalPage = Math.ceil(Number(totalRecords / pageSize)); 

        const limit = pageSize; // Number of records to retrieve
        const offset = (page - 1) * pageSize; // Number of records to skip
        
        const data = await prisma.$queryRawUnsafe(`
            SELECT 
                ip_pengunjung,                   
                asal_negara,     
                TRIM(TO_CHAR(created_date, 'FMDD FMMonth YYYY')) AS created_date
            FROM 
                tb_halaman_pengunjung_det   
                WHERE 
                    id_halaman_pengunjung = 2
                    and created_date BETWEEN $4::date AND $5::date
                AND (
                    ip_pengunjung::text LIKE '%' || $3 || '%'
                )
            ORDER BY 
               id_halaman_pengunjung_det desc
            LIMIT $1 OFFSET $2;
        `, limit, offset, search,startDate, endDate);
        
        

        return {
            success: true,
            totalRecords: totalRecords, // Total number of records in the database
            totalPage: totalPage, // Total number of pages
            page: page,
            pageSize: pageSize,
            data: data, // Data for the current page
        };

    }
    public async get_kawasan_detail_dt({request,params,response}:HttpContext) {
        const page = parseInt(params.page) || 1;
        const pageSize = parseInt(params.pageSize) || 10;
        const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided
        const { 
            startDate :startDateRaw, 
            endDate :endDateRaw,
            prov,
            kabkot
          } = request.qs();

          let filterWilayah = ''
          if (prov != null) {
            filterWilayah = 'And tap.id_adm_provinsi = '+prov
          } else if (kabkot != null) {
            filterWilayah = 'And tak.id_adm_kabkot = '+kabkot
          }
          const now = new Date();
          now.setDate(now.getDate() + 1)
          const defaultStartDate = new Date(2020, 0, 1);
          const defaultEndDate = now;
          
          
          const startDate = startDateRaw || defaultStartDate.toISOString().split('T')[0];
          const endDate = endDateRaw || defaultEndDate.toISOString().split('T')[0];
        const count = await prisma.$queryRawUnsafe(`SELECT 
                count(*) as jml
                FROM 
                tb_halaman_pengunjung_det thpd   
                left join tb_kawasan_industri tki on tki.id_kawasan_industri =thpd.id_konten
                left join tb_adm_kabkot tak on tak.id_adm_kabkot = tki.id_adm_kabkot    
                left join tb_adm_provinsi tap on tap.id_adm_provinsi =tak.id_adm_provinsi 
                WHERE 
                    id_halaman_pengunjung = 3
                    and created_date BETWEEN $2::date AND $3::date
                    ${filterWilayah}                    
                AND (
                    tap.nama ILIKE '%' || $1 || '%'
                    OR tak.nama ILIKE '%' || $1|| '%'
                    OR thpd.ip_pengunjung::text ILIKE '%' || $1 || '%'
                
                )`,search,startDate, endDate)
                const totalRecords = Number(count[0].jml)
        const totalPage = Math.ceil(Number(totalRecords / pageSize)); 

        const limit = pageSize; // Number of records to retrieve
        const offset = (page - 1) * pageSize; // Number of records to skip
        
        const data = await prisma.$queryRawUnsafe(`
            SELECT 
                ip_pengunjung,                   
                asal_negara,     
                TRIM(TO_CHAR(created_date, 'FMDD FMMonth YYYY')) AS created_date,
                tap.nama as nama_provinsi,
                tak.nama as nama_kabkot
            FROM 
                tb_halaman_pengunjung_det thpd   
                left join tb_kawasan_industri tki on tki.id_kawasan_industri =thpd.id_konten
                left join tb_adm_kabkot tak on tak.id_adm_kabkot = tki.id_adm_kabkot    
                left join tb_adm_provinsi tap on tap.id_adm_provinsi =tak.id_adm_provinsi 
                WHERE 
                    id_halaman_pengunjung = 3
                    and created_date BETWEEN $4::date AND $5::date
                    ${filterWilayah}                    
                AND (
                    tap.nama ILIKE '%' || $3 || '%'
                    OR tak.nama ILIKE '%' || $3|| '%'
                    OR thpd.ip_pengunjung::text ILIKE '%' || $3 || '%'
                )
            ORDER BY 
               id_halaman_pengunjung_det desc
            LIMIT $1 OFFSET $2;
        `, limit, offset, search,startDate, endDate);
        
        

        return {
            success: true,
            totalRecords: totalRecords, // Total number of records in the database
            totalPage: totalPage, // Total number of pages
            page: page,
            pageSize: pageSize,
            data: data, // Data for the current page
        };

    }
    public async get_daerah_dt({request,params,response}:HttpContext) {
        const page = parseInt(params.page) || 1;
        const pageSize = parseInt(params.pageSize) || 10;
        const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided
        const { 
            startDate :startDateRaw, 
            endDate :endDateRaw 
          } = request.qs();
       
          const now = new Date();
          now.setDate(now.getDate() + 1)
          const defaultStartDate = new Date(2020, 0, 1);
          const defaultEndDate = now;
          
          const startDate = startDateRaw || defaultStartDate.toISOString().split('T')[0];
          const endDate = endDateRaw || defaultEndDate.toISOString().split('T')[0];
        const count = await prisma.$queryRawUnsafe(`SELECT 
                count(*) as jml
                FROM 
                tb_halaman_pengunjung_det                   
                WHERE 
                    id_halaman_pengunjung = 4
                    and created_date BETWEEN $2::date AND $3::date
                AND (
                    ip_pengunjung ILIKE '%' || $1 || '%'
                )`,search,startDate, endDate)
                const totalRecords = Number(count[0].jml)
        const totalPage = Math.ceil(Number(totalRecords / pageSize)); 

        const limit = pageSize; // Number of records to retrieve
        const offset = (page - 1) * pageSize; // Number of records to skip
        
        const data = await prisma.$queryRawUnsafe(`
            SELECT 
                ip_pengunjung,                   
                asal_negara,     
                TRIM(TO_CHAR(created_date, 'FMDD FMMonth YYYY')) AS created_date
            FROM 
                tb_halaman_pengunjung_det   
                WHERE 
                    id_halaman_pengunjung = 4
                    and created_date BETWEEN $4::date AND $5::date
                AND (
                    ip_pengunjung::text LIKE '%' || $3 || '%'
                )
            ORDER BY 
               id_halaman_pengunjung_det desc
            LIMIT $1 OFFSET $2;
        `, limit, offset, search,startDate, endDate);
        
        

        return {
            success: true,
            totalRecords: totalRecords, // Total number of records in the database
            totalPage: totalPage, // Total number of pages
            page: page,
            pageSize: pageSize,
            data: data, // Data for the current page
        };

    }
   
    public async get_ppi({params,response}:HttpContext) {

            const results = await prisma.$queryRaw`
                SELECT 
                    tap.nama AS nama_provinsi,
                    tak.nama AS nama_kabkot,
                    tpk.nama,
                    COUNT(*) AS jumlah
                FROM 
                    tb_halaman_pengunjung thp  
                JOIN 
                    tb_halaman_pengunjung_det thpd ON thp.id_halaman_pengunjung = thpd.id_halaman_pengunjung
                JOIN 
                    tb_peluang_kabkot tpk ON tpk.id_peluang_kabkot = thpd.id_konten
                JOIN 
                    tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
                JOIN 
                    tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
                WHERE 
                    thpd.id_halaman_pengunjung = 7
                    AND tpk.status = '99'
                    AND tpk.is_ipro IS FALSE
                    and tpk.id_prioritas = '1'
                GROUP BY 
                    tap.nama, tak.nama, tpk.nama
                ORDER BY 
                    tap.nama, tak.nama, tpk.nama;
            `;

            return response.json({success:true,data:results});

    }
    public async get_ppi_dt({request,params,response}:HttpContext) {
        const page = parseInt(params.page) || 1;
        const pageSize = parseInt(params.pageSize) || 10;
        const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided
        const { 
            startDate :startDateRaw, 
            endDate :endDateRaw, 
            prov,
            kabkot
          } = request.qs();
          
          let filterWilayah = ''
          if (prov != null) {
            filterWilayah = 'And tap.id_adm_provinsi = '+prov
          } else if (kabkot != null) {
            filterWilayah = 'And tak.id_adm_kabkot = '+kabkot
          }
       
          const now = new Date();
          now.setDate(now.getDate() + 1)
          const defaultStartDate = new Date(2020, 0, 1);
          const defaultEndDate = now;
          
          const startDate = startDateRaw || defaultStartDate.toISOString().split('T')[0];
          const endDate = endDateRaw || defaultEndDate.toISOString().split('T')[0];
        const count = await prisma.$queryRawUnsafe(`SELECT 
                count(*) as jml
                FROM 
                    tb_halaman_pengunjung thp  
                left JOIN 
                    tb_halaman_pengunjung_det thpd ON thp.id_halaman_pengunjung = thpd.id_halaman_pengunjung
                left JOIN 
                    tb_peluang_kabkot tpk ON tpk.id_peluang_kabkot = thpd.id_konten
                left JOIN 
                    tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
                left JOIN 
                    tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
                WHERE 
                    thpd.id_halaman_pengunjung = 7
                    and created_date BETWEEN $2::date AND $3::date
                    AND tpk.status = '99'
                    AND tpk.is_ipro IS FALSE
                    and tpk.id_prioritas = '1'
                    ${filterWilayah}                    
                AND (
                    tap.nama ILIKE '%' || $1 || '%'
                    OR tak.nama ILIKE '%' || $1 || '%'
                    OR tpk.nama ILIKE '%' || $1 || '%'
                    OR thpd.ip_pengunjung::text ILIKE '%' || $1 || '%'
                )`,search,startDate, endDate)
        const totalRecords = Number(count[0].jml)
        const totalPage = Math.ceil(Number(totalRecords / pageSize)); 

        const limit = pageSize; // Number of records to retrieve
        const offset = (page - 1) * pageSize; // Number of records to skip
        
        const data = await prisma.$queryRawUnsafe(`
            SELECT 
                tap.nama AS nama_provinsi,
                tak.nama AS nama_kabkot,
                tpk.nama,
                thpd.ip_pengunjung,           
                asal_negara,        
                TRIM(TO_CHAR(thpd.created_date, 'FMDD FMMonth YYYY')) AS created_date
            FROM 
                    tb_halaman_pengunjung thp  
                left JOIN 
                    tb_halaman_pengunjung_det thpd ON thp.id_halaman_pengunjung = thpd.id_halaman_pengunjung
                left JOIN 
                    tb_peluang_kabkot tpk ON tpk.id_peluang_kabkot = thpd.id_konten
                left JOIN 
                    tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
                JOIN 
                    tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
                WHERE 
                    thpd.id_halaman_pengunjung = 7
                    and created_date BETWEEN $4::date AND $5::date
                    AND tpk.status = '99'
                    AND tpk.is_ipro IS FALSE
                    and tpk.id_prioritas = '1'
                    ${filterWilayah}                    
                AND (
                    tap.nama ILIKE '%' || $3 || '%'
                    OR tak.nama ILIKE '%' || $3 || '%'
                    OR tpk.nama ILIKE '%' || $3 || '%'
                    OR thpd.ip_pengunjung::text ILIKE '%' || $3 || '%'
                )
            ORDER BY 
                tap.nama, tak.nama, tpk.nama
             LIMIT $1 OFFSET $2;
        `, limit, offset, search,startDate, endDate)
        
        

        return {
            success: true,
            totalRecords: totalRecords, // Total number of records in the database
            totalPage: totalPage, // Total number of pages
            page: page,
            pageSize: pageSize,
            data: data, // Data for the current page
        };

    }
    public async get_ipro({params,response}:HttpContext) {

            const results = await prisma.$queryRaw`
                SELECT 
                    tap.nama AS nama_provinsi,
                    tak.nama AS nama_kabkot,
                    tpk.nama,
                    COUNT(*) AS jumlah
                FROM 
                    tb_halaman_pengunjung thp  
                JOIN 
                    tb_halaman_pengunjung_det thpd ON thp.id_halaman_pengunjung = thpd.id_halaman_pengunjung
                JOIN 
                    tb_peluang_kabkot tpk ON tpk.id_peluang_kabkot = thpd.id_konten
                JOIN 
                    tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
                JOIN 
                    tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
                WHERE 
                    thpd.id_halaman_pengunjung = 7
                    AND tpk.status = '99'
                    AND tpk.is_ipro IS TRUE
                    and tpk.id_prioritas = '1'
                GROUP BY 
                    tap.nama, tak.nama, tpk.nama
                ORDER BY 
                    tap.nama, tak.nama, tpk.nama
            `;

            return response.json({success:true,data:results});

    }
    public async get_ipro_dt({request,params,response}:HttpContext) {
        const page = parseInt(params.page) || 1;
        const pageSize = parseInt(params.pageSize) || 10;
        const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided
        const limit = pageSize; // Number of records to retrieve
        const offset = (page - 1) * pageSize; // Number of records to skip
        const { 
            startDate :startDateRaw, 
            endDate :endDateRaw,
            prov,
            kabkot
          } = request.qs();
          
          let filterWilayah = ''
          if (prov != null) {
            filterWilayah = 'And tap.id_adm_provinsi = '+prov
          } else if (kabkot != null) {
            filterWilayah = 'And tak.id_adm_kabkot = '+kabkot
          }
       
          const now = new Date();
          now.setDate(now.getDate() + 1)
          const defaultStartDate = new Date(2020, 0, 1);
          const defaultEndDate = now;
          
          const startDate = startDateRaw || defaultStartDate.toISOString().split('T')[0];
          const endDate = endDateRaw || defaultEndDate.toISOString().split('T')[0];
        const count = await prisma.$queryRawUnsafe(`SELECT 
                count(*) as jml
            FROM 
                tb_halaman_pengunjung thp  
            JOIN 
                tb_halaman_pengunjung_det thpd ON thp.id_halaman_pengunjung = thpd.id_halaman_pengunjung
            JOIN 
                tb_peluang_kabkot tpk ON tpk.id_peluang_kabkot = thpd.id_konten
            JOIN 
                tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
            JOIN 
                tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
            WHERE 
                thpd.id_halaman_pengunjung = 7
                AND created_date BETWEEN $2::date AND $3::date
                AND tpk.status = '99'
                AND tpk.is_ipro IS TRUE
                AND tpk.id_prioritas = '1'
                AND (
                    tap.nama ILIKE '%' || $1 || '%'
                    OR tak.nama ILIKE '%' || $1 || '%'
                    OR tpk.nama ILIKE '%' || $1 || '%'
                    OR thpd.ip_pengunjung::text ILIKE '%' || $1 || '%'
                )  
        `, search,startDate, endDate)
        const totalRecords = Number(count[0].jml)
        const totalPage = Math.ceil(Number(totalRecords / pageSize)); 

      
        const data = await prisma.$queryRawUnsafe(`
            SELECT 
                tap.nama AS nama_provinsi,
                tak.nama AS nama_kabkot,
                tpk.nama,
                thpd.ip_pengunjung,           
                asal_negara,        
                TRIM(TO_CHAR(thpd.created_date, 'FMDD FMMonth YYYY')) AS created_date
            FROM 
                tb_halaman_pengunjung thp  
            JOIN 
                tb_halaman_pengunjung_det thpd ON thp.id_halaman_pengunjung = thpd.id_halaman_pengunjung
            JOIN 
                tb_peluang_kabkot tpk ON tpk.id_peluang_kabkot = thpd.id_konten
            JOIN 
                tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
            JOIN 
                tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
            WHERE 
                thpd.id_halaman_pengunjung = 7
                and created_date BETWEEN $4::date AND $5::date
                AND tpk.status = '99'
                AND tpk.is_ipro IS TRUE
                AND tpk.id_prioritas = '1'
                AND (
                    tap.nama ILIKE '%' || $3 || '%'
                    OR tak.nama ILIKE '%' || $3 || '%'
                    OR tpk.nama ILIKE '%' || $3 || '%'
                    OR thpd.ip_pengunjung::text ILIKE '%' || $3 || '%'
                )
            ORDER BY 
                tap.nama, tak.nama, tpk.nama
             LIMIT $1 OFFSET $2;
        `, limit, offset, search,startDate, endDate)
        
        

        return {
            success: true,
            totalRecords: totalRecords, // Total number of records in the database
            totalPage: totalPage, // Total number of pages
            page: page,
            pageSize: pageSize,
            data: data, // Data for the current page
        };

    }
    public async get_pid({params,response}:HttpContext) {

            const results = await prisma.$queryRaw`
                SELECT 
                    case when tap.nama is not null then tap.nama else tap2.nama end AS nama_provinsi,
                    tak.nama AS nama_kabkot,
                    tpk.judul,
                    COUNT(*) AS jumlah
                FROM 
                    tb_halaman_pengunjung thp  
                JOIN 
                    tb_halaman_pengunjung_det thpd ON thp.id_halaman_pengunjung = thpd.id_halaman_pengunjung
                JOIN 
                    tb_peluang_daerah tpk ON tpk.id_peluang_daerah  = thpd.id_konten
                left JOIN 
                    tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
                left JOIN 
                    tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
                left JOIN 
                    tb_adm_provinsi tap2 ON tap2.id_adm_provinsi = tpk.id_adm_provinsi
                WHERE 
                    thpd.id_halaman_pengunjung = 8
                GROUP BY 
                    nama_provinsi, tak.nama, tpk.judul
                ORDER BY 
                    nama_provinsi, tak.nama, tpk.judul;
            `;

            return response.json({success:true,data:results});

    }
    public async get_pid_dt({request,params,response}:HttpContext) {
        const page = parseInt(params.page) || 1;
        const pageSize = parseInt(params.pageSize) || 10;
        const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided
        const limit = pageSize;
        const offset = (page - 1) * pageSize; 
        const { 
            startDate :startDateRaw, 
            endDate :endDateRaw ,
            prov,
            kabkot
          } = request.qs();
          
          let filterWilayah = ''
          if (prov != null) {
            filterWilayah = 'And tap.id_adm_provinsi = '+prov
          } else if (kabkot != null) {
            filterWilayah = 'And tak.id_adm_kabkot = '+kabkot
          }
       
          const now = new Date();
          now.setDate(now.getDate() + 1)
          const defaultStartDate = new Date(2020, 0, 1);
          const defaultEndDate = now;
          
          const startDate = startDateRaw || defaultStartDate.toISOString().split('T')[0];
          const endDate = endDateRaw || defaultEndDate.toISOString().split('T')[0];
        const count = await prisma.$queryRawUnsafe(`SELECT 
                count(*) as jml
                FROM 
                    tb_halaman_pengunjung thp  
                JOIN 
                    tb_halaman_pengunjung_det thpd ON thp.id_halaman_pengunjung = thpd.id_halaman_pengunjung
                JOIN 
                    tb_peluang_daerah tpk ON tpk.id_peluang_daerah  = thpd.id_konten
                left JOIN 
                    tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
                left JOIN 
                    tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
                left JOIN 
                    tb_adm_provinsi tap2 ON tap2.id_adm_provinsi = tpk.id_adm_provinsi
                WHERE 
                    thpd.id_halaman_pengunjung = 8
                    and created_date BETWEEN $2::date AND $3::date
                    ${filterWilayah}                    
                AND (
                    tap.nama ILIKE '%' || $1 || '%'
                    OR tap2.nama ILIKE '%' || $1 || '%'
                    OR tak.nama ILIKE '%' || $1|| '%'
                    OR tpk.judul ILIKE '%' || $1 || '%'
                    OR thpd.ip_pengunjung::text ILIKE '%' || $1 || '%'
                ) 
        `,search,startDate, endDate)
        const totalRecords = Number(count[0].jml)
        const totalPage = Math.ceil(Number(totalRecords / pageSize)); 

        
        const data = await prisma.$queryRawUnsafe(`
            SELECT 
                case when tap.nama is not null then tap.nama else tap2.nama end AS nama_provinsi,
                tak.nama AS nama_kabkot,
                tpk.judul,
                thpd.ip_pengunjung,           
                asal_negara,        
                TRIM(TO_CHAR(thpd.created_date, 'FMDD FMMonth YYYY')) AS created_date
            FROM 
                    tb_halaman_pengunjung thp  
                JOIN 
                    tb_halaman_pengunjung_det thpd ON thp.id_halaman_pengunjung = thpd.id_halaman_pengunjung
                JOIN 
                    tb_peluang_daerah tpk ON tpk.id_peluang_daerah  = thpd.id_konten
                left JOIN 
                    tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
                left JOIN 
                    tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
                left JOIN 
                    tb_adm_provinsi tap2 ON tap2.id_adm_provinsi = tpk.id_adm_provinsi
                WHERE 
                    thpd.id_halaman_pengunjung = 8
                    and created_date BETWEEN $4::date AND $5::date
                    ${filterWilayah}                    
                AND (
                    tap.nama ILIKE '%' || $3 || '%'
                    OR tak.nama ILIKE '%' || $3 || '%'
                    OR tpk.judul ILIKE '%' || $3 || '%'
                    OR thpd.ip_pengunjung::text ILIKE '%' || $3 || '%'
                )
            ORDER BY 
                tap.nama, tak.nama, tpk.judul
             LIMIT $1 OFFSET $2;
        `, limit, offset, search,startDate, endDate);
        
        

        return {
            success: true,
            totalRecords: totalRecords, // Total number of records in the database
            totalPage: totalPage, // Total number of pages
            page: page,
            pageSize: pageSize,
            data: data, // Data for the current page
        };

    }
    public async get_daerah_detail_dt({request,params,response}:HttpContext) {
        const page = parseInt(params.page) || 1;
        const pageSize = parseInt(params.pageSize) || 10;
        const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided
        const limit = pageSize; // Number of records to retrieve
        const offset = (page - 1) * pageSize; // Number of records to skip
    const { 
            startDate :startDateRaw, 
            endDate :endDateRaw ,
            prov,
            kabkot
          } = request.qs();
          
          let filterWilayah = ''
          if (prov != null) {
            filterWilayah = 'And tap.id_adm_provinsi = '+prov
          } else if (kabkot != null) {
            filterWilayah = 'And tak.id_adm_kabkot = '+kabkot
          }
       
          const now = new Date();
          const defaultStartDate = new Date(2020, 0, 1);
          const defaultEndDate = now;
          
          const startDate = startDateRaw || defaultStartDate.toISOString().split('T')[0];
          const endDate = endDateRaw || defaultEndDate.toISOString().split('T')[0];
        const count = await prisma.$queryRawUnsafe(`SELECT 
                count(*) as jml
                FROM 
                    tb_halaman_pengunjung thp  
                JOIN 
                    tb_halaman_pengunjung_det thpd ON thp.id_halaman_pengunjung = thpd.id_halaman_pengunjung
                left JOIN 
                    tb_adm_kabkot tak ON tak.id_adm_kabkot = thpd.id_konten
                left JOIN 
                    tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
                left JOIN 
                    tb_adm_provinsi tap2 ON tap2.id_adm_provinsi = thpd.id_konten
                WHERE 
                    thpd.id_halaman_pengunjung = 5
                    and created_date BETWEEN $2::date AND $3::date
                    ${filterWilayah}                    
                AND (
                    tap.nama ILIKE '%' || $1 || '%'
                    OR tap2.nama ILIKE '%' || $1 || '%'
                    OR tak.nama ILIKE '%' || $1|| '%'
                    OR thpd.ip_pengunjung::text ILIKE '%' || $1 || '%'
                ) 
        `,search,startDate, endDate)
        const totalRecords = Number(count[0].jml)
        const totalPage = Math.ceil(Number(totalRecords / pageSize)); 

        
        const data = await prisma.$queryRawUnsafe(`
            SELECT 
                case when tap.nama is not null then tap.nama else tap2.nama end AS nama_provinsi,
                tak.nama AS nama_kabkot,
                thpd.ip_pengunjung,           
                asal_negara,        
                TRIM(TO_CHAR(thpd.created_date, 'FMDD FMMonth YYYY')) AS created_date
            FROM 
                    tb_halaman_pengunjung thp  
                JOIN 
                    tb_halaman_pengunjung_det thpd ON thp.id_halaman_pengunjung = thpd.id_halaman_pengunjung
                left JOIN 
                    tb_adm_kabkot tak ON tak.id_adm_kabkot = thpd.id_konten
                left JOIN 
                    tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
                left JOIN 
                    tb_adm_provinsi tap2 ON tap2.id_adm_provinsi = thpd.id_konten
                WHERE 
                    thpd.id_halaman_pengunjung = 5
                    and created_date BETWEEN $4::date AND $5::date
                    ${filterWilayah}                    
                AND (
                    tap.nama ILIKE '%' || $3 || '%'
                    OR tak.nama ILIKE '%' || $3 || '%'
                    OR thpd.ip_pengunjung::text ILIKE '%' || $3 || '%'
                )
            ORDER BY 
                tap.nama, tak.nama
             LIMIT $1 OFFSET $2;
        `, limit, offset, search,startDate, endDate);
        
        

        return {
            success: true,
            totalRecords: totalRecords, // Total number of records in the database
            totalPage: totalPage, // Total number of pages
            page: page,
            pageSize: pageSize,
            data: data, // Data for the current page
        };

    }

    public async get_kajian_dt({request,params,response}:HttpContext) {
        const page = parseInt(params.page) || 1;
        const pageSize = parseInt(params.pageSize) || 10;
        const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided
        const { 
            startDate :startDateRaw, 
            endDate :endDateRaw 
          } = request.qs();
       
          const now = new Date();
          now.setDate(now.getDate() + 1)
          const defaultStartDate = new Date(2020, 0, 1);
          const defaultEndDate = now;
          const startDate = startDateRaw || defaultStartDate.toISOString().split('T')[0];
          const endDate = endDateRaw || defaultEndDate.toISOString().split('T')[0];
        const count = await prisma.$queryRawUnsafe(`SELECT 
                count(*) as jml
                FROM 
                tb_halaman_pengunjung_det thpd                   
                left join tb_komoditi_nasional_ref on tb_komoditi_nasional_ref.id_komoditi = thpd.id_konten
                WHERE 
                    id_halaman_pengunjung = 10
                    and created_date BETWEEN $2::timestamp AND $3::timestamp
                AND (
                    ip_pengunjung ILIKE '%' || $1 || '%' OR
                    tb_komoditi_nasional_ref.nama ILIKE '%' || $1 || '%'

                )`,search,startDate, endDate)
                const totalRecords = Number(count[0].jml)
        const totalPage = Math.ceil(Number(totalRecords / pageSize)); 

        const limit = pageSize; // Number of records to retrieve
        const offset = (page - 1) * pageSize; // Number of records to skip
        
        const data = await prisma.$queryRawUnsafe(`
            SELECT 
                tb_komoditi_nasional_ref.nama as nama_komoditi,
                ip_pengunjung,              
                asal_negara,     
                TRIM(TO_CHAR(created_date, 'FMDD FMMonth YYYY')) AS created_date
            FROM 
                tb_halaman_pengunjung_det thpd
                left join tb_komoditi_nasional_ref on tb_komoditi_nasional_ref.id_komoditi = thpd.id_konten
                WHERE 
                    id_halaman_pengunjung = 10
                    and created_date BETWEEN $4::date AND $5::date
                AND (
                    ip_pengunjung::text ILIKE '%' || $3 || '%' OR
                    tb_komoditi_nasional_ref.nama ILIKE '%' || $3 || '%'
                )
            ORDER BY 
               id_halaman_pengunjung_det desc
            LIMIT $1 OFFSET $2;
        `, limit, offset, search,startDate, endDate);
        

        return {
            success: true,
            totalRecords: totalRecords, // Total number of records in the database
            totalPage: totalPage, // Total number of pages
            page: page,
            pageSize: pageSize,
            data: data, // Data for the current page
        };

    }
    public async get_hilirisasi_dt({request,params,response}:HttpContext) {
        const page = parseInt(params.page) || 1;
        const pageSize = parseInt(params.pageSize) || 10;
        const search = decodeURIComponent(params.search) == "undefined" ?  '' : decodeURIComponent(params.search); // Default to an empty string if search is not provided
        const { 
            startDate :startDateRaw, 
            endDate :endDateRaw 
          } = request.qs();
       
          const now = new Date();
          now.setDate(now.getDate() + 1)
          const defaultStartDate = new Date(2020, 0, 1);
          const defaultEndDate = now;
          const startDate = startDateRaw || defaultStartDate.toISOString().split('T')[0];
          const endDate = endDateRaw || defaultEndDate.toISOString().split('T')[0];
        const count = await prisma.$queryRawUnsafe(`SELECT 
                count(*) as jml
                FROM 
                tb_halaman_pengunjung_det thpd                   
                left join tb_komoditi_nasional_ref on tb_komoditi_nasional_ref.id_komoditi = thpd.id_konten
                WHERE 
                    id_halaman_pengunjung = 9
                    and created_date BETWEEN $2::timestamp AND $3::timestamp
                AND (
                    ip_pengunjung ILIKE '%' || $1 || '%' OR
                    tb_komoditi_nasional_ref.nama ILIKE '%' || $1 || '%'

                )`,search,startDate, endDate)
                const totalRecords = Number(count[0].jml)
        const totalPage = Math.ceil(Number(totalRecords / pageSize)); 

        const limit = pageSize; // Number of records to retrieve
        const offset = (page - 1) * pageSize; // Number of records to skip
        
        const data = await prisma.$queryRawUnsafe(`
            SELECT 
                tb_komoditi_nasional_ref.nama as nama_komoditi,
                ip_pengunjung,              
                asal_negara,     
                TRIM(TO_CHAR(created_date, 'FMDD FMMonth YYYY')) AS created_date
            FROM 
                tb_halaman_pengunjung_det thpd
                left join tb_komoditi_nasional_ref on tb_komoditi_nasional_ref.id_komoditi = thpd.id_konten
                WHERE 
                    id_halaman_pengunjung = 9
                    and created_date BETWEEN $4::date AND $5::date
                AND (
                    ip_pengunjung::text ILIKE '%' || $3 || '%' OR
                    tb_komoditi_nasional_ref.nama ILIKE '%' || $3 || '%'
                )
            ORDER BY 
               id_halaman_pengunjung_det desc
            LIMIT $1 OFFSET $2;
        `, limit, offset, search,startDate, endDate);
        

        return {
            success: true,
            totalRecords: totalRecords, // Total number of records in the database
            totalPage: totalPage, // Total number of pages
            page: page,
            pageSize: pageSize,
            data: data, // Data for the current page
        };

    }
    

    public async get_download_ppi({request, response }: HttpContext) {
        const { 
            page: pageRaw = '1', 
            pageSize: pageSizeRaw = '10', 
            search: searchRaw = '', 
            startDate :startDateRaw, 
            endDate :endDateRaw,
            prov,
            kabkot
          } = request.qs();
          
          let filterWilayah = ''
          if (prov != null) {
            filterWilayah = 'And tap.id_adm_provinsi = '+prov
          } else if (kabkot != null) {
            filterWilayah = 'And tak.id_adm_kabkot = '+kabkot
          }
          
          const page = parseInt(pageRaw);
          const pageSize = parseInt(pageSizeRaw);
          const search = searchRaw === 'undefined' ? '' : decodeURIComponent(searchRaw);
          
          const now = new Date();
          now.setDate(now.getDate() + 1)
          const defaultStartDate = new Date(2020, 0, 1);
          const defaultEndDate = now;
          
          const startDate = startDateRaw || defaultStartDate.toISOString().split('T')[0];
          const endDate = endDateRaw || defaultEndDate.toISOString().split('T')[0];
        // if (startDate == null ) {
        //     return {
        //         success: true,
        //         totalRecords: 0, // Total number of records in the database
        //         totalPage: 1, // Total number of pages
        //         page: 1,
        //         pageSize:10,
        //         data: [], // Data for the current page
        //     };
        // }
        const count = await prisma.$queryRawUnsafe(`SELECT
            count(*) as jml
            FROM tb_unduh_data tud 
            JOIN tb_unduh_data_keperluan tudk ON tud.id_unduh_data_tujuan = tudk.id_unduh_data_keperluan
            JOIN tb_peluang_kabkot tpk ON tpk.id_peluang_kabkot = tud.id_konten
            LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
            LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
            WHERE tud.id_jenis_konten = 'peluang'
            AND status = '99'
            AND tpk.id_prioritas = '1'
            AND tpk.is_ipro IS FALSE
            AND created_date BETWEEN $2::timestamp AND $3::timestamp
            ${filterWilayah}                    
                AND (
                tpk.nama::text ILIKE '%' || $1 || '%'
                OR tud.nama::text ILIKE '%' || $1 || '%'
                OR tud.email::text ILIKE '%' || $1 || '%'
                OR tudk.nama::text ILIKE '%' || $1 || '%'
                OR tud.no_telp::text ILIKE '%' || $1 || '%'
                OR tud.ip_pengunjung::text ILIKE '%' || $1 || '%'
            )`,search,startDate, endDate)
        const totalRecords = Number(count[0].jml)
        const totalPage = Math.ceil(Number(totalRecords / pageSize)); 
        
        const limit = pageSize; // Number of records to retrieve
        const offset = (page - 1) * pageSize; // Number of records to skip

        const data = await prisma.$queryRawUnsafe(`
            SELECT tpk.nama as nama_peluang, tud.nama, tud.email, tudk.nama as keperluan, tud.no_telp, tud.ip_pengunjung, tud.created_date
            FROM tb_unduh_data tud 
            JOIN tb_unduh_data_keperluan tudk ON tud.id_unduh_data_tujuan = tudk.id_unduh_data_keperluan
            JOIN tb_peluang_kabkot tpk ON tpk.id_peluang_kabkot = tud.id_konten
            LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
            LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
            WHERE tud.id_jenis_konten = 'peluang'
            AND status = '99'
            AND tpk.id_prioritas = '1'
            AND tpk.is_ipro IS FALSE
            AND created_date BETWEEN $4::timestamp AND $5::timestamp
            ${filterWilayah}                    
            AND (
                tpk.nama::text ILIKE '%' || $3 || '%'
                OR tud.nama::text ILIKE '%' || $3 || '%'
                OR tud.email::text ILIKE '%' || $3 || '%'
                OR tudk.nama::text ILIKE '%' || $3 || '%'
                OR tud.no_telp::text ILIKE '%' || $3 || '%'
                OR tud.ip_pengunjung::text ILIKE '%' || $3 || '%'
            )
            LIMIT $1 OFFSET $2;
        `, limit, offset, search, startDate, endDate);
        
        

        return {
            success: true,
            totalRecords: totalRecords, // Total number of records in the database
            totalPage: totalPage, // Total number of pages
            page: page,
            pageSize: pageSize,
            data: data, // Data for the current page
        };

    }

    public async get_download_ipro({request, response }: HttpContext) {
        const { 
            page: pageRaw = '1', 
            pageSize: pageSizeRaw = '10', 
            search: searchRaw = '', 
            startDate :startDateRaw, 
            endDate :endDateRaw,
            prov,
            kabkot
          } = request.qs();
          
          let filterWilayah = ''
          if (prov != null) {
            filterWilayah = 'And tap.id_adm_provinsi = '+prov
          } else if (kabkot != null) {
            filterWilayah = 'And tak.id_adm_kabkot = '+kabkot
          }
          
          const page = parseInt(pageRaw);
          const pageSize = parseInt(pageSizeRaw);
          const search = searchRaw === 'undefined' ? '' : decodeURIComponent(searchRaw);
          
          const now = new Date();
          now.setDate(now.getDate() + 1)
          const defaultStartDate = new Date(2020, 0, 1);
          const defaultEndDate = now;
          
          const startDate = startDateRaw || defaultStartDate.toISOString().split('T')[0];
          const endDate = endDateRaw || defaultEndDate.toISOString().split('T')[0];
        if (startDate == null ) {
            return {
                success: true,
                totalRecords: 0, // Total number of records in the database
                totalPage: 1, // Total number of pages
                page: 1,
                pageSize:10,
                data: [], // Data for the current page
            };
        }
        const count = await prisma.$queryRawUnsafe(`SELECT
            count(*) as jml
            FROM tb_unduh_data tud 
            JOIN tb_unduh_data_keperluan tudk ON tud.id_unduh_data_tujuan = tudk.id_unduh_data_keperluan
            JOIN tb_peluang_kabkot tpk ON tpk.id_peluang_kabkot = tud.id_konten
            LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
            LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
            WHERE tud.id_jenis_konten = 'peluang'
            AND status = '99'
            AND tpk.id_prioritas = '1'
            AND tpk.is_ipro IS TRUE
            AND created_date BETWEEN $2::timestamp AND $3::timestamp
            ${filterWilayah}                    
                AND (
                tpk.nama::text ILIKE '%' || $1 || '%'
                OR tud.nama::text ILIKE '%' || $1 || '%'
                OR tud.email::text ILIKE '%' || $1 || '%'
                OR tudk.nama::text ILIKE '%' || $1 || '%'
                OR tud.no_telp::text ILIKE '%' || $1 || '%'
                OR tud.ip_pengunjung::text ILIKE '%' || $1 || '%'
            )`,search,startDate, endDate)
        
        const totalRecords = Number(count[0].jml)
        const totalPage = Math.ceil(Number(totalRecords / pageSize)); 

        const limit = pageSize; // Number of records to retrieve
        const offset = (page - 1) * pageSize; // Number of records to skip

        const data = await prisma.$queryRawUnsafe(`
            SELECT tpk.nama as nama_peluang, tud.nama, tud.email, tudk.nama as keperluan, tud.no_telp, tud.ip_pengunjung, tud.created_date
            FROM tb_unduh_data tud 
            JOIN tb_unduh_data_keperluan tudk ON tud.id_unduh_data_tujuan = tudk.id_unduh_data_keperluan
            JOIN tb_peluang_kabkot tpk ON tpk.id_peluang_kabkot = tud.id_konten
            LEFT JOIN tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
            LEFT JOIN tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
            WHERE tud.id_jenis_konten = 'peluang'
            AND status = '99'
            AND tpk.id_prioritas = '1'
            AND tpk.is_ipro IS TRUE
            AND created_date BETWEEN $4::timestamp AND $5::timestamp
            ${filterWilayah}                    
            AND (
                tpk.nama::text ILIKE '%' || $3 || '%'
                OR tud.nama::text ILIKE '%' || $3 || '%'
                OR tud.email::text ILIKE '%' || $3 || '%'
                OR tudk.nama::text ILIKE '%' || $3 || '%'
                OR tud.no_telp::text ILIKE '%' || $3 || '%'
                OR tud.ip_pengunjung::text ILIKE '%' || $3 || '%'
            )
            LIMIT $1 OFFSET $2;
        `, limit, offset, search, startDate, endDate);
        
        

        return {
            success: true,
            totalRecords: totalRecords, // Total number of records in the database
            totalPage: totalPage, // Total number of pages
            page: page,
            pageSize: pageSize,
            data: data, // Data for the current page
        };

           
    }
    public async get_download_pid({request, response }: HttpContext) {
        const { 
            page: pageRaw = '1', 
            pageSize: pageSizeRaw = '10', 
            search: searchRaw = '', 
            startDate :startDateRaw, 
            endDate :endDateRaw ,
            prov,
            kabkot
          } = request.qs();
          
          let filterWilayah = ''
          if (prov != null) {
            filterWilayah = 'And tap.id_adm_provinsi = '+prov
          } else if (kabkot != null) {
            filterWilayah = 'And tak.id_adm_kabkot = '+kabkot
          }
          
          const page = parseInt(pageRaw);
          const pageSize = parseInt(pageSizeRaw);
          const search = searchRaw === 'undefined' ? '' : decodeURIComponent(searchRaw);
          
          const now = new Date();
          now.setDate(now.getDate() + 1)
          const defaultStartDate = new Date(2020, 0, 1);
          const defaultEndDate = now;
          
          const startDate = startDateRaw || defaultStartDate.toISOString().split('T')[0];
          const endDate = endDateRaw || defaultEndDate.toISOString().split('T')[0];
        if (startDate == null ) {
            return {
                success: true,
                totalRecords: 0, // Total number of records in the database
                totalPage: 1, // Total number of pages
                page: 1,
                pageSize:10,
                data: [], // Data for the current page
            };
        }
        const count = await prisma.$queryRawUnsafe(`SELECT 
                count(*) as jml
                    from tb_unduh_data tud 
                join tb_unduh_data_keperluan tudk 
                on tud.id_unduh_data_tujuan =tudk.id_unduh_data_keperluan
                join tb_peluang_daerah tpk on tpk.id_peluang_daerah =tud.id_konten
                left JOIN 
                    tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
                left JOIN 
                tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
                where tud.id_jenis_konten = 'peluang_daerah'
                and status = '99'
                AND created_date BETWEEN $2::timestamp AND $3::timestamp
                ${filterWilayah}                    
                AND (
                tpk.judul::text ILIKE '%' || $1 || '%'
                OR tud.nama::text ILIKE '%' || $1 || '%'
                OR tud.email::text ILIKE '%' || $1 || '%'
                OR tudk.nama::text ILIKE '%' || $1 || '%'
                OR tud.no_telp::text ILIKE '%' || $1 || '%'
                OR tud.ip_pengunjung::text ILIKE '%' || $1 || '%'
            )`,search,startDate, endDate)
        
        const totalRecords = Number(count[0].jml)
        const totalPage = Math.ceil(Number(totalRecords / pageSize)); 

        const limit = pageSize; // Number of records to retrieve
        const offset = (page - 1) * pageSize; // Number of records to skip

        const data = await prisma.$queryRawUnsafe(`
           select tpk.judul as nama_peluang,tud.nama,tud.email,tudk.nama as keperluan,tud.no_telp,tud.ip_pengunjung,tud.created_date
            from tb_unduh_data tud 
            join tb_unduh_data_keperluan tudk 
            on tud.id_unduh_data_tujuan =tudk.id_unduh_data_keperluan
            join tb_peluang_daerah tpk on tpk.id_peluang_daerah =tud.id_konten
            left JOIN 
                tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
            left JOIN 
                tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
                where tud.id_jenis_konten = 'peluang_daerah'
                and status = '99'
            AND created_date BETWEEN $4::timestamp AND $5::timestamp
            ${filterWilayah}                    
            AND (
                tpk.judul::text ILIKE '%' || $3 || '%'
                OR tud.nama::text ILIKE '%' || $3 || '%'
                OR tud.email::text ILIKE '%' || $3 || '%'
                OR tudk.nama::text ILIKE '%' || $3 || '%'
                OR tud.no_telp::text ILIKE '%' || $3 || '%'
                OR tud.ip_pengunjung::text ILIKE '%' || $3 || '%'
            )
            LIMIT $1 OFFSET $2;
        `, limit, offset, search, startDate, endDate);
        
        

        return {
            success: true,
            totalRecords: totalRecords, // Total number of records in the database
            totalPage: totalPage, // Total number of pages
            page: page,
            pageSize: pageSize,
            data: data, // Data for the current page
        };

       

    }
    public async get_download_kawasan({ request, response }: HttpContext) {
        const { 
            page: pageRaw = '1', 
            pageSize: pageSizeRaw = '10', 
            search: searchRaw = '', 
            startDate :startDateRaw, 
            endDate :endDateRaw ,
            prov,
            kabkot
          } = request.qs();
          
          let filterWilayah = ''
          if (prov != null) {
            filterWilayah = 'And tap.id_adm_provinsi = '+prov
          } else if (kabkot != null) {
            filterWilayah = 'And tak.id_adm_kabkot = '+kabkot
          }
          
          const page = parseInt(pageRaw);
          const pageSize = parseInt(pageSizeRaw);
          const search = searchRaw === 'undefined' ? '' : decodeURIComponent(searchRaw);
          
          const now = new Date();
          now.setDate(now.getDate() + 1)
          const defaultStartDate = new Date(2020, 0, 1);
          const defaultEndDate = now;
          
          const startDate = startDateRaw || defaultStartDate.toISOString().split('T')[0];
          const endDate = endDateRaw || defaultEndDate.toISOString().split('T')[0];
          if (startDate == null ) {
            return {
                success: true,
                totalRecords: 0, // Total number of records in the database
                totalPage: 1, // Total number of pages
                page: 1,
                pageSize:10,
                data: [], // Data for the current page
            };
        }
        const count = await prisma.$queryRawUnsafe(`SELECT 
                count(*) as jml
                   from tb_unduh_data tud 
                join tb_unduh_data_keperluan tudk 
                on tud.id_unduh_data_tujuan =tudk.id_unduh_data_keperluan
                join tb_kawasan_industri tpk on tpk.id_kawasan_industri =tud.id_konten
                left JOIN 
                    tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
                left JOIN 
                    tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
                    where tud.id_jenis_konten = 'kawasan'
                    and status = '99'
                AND created_date BETWEEN $2::timestamp AND $3::timestamp
                ${filterWilayah}                    
                 AND (
                tpk.nama::text ILIKE '%' || $1 || '%'
                OR tud.nama::text ILIKE '%' || $1 || '%'
                OR tud.email::text ILIKE '%' || $1 || '%'
                OR tudk.nama::text ILIKE '%' || $1 || '%'
                OR tud.no_telp::text ILIKE '%' || $1 || '%'
                OR tud.ip_pengunjung::text ILIKE '%' || $1 || '%'
            )`,search,startDate, endDate)
        
        const totalRecords = Number(count[0].jml)
        const totalPage = Math.ceil(Number(totalRecords / pageSize)); 

        const limit = pageSize; // Number of records to retrieve
        const offset = (page - 1) * pageSize; // Number of records to skip

        const data = await prisma.$queryRawUnsafe(`
           select tpk.nama as nama_peluang,tud.nama,tud.email,tudk.nama as keperluan,tud.no_telp,tud.ip_pengunjung,tud.created_date
            from tb_unduh_data tud 
                join tb_unduh_data_keperluan tudk 
                on tud.id_unduh_data_tujuan =tudk.id_unduh_data_keperluan
                join tb_kawasan_industri tpk on tpk.id_kawasan_industri =tud.id_konten
                left JOIN 
                    tb_adm_kabkot tak ON tak.id_adm_kabkot = tpk.id_adm_kabkot
                left JOIN 
                    tb_adm_provinsi tap ON tap.id_adm_provinsi = tak.id_adm_provinsi
                    where tud.id_jenis_konten = 'kawasan'
                    and status = '99'
            AND created_date BETWEEN $4::timestamp AND $5::timestamp
            ${filterWilayah}                    
            AND (
                tpk.nama::text ILIKE '%' || $3 || '%'
                OR tud.nama::text ILIKE '%' || $3 || '%'
                OR tud.email::text ILIKE '%' || $3 || '%'
                OR tudk.nama::text ILIKE '%' || $3 || '%'
                OR tud.no_telp::text ILIKE '%' || $3 || '%'
                OR tud.ip_pengunjung::text ILIKE '%' || $3 || '%'
            )
            LIMIT $1 OFFSET $2;
        `, limit, offset, search, startDate, endDate);
        
        

        return {
            success: true,
            totalRecords: totalRecords, // Total number of records in the database
            totalPage: totalPage, // Total number of pages
            page: page,
            pageSize: pageSize,
            data: data, // Data for the current page
        };

    }

    
}