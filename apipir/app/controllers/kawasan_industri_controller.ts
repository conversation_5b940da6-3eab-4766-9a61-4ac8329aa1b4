import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../helpers/model_helper.js';

import prisma from '../lib/prisma.js';



export default class KawasanIndustriController {
  private async schemaData() {
    const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
      id_kawasan_industri: {column: 'id_kawasan_industri', alias: 'id_kawasan_industri', type: 'int'},
      id_adm_kabkot: {column: 'id_adm_kabkot', alias: 'id_adm_kabkot', type: 'int'},
      id_sumber_data: {column: 'id_sumber_data', alias: 'id_sumber_data', type: 'int'},
      id_kategori: {column: 'id_kategori', alias: 'id_kategori', type: 'int'},
      nama: {column: 'nama', alias: 'nama', type: 'string'},
      keterangan: {column: 'keterangan', alias: 'keterangan', type: 'string'},
      alamat: {column: 'alamat', alias: 'alamat', type: 'string'},
      luas: {column: 'luas', alias: 'luas', type: 'string'},
      luas_satuan: {column: 'luas_satuan', alias: 'luas_satuan', type: 'string'},
      id_bandara_terdekat: {column: 'id_bandara_terdekat', alias: 'id_bandara_terdekat', type: 'int'},
      jarak_bandara_terdekat: {column: 'jarak_bandara_terdekat', alias: 'jarak_bandara_terdekat', type: 'string'},
      id_pelabuhan_terdekat: {column: 'id_pelabuhan_terdekat', alias: 'id_pelabuhan_terdekat', type: 'int'},
      jarak_pelabuhan_terdekat: {column: 'jarak_pelabuhan_terdekat', alias: 'jarak_pelabuhan_terdekat', type: 'int'},
      jarak_ibukota: {column: 'jarak_ibukota', alias: 'jarak_ibukota', type: 'string'},
      url_web: {column: 'url_web', alias: 'url_web', type: 'string'},
      no_telp: {column: 'no_telp', alias: 'no_telp', type: 'string'},
      no_fax: {column: 'no_fax', alias: 'no_fax', type: 'string'},
      email: {column: 'email', alias: 'email', type: 'string'},
      cp: {column: 'cp', alias: 'cp', type: 'string'},
      ketersediaan: {column: 'ketersediaan', alias: 'ketersediaan', type: 'string'},
      status: {column: 'status', alias: 'status', type: 'string'},
      lon: {column: 'lon', alias: 'lon', type: 'string'},
      lat: {column: 'lat', alias: 'lat', type: 'string'},
      shape: {column: 'shape', alias: 'shape', type: 'string'},
      is_ikn: {column: 'is_ikn', alias: 'is_ikn', type: 'int'},
      id_kawasan_industri_ref_range: {column: 'id_kawasan_industri_ref_range', alias: 'id_kawasan_industri_ref_range', type: 'string'},
      major_tenants: {column: 'major_tenants', alias: 'major_tenants', type: 'string'},
      id_kawasan_industri_occupancy: {column: 'id_kawasan_industri_occupancy', alias: 'id_kawasan_industri_occupancy', type: 'string'},
    };
  
    const joins = {};
  
    const where = {};
  
    return {
      columns: columnMappings,
      join: joins,
      where: where,
    };
  }

  public async get({ request, params, response }: HttpContext) {
    let isAll = false;
    let q = '';
    // return false
    interface Options {
      skip: number; // Keep it non-optional
      take: number;
      select?: { [key: string]: boolean };
    }

    if (params['all'] || params['all'] == 'true') {
      isAll = true
      delete params['all'];
    }

    if (request.input('q')) {
      q = request.input('q');
    }

    let whereDefault = {};

    if (q) {
          whereDefault = {
                  OR: [
                    {
                      nama: {
                              contains: q,
                              mode: 'insensitive', // Mengabaikan besar kecil huruf
                      },
                  },
                  {
                    alamat: {
                            contains: q,
                            mode: 'insensitive', // Mengabaikan besar kecil huruf
                    },
                },
                {
                  email: {
                          contains: q,
                          mode: 'insensitive', // Mengabaikan besar kecil huruf
                  },
              },
              {
                cp: {
                        contains: q,
                        mode: 'insensitive', // Mengabaikan besar kecil huruf
                },
            },
                  ],
              };
    }

    const page = parseInt(request.input('page', 1));
    const perPage = parseInt(request.input('per_page', 10));
    delete params['page'];
    delete params['per_page'];

    let options: Options = {
      skip: (page - 1) * perPage,
      take: perPage,
      where: whereDefault,
    };

    if (isAll) {
      delete options['skip'];
      delete options['take'];
    }

    if (params['select']) {
      options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
      delete params['select'];
    }


    let where = modelWhereAnd((await this.schemaData()).columns, params);

    if (Object.keys(where).length !== 0) {
            options['where'] = {
                AND: [
                    whereDefault, 
                    where,        
                ],
            };
        }

    const data = await prisma.tb_kawasan_industri.findMany(options);
    if (isAll) {
      return response.status(200).json({
        success: true,
        data: data
      });      
    }

    const totalCount = await prisma.tb_kawasan_industri.count();
    
    return response.status(200).json({
      success: true,
      data: data,
      pagination: {
        page: page,
        per_page: perPage,
        total_count: totalCount,
        total_pages: Math.ceil(totalCount / perPage),
      },
    });
  }

  public async getById({ request, params, response }: HttpContext) {
    interface Options {
      select?: { [key: string]: boolean };
      where?: { [key: string]: any; };
    }

    let options: Options = {};

    if (params['select']) {
      options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
      delete params['select'];
    }

    options['where'] = {
      'id_kawasan_industri': parseInt(params.id)
    }

    const data = await prisma.tb_kawasan_industri.findFirst(options);

    return response.status(200).json({
      success: true,
      data: data
    });      
  }

  public async createOrUpdate({ request, response }: HttpContext) {
    let params = request.all()

    try {
      const result = await prisma.$transaction(async (prisma) => {
        if (params.id_kawasan_industri) {
          // Jika `id` ada, lakukan operasi update
          const update = await prisma.tb_kawasan_industri.update({
            where: { id_kawasan_industri: params.id_kawasan_industri },
            data: params,
          })

          return {
            status: 'success',
            message: 'Successfully Updated Data',
            data: update,
          }
        } else {
          console.log("Sync tb_kawasan_industri id sequence",
            await prisma.$executeRaw`
            SELECT setval((
                SELECT PG_GET_SERIAL_SEQUENCE('"tb_kawasan_industri"', 'id_kawasan_industri')),
                (SELECT (MAX("id_kawasan_industri") + 1) FROM "tb_kawasan_industri"),
                false) FROM "tb_kawasan_industri";
            `);

          const save = await prisma.tb_kawasan_industri.create({
            data: params,
          })

          return {
            status: 'success',
            message: 'Successfully Added Data',
            data: save,
          }
        }
      })

      return response.json(result)

    } catch (error) {
      // Handle error jika ada kesalahan dalam transaksi
      return response.status(500).json({
        status: 'error',
        message: 'An error occurred while processing data',
        error: error.message,
      })
    }
  }

  public async deleteById({ request, params, response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_kawasan_industri.delete({
        where: {
          id_kawasan_industri: Id,
        },
      })

      return response.status(200).json({
        status: 'success',
        message: 'Success Delete Data',
        data: deletePost,
      })
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }  
  }
}
//nn