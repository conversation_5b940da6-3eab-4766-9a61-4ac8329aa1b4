import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../helpers/model_helper.js';

import prisma from '../../lib/prisma.js';


// adm_provinsi_layer_spasial_controller

export default class AdmProvinsiLayerSpasialController {
  private async schemaData() {
    const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
      id_adm_provinsi_layer_spasial: {column: 'id_adm_provinsi_layer_spasial', alias: 'id_adm_provinsi_layer_spasial', type: 'int'},
      id_adm_provinsi: {column: 'id_adm_provinsi', alias: 'id_adm_provinsi', type: 'int'},
      nama_layer: {column: 'nama_layer', alias: 'nama_layer', type: 'string'},
      tipe: {column: 'tipe', alias: 'tipe', type: 'int'},
      url_service: {column: 'url_service', alias: 'url_service', type: 'string'},
      status: {column: 'status', alias: 'status', type: 'int'},
      is_active: {column: 'is_active', alias: 'is_active', type: 'int'},
    };
  
    const joins = {};
  
    const where = {};
  
    return {
      columns: columnMappings,
      join: joins,
      where: where,
    };
  }

  public async get({ request, params, response }: HttpContext) {
    const query = request.qs(); // Ini akan mengembalikan objek query string

    console.log(query); // { id_kawasan_industri: '201' }
    let isAll = false;

    interface Options {
      skip: number; // Keep it non-optional
      take: number;
      select?: { [key: string]: boolean };
    }

    if (query['all'] || query['all'] == 'true') {
      isAll = true
      delete query['all'];
    }

    const page = parseInt(request.input('page', 1));
    const perPage = parseInt(request.input('per_page', 10));
    delete query['page'];
    delete query['per_page'];

    let options: Options = {
      skip: (page - 1) * perPage,
      take: perPage,
    };

    if (isAll) {
      delete options['skip'];
      delete options['take'];
    }

    if (query['select']) {
      options['select'] = modelSelect((await this.schemaData()).columns, query['select']);
      delete query['select'];
    }

    options['where'] = modelWhereAnd((await this.schemaData()).columns, query);

    const data = await prisma.tb_adm_provinsi_layer_spasial.findMany(options);

    if (isAll) {
      return response.status(200).json({
        success: true,
        data: data
      });      
    }

    const totalCount = await prisma.tb_adm_provinsi_layer_spasial.count();
    
    return response.status(200).json({
      success: true,
      data: data,
      pagination: {
        page: page,
        per_page: perPage,
        total_count: totalCount,
        total_pages: Math.ceil(totalCount / perPage),
      },
    });
  }

  public async getById({ request, params, response }: HttpContext) {
    interface Options {
      select?: { [key: string]: boolean };
      where?: { [key: string]: any; };
    }

    let options: Options = {};

    if (params['select']) {
      options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
      delete params['select'];
    }

    options['where'] = {
      'id_adm_provinsi_layer_spasial': parseInt(params.id)
    }

    const data = await prisma.tb_adm_provinsi_layer_spasial.findFirst(options);

    return response.status(200).json({
      success: true,
      data: data
    });      
  }

  public async createOrUpdate({ request, response }: HttpContext) {
    let params = request.all()

    try {
      const result = await prisma.$transaction(async (prisma) => {
        if (params.id_adm_provinsi_layer_spasial) {
          // Jika `id` ada, lakukan operasi update
          const update = await prisma.tb_adm_provinsi_layer_spasial.update({
            where: { id_adm_provinsi_layer_spasial: params.id_adm_provinsi_layer_spasial },
            data: params,
          })

          return {
            status: 'success',
            message: 'Successfully Updated Data',
            data: update,
          }
        } else {
          console.log("Sync tb_adm_provinsi_layer_spasial id sequence",
            await prisma.$executeRaw`
            SELECT setval((
                SELECT PG_GET_SERIAL_SEQUENCE('"tb_adm_provinsi_layer_spasial"', 'id_adm_provinsi_layer_spasial')),
                (SELECT (MAX("id_adm_provinsi_layer_spasial") + 1) FROM "tb_adm_provinsi_layer_spasial"),
                false) FROM "tb_adm_provinsi_layer_spasial";
            `);

            if (Array.isArray(params)) {
              await prisma.tb_adm_provinsi_layer_spasial.deleteMany({
                where: {
                  id_adm_provinsi: params[0]['id_adm_provinsi'],
                },
              });
  
              for (const item of params) {
                await prisma.tb_adm_provinsi_layer_spasial.create({
                  data: item,
                });
              }
  
              return {
                status: 'success',
                message: 'Successfully Added Data',
                data: null,
              }
            } else {
              const save = await prisma.tb_adm_provinsi_layer_spasial.create({
                data: params,
              })
    
              return {
                status: 'success',
                message: 'Successfully Added Data',
                data: save,
              }
            }
        }
      })

      return response.json(result)

    } catch (error) {
      // Handle error jika ada kesalahan dalam transaksi
      return response.status(500).json({
        status: 'error',
        message: 'An error occurred while processing data',
        error: error.message,
      })
    }
  }

  public async deleteById({ request, params, response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_adm_provinsi_layer_spasial.delete({
        where: {
          id_adm_provinsi_layer_spasial: Id,
        },
      })

      return response.status(200).json({
        status: 'success',
        message: 'Success Delete Data',
        data: deletePost,
      })
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }  
  }
}