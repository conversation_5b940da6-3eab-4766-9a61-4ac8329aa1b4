import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../../helpers/model_helper.js';

import prisma from '../../lib/prisma.js';


// kawasan_layers_controller
export default class KawasanLayersController {
  private async schemaData() {
    const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
      id_kl: {column: 'id_kl', alias: 'id_kl', type: 'int'},
      id_kawasan_industri: {column: 'id_kawasan_industri', alias: 'id_kawasan_industri', type: 'int'},
      layeruid: {column: 'layeruid', alias: 'layeruid', type: 'string'},
      kategori: {column: 'kategori', alias: 'kategori', type: 'int'},
      keterangan: {column: 'keterangan', alias: 'keterangan', type: 'string'},
      is_active: {column: 'is_active', alias: 'is_active', type: 'int'},
    };
  
    const joins = {};
  
    const where = {};
  
    return {
      columns: columnMappings,
      join: joins,
      where: where,
    };
  }

  public async get({ request, params, response }: HttpContext) {
    const query = request.qs(); // Ini akan mengembalikan objek query string

    console.log(query); // { id_kawasan_industri: '201' }
    let isAll = false;

    interface Options {
      skip: number; // Keep it non-optional
      take: number;
      select?: { [key: string]: boolean };
    }

    if (query['all'] || query['all'] == 'true') {
      isAll = true
      delete query['all'];
    }

    const page = parseInt(request.input('page', 1));
    const perPage = parseInt(request.input('per_page', 10));
    delete query['page'];
    delete query['per_page'];

    let options: Options = {
      skip: (page - 1) * perPage,
      take: perPage,
    };

    if (isAll) {
      delete options['skip'];
      delete options['take'];
    }

    if (query['select']) {
      options['select'] = modelSelect((await this.schemaData()).columns, query['select']);
      delete query['select'];
    }

    options['where'] = modelWhereAnd((await this.schemaData()).columns, query);

    const data = await prisma.tb_kawasan_layers.findMany(options);

    if (isAll) {
      return response.status(200).json({
        success: true,
        data: data
      });      
    }

    const totalCount = await prisma.tb_kawasan_layers.count();
    
    return response.status(200).json({
      success: true,
      data: data,
      pagination: {
        page: page,
        per_page: perPage,
        total_count: totalCount,
        total_pages: Math.ceil(totalCount / perPage),
      },
    });
  }

  public async getById({ request, params, response }: HttpContext) {
    interface Options {
      select?: { [key: string]: boolean };
      where?: { [key: string]: any; };
    }

    let options: Options = {};

    if (params['select']) {
      options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
      delete params['select'];
    }

    options['where'] = {
      'id_kl': parseInt(params.id)
    }

    const data = await prisma.tb_kawasan_layers.findFirst(options);

    return response.status(200).json({
      success: true,
      data: data
    });      
  }

  public async createOrUpdate({ request, response }: HttpContext) {
    let params = request.all()

    try {
      const result = await prisma.$transaction(async (prisma) => {
        if (params.id_kl) {
          // Jika `id` ada, lakukan operasi update
          const update = await prisma.tb_kawasan_layers.update({
            where: { id_kl: params.id_kl },
            data: params,
          })

          return {
            status: 'success',
            message: 'Successfully Updated Data',
            data: update,
          }
        } else {
          console.log("Sync tb_kawasan_layers id sequence",
            await prisma.$executeRaw`
            SELECT setval((
                SELECT PG_GET_SERIAL_SEQUENCE('"tb_kawasan_layers"', 'id_kl')),
                (SELECT (MAX("id_kl") + 1) FROM "tb_kawasan_layers"),
                false) FROM "tb_kawasan_layers";
            `);
          
          if (Array.isArray(params)) {
            await prisma.tb_kawasan_layers.deleteMany({
              where: {
                id_kawasan_industri: params[0]['id_kawasan_industri'],
              },
            });

            for (const item of params) {
              await prisma.tb_kawasan_layers.create({
                data: item,
              });
            }

            return {
              status: 'success',
              message: 'Successfully Added Data',
              data: null,
            }
          } else {
            const save = await prisma.tb_kawasan_layers.create({
              data: params,
            })
  
            return {
              status: 'success',
              message: 'Successfully Added Data',
              data: save,
            }
          }
        }
      })

      return response.json(result)

    } catch (error) {
      // Handle error jika ada kesalahan dalam transaksi
      return response.status(500).json({
        status: 'error',
        message: 'An error occurred while processing data',
        error: error.message,
      })
    }
  }

  public async deleteById({ request, params, response }: HttpContext) {
    const Id = parseInt(params.id, 10)
    if (isNaN(Id)) {
      return response.status(400).send({ error: 'Invalid ID provided' })
    }

    try {
      const deletePost = await prisma.tb_kawasan_layers.delete({
        where: {
          id_kl: Id,
        },
      })

      return response.status(200).json({
        status: 'success',
        message: 'Success Delete Data',
        data: deletePost,
      })
    } catch (error) {
      return response.status(500).send({ error: 'Error deleting data' })
    }  
  }
}