import type { HttpContext } from '@adonisjs/core/http'

import { modelSelect, modelWhereAnd } from '../helpers/model_helper.js';

import uploadFile from '../helpers/file_uploader.js';

import prisma from '../lib/prisma.js';

export default class SektorDaerahInsentifController {
        private async schemaData() {
                const columnMappings: { [key: string]: { column: string; alias: string; type: string } } = {
                        id_sektor_daerah_insentif : { column: 'id_sektor_daerah_insentif', alias: 'id_sektor_daerah_insentif', type: 'int' },
                        id_sektor_daerah : { column: 'id_sektor_daerah', alias: 'id_sektor_daerah', type: 'int' },
                        nama : { column: 'nama', alias: 'nama', type: 'string' },
                        deskripsi : { column: 'deskripsi', alias: 'deskripsi', type: 'string' },
                        status : { column: 'status', alias: 'status', type: 'int' },
                };

                const joins = {};

                const where = {};

                return {
                        columns: columnMappings,
                        join: joins,
                        where: where,
                };
        }

        public async get({ request, params, response }: HttpContext) {
                let isAll = false;

                interface Options {
                        skip?: number; // Keep it non-optional
                        take?: number;
                        select?: { [key: string]: boolean };
                        where?: any;
                        include?: any;
                }


                if (request.input('all') || request.input('all') == 'true') {
                        isAll = true
                }

                const page = parseInt(request.input('page', 1));
                const perPage = parseInt(request.input('per_page', 10));

                const queryParams = { ...params, ...request.qs() };

                let options: Options = {
                        skip: (page - 1) * perPage,
                        take: perPage,
                }


                if (isAll) {
                        delete options['skip'];
                        delete options['take'];
                }

                // Handling select fields (if provided in query)
                if (queryParams['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, queryParams['select']);
                        delete queryParams['select'];  // Remove select from query params to avoid conflict
                }

                // Apply the where conditions
                options['where'] = modelWhereAnd((await this.schemaData()).columns, queryParams);

                try {
                        const data = await prisma.tb_sektor_daerah_insentif.findMany(options);

                        // If where conditions are applied and no data is found, return custom message
                        if (options['where'] && (!data || data.length === 0)) {
                                return response.status(404).json({
                                        success: false,
                                        message: "Data yang kamu cari tidak ada"
                                });
                        }
                        // Pagination
                        const totalCount = await prisma.tb_sektor_daerah_insentif.count({
                                where: options['where'],  // Make sure to pass the `where` to count query
                        });

                        if (isAll) {
                                return response.status(200).json({
                                        success: true,
                                        pagination: {
                                                page: page,
                                                per_page: totalCount,
                                                total_count: totalCount,
                                                total_pages: Math.ceil(totalCount / totalCount),
                                        },
                                        data: data,
                                });
                        }


                        return response.status(200).json({
                                success: true,
                                pagination: {
                                        page: page,
                                        per_page: perPage,
                                        total_count: totalCount,
                                        total_pages: Math.ceil(totalCount / perPage),
                                },
                                data: data,
                        });
                } catch (error) {
                        console.error(error);
                        return response.status(500).json({
                                success: false,
                                message: 'Internal Server Error',
                        });
                }
        }

        public async getById({ params, response }: HttpContext) {
                interface Options {
                        select?: { [key: string]: boolean };
                        where?: { [key: string]: any; };
                        include?: any
                }

                let options: Options = {};

                if (params['select']) {
                        options['select'] = modelSelect((await this.schemaData()).columns, params['select']);
                        delete params['select'];
                }

                options['where'] = {
                        'id_peluang_daerah': parseInt(params.id)
                }

                const data = await prisma.tb_sektor_daerah_insentif.findFirst(options);

                return response.status(200).json({
                        success: true,
                        data: data
                });
        }

        public async createOrUpdate({ request, response, params }: HttpContext) {
                let reqBody: any = request.all();
                const paramID = parseInt(params.id)
                try {

                        const result = await prisma.$transaction(async (prisma) => {
                                if (params.id) {
                                        // Jika `id` ada, lakukan operasi update
                                        const update = await prisma.tb_sektor_daerah_insentif.update({
                                                where: { id_sektor_daerah_insentif: paramID },
                                                data: reqBody,
                                        })

                                        await prisma.tb_sektor_daerah_insentif_tr.deleteMany({
                                                where: {
                                                        id_sektor_daerah_insentif: paramID
                                                }
                                        });

                                        console.log("Sync tb_sektor_daerah_insentif_tr id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_sektor_daerah_insentif_tr"', 'id_sektor_daerah_insentif_tr')),
                                                (SELECT (MAX("id_sektor_daerah_insentif_tr") + 1) FROM "tb_sektor_daerah_insentif_tr"),
                                                false) FROM "tb_sektor_daerah_insentif_tr";
                                         `);

                                        const data_sub_sektor_daerah_tr: any = {
                                                id_sektor_daerah_insentif: paramID,
                                                kd_bahasa: request.input("kd_bahasa") || 'en',
                                                deskripsi: request.input("deskripsi") || '-',
                                                nama: request.input("translate_nama") || ''
                                        }
                                        await prisma.tb_sektor_daerah_insentif_tr.create({ data: data_sub_sektor_daerah_tr });

                                        return {
                                                status: 'success',
                                                message: 'Successfully Updated Data',
                                                data: update,
                                        }
                                } else {
                                        console.log("Sync tb_sektor_daerah_insentif id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                        SELECT PG_GET_SERIAL_SEQUENCE('"tb_sektor_daerah_insentif"', 'id_sektor_daerah_insentif')),
                                                        (SELECT (MAX("id_sektor_daerah_insentif") + 1) FROM "tb_sektor_daerah_insentif"),
                                                        false) FROM "tb_sektor_daerah_insentif";
                                                `);
                                        const save = await prisma.tb_sektor_daerah_insentif.create({ data: reqBody })

                                        console.log("Sync tb_sektor_daerah_insentif_tr id sequence",
                                                await prisma.$executeRaw`
                                                SELECT setval((
                                                SELECT PG_GET_SERIAL_SEQUENCE('"tb_sektor_daerah_insentif_tr"', 'id_sektor_daerah_insentif_tr')),
                                                (SELECT (MAX("id_sektor_daerah_insentif_tr") + 1) FROM "tb_sektor_daerah_insentif_tr"),
                                                false) FROM "tb_sektor_daerah_insentif_tr";
                                         `);

                                        const data_sub_sektor_daerah_tr: any = {
                                                id_sektor_daerah_insentif: save.id_sektor_daerah_insentif,
                                                kd_bahasa: request.input("kd_bahasa") || 'en',
                                                deskripsi: request.input("deskripsi") || '-',
                                                nama: request.input("translate_nama") || ''
                                        }
                                        await prisma.tb_sektor_daerah_insentif_tr.create({ data: data_sub_sektor_daerah_tr });

                                        return {
                                                status: 'success',
                                                message: 'Successfully Added Data',
                                                data: save,
                                        }
                                }
                        })

                        return response.json(result)

                } catch (error) {
                        // Handle error jika ada kesalahan dalam transaksi
                        console.log(error)
                        return response.status(500).json({
                                status: 'error',
                                message: 'An error occurred while processing data',
                                error: error,
                        })
                }
        }

        public async deleteById({ params, response }: HttpContext) {
                const Id = parseInt(params.id, 10)
                if (isNaN(Id)) {
                        return response.status(400).send({ error: 'Invalid ID provided' })
                }

                try {
                        const deletePost = await prisma.tb_sektor_daerah_insentif.delete({
                                where: { id_sektor_daerah_insentif: Id, },
                        })

                        return response.status(200).json({
                                status: 'success',
                                message: 'Success Delete Data',
                                data: deletePost,
                        })
                } catch (error) {
                        return response.status(500).send({ error: 'Error deleting data' })
                }
        }

        public async getBySubSektorDaerah({ request, response }: HttpContext) {
                let params = request.qs();

                try {
                        const idSubSektorDaerah = parseInt(params['id_sub_sektor_daerah']);

                        const data = await prisma.$queryRaw<any[]>`
                        SELECT
                        tb_sektor_daerah_insentif.id_sektor_daerah,
                        tb_sektor_daerah_insentif.nama,
                        tb_sektor_daerah_insentif.id_sektor_daerah_insentif
                        FROM tb_sektor_daerah_insentif
                        JOIN tb_sub_sektor_daerah
                        ON tb_sub_sektor_daerah.id_sektor_daerah = tb_sektor_daerah_insentif.id_sektor_daerah
                        WHERE tb_sub_sektor_daerah.id_sub_sektor_daerah = ${idSubSektorDaerah};
                        `;

                        return response.status(200).json({
                                success: true,
                                message: 'Success Retrieve Data',
                                data: data
                        });
                } catch (error) {
                        console.error('Error executing query:', error);
                        throw new Error('Failed to fetch data');
                }
        }
}