import { BaseModel, column } from '@adonisjs/lucid/orm'

export default class IamUser extends BaseModel {
  @column({ isPrimary: true })
  declare user_uid: string

  @column()
  declare login_name: string

  @column()
  declare full_name: string

  @column()
  declare email: string

  @column()
  declare address: string | null

  @column()
  declare avatar: string | null

  @column()
  declare start_date: Date | null

  @column()
  declare end_date: Date | null

  @column()
  declare pwd_hash: string | null

  @column()
  declare notes: any | null

  static table = 'public.iam_users'
  static connection = 'gp'
}
