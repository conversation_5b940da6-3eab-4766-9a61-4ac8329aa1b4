import env from '#start/env'
import { createCipheriv, createDecipheriv, randomBytes, scryptSync } from 'crypto'

/**
 * Secure Credentials Manager
 * 
 * This class provides methods to securely handle credentials in the application.
 * It uses environment variables and encryption to avoid hardcoded credentials.
 */
export default class SecureCredentialsManager {
  private static readonly ENCRYPTION_KEY = env.get('APP_KEY', '')
  private static readonly ALGORITHM = 'aes-256-gcm'
  
  /**
   * Encrypt sensitive data
   * 
   * @param plaintext The data to encrypt
   * @returns Encrypted data in format: iv:authTag:encryptedData
   */
  public static encrypt(plaintext: string): string {
    try {
      // Generate a salt
      const salt = randomBytes(16)
      
      // Derive a key using scrypt
      const key = scryptSync(this.ENCRYPTION_KEY, salt, 32)
      
      // Generate an initialization vector
      const iv = randomBytes(16)
      
      // Create cipher
      const cipher = createCipheriv(this.ALGORITHM, key, iv)
      
      // Encrypt the data
      let encrypted = cipher.update(plaintext, 'utf8', 'hex')
      encrypted += cipher.final('hex')
      
      // Get the authentication tag
      const authTag = cipher.getAuthTag().toString('hex')
      
      // Return the encrypted data with IV, auth tag, and salt
      return `${iv.toString('hex')}:${authTag}:${salt.toString('hex')}:${encrypted}`
    } catch (error) {
      console.error('Encryption error:', error)
      throw new Error('Failed to encrypt data')
    }
  }
  
  /**
   * Decrypt sensitive data
   * 
   * @param ciphertext The encrypted data in format: iv:authTag:salt:encryptedData
   * @returns Decrypted data
   */
  public static decrypt(ciphertext: string): string {
    try {
      // Split the ciphertext into its components
      const [ivHex, authTagHex, saltHex, encryptedData] = ciphertext.split(':')
      
      // Convert from hex
      const iv = Buffer.from(ivHex, 'hex')
      const authTag = Buffer.from(authTagHex, 'hex')
      const salt = Buffer.from(saltHex, 'hex')
      
      // Derive the key using the same salt
      const key = scryptSync(this.ENCRYPTION_KEY, salt, 32)
      
      // Create decipher
      const decipher = createDecipheriv(this.ALGORITHM, key, iv)
      decipher.setAuthTag(authTag)
      
      // Decrypt the data
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8')
      decrypted += decipher.final('utf8')
      
      return decrypted
    } catch (error) {
      console.error('Decryption error:', error)
      throw new Error('Failed to decrypt data')
    }
  }
  
  /**
   * Get a credential from environment variables
   * 
   * @param key The environment variable name
   * @param defaultValue Optional default value
   * @returns The credential value
   */
  public static getCredential(key: string, defaultValue: string = ''): string {
    const value = env.get(key, defaultValue)
    
    // Warn if the credential is not set
    if (!value && !defaultValue) {
      console.warn(`Warning: Credential ${key} is not set in environment variables`)
    }
    
    return value
  }
  
  /**
   * Store a credential in a secure way (for runtime use only)
   * 
   * @param key The key to identify the credential
   * @param value The value to store
   * @returns Encrypted credential
   */
  public static storeCredential(key: string, value: string): string {
    // Log the key being stored (for debugging/auditing)
    console.debug(`Storing credential with key: ${key}`)
    
    // Check if the value is already encrypted
    if (value.includes(':')) {
      try {
        this.decrypt(value)
        return value // Already encrypted
      } catch (error) {
        // Not encrypted or invalid format, continue with encryption
      }
    }
    
    // Encrypt the value
    return this.encrypt(value)
  }
  
  /**
   * Check if a string might be a hardcoded credential
   * 
   * @param value The string to check
   * @param keyName Optional key name to check against common credential patterns
   * @returns True if the string might be a credential
   */
  public static isPotentialCredential(value: string, keyName?: string): boolean {
    // Check if the string matches common credential patterns
    const credentialPatterns = [
      /password/i,
      /secret/i,
      /apikey/i,
      /api_key/i,
      /token/i,
      /auth/i,
      /credential/i,
    ]
    
    // If keyName is provided, check if it matches credential patterns
    if (keyName) {
      return credentialPatterns.some(pattern => pattern.test(keyName) || pattern.test(value))
    }
    
    // Otherwise just check the value
    return credentialPatterns.some(pattern => pattern.test(value))
  }
}
