import fs from 'fs/promises'
import path from 'path'
import { promisify } from 'util'
import { exec as execCallback } from 'child_process'

const exec = promisify(execCallback)

/**
 * Helper class to check for hardcoded credentials in the codebase
 * This can be run as a pre-commit hook or during CI/CD
 */
export default class CredentialsChecker {
  // Patterns that might indicate hardcoded credentials
  private static readonly CREDENTIAL_PATTERNS = [
    /password\s*[:=]\s*['"][^'"]+['"]/i,
    /secret\s*[:=]\s*['"][^'"]+['"]/i,
    /apikey\s*[:=]\s*['"][^'"]+['"]/i,
    /api_key\s*[:=]\s*['"][^'"]+['"]/i,
    /token\s*[:=]\s*['"][^'"]+['"]/i,
    /auth\s*[:=]\s*['"][^'"]+['"]/i,
    /credential\s*[:=]\s*['"][^'"]+['"]/i,
    /pass\s*[:=]\s*['"][^'"]+['"]/i,
    /pwd\s*[:=]\s*['"][^'"]+['"]/i,
  ]

  // Files and directories to exclude
  private static readonly EXCLUDED_PATHS = [
    'node_modules',
    '.git',
    'dist',
    'build',
    'tmp',
    '.env.example',
    'package-lock.json',
  ]

  /**
   * Check if a file should be excluded from scanning
   */
  private static shouldExcludeFile(filePath: string): boolean {
    return this.EXCLUDED_PATHS.some(excluded => 
      filePath.includes(`/${excluded}/`) || filePath.endsWith(`/${excluded}`)
    )
  }

  /**
   * Check if a file might contain hardcoded credentials
   */
  private static async checkFile(filePath: string): Promise<{ file: string; matches: string[] }> {
    try {
      // Skip binary files and excluded paths
      if (this.shouldExcludeFile(filePath)) {
        return { file: filePath, matches: [] }
      }

      // Read file content
      const content = await fs.readFile(filePath, 'utf-8')
      const matches: string[] = []

      // Check for credential patterns
      this.CREDENTIAL_PATTERNS.forEach(pattern => {
        const match = content.match(pattern)
        if (match) {
          matches.push(match[0])
        }
      })

      return { file: filePath, matches }
    } catch (error) {
      console.error(`Error checking file ${filePath}:`, error)
      return { file: filePath, matches: [] }
    }
  }

  /**
   * Recursively scan a directory for files
   */
  private static async scanDirectory(dir: string): Promise<string[]> {
    const files: string[] = []
    
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true })
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name)
        
        if (this.shouldExcludeFile(fullPath)) {
          continue
        }
        
        if (entry.isDirectory()) {
          const subFiles = await this.scanDirectory(fullPath)
          files.push(...subFiles)
        } else {
          files.push(fullPath)
        }
      }
    } catch (error) {
      console.error(`Error scanning directory ${dir}:`, error)
    }
    
    return files
  }

  /**
   * Run the credentials check on the entire codebase
   */
  public static async checkCodebase(rootDir: string): Promise<{ file: string; matches: string[] }[]> {
    console.log('Checking for hardcoded credentials in the codebase...')
    
    // Get all files in the codebase
    const files = await this.scanDirectory(rootDir)
    
    // Check each file for credentials
    const results = await Promise.all(files.map(file => this.checkFile(file)))
    
    // Filter out files with no matches
    return results.filter(result => result.matches.length > 0)
  }

  /**
   * Check for hardcoded credentials in git diff
   * Useful for pre-commit hooks
   */
  public static async checkGitDiff(): Promise<{ file: string; matches: string[] }[]> {
    try {
      // Get the staged files
      const { stdout } = await exec('git diff --cached --name-only')
      const stagedFiles = stdout.split('\n').filter(Boolean)
      
      // Check each staged file
      const results = await Promise.all(
        stagedFiles.map(async file => {
          // Get the diff content
          const { stdout: diffContent } = await exec(`git diff --cached -- "${file}"`)
          
          const matches: string[] = []
          
          // Check for credential patterns in the diff
          this.CREDENTIAL_PATTERNS.forEach(pattern => {
            const match = diffContent.match(pattern)
            if (match) {
              matches.push(match[0])
            }
          })
          
          return { file, matches }
        })
      )
      
      // Filter out files with no matches
      return results.filter(result => result.matches.length > 0)
    } catch (error) {
      console.error('Error checking git diff:', error)
      return []
    }
  }
}
