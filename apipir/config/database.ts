import env from '#start/env'
import { defineConfig } from '@adonisjs/lucid'

const dbConfig = defineConfig({
  connection: 'postgres',
  connections: {
    postgres: {
      client: 'pg',
      connection: {
        host: env.get('DB_HOST'),
        port: env.get('DB_PORT'),
        user: env.get('DB_USER'),
        password: env.get('DB_PASSWORD'),
        database: env.get('DB_DATABASE'),
        statement_timeout: 300000,
      },
      migrations: {
        naturalSort: true,
        paths: ['database/migrations'],
      },
      pool: {
        afterCreate: async (conn, done) => {
          try {
            await conn.query("SET TIME ZONE 'America/New_York';");
            done(null, conn);
          } catch (error) {
            done(error, conn);
          }
        },
      },
    },
    gp: {
      client: 'pg',
      connection: {
        host: env.get('DB_GP_HOST'),
        port: env.get('DB_GP_PORT'),
        user: env.get('DB_GP_USER'),
        password: env.get('DB_GP_PASSWORD'),
        database: env.get('DB_GP_DATABASE'),
      },
    },
  },
})

export default dbConfig