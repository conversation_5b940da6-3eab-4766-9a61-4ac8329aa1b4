services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    image: sismonp3tgai-app
    container_name: sismonp3tgai-app
    restart: unless-stopped
    # working_dir: /var/www/html/sismonp3tgai
    volumes:
      - .:/var/www/html/sismonp3tgai
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - p3tgai-network

  webserver:
    image: nginx:1.18-alpine
    container_name: sismonp3tgai-nginx
    restart: unless-stopped
    ports:
      - "8081:80"
    volumes:
      - .:/var/www/html/sismonp3tgai
      - ./docker/nginx/conf.d/:/etc/nginx/conf.d/
      - ./docker/nginx/conf.d/app.conf:/etc/nginx/conf.d/default.conf
      - ./error_pages:/var/www/html
    networks:
      - p3tgai-network
    depends_on:
      - app

  # db:
  #   image: postgres:13
  #   container_name: sismonp3tgai-postgres
  #   restart: unless-stopped
  #   environment:
  #     - POSTGRES_DB=sismonp3tgai
  #     - POSTGRES_USER=postgres
  #     - POSTGRES_PASSWORD=your_password_here
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - pgdata:/var/lib/postgresql/data
  #   networks:
  #     - app-network

networks:
  p3tgai-network:
    driver: bridge

# volumes:
#   pgdata:
