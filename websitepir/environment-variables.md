# Required Environment Variables

Create a `.env.local` file in your project root with the following variables:

```bash
# API Configuration
NEXT_PUBLIC_BASE_URL=https://your-api-backend.com
NEXT_PUBLIC_API_BASE_URL_GEO=https://your-geo-api.com
NEXT_PUBLIC_BASE_URL2=https://your-frontend.com
NEXT_PUBLIC_BASE_URL_FE=https://your-frontend.com

# Geographic API Credentials (CRITICAL: Replace with actual credentials)
NEXT_PUBLIC_GEO_USERNAME=your_NEXT_PUBLIC_GEO_USERNAME
NEXT_PUBLIC_GEO_PASSWORD=your_secure_NEXT_PUBLIC_GEO_PASSWORD

# Geoportal Credentials
NEXT_PUBLIC_UNAME_GEOPORTAL=your_geoportal_username
NEXT_PUBLIC_PASS_GEOPORTAL=your_geoportal_password

# Role UIDs
NEXT_PUBLIC_ROLE_UID_1=role_uid_1
NEXT_PUBLIC_ROLE_UID_2=role_uid_2
NEXT_PUBLIC_ROLE_UID_3=role_uid_3

# Encryption Key (32 character hex string)
NEXT_PUBLIC_ENCRYPTION_KEY=your_32_character_hex_encryption_key

# Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS=GA_MEASUREMENT_ID

# Application Path (can be empty for root path)
NEXT_PUBLIC_GEOPORTAL_PATH=

# Internal API URL
API_URL=http://localhost:3000/api

# Server Secret (server-side only)
MY_SECRET=your_server_secret

# App2 URL
APP2_URL=https://your-app2.com
```

## Security Notes

1. **Never commit the `.env.local` file to version control**
2. **Use strong, unique passwords for all credentials**
3. **Rotate credentials regularly**
4. **Use HTTPS URLs in production**
5. **Generate a secure 32-character hex key for encryption**

## Generating Secure Values

### Encryption Key
```bash
# Generate a secure 32-character hex key
node -e "console.log(require('crypto').randomBytes(16).toString('hex'))"
```

### Strong Passwords
Use a password manager to generate strong, unique passwords for each service. 