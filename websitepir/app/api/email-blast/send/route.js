import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'

export async function POST(request) {
  try {
    // Get token from cookies instead of next-auth
    const cookieStore = cookies()
    const token = cookieStore.get('token')?.value
    
    if (!token) {
      return NextResponse.json(
        { status: 'error', message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const payload = await request.json()

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/email-blast/send`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payload),
    })

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error sending email blast:', error)
    return NextResponse.json(
      { status: 'error', message: 'Failed to send email blast' },
      { status: 500 }
    )
  }
}
