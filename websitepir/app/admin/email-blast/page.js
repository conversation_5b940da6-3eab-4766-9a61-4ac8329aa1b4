'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { format } from 'date-fns'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Loader2 } from 'lucide-react'
import { AlertCircle, CheckCircle, Info } from 'lucide-react'
import AdminLayout from '../AdminLayout'
import Api from '@/utils/Api'
import ApiForm from '@/utils/ApiForm'

// Mock session for development
const mockSession = { accessToken: 'mock-token' };

export default function EmailBlast() {
  const session = mockSession; // Gunakan session yang sudah didefinisikan di luar
  const [users, setUsers] = useState([])
  const [logs, setLogs] = useState([])
  const [loading, setLoading] = useState(false)
  const [sendingEmail, setSendingEmail] = useState(false)
  const [selectedUsers, setSelectedUsers] = useState([])
  const [selectAll, setSelectAll] = useState(false)
  const [emailContent, setEmailContent] = useState('')
  const [emailSubject, setEmailSubject] = useState('')
  const [activeTab, setActiveTab] = useState('compose')

  // Fetch users with emails
  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true)
      try {
        const response = await Api.get('/api/email-blast/users');
        
        if (response?.status === "success") {
          console.log('Users loaded successfully')
          setUsers(response?.data || [])
        } else {
          console.log('Failed to load users')
        }
      } catch (error) {
        console.error('Error fetching users:', error)
        console.log('Error loading users')
      } finally {
        setLoading(false)
      }
    }

    fetchUsers()
  }, []) // Hapus session dan loading dari dependencies

  // Fetch email blast logs
  useEffect(() => {
    const fetchLogs = async () => {
      try {
        const response = await Api.get('/api/email-blast/logs');
        
        if (response?.status === "success") {
          console.log('Email logs loaded successfully')
          setLogs(response?.data || [])
        } else {
          console.log('Failed to load email logs')
        }
      } catch (error) {
        console.error('Error fetching logs:', error)
        console.log('Error loading email logs')
      }
    }

    if (activeTab === 'logs') {
      fetchLogs()
    }
  }, [activeTab]) // Hapus session dan loading dari dependencies

  // Handle select all users
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedUsers([])
    } else {
      setSelectedUsers(users.map(user => user.id))
    }
    setSelectAll(!selectAll)
  }

  // Handle individual user selection
  const handleSelectUser = (userId) => {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter(id => id !== userId))
      setSelectAll(false)
    } else {
      setSelectedUsers([...selectedUsers, userId])
      if (selectedUsers.length + 1 === users.length) {
        setSelectAll(true)
      }
    }
  }

  // Send email blast
  const handleSendEmailBlast = async () => {
    if (!emailSubject.trim()) {
      console.log('Email subject is required')
      return
    }

    if (!emailContent.trim()) {
      console.log('Email content is required')
      return
    }

    if (selectedUsers.length === 0) {
      console.log('Please select at least one recipient')
      return
    }

    setSendingEmail(true)
    try {
      // Log data yang akan dikirim untuk debugging
      console.log('Sending email to:', selectedUsers)
      
      const response = await ApiForm.post('/api/email-blast/send', {
        subject: emailSubject,
        content: emailContent,
        recipients: selectedUsers,
      });
      
      if (response?.status === 200) {
        console.log('Email blast sent successfully')
        // Reset form
        setEmailSubject('')
        setEmailContent('')
        setSelectedUsers([])
        setSelectAll(false)
        // Switch to logs tab
        setActiveTab('logs')
      } else {
        console.log(response?.message || 'Failed to send email blast')
      }
    } catch (error) {
      console.error('Error sending email blast:', error)
      console.log('Error sending email blast')
    } finally {
      setSendingEmail(false)
    }
  }

  return (
    <AdminLayout>
        <section className="title-section">
        <div className="title-section-name">
          Email Blast</div>
        <div className="container mx-auto py-6">
          <h1 className="text-3xl font-bold mb-6">Email Blast</h1>

          <div className="mb-4">
            <button onClick={() => setActiveTab('compose')}>Compose Email</button>
            <button onClick={() => setActiveTab('logs')}>Email Logs</button>
          </div>

          {activeTab === 'compose' ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle>Compose Email</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="subject">Subject</Label>
                      <Input
                        id="subject"
                        placeholder="Email subject"
                        value={emailSubject}
                        onChange={(e) => setEmailSubject(e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="content">Content</Label>
                      <Textarea
                        id="content"
                        placeholder="Email content"
                        className="min-h-[200px]"
                        value={emailContent}
                        onChange={(e) => setEmailContent(e.target.value)}
                      />
                      {/* <p className="text-sm text-gray-500 mt-2">
                        You can use {'{name}'} as a placeholder for the recipient's name.
                      </p> */}
                    </div>
                    <Button 
                      onClick={handleSendEmailBlast} 
                      disabled={sendingEmail || !emailSubject || !emailContent}
                      className="w-full"
                    >
                      {sendingEmail ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Sending...
                        </>
                      ) : (
                        'Send Email Blast'
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recipients</CardTitle>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="flex justify-center items-center h-40">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id="selectAll" 
                          checked={selectAll}
                          onCheckedChange={handleSelectAll}
                        />
                        <Label htmlFor="selectAll">Select All</Label>
                      </div>
                      <div className="max-h-[400px] overflow-y-auto">
                        {users.length > 0 ? (
                          users.map((user) => (
                            <div key={user.id} className="flex items-center space-x-2 py-2 border-b">
                              <Checkbox 
                                id={`user-${user.id}`}
                                checked={selectedUsers.includes(user.id)}
                                onCheckedChange={() => handleSelectUser(user.id)}
                              />
                              <Label htmlFor={`user-${user.id}`} className="flex-1">
                                <div>{user.full_name}</div>
                                <div className="text-sm text-gray-500">{user.email}</div>
                              </Label>
                            </div>
                          ))
                        ) : (
                          <div>
                            <Info className="h-4 w-4" />
                            <div>No users found</div>
                            <div>No users with email addresses were found.</div>
                          </div>
                        )}
                      </div>
                      <div className="text-sm text-gray-500">
                        {selectedUsers.length} of {users.length} users selected
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Email Blast Logs</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Subject</TableHead>
                      <TableHead>Recipients</TableHead>
                      <TableHead>Sent At</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {logs.length > 0 ? (
                      logs.map((log) => (
                        <TableRow key={log.id}>
                          <TableCell>{log.subject}</TableCell>
                          <TableCell>{log.total_recipients}</TableCell>
                          <TableCell>
                            {format(new Date(log.sent_at), 'PPpp')}
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={3} className="text-center py-4">
                          No email blast logs found
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </div>
        </section>
    </AdminLayout>
  )
}
