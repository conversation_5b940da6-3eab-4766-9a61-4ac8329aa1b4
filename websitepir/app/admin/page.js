"use client";

import AdminLayout from "./AdminLayout";
import Dashboard1 from "./Components/admin/dashboard1";
import Dashboard2 from "./Components/admin/dashboard2";
import DetailPengguna from "./Components/admin/detail-pengguna";
import FilterDateForm from "./Components/admin/filter-date-form";
import Daerah from "./Components/admin/pending/daerah";
import Kabupaten from "./Components/admin/pending/kabupaten";
import Kantor from "./Components/admin/pending/kantor";
import Nasional from "./Components/admin/pending/nasional";
import Provinsi from "./Components/admin/pending/provinsi";
import Sarpras from "./Components/admin/pending/sarpras";
import PenggunaTable from "./Components/admin/pengguna-table";
import StatusDataKab from "./Components/admin/status-data-kab";
import StatusDataProv from "./Components/admin/status-data-prov";
import PIDTable from "./Components/histori/PIDTable";
import PeluangTable from "./Components/histori/PeluangTable";
import VisitorCard from "./Components/histori/VisitorCard";
import VisitorCard2 from "./Components/histori/VisitorCard2";
import VisitorTable from "./Components/histori/VisitorTable";
import { Button } from "@/components/ui/button";
import useSelectedDataStore from "@/store/cms/SelectedDataStore";
import useVisitorStore from "@/store/useVisitorStore";
import { getTokenUserFromLocalStorage } from "@/utils/TokenManager";
import { getCookie } from "cookies-next";
import { ChartColumnBig, Clock, Clock4, History } from "lucide-react";
import { useRouter } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";
import useAuthStore from "@/store/AuthStore";

function Admin() {
  const router = useRouter();
  const [isLogin, setIsLogin] = useState();
  const [filters, setFilters] = useState({ startDate: "", endDate: "" });
  const [activeTab, setActiveTab] = useState("laporan");
  const { visitors, fetchVisitors, loading } = useVisitorStore();
  const [visibleTable, setVisibleTable] = useState(false);
  
  // Mengambil fungsi getMe dan state user dari AuthStore
  const { user, getMe, roleId, id_adm_kabkot, id_adm_provinsi, id_kawasan } = useAuthStore();

  const detailSectionRef = useRef(null);

  const {
    selectedData,
    selectedUser,
    shouldScrollToDetail,
    setShouldScrollToDetail,
  } = useSelectedDataStore();

  useEffect(() => {
    const token = getTokenUserFromLocalStorage();
    if (token) {
      setIsLogin(true);
      // Memanggil getMe untuk mendapatkan data user jika token ada
      getMe();
    } else {
      router.push("/admin/auth/login");
    }
  }, [router, getMe]);

  // Menampilkan data user ke console untuk debugging
  useEffect(() => {
    if (user) {
      console.log("User data:", user);
      console.log("Role ID:", roleId);
      console.log("ID Kabupaten/Kota:", id_adm_kabkot);
      console.log("ID Provinsi:", id_adm_provinsi);
      console.log("ID Kawasan:", id_kawasan);
    }
  }, [user, roleId, id_adm_kabkot, id_adm_provinsi, id_kawasan]);

  useEffect(() => {
    if (shouldScrollToDetail && detailSectionRef.current && selectedData) {
      setTimeout(() => {
        detailSectionRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });

        setShouldScrollToDetail(false);
      }, 100);
    }
  }, [shouldScrollToDetail, selectedData, setShouldScrollToDetail]);

  useEffect(() => {
    fetchVisitors();
  }, [fetchVisitors]);

  // Toggle tampil/hidden VisitorTable
  const toggleTable = () => {
    setVisibleTable((prev) => !prev);
    router.push("/histori/pengunjung");
  };

  const handleDataChange = (startDate, endDate) => {
    setFilters({ startDate, endDate });
  };

  // Tambahkan loading state agar render tetap konsisten
  if (isLogin === null) {
    return <div className="text-center py-10">Loading...</div>;
  }

  // Function to determine which content to show based on active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case "laporan":
        return (
          <>
            <section className="title-section">
              <FilterDateForm onDataChange={handleDataChange} />
              <Dashboard1
                startDate={filters.startDate}
                endDate={filters.endDate}
              />
            </section>
            <section className="title-section">
              <PenggunaTable user={user}/>
            </section>

            {selectedData && (
              <section className="title-section" ref={detailSectionRef}>
                <DetailPengguna data={selectedData} user={selectedUser} />
              </section>
            )}

            <section className="title-section">
              <div className="title-section-name">
                {`Status Data Infrastruktur Tahun ${new Date().getFullYear()}`}
              </div>
              <Dashboard2 />
            </section>

            <section className="title-section">
              <div className="title-section-name">{`Status Data Tahun Provinsi`}</div>
              <StatusDataProv />
            </section>

            <section className="title-section">
              <div className="title-section-name">
                {`Status Data Tahun Kabupaten/Kota`}
              </div>
              <StatusDataKab />
            </section>
          </>
        );
      case "pending":
        return (
          <>
            <section className="title-section">
              <div className="title-section-name">Sarana dan Prasarana</div>
              <Sarpras />
            </section>
            <section className="title-section">
              <div className="title-section-name">Demografi Provinsi</div>
              <Provinsi/>
            </section>
            <section className="title-section">
              <div className="title-section-name">Demografi Kabupaten</div>
              <Kabupaten/>
            </section>
            <section className="title-section">
              <div className="title-section-name"></div>
              <Kantor/>
            </section>
            <section className="title-section">
              <div className="title-section-name">
                Sektor Nasional, Sub Sektor Nasional, Komoditi Nasional, PDB
                Sektor Nasional, Insentif Sektor Nasional
              </div>
              <Nasional/>
            </section>
            <section className="title-section">
              <div className="title-section-name">
                Sektor Daerah, Sub Sektor Daerah, Komoditi Daerah, PDRB Sektor
                Daerah, Insentif Sektor Daerah, Peluang Daerah
              </div>
              <Daerah/>
            </section>
          </>
        );
      case "history":
        return (
          <>
            <section className="title-section">
              <div className="title-section-name bg-green-600">
                Data Pengunjung
              </div>
              {loading ? (
                <p>Loading...</p>
              ) : (
                <div className="flex flex-wrap gap-4 justify-center items-center">
                  {visitors.map((visitor) => (
                    <VisitorCard2
                      key={visitor.id_halaman_pengunjung}
                      id={visitor.id_halaman_pengunjung}
                      nama={visitor.nama}
                      jumlah={visitor.jumlah}
                      isVisible={visibleTable}
                      onToggle={toggleTable}
                    />
                  ))}
                </div>
              )}
              {/* {visibleTable && <VisitorTable />}
            <VisitorTable /> */}
            </section>
            <section className="title-section">
              <div className="title-section-name bg-green-600">
                Data Download Peluang Daerah
              </div>

              <PIDTable />
            </section>

            <section className="title-section">
              <div className="title-section-name bg-green-600">
                Data Info Pengunjung Download PPI
              </div>
              <PeluangTable />
            </section>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <AdminLayout>
      <section className="title-section">
        <div className="title-section-name">Dashboard</div>

        {/* Tab Navigation Buttons */}
        <div className="flex gap-2 mt-4 mb-6">
          <Button
            onClick={() => setActiveTab("laporan")}
            className={`px-4 py-2 rounded-md font-medium ${
              activeTab === "laporan"
                ? "bg-blue-600 text-white"
                : "bg-white text-gray-700 hover:bg-gray-300"
            } transition-colors duration-200`}
          >
            <ChartColumnBig className="mr-1 h-4 w-4" /> Laporan
          </Button>

          <Button
            onClick={() => setActiveTab("pending")}
            className={`px-4 py-2 rounded-md font-medium ${
              activeTab === "pending"
                ? "bg-blue-600 text-white"
                : "bg-white text-gray-700 hover:bg-gray-300"
            } transition-colors duration-200`}
          >
            <Clock4 className="mr-1 h-4 w-4" /> Status Data Pending
          </Button>

          <Button
            onClick={() => setActiveTab("history")}
            className={`px-4 py-2 rounded-md font-medium ${
              activeTab === "history"
                ? "bg-blue-600 text-white"
                : "bg-white text-gray-700 hover:bg-gray-300"
            } transition-colors duration-200`}
          >
            <History className="mr-1 h-4 w-4" /> History
          </Button>
        </div>
      </section>

      {/* Render content based on active tab */}
      {renderTabContent()}
    </AdminLayout>
  );
}

export default Admin;
