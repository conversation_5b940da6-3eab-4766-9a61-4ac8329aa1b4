'use client'
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  getReferensiService
} from "@/services/AllService";
import { yupResolver } from "@hookform/resolvers/yup";
import { useEffect, useState } from 'react';
import { useForm } from "react-hook-form";
import * as Yup from 'yup';
import VirtualizedSelect from '../../../Components/VirtualizedSelect';
import { PostDataService } from '@/services/GetPostPatchDeleteService';
import <PERSON><PERSON><PERSON><PERSON> from '../../../Components/SweetAlert';
import MapView from '@/app/user/daerah/components/MapView/MapView';
import FormBandara from './_components/FormBandara';
import useAuthStore from "@/store/AuthStore";

// Schema validasi menggunakan Yup
const validationSchema = Yup.object({
  sumberData: Yup.object().required("Sumber data wajib diisi"),
  nama: Yup.string().required("Nama bandara wajib diisi"),
  kodeIata: Yup.string().required("Kode IATA wajib diisi"),
  alamat: Yup.string().required("Alamat wajib diisi"),
  provinsi: Yup.object(),
  kabupaten: Yup.object(),
  // jarakDariIbuKota: Yup.number(),
  kelas: Yup.string().required("Kelas bandara wajib diisi"),
  kategori: Yup.string().required("Kategori bandara wajib diisi"),
  telepon: Yup.string().required("No. Telepon wajib diisi"),
  fax: Yup.string().required("No. Fax wajib diisi"),
  maskapai: Yup.string(),
  tipePesawat: Yup.string(),
  website: Yup.string().url().required("URL website wajib diisi"),
  zonaWaktu: Yup.string().required("Zona waktu wajib diisi"),
  jamOperasionalFrom: Yup.string().required("Jam operasional wajib diisi"),
  jamOperasionalTo: Yup.string().required("Jam operasional wajib diisi"),
  keterangan: Yup.string().required("Keterangan wajib diisi"),
  lat: Yup.number(),
  lon: Yup.number(),
});

const INITIAL_VALUES = {
  sumberData: "",
  nama: "",
  kodeIata: "",
  alamat: "",
  provinsi: "",
  kabupaten: "",
  jarakDariIbuKota: 0,
  kelas: "",
  kategori: "",
  telepon: "",
  fax: "",
  maskapai: "",
  tipePesawat: "",
  website: "",
  zonaWaktu: "",
  jamOperasionalFrom: "",
  jamOperasionalTo: "",
  keterangan: "",
  lat: 0,
  lon: 0
};

const CreateBandara = ({onDataChange}) => {
  const [isLoading, setIsLoading] = useState(true); // Tambahkan state untuk loading
  const [open, setOpen] = useState(false);
  const [openSumberData, setOpenSumberData] = useState(false);
  const [openKabupaten, setOpenKabupaten] = useState(false);
  const [openProvinsi, setOpenProvinsi] = useState(false);
  const [allOption, setAllOption] = useState({
    sumberDataOptions: [],
    provinsiOptions: [],
    kabkotOptions: [],
    kelasOptions: [],
    bandaraKategoriOptions: [],
  });
  const [filterKabupaten, setFilterKabupaten] = useState(false)

  const form = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: INITIAL_VALUES,
  });
  const { control, handleSubmit, setValue, watch, formState: { errors },reset } = form;

  const onSubmit = async (data) => {

    // Handle form submission

    const [hoursFrom, minutesFrom] = data.jamOperasionalFrom.split(":").map(Number); 
    const [hoursTo, minutesTo] = data.jamOperasionalTo.split(":").map(Number)

    const jamAwal = new Date();
    const jamAkhir = new Date();
    jamAwal.setHours(hoursFrom, minutesFrom, 0);
    jamAkhir.setHours(hoursTo, minutesTo, 0);

    console.log('jam', jamAwal, jamAkhir, hoursFrom, minutesFrom, hoursTo, minutesTo);
    const body = {
      id_kategori_infrastruktur: 1,
      id_adm_kabkot: data.kabupaten.id_adm_kabkot,
      id_sumber_data: data.sumberData.id_sumber_data,
      id_kelas: Number(data.kelas),
      id_kategori: Number(data.kategori),
      nama: data.nama,
      keterangan: data.keterangan,
      jarak_ibu_kota_provinsi: data.jarakDariIbuKota,
      iata: data.kodeIata,
      alamat: data.alamat,
      no_telp: data.telepon,
      no_fax: data.fax,
      url_web: data.website,
      jam_operasional_awal: jamAwal.toISOString(),
      jam_operasional_akhir: jamAkhir.toISOString(),
      id_zona_waktu: Number(data.zonaWaktu),
      jenis_pesawat: data.tipePesawat,
      maskapai: data.maskapai,
      lon: data.lon,
      lat: data.lat,
      status: 0,
      is_ikn: null,
      kd_bahasa: "ID",
      tr_nama: "test",
      tr_keterangan: "test"
    }
    try {
      setOpen(false);
      await PostDataService({ url: '/admin/sarpras_bandara/create', body })
      SweetAlert.success("Success", "Berhasil menambahkan data", () => {
        // window.location.reload();
        reset(INITIAL_VALUES);
        onDataChange?.(); 
      });
    } catch (error) {
      console.error(error)
    }
  }

  const { user, roleId } = useAuthStore();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const [sumberDataRes, provinsiRes, kelasRes, kategoriRes] = await Promise.all([
          getReferensiService('vw_sumber_data_id'),
          getReferensiService('tb_adm_provinsi'),
          getReferensiService("tb_bandara_kelas"),
          getReferensiService("tb_bandara_kategori"),
        ]);

        // Filter provinsi based on role
        let provinsiData = provinsiRes?.data;
        if (roleId === 14 && user?.pic_prov_arr?.length > 0) {
          const picProvIds = user.pic_prov_arr;
          provinsiData = provinsiData.filter(item => picProvIds.includes(item.id_adm_provinsi));
        }
        if (roleId === 3 && user?.id_adm_provinsi) {
          provinsiData = provinsiData.filter(item => item.id_adm_provinsi === user.id_adm_provinsi);
        }
        if (roleId === 4 && user?.id_adm_kabkot) {
          provinsiData = provinsiData.filter(item => item.id_adm_provinsi === user.id_adm_kabkot.slice(0, 2));
        }

        setAllOption({
          sumberDataOptions: sumberDataRes?.data || [],
          provinsiOptions: provinsiData || [],
          kelasOptions: kelasRes?.data || [],
          bandaraKategoriOptions: kategoriRes?.data || [],
          kabkotOptions: [],
        });
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        setIsLoading(false);
      }
    };

    fetchData();
  }, [user, roleId]); // Tambahkan user dan roleId sebagai dependencies

  return (
    <>
      <Dialog 
        open={open} 
        onOpenChange={(op) => {
          setOpen(op);
          form.reset();
        }}>
        <DialogTrigger asChild>
          <Button>Tambah Data Bandara</Button>
        </DialogTrigger>
        <DialogContent className="lg:max-w-screen-lg max-w-2xl  max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Form Bandara</DialogTitle>
          </DialogHeader>
          <FormBandara 
            mode="create" 
            onSubmit={handleSubmit(onSubmit)}
            setValue={setValue}
            watch={watch} 
            control={control} 
            form={form}
            isLoading={isLoading}
            openSumberData={openSumberData}
            allOption={allOption}
            setOpenSumberData={setOpenSumberData}
            openProvinsi={openProvinsi}
            setOpenProvinsi={setOpenProvinsi}
            openKabupaten={openKabupaten}
            setOpenKabupaten={setOpenKabupaten}
            filterKabupaten={filterKabupaten}
            setFilterKabupaten={setFilterKabupaten} 
          />
      
        </DialogContent>
      </Dialog>
    </>
  )
}

export default CreateBandara;