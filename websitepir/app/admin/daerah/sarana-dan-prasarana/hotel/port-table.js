"use client";

import <PERSON><PERSON>lert from "../../../Components/SweetAlert";
import DeleteConfirm from "../../../Components/delete-confirm";
import UpdateHotel from "./UpdateHotel";
import PaginationStyled from "@/components/PaginationStyled";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import StatusButtonFrom from "@/components/ui/StatusButtonFrom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { getReferensiService } from "@/services/AllService";
import { deleteDataService } from "@/services/GetPostPatchDeleteService";
import { fetchTableData } from "@/services/ReactQuery";
import { APICORS } from "@/utils/Api";
import { useQuery } from "@tanstack/react-query";
import { getCookie } from "cookies-next";
import {
  Edit,
  Trash2,
  MoreVertical,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useDebounce } from "use-debounce";
import { Copy, Download, FileSpreadsheet, FileText } from 'lucide-react';
import { saveAs } from "file-saver";
import { utils, write } from "xlsx";
import { CopyToClipboard } from "react-copy-to-clipboard";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import useAuthStore from '@/store/AuthStore';
import { getTokenUserFromLocalStorage } from "@/utils/TokenManager";

export default function PortTable(
  {
    status,
    handleEdit,
    refreshTrigger
  }
) {
  const [page, setPage] = useState(1);
  const [entriesPerPage, setEntriesPerPage] = useState("10");
  const [searchQuery, setSearchQuery] = useState("");
  const [expandedRow, setExpandedRow] = useState(null);
  const [debouncedSearchQuery] = useDebounce(searchQuery, 500);

  const [sortColumn, setSortColumn] = useState(null);
  const [sortOrder, setSortOrder] = useState(null);

  const [localRefreshTrigger, setLocalRefreshTrigger] = useState(0);
  const [combinedTrigger, setCombinedTrigger] = useState(refreshTrigger);

  useEffect(() => {
    setCombinedTrigger(refreshTrigger + localRefreshTrigger);
  }, [refreshTrigger, localRefreshTrigger]);

  // Generic status change handler
  const handleStatusChange = () => {
    setLocalRefreshTrigger((prev) => prev + 1);
    // if (onStatusChange) {
    //   onStatusChange();
    // }
  };

  const { user, isLoading: userLoading } = useAuthStore();

  const statusService = async (body, id) => {
    const token = getTokenUserFromLocalStorage();
    return await APICORS.patch(`/admin/sarpras_hotel/toggle_status/${id}`, body, {
      headers: {
        Authorization: `Bearer ${token}`, // Include the token in the Authorization header
      },
    });
  }

  useEffect(() => {
    setPage(1);
  }, [debouncedSearchQuery]);

  const {
    isLoading,
    error,
    data: res,
    refetch,
  } = useQuery({
    queryKey: ["bandara", page, entriesPerPage, debouncedSearchQuery, combinedTrigger,
      sortColumn,
      sortOrder,],
    queryFn: () =>
      fetchTableData({
        url: "/admin/sarpras_hotel/lists",
        params: {
          page,
          per_page: Number(entriesPerPage),
          q: debouncedSearchQuery,
          id_adm_provinsi: user?.id_adm_provinsi,
          id_adm_kabkot: user?.id_adm_kabkot,
          status: status ?? undefined,
          ...(user?.roleId === 14 && { pic: user.id }),
          ...(sortColumn && {
            order: sortColumn,
            by: sortOrder || "asc",
          }),
        },
      }),
    keepPreviousData: true,
    enabled: !userLoading
  });

  const handleSort = (column) => {
    // Jika kolom sama
    if (sortColumn === column) {
      if (sortOrder === null) {
        // Jika sedang null, set ke asc
        setSortOrder("asc");
      } else if (sortOrder === "asc") {
        // Jika sedang asc, ubah ke desc
        setSortOrder("desc");
      } else if (sortOrder === "desc") {
        // Jika sedang desc, reset keseluruhan
        setSortColumn(null);
        setSortOrder(null);
      }
    } else {
      // Jika kolom berbeda, set ke asc
      setSortColumn(column);
      setSortOrder("asc");
    }
  };

  const { data: kelasHotel } = useQuery({
    queryKey: ["tb_hotel_kelas"],
    queryFn: () => getReferensiService("tb_hotel_kelas"),
  });

  const toggleRow = (id) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  const handleDelete = async (id) => {
    try {
      await deleteDataService(`/admin/sarpras_hotel/delete/${id}`);
      SweetAlert.success("Success", "Berhasil menghapus data", () => {
        // window.location.reload();
        refetch();
      });
    } catch (error) {
      console.log(error);
    }
  };

  const getKelasHotelById = (id) => {
    return (
      kelasHotel?.data?.find((item) => item.id_hotel_kelas === id)?.nama || "-"
    );
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  const fetchAllData = async () => {
    const allData = [];
    let page = 1;
    let totalPages = 1;
    let totalRecords = 0;

    // Fetch the first page to get the total count
    const initialResponse = await fetchTableData({
      url: "/admin/sarpras_hotel/lists",
      params: {
        page,
        per_page: 1, // Fetch only one record to get the total count
        q: debouncedSearchQuery,
        id_adm_provinsi: user?.id_adm_provinsi,
        id_adm_kabkot: user?.id_adm_kabkot,
          ...(user?.roleId === 14 && { pic: user.id }),
      },
    });
    totalRecords = initialResponse.pagination.total_count;

    // Set per_page to totalRecords to fetch all data at once
    const response = await fetchTableData({
      url: "/admin/sarpras_hotel/lists",
      params: {
        page: 1,
        per_page: totalRecords,
        q: debouncedSearchQuery,
        id_adm_provinsi: user?.id_adm_provinsi,
        id_adm_kabkot: user?.id_adm_kabkot,
        ...(user?.roleId === 14 && { pic: user.id }),
      },
    });
    allData.push(...response.data);

    return allData;
  };

  const exportToExcel = async () => {
    const headers = ["Nama Hotel", "Alamat", "Provinsi", "Kabupaten", "Kelas"];
    const allData = await fetchAllData();
    const worksheetData = allData.map((item) => [
      item.nama,
      item.alamat,
      item.tb_adm_kabkot?.tb_adm_provinsi?.nama,
      item.tb_adm_kabkot?.nama,
      item.id_kelas ? getKelasHotelById(item.id_kelas) : "-"
    ]) || [];
    const worksheet = utils.aoa_to_sheet([headers, ...worksheetData]);
    const workbook = utils.book_new();
    utils.book_append_sheet(workbook, worksheet, "Data");
    const excelBuffer = write(workbook, { bookType: "xlsx", type: "array" });
    const data = new Blob([excelBuffer], { type: "application/octet-stream" });
    saveAs(data, "data.xlsx");
  };

  const exportToCSV = async () => {
    const headers = ["Nama Hotel", "Alamat", "Provinsi", "Kabupaten", "Kelas"];
    const allData = await fetchAllData();
    const worksheetData = allData.map((item) => [
      item.nama,
      item.alamat,
      item.tb_adm_kabkot?.tb_adm_provinsi?.nama,
      item.tb_adm_kabkot?.nama,
      item.id_kelas ? getKelasHotelById(item.id_kelas) : "-"
    ]) || [];
    const csvContent = [headers, ...worksheetData].map(e => e.join(",")).join("\n");
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    saveAs(blob, "data.csv");
  };

  const exportToPDF = async () => {
    const doc = new jsPDF();
    const tableColumn = ["Nama Hotel", "Alamat", "Provinsi", "Kabupaten", "Kelas"];
    const allData = await fetchAllData();
    const tableRows = allData.map((item) => [
      item?.nama || "-",
      item?.alamat || "-",
      item?.tb_adm_kabkot?.tb_adm_provinsi?.nama || "-",
      item?.tb_adm_kabkot?.nama || "-",
      item?.id_kelas ? getKelasHotelById(item.id_kelas) : "-"
    ]) || [];

    autoTable(doc, {
      head: [tableColumn],
      body: tableRows,
    });

    doc.save("data.pdf");
  };

  const formatDataForClipboard = async () => {
    const headers = ["Nama Hotel", "Alamat", "Provinsi", "Kabupaten", "Kelas"];
    const allData = await fetchAllData();
    const rows = allData.map((item) => [
      item.nama,
      item.alamat,
      item.tb_adm_kabkot?.tb_adm_provinsi?.nama,
      item.tb_adm_kabkot?.nama,
      item.id_kelas ? getKelasHotelById(item.id_kelas) : "-"
    ]) || [];
    return [headers, ...rows].map(e => e.join(",")).join("\n");
  };

  const copyToClipboard = async () => {
        try {
          const csvContent = await formatDataForClipboard(); // Await the CSV string
          await navigator.clipboard.writeText(csvContent); // Write text directly
          SweetAlert.success("Success", "Data copied to clipboard");
      } catch (error) {
          SweetAlert.error("Error", "Failed to copy data: " + error.message);
      }
  };

  const handleEditSuccess = () => {
    setLocalRefreshTrigger(prev => prev + 1);
    // This will trigger a refetch since localRefreshTrigger is part of combinedTrigger
  };
  
  return (
    <div className="rounded-md border">
       <div className="p-4 flex items-center justify-end">
        <div className="flex items-center space-x-2">
          <Button onClick={exportToExcel} variant="outline" size="sm" title="Download Excel">
            <FileSpreadsheet className="w-4 h-4 mr-1" /> Excel
          </Button>
          <Button onClick={exportToCSV} variant="outline" size="sm" title="Download CSV">
            <FileText className="w-4 h-4 mr-1" /> CSV
          </Button>
          <Button onClick={exportToPDF} variant="outline" size="sm" title="Download PDF">
            <Download className="w-4 h-4 mr-1" /> PDF
          </Button>
          <CopyToClipboard text={JSON.stringify(res?.data)}>
            <Button onClick={copyToClipboard} variant="outline" size="sm" title="Copy to Clipboard">
              <Copy className="w-4 h-4 mr-1" /> Copy
            </Button>
          </CopyToClipboard>
        </div>
      </div>
      <div className="p-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">Show</span>
          <select
            value={entriesPerPage}
            onChange={(e) => setEntriesPerPage(e.target.value)}
            className="border rounded px-2 py-1"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
          </select>
          <span className="text-sm text-gray-500">entries</span>
        </div>
        <div className="flex items-center">
          <input
            type="search"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search..."
            className="border rounded px-3 py-1"
          />
        </div>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50px]"></TableHead>
            <TableHead className="w-[100px]">Aksi</TableHead>
            <TableHead
              onClick={() => handleSort("status")}
              className="cursor-pointer hover:bg-gray-100"
            >
              Status
              {sortColumn === "status"
                ? sortOrder === "asc"
                  ? " ↑"
                  : sortOrder === "desc"
                  ? " ↓"
                  : " ↕️"
                : " ↕️"}
            </TableHead>
            <TableHead>Nama Hotel / Penginapan</TableHead>
            <TableHead
              onClick={() => handleSort("provinsi")}
              className="cursor-pointer hover:bg-gray-100"
            >
              Provinsi
              {sortColumn === "provinsi"
                ? sortOrder === "asc"
                  ? " ↑"
                  : sortOrder === "desc"
                  ? " ↓"
                  : " ↕️"
                : " ↕️"}
            </TableHead>
            <TableHead
              onClick={() => handleSort("kabkot")}
              className="cursor-pointer hover:bg-gray-100"
            >
              Kabupaten/Kota
              {sortColumn === "kabkot"
                ? sortOrder === "asc"
                  ? " ↑"
                  : sortOrder === "desc"
                  ? " ↓"
                  : " ↕️"
                : " ↕️"}
            </TableHead>
            <TableHead>Kelas</TableHead>
            <TableHead>Alamat</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {res?.data?.map((item) => (
            <>
              <TableRow
                key={item.id_hotel}
                className="cursor-pointer hover:bg-gray-50"
              >
                <TableCell
                  onClick={() => toggleRow(item.id_hotel)}
                  className="w-[50px]"
                >
                  {expandedRow === item.id_hotel ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <UpdateHotel id={item.id_hotel} onUpdateSuccess={handleEditSuccess}/>
                    <DeleteConfirm
                      variant="outline"
                      size="sm"
                      className="p-2 text-white bg-red-500 hover:bg-red-700 hover:text-white"
                      onSubmit={() => {
                        handleDelete(item?.id_hotel);
                      }}
                    />
                  </div>
                </TableCell>
                <TableCell>
                  <StatusButtonFrom
                    status={Number(item?.status)}
                    dataEdit={{ ...item, status: Number(item?.status) }}
                    id={item?.id_hotel}
                    onStatusChange={handleStatusChange}
                    service={statusService}
                  />
                </TableCell>
                <TableCell>{item?.nama || "-"}</TableCell>
                <TableCell>{item?.tb_adm_kabkot?.tb_adm_provinsi?.nama || "-"}</TableCell>
                <TableCell>{item?.tb_adm_kabkot?.nama || "-"}</TableCell>

                <TableCell>
                  {item?.id_kelas ? getKelasHotelById(item?.id_kelas) : "-"}
                </TableCell>
                <TableCell>{item?.alamat || "-"}</TableCell>
              </TableRow>
              {expandedRow === item.id_hotel && (
                <TableRow key={item.id_hotel + "collspan"}>
                  <TableCell colSpan={8}>
                    <div className="p-4 bg-gray-50">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-white p-2">
                          <span className="text-gray-600">
                            Nama Hotel / Penginapan
                          </span>
                          <span className="text-gray-800 ml-2">
                            : {item?.nama || "-"}
                          </span>
                        </div>
                        <div className="bg-white p-2">
                          <span className="text-gray-600">Kelas</span>
                          <span className="text-gray-800 ml-2">
                            :{" "}
                            {item?.id_kelas
                              ? getKelasHotelById(item?.id_kelas)
                              : "-"}
                          </span>
                        </div>
                        <div className="bg-white p-2">
                          <span className="text-gray-600">Alamat</span>
                          <span className="text-gray-800 ml-2">
                            : {item?.alamat || "-"}
                          </span>
                        </div>
                        <div className="bg-white p-2">
                          <span className="text-gray-600">Provinsi</span>
                          <span className="text-gray-800 ml-2">
                            : {item?.tb_adm_kabkot?.nama_ibukota || "-"}
                          </span>
                        </div>
                        <div className="bg-white p-2">
                          <span className="text-gray-600">Kabupaten/Kota</span>
                          <span className="text-gray-800 ml-2">
                            : {item?.tb_adm_kabkot?.nama || "-"}
                          </span>
                        </div>
                        <div className="bg-white p-2">
                          <span className="text-gray-600">No Telp</span>
                          <span className="text-gray-800 ml-2">
                            : {item?.no_telp || "-"}
                          </span>
                        </div>
                        <div className="bg-white p-2">
                          <span className="text-gray-600">No Fax</span>
                          <span className="text-gray-800 ml-2">
                            : {item?.no_fax || "-"}
                          </span>
                        </div>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </>
          ))}
        </TableBody>
      </Table>
      <PaginationStyled page={page} setPage={setPage} totalPages={res?.pagination?.total_pages} pageSize={res?.pagination?.per_page} totalRecords={res?.pagination?.total_count} />
    </div>
  );
}
