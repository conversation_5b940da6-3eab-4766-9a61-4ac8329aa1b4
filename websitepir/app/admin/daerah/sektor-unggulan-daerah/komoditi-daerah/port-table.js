"use client";

import KomoditiDaerahEdit from "./EditKomoditiDaerah";
import NewEditKomoditiDaerah from "./NewEditKomoditiDaerah";
import SweetAlert from "@/app/admin/Components/SweetAlert";
import StatusButtonFrom from "@/components/ui/StatusButtonFrom";
import DynamicTable from "@/components/ui/TableCRUD";
import useAuthStore from "@/store/AuthStore";
import Api, { APICORS } from "@/utils/Api";
import { formatNumber } from "@/utils/formatNumber";
import { mapStatus } from "@/utils/status";

const API_ENDPOINTS = {
  LIST: "/admin/sud_komoditi_daerah/lists?is_get_table_admin=true",
  DELETE: "/admin/sud_komoditi_daerah/delete",
};

// Main component
const TableLayout = ({ status, onEdit, refreshTrigger, onEditSuccess }) => {
  const deleteService = async (id) => {
    return await Api.delete(`${API_ENDPOINTS.DELETE}/${id}`);
  };

  const statusService = async (body, id) => {
    return await APICORS.patch(
      `/admin/sud_komoditi_daerah/toggle_status/${id}`,
      body
    );
  };

  // Column definitions
  const columns = [
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ cell }) => mapStatus(cell.getValue()),
    },
    { accessorKey: "sektor", header: "Sektor" },
    { accessorKey: "sub_sektor", header: "Sub Sektor" },
    { accessorKey: "nama_komoditi", header: "Nama Komoditi" },
    { accessorKey: "provinsi", header: "Provinsi" },
    { accessorKey: "kabkota", header: "Kabupaten/Kota" },
    { accessorKey: "sentra_produksi", header: "Sentra Produksi" },
  ];
  const DynamicSektorDaerah = ({ data }) => {
    return (
      <NewEditKomoditiDaerah
        id={data.id_komoditi_daerah}
        onSuccess={(res) => {
          if (onEditSuccess && typeof onEditSuccess === "function") {
            onEditSuccess(res);
          }
        }}
      />
    );
  };

  const { user, isLoading: userLoading } = useAuthStore();

  const StatusComponent = ({
    status,
    dataEdit,
    id,
    onStatusChange,
    service,
  }) => {
    return (
      <StatusButtonFrom
        status={status}
        dataEdit={dataEdit}
        id={id}
        onStatusChange={onStatusChange}
        service={service}
      />
    );
  };

  // const getDataURL = () => {
  //   return  API_ENDPOINTS.LIST + (userLoading === false && user && user.id_adm_kabkot ? `&id_adm_kabkot=${user.id_adm_kabkot}` : '') + (userLoading === false && user && user.id_adm_provinsi ? `&id_adm_provinsi=${user.id_adm_provinsi}` : '');
  // }
  const getDataURL = () => {
    return (
      API_ENDPOINTS.LIST +
      (userLoading === false && user?.roleId === 14
        ? `&pic=${user.id}`
        : "") +
      (userLoading === false && user?.id_adm_kabkot
        ? `&id_adm_kabkot=${user.id_adm_kabkot}`
        : "") +
      (userLoading === false && user?.id_adm_provinsi
        ? `&id_adm_provinsi=${user.id_adm_provinsi}`
        : "") +
      (status !== undefined && status !== null && status !== ""
        ? `&status=${status}`
        : "")
    );
  };

  return (
    <>
      {!userLoading ? (
        <DynamicTable
          idKey="id_komoditi_daerah"
          status={status}
          fetchDataUrl={getDataURL()}
          deleteService={deleteService}
          statusService={statusService}
          columns={columns}
          showEdit={false}
          onEdit={onEdit}
          EditComponent={DynamicSektorDaerah}
          StatusComponent={StatusComponent}
          refreshTrigger={refreshTrigger}
          filterPlaceholder="Cari Sektor..."
          alertService={SweetAlert} // If you're using SweetAlert
        />
      ) : (
        ""
      )}
    </>
  );
};

export default TableLayout;
