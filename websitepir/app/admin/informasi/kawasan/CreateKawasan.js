"use client";

import MapView from "@/app/user/daerah/components/MapView/MapView";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { getReferensiService } from "@/services/AllService";
import Api from "@/utils/Api";
import { Formik } from "formik";
import {
  ChevronDown,
  ChevronRight,
  PlusCircle,
  Trash,
  XCircle,
} from "lucide-react";
import dynamic from 'next/dynamic';
import { useEffect, useRef, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import SweetAlert from "../../Components/SweetAlert";
import VirtualizedSelect from "../../Components/VirtualizedSelect";
import { useMapServiceStore } from "@/store/MapServiceStore";
import MapService from "../../Components/beranda/headline-IKN/MapService";
import MapTag from "../../Components/peluang-investasi/daftar-peluang-investasi/MapTag";

// // Dynamically import CKEditor with no SSR
// const CKEditor = dynamic(
//   () => import('@ckeditor/ckeditor5-react').then(mod => mod.CKEditor),
//   { ssr: false }
// );

// // Also import ClassicEditor dynamically if needed
// const ClassicEditor = dynamic(
//   () => import('@ckeditor/ckeditor5-build-classic'),
//   { ssr: false }
// );

// Schema validasi menggunakan Yup
// const validationSchema = Yup.object({
//     id_adm_kabkot: Yup.string().nullable(),  // assuming it can be an empty string or null
//     id_sumber_data: Yup.string().required("Sumber data wajib diisi"),
//     id_kategori: Yup.string().required("Kategori bandara wajib diisi"),
//     nama: Yup.string().required("Nama bandara wajib diisi"),
//     keterangan: Yup.string(),
//     alamat: Yup.string().required("Alamat wajib diisi"),
//     luas: Yup.number(), // Assuming luas is required based on the initial value
//     luas_satuan: Yup.string().oneOf(["HA", "m2"]), // Example unit options, adjust as needed
//     id_bandara_terdekat: Yup.string().nullable(),  // Assuming it can be an empty string or null
//     jarak_bandara_terdekat: Yup.number(),
//     id_pelabuhan_terdekat: Yup.string().nullable(),  // Assuming it can be an empty string or null
//     jarak_pelabuhan_terdekat: Yup.number(),
//     jarak_ibukota: Yup.number(),
//     url_web: Yup.string(),
//     no_telp: Yup.number(),
//     no_fax: Yup.string(),
//     email: Yup.string(), // Assuming email might be optional or required based on usage
//     cp: Yup.string(), // Assuming optional, no specific validation
//     ketersediaan: Yup.string(),
//     status: Yup.number(),
//     lon: Yup.number(),
//     lat: Yup.number(),
//     is_ikn: Yup.boolean().nullable(),  // Assuming it can be null or true/false
//     id_kawasan_industri_ref_range: Yup.string().nullable(),  // Assuming it's nullable
//     id_kawasan_industri_occupancy: Yup.string().nullable(),  // Assuming it's nullable
// });

const INITIAL_VALUES = {
  id_adm_kabkot: "",
  id_sumber_data: "",
  id_kategori: "",
  nama: "",
  keterangan: "",
  alamat: "",
  luas: "",
  luas_satuan: "HA",
  id_bandara_terdekat: "",
  jarak_bandara_terdekat: "",
  id_pelabuhan_terdekat: "",
  jarak_pelabuhan_terdekat: "",
  jarak_ibukota: "",
  url_web: "",
  no_telp: "",
  no_fax: "",
  email: "",
  cp: "",
  ketersediaan: "",
  status: "",
  map_service: [{ type: "", nama_layer: "", layer: "" }],
  lon: "",
  lat: "",
  is_ikn: null,
  id_kawasan_industri_ref_range: "",
  id_kawasan_industri_occupancy: "",
  upload_foto: [{ file: null }],
  upload_document: null,
  upload_video: null,
  major_tenants: null,
  keterangan_tr: null,
};

const CreateKawasan = () => {
  const [isLoading, setIsLoading] = useState(true); // Tambahkan state untuk loading
  const [isTranslateShow, setIsTranslateShow] = useState(false);
  const [open, setOpen] = useState(false);
  const [openSumberData, setOpenSumberData] = useState(false);
  const [openKabupaten, setOpenKabupaten] = useState(false);
  const [openProvinsi, setOpenProvinsi] = useState(false);
  const [openBandara, setOpenBandara] = useState(false);
  const [openPelabuhan, setOpenPelabuhan] = useState(false);
  const [allOption, setAllOption] = useState({
    sumberDataOptions: [],
    provinsiOptions: [],
    kabkotOptions: [],
    kelasOptions: [],
    bandaraOptions: [],
    pelabuhanOptions: [],
    bandaraKategoriOptions: [],
  });

  const { previewLayer, setPreviewLayer } = useMapServiceStore();
  const [editorLoaded, setEditorLoaded] = useState(false);
  const editorRef = useRef();
  const { CKEditor, ClassicEditor } = editorRef.current || {};

  useEffect(() => {
    editorRef.current = {
      CKEditor: require("@ckeditor/ckeditor5-react").CKEditor,
      ClassicEditor: require("@ckeditor/ckeditor5-build-classic"),
    };
    setEditorLoaded(true);
  }, []);

  const form = useForm({
    // resolver: yupResolver(validationSchema),
    defaultValues: INITIAL_VALUES,
  });
  const {
    register,
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = form;

  // Untuk upload_foto
  const {
    fields: fotoFields,
    append: appendFoto,
    remove: removeFoto,
  } = useFieldArray({
    control,
    name: "upload_foto",
  });

  // Untuk map_service
  const {
    fields: mapServiceFields,
    append: appendMapService,
    remove: removeMapService,
  } = useFieldArray({
    control,
    name: "map_service",
  });

  const watchKeterangan = watch("keterangan");
  const watchMajorTenants = watch("major_tenants");
  const watchKeteranganTR = watch("keterangan_tr");
  const watchProvinsi = watch("provinsi");
  const valueLat = watch("lat");
  const valueLon = watch("lon");
  
  const onSubmit = async (data) => {
    console.log("INIII DATAAAAAAAAAA SUBMIT =>", data, JSON.stringify(data));

    const formData = new FormData();

    formData.append("id_adm_kabkot", parseInt(data.kabupaten?.id_adm_kabkot));
    formData.append("id_sumber_data", parseInt(data.sumberData?.id_sumber_data));
    formData.append("id_kategori", parseInt(data.kategori));
    formData.append("nama", data.nama);
    formData.append("keterangan", data.keterangan);
    formData.append("alamat", data.alamat);
    formData.append("luas", parseInt(data.luas));
    formData.append("luas_satuan", "HA");
    formData.append("major_tenants", data.major_tenants);
    formData.append("id_bandara_terdekat", data.bandara?.id_bandara ? parseInt(data.bandara.id_bandara) : null);
    formData.append(
      "jarak_bandara_terdekat",
      parseFloat(data.jarak_bandara_terdekat)
    );
    formData.append(
      "id_pelabuhan_terdekat",
      data.pelabuhan?.id_pelabuhan ? parseInt(data.pelabuhan.id_pelabuhan) : null
    );
    formData.append(
      "jarak_pelabuhan_terdekat",
      parseFloat(data.jarak_pelabuhan_terdekat)
    );
    formData.append("jarak_ibukota", parseFloat(data.jarak_ibukota));
    formData.append("url_web", data.url_web);
    formData.append("no_telp", data.telepon);
    formData.append("no_fax", data.fax);
    formData.append("email", data.email);
    formData.append("cp", data.cp);
    formData.append("ketersediaan", data.ketersediaan);
    formData.append("status", 99);
    formData.append("lon", parseFloat(data.lon));
    formData.append("lat", parseFloat(data.lat));
    formData.append("is_ikn", data.is_ikn === "true" ? true : false);
    formData.append(
      "id_kawasan_industri_ref_range",
      data.range_harga ? parseInt(data.range_harga) : null
    );
    formData.append("id_kawasan_industri_occupancy", data.okupansi ? parseInt(data.okupansi) : null);

    console.log(data);


    data.map_service?.forEach((item, index) => {
      formData.append(`map_service[${index}][type]`, parseInt(item.type));
      formData.append(`map_service[${index}][nama_layer]`, item.nama_layer);
      formData.append(`map_service[${index}][layer]`, item.layer);
    });

    data.upload_foto?.forEach((item, index) => {
      formData.append(`upload_foto[${index}]`, item?.file);
    });
    formData.append("upload_video", data.upload_video);
    formData.append("upload_document", data.upload_document);
    formData.append("nama_tr", data.nama_tr);
    formData.append("keterangan_tr", data.keterangan_tr);

    try {
      // console.log(body, "BODY");
      await Api.post(`/admin/kawasan_industri/create`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      setOpen(false);
      SweetAlert.success("Success", "Berhasil menambahkan data", () => {
        // window.location.reload();
      });
    } catch (error) {
      console.error("Full Error Object:", error);

      let errorMessage = "Gagal menyimpan data"; // Pesan default

      // Cek apakah error memiliki properti `errors` dan itu adalah array
      if (error?.errors && Array.isArray(error.errors)) {
        // Ambil pesan dari elemen pertama array `errors`
        errorMessage = error.errors[0]?.message || errorMessage;
      } else if (error.message) {
        // Gunakan pesan error standar jika ada
        errorMessage = error.message;
      }
      setOpen(false);
      SweetAlert.error("Error", errorMessage, () => { });
    }
  };

  useEffect(() => {
    async function fetchData() {
      try {
        const [
          res_vw_sumber_data_id,
          res_tb_adm_provinsi,
          res_tb_adm_kabkot,
          res_tb_bandara,
          res_tb_bandara_kelas,
          res_tb_bandara_kategori,
          res_tb_pelabuhan,
        ] = await Promise.all([
          getReferensiService("vw_sumber_data_id"),
          getReferensiService("tb_adm_provinsi"),
          getReferensiService("tb_adm_kabkot"),
          getReferensiService("tb_bandara"),
          getReferensiService("tb_bandara_kelas"),
          getReferensiService("tb_bandara_kategori"),
          getReferensiService("tb_pelabuhan"),
        ]);

        const fetchedData = {
          sumberDataOptions: res_vw_sumber_data_id.data,
          provinsiOptions: res_tb_adm_provinsi.data,
          kabkotOptions: res_tb_adm_kabkot.data,
          bandaraOptions: res_tb_bandara.data,
          kelasOptions: res_tb_bandara_kelas.data,
          bandaraKategoriOptions: res_tb_bandara_kategori.data,
          pelabuhanOptions: res_tb_pelabuhan.data,
        };

        setAllOption(fetchedData);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setIsLoading(false);
      }
    }
    if (open) {
      fetchData();
    }
  }, [open]);

  useEffect(() => {
    if (watchProvinsi) {
      const _filteredKabkotByProvinsi = allOption.kabkotOptions.filter(
        (item) => item.id_adm_provinsi === watchProvinsi.id_adm_provinsi
      );
      setAllOption((prev) => ({
        ...prev,
        kabkotOptions: _filteredKabkotByProvinsi,
      }));
    }
  }, [watchProvinsi]);

  return (
    <Formik>
      <>
        <Dialog
          open={open}
          onOpenChange={setOpen}
          aria-describedby="tambah-kawasan"
        >
          <DialogTrigger asChild>
            <Button>Tambah Data Kawasan</Button>
          </DialogTrigger>
          <DialogContent className=" w-[900px] max-w-[1200px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Form Kawasan</DialogTitle>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  {/* Nama */}
                  <FormField
                    control={control}
                    name="nama"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">
                          Nama Kawasan *
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Input Nama" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Provinsi */}
                  <FormField
                    control={control}
                    name="provinsi"
                    render={({ field }) => (
                      <FormItem>
                        <FormItem>
                          <VirtualizedSelect
                            value={field.value}
                            open={openProvinsi}
                            options={allOption.provinsiOptions}
                            onOpenChange={() => setOpenProvinsi(!openProvinsi)}
                            setSearchTerm={() => console.log("SEAAAAAARCH =>")}
                            fieldChange={field.onChange}
                            setOpen={() => setOpenProvinsi()}
                            valueBy={"id_adm_provinsi"}
                            labelBy={"nama"}
                            label={"Provinsi"}
                          />
                          <FormMessage />
                        </FormItem>
                      </FormItem>
                    )}
                  />

                  {/* Kategori */}
                  <FormField
                    control={control}
                    name="kategori"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Kategori</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="- Pilih Salah Satu -" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="1">
                              Kawasan Ekonomi Khusus (KEK)
                            </SelectItem>
                            <SelectItem value="2">
                              Kawasan Industri dan Blok
                            </SelectItem>
                            <SelectItem value="3">Kawasan Industri</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Kabupaten */}
                  <FormField
                    control={control}
                    name="kabupaten"
                    render={({ field }) => (
                      <FormItem>
                        <VirtualizedSelect
                          label={"Kabupaten"}
                          value={field.value}
                          open={openKabupaten}
                          options={allOption.kabkotOptions}
                          onOpenChange={() => setOpenKabupaten(!openKabupaten)}
                          setSearchTerm={() => console.log("SEAAAAAARCH =>")}
                          fieldChange={field.onChange}
                          setOpen={() => setOpenKabupaten()}
                          valueBy={"id_adm_kabkot"}
                          labelBy={"nama"}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Alamat */}
                  <FormField
                    control={control}
                    name="alamat"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Alamat *</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Input Alamat" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Sumber Data */}
                  <FormField
                    control={control}
                    name="sumberData"
                    render={({ field }) => (
                      <FormItem>
                        <VirtualizedSelect
                          value={field.value}
                          open={openSumberData}
                          options={allOption.sumberDataOptions}
                          onOpenChange={() =>
                            setOpenSumberData(!openSumberData)
                          }
                          setSearchTerm={() => console.log("SEAAAAAARCH =>")}
                          fieldChange={field.onChange}
                          setOpen={() => setOpenSumberData()}
                          valueBy={"id_sumber_data"}
                          labelBy={"judul"}
                          label={"Sumber Data"}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Luas */}
                  <FormField
                    control={control}
                    name="luas"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">
                          Luas * (Satuan(Ha) Desimal menggunakan (.))
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Input Luas"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/*Bandara Terdekat*/}
                  <FormField
                    control={control}
                    name="bandara"
                    render={({ field }) => (
                      <FormItem>
                        <VirtualizedSelect
                          value={field.value}
                          open={openBandara}
                          options={allOption.bandaraOptions}
                          onOpenChange={() => setOpenBandara(!openBandara)}
                          setSearchTerm={() => console.log("SEAAAAAARCH =>")}
                          fieldChange={field.onChange}
                          setOpen={() => setOpenBandara()}
                          valueBy={"id_bandara"}
                          labelBy={"nama"}
                          label={"Bandara Terdekat"}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Contact Person */}
                  <FormField
                    control={control}
                    name="cp"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">
                          Contact Person *
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="text"
                            placeholder="Input Contact"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Jarak Bandara Terdekat */}
                  <FormField
                    control={control}
                    name="jarak_bandara_terdekat"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">
                          Jarak Bandara Terdekat *
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Input Jarak Bandara"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* No. Telepon */}
                  <FormField
                    control={control}
                    name="telepon"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">No. Telepon *</FormLabel>
                        <FormControl>
                          <Input placeholder="Input No. Telepon" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/*Pelabuhan Terdekat*/}
                  <FormField
                    control={control}
                    name="pelabuhan"
                    render={({ field }) => (
                      <FormItem>
                        <VirtualizedSelect
                          value={field.value}
                          open={openPelabuhan}
                          options={allOption.pelabuhanOptions}
                          onOpenChange={() => setOpenPelabuhan(!openPelabuhan)}
                          setSearchTerm={() => console.log("SEAAAAAARCH =>")}
                          fieldChange={field.onChange}
                          setOpen={() => setOpenPelabuhan()}
                          valueBy={"id_pelabuhan"}
                          labelBy={"nama"}
                          label={"Pelabuhan Terdekat"}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* No. Fax */}
                  <FormField
                    control={control}
                    name="fax"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">No. Fax *</FormLabel>
                        <FormControl>
                          <Input placeholder="Input No. Fax" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Jarak Pelabuhan Terdekat */}
                  <FormField
                    control={control}
                    name="jarak_pelabuhan_terdekat"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">
                          Jarak Pelabuhan Terdekat *
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Input Jarak Pelabuhan"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Email */}
                  <FormField
                    control={control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Email *</FormLabel>
                        <FormControl>
                          <Input placeholder="Input Email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Jarak Ibukota */}
                  <FormField
                    control={control}
                    name="jarak_ibukota"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">
                          Jarak Ibukota Provinsi (KM)
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Input Jarak Ibukota"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Url Web */}
                  <FormField
                    control={control}
                    name="url_web"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Url Web</FormLabel>
                        <FormControl>
                          <Input placeholder="Input Url Web" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Ketersediaan */}
                  {/* <FormField
                    control={control}
                    name="ketersediaan"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Ketersediaan</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="- Pilih Salah Satu -" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="true">Ada</SelectItem>
                            <SelectItem value="false">Tidak</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  /> */}

                  {/* is ikn */}
                  <FormField
                    control={control}
                    name="is_ikn"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Is IKN</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="- Pilih Salah Satu -" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="true">Yes</SelectItem>
                            <SelectItem value="false">No</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Keterangan */}
                  <FormField
                    control={control}
                    name="keterangan"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Keterangan *</FormLabel>
                        <FormControl>
                          <CKEditor
                            editor={ClassicEditor}
                            data={watchKeterangan} // Menetapkan nilai saat ini dari form
                            onChange={(event, editor) => {
                              const data = editor.getData();
                              setValue("keterangan", data); // Mengupdate nilai form
                            }}
                            config={{
                              placeholder: "Keterangan...",
                            }}
                            className="w-full" // Mengatur lebar CKEditor
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Major Tenants */}
                  <FormField
                    control={control}
                    name="major_tenants"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">
                          Major Tenants *
                        </FormLabel>
                        <FormControl>
                          <CKEditor
                            editor={ClassicEditor}
                            data={watchMajorTenants} // Menetapkan nilai saat ini dari form
                            onChange={(event, editor) => {
                              const data = editor.getData();
                              setValue("major_tenants", data); // Mengupdate nilai form
                            }}
                            config={{
                              placeholder: "Major tenants...",
                            }}
                            className="w-full" // Mengatur lebar CKEditor
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Range Harga Blok /m2 */}
                  <FormField
                    control={control}
                    name="range_harga"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">
                          Range Harga Blok /m2
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="- Pilih Salah Satu -" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="negosiasi">Negosiasi</SelectItem>
                            <SelectItem value="1">{"<"} 750.000</SelectItem>
                            <SelectItem value="2">
                              1.000.000 - 3.000.000
                            </SelectItem>
                            <SelectItem value="3">
                              3.000.000 - 5.000.000
                            </SelectItem>
                            <SelectItem value="4">
                              5.000.000 - 7.000.000
                            </SelectItem>
                            <SelectItem value="5">
                              7.000.000 - 9.000.000
                            </SelectItem>
                            <SelectItem value="6">
                              9.000.000 - 11.000.000
                            </SelectItem>
                            <SelectItem value="7">
                              11.000.000 - 13.000.000
                            </SelectItem>
                            <SelectItem value="8">
                              13.000.000 - 15.000.000
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Okupansi Kawasan */}
                  <FormField
                    control={control}
                    name="okupansi"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">
                          Okupansi Kawasan
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="- Pilih Salah Satu -" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="1">0%</SelectItem>
                            <SelectItem value="2">10%</SelectItem>
                            <SelectItem value="3">20%</SelectItem>
                            <SelectItem value="4">30%</SelectItem>
                            <SelectItem value="5">40%</SelectItem>
                            <SelectItem value="6">50%</SelectItem>
                            <SelectItem value="7">60%</SelectItem>
                            <SelectItem value="8">70%</SelectItem>
                            <SelectItem value="9">80%</SelectItem>
                            <SelectItem value="10">90%</SelectItem>
                            <SelectItem value="11">Full</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* FOTO PELUANG */}
                  <div className="col-span-2">
                    {fotoFields.map((f, index) => (
                      <div key={f.id} className="flex items-end gap-4">
                        <div className="w-full">
                          <FormField
                            control={control}
                            name={`upload_foto.${index}.file`}
                            render={({ field }) => {
                              return (
                                <FormItem>
                                  <FormLabel className="text-xs">
                                    File Foto *
                                    <div className="text-xs text-gray-400">
                                      Format: JPG, PNG, etc (Max. 5MB)
                                    </div>
                                  </FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder="Input File"
                                      type="file"
                                      accept='image/*'
                                      onChange={(e) => {
                                        const file = e.target.files[0];
                                        if (file) {
                                          const fileSize = file.size / 1024 / 1024; // Convert to MB
                                          if (fileSize > 5) {
                                            alert('File size should not exceed 5MB');
                                            e.currentTarget.value = ''; // Reset input
                                            setValue(`upload_foto[${index}].file`, null);
                                            return;
                                          }

                                          setValue(`upload_foto[${index}].file`, file);
                                        }
                                      }}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )
                            }}
                          />
                        </div>
                        <Button
                          type="button"
                          variant="destructive"
                          onClick={() => removeFoto(index)}
                        >
                          <Trash />
                        </Button>
                      </div>
                    ))}

                    <Button
                      className="mt-4"
                      type="button"
                      variant="secondary"
                      onClick={() => appendFoto({ file: null })}
                    >
                      Add Foto
                    </Button>
                  </div>

                  {/* VIDEO */}
                  <div className="col-span-2">
                    <FormField
                      control={control}
                      name={`upload_video`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-xs">
                            File Video *
                            <div className="text-xs text-gray-400">
                              Format: Mp4, mov, etc (Max. 20MB)
                            </div>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Input File"
                              type="file"
                              onChange={(e) => {
                                const file = e.target.files[0];
                                if (file) {
                                  const fileSize = file.size / 1024 / 1024; // Convert to MB
                                  if (fileSize > 20) {
                                    alert('File size should not exceed 20MB');
                                    e.target.value = ''; // Reset input
                                    field.onChange(null);
                                    return;
                                  }
                                  field.onChange(file);
                                }
                              }}
                              accept="video/*" // Add accepted video formats
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Dokumen */}
                  <div className="col-span-2">
                    <FormField
                      control={control}
                      name={`upload_document`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-xs">
                            File Dokumen *
                            <div className="text-xs text-gray-400">
                              Format: PDF (Max. 5MB)
                            </div>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Input File"
                              type="file"
                              onChange={(e) => {
                                const file = e.target.files[0];
                                if (file) {
                                  const fileSize = file.size / 1024 / 1024; // Convert to MB
                                  if (fileSize > 5) {
                                    alert('File size should not exceed 5MB');
                                    e.target.value = ''; // Reset input
                                    field.onChange(null);
                                    return;
                                  }
                                  field.onChange(file);
                                }
                              }}
                              accept=".pdf" // Only allow PDF files
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="my-8">
                    <div
                      className="flex gap-2 items-center cursor-pointer my-4 font-bold text-lg"
                      onClick={() => setIsTranslateShow(!isTranslateShow)}
                    >
                      Translate
                      {isTranslateShow ? (
                        <ChevronRight size={"16"} />
                      ) : (
                        <ChevronDown size={"16"} />
                      )}
                    </div>

                    {isTranslateShow && (
                      <div className="grid grid-cols-4 gap-4 items-start">
                        <Label className="text-xs">Nama Kawasan (En)</Label>
                        <div className="col-span-3 w-full">
                          <FormField
                            control={control}
                            name={"nama_tr"}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-xs">
                                  Nama EN
                                </FormLabel>
                                <FormControl>
                                  <Input placeholder="Nama EN" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <Label className="text-xs">Deskripsi (En)</Label>
                        <div className="col-span-3 w-full">
                          <FormField
                            control={control}
                            name="keterangan_tr"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-xs">
                                  Keterangan EN
                                </FormLabel>
                                <FormControl>
                                  <CKEditor
                                    editor={ClassicEditor}
                                    data={watchKeteranganTR} // Menetapkan nilai saat ini dari form
                                    onChange={(event, editor) => {
                                      const data = editor.getData();
                                      setValue("keterangan_tr", data); // Mengupdate nilai form
                                    }}
                                    config={{
                                      placeholder: "Keterangan EN...",
                                    }}
                                    className="w-full" // Mengatur lebar CKEditor
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="col-span-3 hidden">
                          <Label className="text-xs">Potensi Pasar (En)</Label>
                          <CKEditor
                            editor={ClassicEditor}
                            data={watch("potensi_pasar_en")}
                            onChange={(event, editor) => {
                              const data = editor.getData();
                              setValue("potensi_pasar_en", data);
                            }}
                            config={{
                              placeholder: "Potensi Pasar EN...",
                            }}
                            className="w-full"
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  {/* MAP SERVICE */}
                  <div className="col-span-2">
                    <Label className="block text-xs font-medium">
                      Map service
                    </Label>
                    {/* <div className="justify-between col-span-3 w-full">
                      {mapServiceFields.map((field, index) => (
                        <div
                          key={field.id}
                          className="mb-4 flex flex-col space-y-2"
                        >
                          <div className="flex items-center space-x-2">
                            <div className="w-full flex flex-col space-y-2">
                              <div className="flex gap-4">
                                <div>
                                  <Select
                                    onValueChange={(value) => {
                                      // Gunakan setValue dari react-hook-form untuk mengupdate nilai
                                      setValue(
                                        `map_service.${index}.type`,
                                        value
                                      );
                                    }}
                                    defaultValue={watch(
                                      `map_service.${index}.type`
                                    )}
                                  >
                                    <SelectTrigger className="w-full text-xs">
                                      <SelectValue placeholder="Pilih tipe map service" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="0">webgis</SelectItem>
                                      <SelectItem value="1">
                                        geoserver
                                      </SelectItem>
                                    </SelectContent>
                                  </Select>
                                  {errors?.map_service?.[index]?.type && (
                                    <div className="text-red-500">
                                      {errors.map_service[index].type.message}
                                    </div>
                                  )}
                                </div>
                                <div className="flex-1">
                                 
                                  <Input
                                    {...register(
                                      `map_service.${index}.nama_layer`
                                    )}
                                    type="text"
                                    placeholder="Nama Layer"
                                    className="justify-between col-span-3 w-full text-xs"
                                  />
                                  {errors?.map_service?.[index]?.nama_layer && (
                                    <div className="text-red-500">
                                      {
                                        errors.map_service[index].nama_layer
                                          .message
                                      }
                                    </div>
                                  )}
                                </div>
                              </div>

                              <Input
                                {...register(`map_service.${index}.layer`)}
                                placeholder="Input Layer"
                                type="text"
                                className="justify-between col-span-3 w-full text-xs"
                              />
                              {errors?.map_service?.[index]?.layer && (
                                <div className="text-red-500">
                                  {errors.map_service[index].layer.message}
                                </div>
                              )}
                              <Button
                                type="button"
                                variant="secondary"
                                onClick={() => {
                                  if (previewLayer === field.layer) {
                                    setPreviewLayer(null);
                                  } else {
                                    setPreviewLayer(field.layer);
                                  }
                                  console.log("Preview Layer:", previewLayer);
                                  console.log("Layer to preview:", field.layer);
                                  console.log("Layer to preview2:", field);

                                }}
                                className={`text-xs py-2 px-4 ${previewLayer === field.layer
                                  ? "bg-blue-500 hover:bg-blue-600"
                                  : "bg-gray-500 hover:bg-gray-600"
                                  } text-white`}
                              >
                                {previewLayer === field.layer ? "Unpreview" : "Preview"}
                              </Button>
                            </div>

                          
                            <div className="flex space-x-2">
                              {index === mapServiceFields.length - 1 && (
                                <Button
                                  type="button"
                                  onClick={() =>
                                    appendMapService({
                                      type: "",
                                      nama_layer: "",
                                      layer: "",
                                    })
                                  }
                                  className="text-xs hover:bg-blue-800"
                                  variant="ghost"
                                  size="icon"
                                >
                                  <PlusCircle className="h-6 w-6" />
                                </Button>
                              )}
                              {mapServiceFields.length > 1 && (
                                <Button
                                  type="button"
                                  onClick={() => removeMapService(index)}
                                  className="text-red-500"
                                  variant="ghost"
                                  size="icon"
                                >
                                  <XCircle className="h-6 w-6" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  {previewLayer && (
                  <div className="col-span-2">
                    <h3 className="text-sm font-bold mb-4">Preview Peta</h3>
                    <div className="w-full h-80 border border-gray-300 rounded-md">
                      <MapService layeruid={previewLayer} />
                    </div>
                  </div>
                )} */}
                    <div className="justify-between col-span-3 w-full">
                      {mapServiceFields.map((field, index) => (
                        <div key={field.id} className="mb-4 flex flex-col space-y-2">
                          <div className="flex items-center space-x-2">
                            <div className="w-full flex flex-col space-y-2">
                              <div className="flex gap-4">
                                <div className="col-span-2">
                                  <Select
                                    onValueChange={(value) => setValue(`map_service.${index}.type`, value)}
                                    value={watch(`map_service.${index}.type`) || ""}
                                  >
                                    <SelectTrigger className="w-full text-xs">
                                      <SelectValue placeholder="Pilih tipe map service" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="0">webgis</SelectItem>
                                      <SelectItem value="1">geoserver</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  {errors?.map_service?.[index]?.type && (
                                    <div className="text-red-500">{errors.map_service[index].type.message}</div>
                                  )}
                                </div>
                                <div className="col-span-2">
                                  <Input
                                    {...register(`map_service.${index}.nama_layer`)}
                                    defaultValue={watch(`map_service.${index}.nama_layer`)}
                                    type="text"
                                    placeholder="Nama Layer"
                                    className="justify-between col-span-3 w-full text-xs"
                                  />
                                  {errors?.map_service?.[index]?.nama_layer && (
                                    <div className="text-red-500">{errors.map_service[index].nama_layer.message}</div>
                                  )}
                                </div>

                                <div className="col-span-2">
                                  <Input
                                    {...register(`map_service.${index}.layer`)}
                                    defaultValue={watch(`map_service.${index}.layer`)}
                                    placeholder="Input Layer"
                                    type="text"
                                    className="justify-between col-span-3 w-full text-xs"
                                  />
                                  {errors?.map_service?.[index]?.layer && (
                                    <div className="text-red-500">{errors.map_service[index].layer.message}</div>
                                  )}
                                </div>
                             
                                <div className="col-span-2">
                                  <Button
                                    type="button"
                                    variant="secondary"
                                    onClick={() => {
                                      const layerValue = watch(`map_service.${index}.layer`);
                                      setPreviewLayer(previewLayer === layerValue ? null : layerValue);
                                    }}
                                    className={`text-xs py-2 px-4 ${previewLayer === watch(`map_service.${index}.layer`)
                                      ? "bg-blue-500 hover:bg-blue-600"
                                      : "bg-gray-500 hover:bg-gray-600"
                                      } text-white`}
                                  >
                                    {previewLayer === watch(`map_service.${index}.layer`) ? "Unpreview" : "Preview"}
                                  </Button>
                                </div>
                              </div>
                            </div>

                            {/* Buttons for adding/removing mapServiceFields */}
                            <div className="flex space-x-2">
                              {index === mapServiceFields.length - 1 && (
                                <Button
                                  type="button"
                                  onClick={() =>
                                    appendMapService({
                                      type: "",
                                      nama_layer: "",
                                      layer: "",
                                    })
                                  }
                                  className="text-xs hover:bg-blue-800"
                                  variant="ghost"
                                  size="icon"
                                >
                                  <PlusCircle className="h-6 w-6" />
                                </Button>
                              )}
                              {mapServiceFields.length > 1 && (
                                <Button
                                  type="button"
                                  onClick={() => removeMapService(index)}
                                  className="text-red-500"
                                  variant="ghost"
                                  size="icon"
                                >
                                  <XCircle className="h-6 w-6" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {previewLayer && (
                    <div className="col-span-2">
                      <h3 className="text-sm font-bold mb-4">Preview Peta</h3>
                      <div className="w-full h-80 border border-gray-300 rounded-md">
                        <MapService layeruid={previewLayer} />
                      </div>
                    </div>
                  )}
                  {/* <FormItem className="col-span-2">
                                            <Label className="text-xs">Map service</Label>
                                            <FieldArray name="map_service" validateOnChange={false}>
                                                {({ removeFoto, push }) => (
                                                    <>
                                                        {values.map_service.map((_, index) => (
                                                            <div
                                                                key={index}
                                                                className="w-full flex justify-between gap-4 mb-2"
                                                            >
                                                         
                                                                <div className="w-full">
                                                                    <Input
                                                                        name={`map_service[${index}].type`}
                                                                        type="text"
                                                                        placeholder="Judul map_service"
                                                                        onChange={(event) =>
                                                                            setFieldValue(
                                                                                `map_service[${index}].type`,
                                                                                event.currentTarget.value
                                                                            )
                                                                        }
                                                                    />
                                                                </div>

                                                         
                                                                <div className="w-full">
                                                                    <Input
                                                                        name={`map_service[${index}].nama_layer`}
                                                                        type="text"
                                                                        placeholder="Keterangan"
                                                                        onChange={(event) =>
                                                                            setFieldValue(
                                                                                `map_service[${index}].nama_layer`,
                                                                                event.currentTarget.value
                                                                            )
                                                                        }
                                                                    />
                                                                </div>

                                                 
                                                                <div className="flex space-x-2">
                                                                    {index === values.map_service.length - 1 && (
                                                                        <Button
                                                                            type="button"
                                                                            onClick={() =>
                                                                                push({ type: '', nama_layer: '' })
                                                                            } // Add a new map_service entry
                                                                            className="text-xs hover:bg-blue-800"
                                                                            variant="ghost"
                                                                            size="icon"
                                                                        >
                                                                            <PlusCircle className="h-6 w-6" />
                                                                        </Button>
                                                                    )}
                                                                    {values.map_service.length > 1 && (
                                                                        <Button
                                                                            type="button"
                                                                            onClick={() => removeFoto(index)} // Remove the current map_service entry
                                                                            className="text-red-500"
                                                                            variant="ghost"
                                                                            size="icon"
                                                                        >
                                                                            <XCircle className="h-6 w-6" />
                                                                        </Button>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </>
                                                )}
                                            </FieldArray>

                                        </FormItem> */}

                  {/* Latitude */}
                  <FormField
                    control={control}
                    name="lat"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Latitude</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.000001"
                            placeholder="Input Latitude"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Longitude */}
                  <FormField
                    control={control}
                    name="lon"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Longitude</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.000001"
                            placeholder="Input Longitude"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div>
                  {/* <MapView
                    setValue={setValue}
                    latFormName="lat"
                    lngFormName="lon"
                    defaultValue={{ lat: 0, lng: 0 }}
                  /> */}
                  <MapTag
                  setValue={setValue}
                  latFormName="lat"
                  lngFormName="lon"
                  defaultValue={{ lat: valueLat, lng: valueLon }}
                />
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    type="submit"
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Simpan
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </>
      {/* )} */}
    </Formik>
  );
};

export default CreateKawasan;
