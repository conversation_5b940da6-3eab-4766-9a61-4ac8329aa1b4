"use client";

import <PERSON><PERSON>lert from "../../Components/SweetAlert";
import DeleteConfirm from "../../Components/delete-confirm";
import FormAddPotensiInvestasi from "./AddPotensiInventasi";
import UpdateKawasan from "./UpdateKawasan";
import UpdatePotensiInvestasi from "./UpdatePotensiInvestasi";
import PaginationStyled from "@/components/PaginationStyled";
import StatusButtonFrom from "@/components/ui/StatusButtonFrom";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { StatusConverter } from "@/services/Converter&Checker";
import { fetchTableData } from "@/services/ReactQuery";
import { deleteDataService } from "@/services/globalService";
import useAuthStore from "@/store/AuthStore";
import { APICORS } from "@/utils/Api";
import { useQuery } from "@tanstack/react-query";
import {
  Edit,
  Trash2,
  MoreVertical,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useDebounce } from "use-debounce";
import { Copy, Download, FileSpreadsheet, FileText } from 'lucide-react';
import { saveAs } from "file-saver";
import { utils, write } from "xlsx";
import { CopyToClipboard } from "react-copy-to-clipboard";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";

export default function PortTable({ onEdit, refreshTrigger }) {
  const [page, setPage] = useState(1);
  const [entriesPerPage, setEntriesPerPage] = useState("10");
  const [searchQuery, setSearchQuery] = useState();
  const [expandedRow, setExpandedRow] = useState(null);
  const [potensiInvestasi, setPotensiInvestasi] = useState({});
  //const [AddPotensiInvestasi, setAddPotensiInvestasi] = useState(false);
  const [debouncedSearchQuery] = useDebounce(searchQuery, 500);

  const [sortColumn, setSortColumn] = useState(null);
  const [sortOrder, setSortOrder] = useState(null);

  const [localRefreshTrigger, setLocalRefreshTrigger] = useState(0);
  const [combinedTrigger, setCombinedTrigger] = useState(refreshTrigger);

  const { user, isLoading: userLoading } = useAuthStore();

  useEffect(() => {
    setCombinedTrigger(refreshTrigger + localRefreshTrigger);
  }, [refreshTrigger, localRefreshTrigger]);

  // Generic status change handler
  const handleStatusChange = () => {
    setLocalRefreshTrigger((prev) => prev + 1);
  };

  const statusService = async (body, id) => {
    return await APICORS.patch(
      `/admin/kawasan_industri/toggle_status/${id}`,
      body
    );
  };

  useEffect(() => {
    refetch();
  }, [entriesPerPage]);

  // FETCH KAWASAN INDUSTRI
  const {
    isLoading,
    error,
    data: res,
    refetch,
  } = useQuery({
    queryKey: [
      "kawasan_industri",
      page,
      combinedTrigger,
      sortColumn,
      sortOrder,
      entriesPerPage,
    ],
    queryFn: () => {
      return fetchTableData({
        url: "/admin/kawasan_industri/lists",
        params: {
          page,
          per_page: entriesPerPage,
          id_adm_provinsi: user?.id_adm_provinsi,
          id_adm_kabkot: user?.id_adm_kabkot,
          id_kawasan: user?.id_kawasan,
          q: searchQuery,
          ...(user?.roleId === 14 && { pic: user.id }),
          ...(sortColumn && {
            order: sortColumn,
            by: sortOrder || "asc",
          }),
        },
      });
    },
    keepPreviousData: true,
    enabled: !userLoading,
  });

  // State to control when the query is triggered
  const [shouldFetch, setShouldFetch] = useState(false);

  // Triggered only when `shouldFetch` is true
  const { data: resPotensiInvestasi } = useQuery({
    queryKey: [
      "kawasan_potensi_investasi",
      potensiInvestasi?.id_kawasan_industri,
    ],
    queryFn: () =>
      fetchTableData({
        url: "/admin/kawasan_potensi_investasi/lists",
        params: {
          page,
          all: true,
          id_kawasan_industri: potensiInvestasi?.id_kawasan_industri,
          id_adm_provinsi: user?.id_adm_provinsi,
          id_adm_kabkot: user?.id_adm_kabkot,
          ...(user?.roleId === 14 && { pic: user.id }),
        },
      }),
    enabled: shouldFetch && !userLoading, // Only fetch when shouldFetch is true
    keepPreviousData: true,
  });

  // Button click handler
  const handleClick = (item) => {
    setPotensiInvestasi({
      id_kawasan_industri: item?.id_kawasan_industri,
      nama: item?.nama,
    });
    setShouldFetch(true); // Trigger fetch
  };

  const handleSort = (column) => {
    // Jika kolom sama
    if (sortColumn === column) {
      if (sortOrder === null) {
        // Jika sedang null, set ke asc
        setSortOrder("asc");
      } else if (sortOrder === "asc") {
        // Jika sedang asc, ubah ke desc
        setSortOrder("desc");
      } else if (sortOrder === "desc") {
        // Jika sedang desc, reset keseluruhan
        setSortColumn(null);
        setSortOrder(null);
      }
    } else {
      // Jika kolom berbeda, set ke asc
      setSortColumn(column);
      setSortOrder("asc");
    }
  };

  const toggleRow = (id) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  const handleDelete = async (id) => {
    try {
      await deleteDataService(`/admin/kawasan_industri/deletes/${id}`);
      SweetAlert.success("Success", "Berhasil menghapus data", () => {
        // window.location.reload();
        refetch();
      });
    } catch (error) {
      console.log(error);
    }
  };

  const handleDeletePotensiInvestasi = async (id) => {
    try {
      await deleteDataService(`/admin/kawasan_potensi_investasi/delete/${id}`);
      SweetAlert.success("Success", "Berhasil menghapus data", () => {
        // window.location.reload();
        refetch();
      });
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    console.log('Fetched data:', res);
    console.log('Fetched Potensi Investasi data:', resPotensiInvestasi);
    refetch();
  }, [searchQuery]);

  useEffect(() => {
    refetch();
  }, [entriesPerPage]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    console.error('Error fetching data:', error);
    return <div>Error: {error.message}</div>;
  }

  const fetchAllData = async () => {
    const allData = [];
    let page = 1;
    let totalPages = 1;
    let totalRecords = 0;

    // Fetch the first page to get the total count
    const initialResponse = await fetchTableData({
      url: "/admin/kawasan_industri/lists",
      params: {
        page,
        per_page: 1, // Fetch only one record to get the total count
        q: debouncedSearchQuery
      },
    });
    totalRecords = initialResponse.pagination.total_count;

    // Set per_page to totalRecords to fetch all data at once
    const response = await fetchTableData({
      url: "/admin/kawasan_industri/lists",
      params: {
        page: 1,
        per_page: totalRecords,
        q: debouncedSearchQuery
      },
    });
    allData.push(...response.data);

    return allData;
  };

  const statusOptions = StatusConverter();
  function getStatusName(statusValue) {
    const status = statusOptions.find((option) => option.value === statusValue);
    return status ? status.name : "-";
  }

  // const exportToExcel = async () => {
  //   const headers = ["Status", "Nama", "Alamat"];
  //   const allData = await fetchAllData();
  //   const worksheetData = allData?.map(item => [
  //     item.status,
  //     item.nama,
  //     item.alamat
  //   ]);
  //   const worksheet = utils.aoa_to_sheet([headers, ...worksheetData]);
  //   const workbook = utils.book_new();
  //   utils.book_append_sheet(workbook, worksheet, "Data");
  //   const excelBuffer = write(workbook, { bookType: "xlsx", type: "array" });
  //   const data = new Blob([excelBuffer], { type: "application/octet-stream" });
  //   saveAs(data, "data.xlsx");
  // };

  // const exportToCSV = async () => {
  //   const headers = ["Status", "Nama", "Alamat"];
  //   const allData = await fetchAllData();
  //   const worksheetData = allData?.map(item => [
  //     item.status,
  //     item.nama,
  //     item.alamat
  //   ]);
  //   const csvContent = [headers, ...worksheetData].map(e => e.join(",")).join("\n");
  //   const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  //   saveAs(blob, "data.csv");
  // };

  // const exportToPDF = async () => {
  //   const doc = new jsPDF();
  //   const allData = await fetchAllData();
  //   const tableColumn = ["Status", "Nama", "Alamat"];
  //   const tableRows = [];

  //   allData?.forEach(item => {
  //     const rowData = [
  //       item?.status || "-",
  //       item?.nama || "-",
  //       item?.alamat || "-"
  //     ];
  //     tableRows.push(rowData);
  //   });

  //   autoTable(doc, {
  //     head: [tableColumn],
  //     body: tableRows,
  //   });

  //   doc.save("data.pdf");
  // };

  // const formatDataForClipboard = async () => {
  //   const allData = await fetchAllData();
  //   const headers = ["Nama", "Alamat", "Status"];
  //   const rows = allData?.map(item => [
  //     item.nama,
  //     item.alamat,
  //     item.status
  //   ]);
  //   const csvContent = [headers, ...rows].map(e => e.join(",")).join("\n");
  //   return csvContent;
  // };

  // const copyToClipboard = async () => {
  //   try {
  //     const csvContent = await formatDataForClipboard(); // Await the CSV string
  //     await navigator.clipboard.writeText(csvContent); // Write text directly
  //     SweetAlert.success("Success", "Data copied to clipboard");
  // } catch (error) {
  //     SweetAlert.error("Error", "Failed to copy data: " + error.message);
  // }
  // };

  const fetchAllDataForExport = async () => {
    try {
      return await fetchAllData();
    } catch (error) {
      console.error("Error fetching all data:", error);
      SweetAlert.error("Error", "Failed to fetch all data");
      return [];
    }
  };
  
  // Fungsi export Excel yang diperbaiki
  const exportToExcel = async () => {
    try {
      // SweetAlert.info("Processing", "Fetching all data for export...");
      
      const headers = ["Status", "Nama", "Alamat"];
      const allData = await fetchAllDataForExport(); // Menggunakan fungsi yang sudah ada
      
      if (!allData || allData.length === 0) {
        SweetAlert.warning("Warning", "No data to export");
        return;
      }
      
      const worksheetData = allData.map(item => [
        item.status || "-",
        item.nama || "-",
        item.alamat || "-"
      ]);
      
      const worksheet = utils.aoa_to_sheet([headers, ...worksheetData]);
      const workbook = utils.book_new();
      utils.book_append_sheet(workbook, worksheet, "Data Kawasan Industri");
      const excelBuffer = write(workbook, { bookType: "xlsx", type: "array" });
      const data = new Blob([excelBuffer], { type: "application/octet-stream" });
      saveAs(data, `kawasan_industri_${new Date().toISOString().split('T')[0]}.xlsx`);
      
      SweetAlert.success("Success", `${allData.length} records exported to Excel`);
    } catch (error) {
      console.error("Error exporting to Excel:", error);
      SweetAlert.error("Error", "Failed to export to Excel: " + error.message);
    }
  };
  
  // Fungsi export CSV yang diperbaiki
  const exportToCSV = async () => {
    try {
      // SweetAlert.info("Processing", "Fetching all data for export...");
      
      const headers = ["Status", "Nama", "Alamat"];
      const allData = await fetchAllDataForExport(); // Menggunakan fungsi yang sudah ada
      
      if (!allData || allData.length === 0) {
        SweetAlert.warning("Warning", "No data to export");
        return;
      }
      
      const worksheetData = allData.map(item => [
        item.status || "-",
        item.nama || "-",
        item.alamat || "-"
      ]);
      
      const csvContent = [headers, ...worksheetData].map(e => e.join(",")).join("\n");
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      saveAs(blob, `kawasan_industri_${new Date().toISOString().split('T')[0]}.csv`);
      
      SweetAlert.success("Success", `${allData.length} records exported to CSV`);
    } catch (error) {
      console.error("Error exporting to CSV:", error);
      SweetAlert.error("Error", "Failed to export to CSV: " + error.message);
    }
  };
  
  // Fungsi export PDF yang diperbaiki
  const exportToPDF = async () => {
    try {
      // SweetAlert.info("Processing", "Fetching all data for export...");
      
      const allData = await fetchAllDataForExport(); // Menggunakan fungsi yang sudah ada
      
      if (!allData || allData.length === 0) {
        SweetAlert.warning("Warning", "No data to export");
        return;
      }
      
      const tableColumn = ["Status", "Nama", "Alamat"];
      const tableRows = [];
  
      allData.forEach(item => {
        const rowData = [
          item?.status || "-",
          item?.nama || "-",
          item?.alamat || "-"
        ];
        tableRows.push(rowData);
      });
  
      // Buat dokumen PDF dengan ukuran A4 (default)
      const doc = new jsPDF();
      
      // Tambahkan judul
      doc.setFontSize(16);
      doc.text("Data Kawasan Industri", 14, 15);
      doc.setFontSize(12);
      doc.text(`Export Date: ${new Date().toLocaleDateString()}`, 14, 23);
      
      // Tambahkan tabel data dengan autoTable
      autoTable(doc, {
        startY: 30,
        head: [tableColumn],
        body: tableRows,
        didDrawPage: (data) => {
          // Tambahkan footer dengan nomor halaman
          doc.setFontSize(10);
          const pageSize = doc.internal.pageSize;
          const pageHeight = pageSize.height ? pageSize.height : pageSize.getHeight();
          doc.text(`Page ${data.pageNumber} of ${doc.getNumberOfPages()}`, pageSize.width / 2, pageHeight - 10, { align: 'center' });
        }
      });
  
      doc.save(`kawasan_industri_${new Date().toISOString().split('T')[0]}.pdf`);
      
      SweetAlert.success("Success", `${allData.length} records exported to PDF`);
    } catch (error) {
      console.error("Error exporting to PDF:", error);
      SweetAlert.error("Error", "Failed to export to PDF: " + error.message);
    }
  };
  
  // Fungsi untuk menyiapkan data untuk clipboard yang diperbaiki
  const formatDataForClipboard = async () => {
    try {
      const allData = await fetchAllDataForExport(); // Menggunakan fungsi yang sudah ada
      
      if (!allData || allData.length === 0) {
        return "No data available";
      }
      
      const headers = ["Status", "Nama", "Alamat"];
      const rows = allData.map(item => [
        item.status || "-",
        item.nama || "-",
        item.alamat || "-"
      ]);
      
      const csvContent = [headers, ...rows].map(e => e.join(",")).join("\n");
      return csvContent;
    } catch (error) {
      console.error("Error formatting data for clipboard:", error);
      return `Error: ${error.message}`;
    }
  };
  
  // Fungsi copy ke clipboard yang diperbaiki
  const copyToClipboard = async () => {
    try {
      // SweetAlert.info("Processing", "Fetching all data...");
      
      const csvContent = await formatDataForClipboard(); // Await the CSV string
      
      if (csvContent === "No data available") {
        SweetAlert.warning("Warning", "No data to copy");
        return;
      }
      
      await navigator.clipboard.writeText(csvContent); // Write text directly
      
      // Dapatkan jumlah baris (dikurangi 1 untuk header)
      const rowCount = csvContent.split("\n").length - 1;
      SweetAlert.success("Success", `${rowCount} records copied to clipboard`);
    } catch (error) {
      console.error("Error copying to clipboard:", error);
      SweetAlert.error("Error", "Failed to copy data: " + error.message);
    }
  };

  
  return (
    <div className="rounded-md border">
      <div className="p-4 flex items-center justify-end">
        <div className="flex items-center space-x-2">
          <Button onClick={exportToExcel} variant="outline" size="sm" title="Download Excel">
            <FileSpreadsheet className="w-4 h-4 mr-1" /> Excel
          </Button>
          <Button onClick={exportToCSV} variant="outline" size="sm" title="Download CSV">
            <FileText className="w-4 h-4 mr-1" /> CSV
          </Button>
          <Button onClick={exportToPDF} variant="outline" size="sm" title="Download PDF">
            <Download className="w-4 h-4 mr-1" /> PDF
          </Button>
          <CopyToClipboard text={JSON.stringify(res?.data)}>
            <Button onClick={copyToClipboard} variant="outline" size="sm" title="Copy to Clipboard">
              <Copy className="w-4 h-4 mr-1" /> Copy
            </Button>
          </CopyToClipboard>
        </div>
      </div>
      <div className="p-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">Show</span>
          <select
            value={entriesPerPage}
            onChange={(e) => setEntriesPerPage(e.target.value)}
            className="border rounded px-2 py-1"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
          </select>
          <span className="text-sm text-gray-500">entries</span>
        </div>
        <div className="flex items-center">
          <input
            type="search"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search..."
            className="border rounded px-3 py-1"
          />
        </div>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50px]"></TableHead>
            <TableHead className="w-[100px]">Aksi</TableHead>
            <TableHead
              onClick={() => handleSort("status")}
              className="cursor-pointer hover:bg-gray-100"
            >
              Status
              {sortColumn === "status"
                ? sortOrder === "asc"
                  ? " ↑"
                  : sortOrder === "desc"
                  ? " ↓"
                  : " ↕️"
                : " ↕️"}
            </TableHead>
            <TableHead
            onClick={() => handleSort("nama")}
            className="cursor-pointer hover:bg-gray-100"
            >Nama
            {sortColumn === "nama"
                ? sortOrder === "asc"
                  ? " ↑"
                  : sortOrder === "desc"
                  ? " ↓"
                  : " ↕️"
                : " ↕️"}
                </TableHead>
            <TableHead
            onClick={() => handleSort("alamat")}
            className="cursor-pointer hover:bg-gray-100">Alamat
             {sortColumn === "alamat"
                ? sortOrder === "asc"
                  ? " ↑"
                  : sortOrder === "desc"
                  ? " ↓"
                  : " ↕️"
                : " ↕️"}
                </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {res?.data?.map((item) => (
            <>
              <TableRow
                key={item.id_kawasan_industri}
                className="cursor-pointer hover:bg-gray-50"
              >
                <TableCell
                  className="w-[50px]"
                >
                  <Button
                    onClick={() => {
                      setPotensiInvestasi({
                        id_kawasan_industri: item?.id_kawasan_industri,
                        nama: item?.nama,
                      });
                      handleClick(item);
                    }}
                  >
                    + Potensi Investasi
                  </Button>
                </TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <UpdateKawasan
                      id={item.id_kawasan_industri}
                      onSubmitted={() => {
                        handleStatusChange();
                      }}
                    />
                    <DeleteConfirm
                      variant="outline"
                      size="sm"
                      className="p-2 text-white bg-red-500 hover:bg-red-700 hover:text-white"
                      onSubmit={() => {
                        handleDelete(item?.id_kawasan_industri);
                      }}
                    />
                  </div>
                </TableCell>
                <TableCell>
                  <StatusButtonFrom
                    status={Number(item?.status)}
                    dataEdit={{ ...item, status: Number(item?.status) }}
                    id={item?.id_kawasan_industri}
                    onStatusChange={handleStatusChange}
                    service={statusService}
                  />
                </TableCell>
                <TableCell>{item?.nama || "-"}</TableCell>
                <TableCell>{item?.alamat || "-"}</TableCell>
                {/* <TableCell>{item?.status || '-'}</TableCell> */}
                {/* <TableCell>{item?.nama || '-'}</TableCell> */}

                
              </TableRow>
              {expandedRow === item.id_kawasan_industri && (
                <TableRow key={item.id_kawasan_industri + "collspan"}>
                  <TableCell colSpan={8}>
                    <div className="p-4 bg-gray-50">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-white p-2">
                          <span className="text-gray-600">Nama</span>
                          <span className="text-gray-800 ml-2">
                            : {item?.nama || "-"}
                          </span>
                        </div>
                        <div className="bg-white p-2">
                          <span className="text-gray-600">Kelas</span>
                          <span className="text-gray-800 ml-2">
                            : {item?.kelas || "-"}
                          </span>
                        </div>
                        <div className="bg-white p-2">
                          <span className="text-gray-600">Fungsi</span>
                          <span className="text-gray-800 ml-2">
                            : {item?.fungsi || "-"}
                          </span>
                        </div>
                        <div className="bg-white p-2">
                          <span className="text-gray-600">Provinsi</span>
                          <span className="text-gray-800 ml-2">
                            : {item?.tb_adm_kabkot?.nama_ibukota || "-"}
                          </span>
                        </div>
                        <div className="bg-white p-2">
                          <span className="text-gray-600">Kab/kota</span>
                          <span className="text-gray-800 ml-2">
                            : {item?.tb_adm_kabkot?.nama || "-"}
                          </span>
                        </div>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </>
          ))}
        </TableBody>
      </Table>
      <PaginationStyled
        page={page}
        setPage={setPage}
        totalPages={res?.pagination?.total_pages}
        pageSize={res?.pagination?.per_page}
        totalRecords={res?.pagination?.total_count}
      />

      {/* POTENSI INVESTASI */}
      {potensiInvestasi?.id_kawasan_industri && (
        <>
          <h1 className="px-4 text-xl font-bold">
            Daftar Potensi Peluang - {potensiInvestasi?.nama}
          </h1>
          <div className="p-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">Show</span>
              <select
                value={entriesPerPage}
                onChange={(e) => setEntriesPerPage(e.target.value)}
                className="border rounded px-2 py-1"
              >
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
              </select>
              <span className="text-sm text-gray-500">entries</span>
              <FormAddPotensiInvestasi potensiInvestasi={potensiInvestasi} />
              <Button
                onClick={() => setPotensiInvestasi(null)}
                className="text-[#B91C1C] border-[#B91C1C]"
                variant="outline"
              >
                Tutup
              </Button>
            </div>
            <div className="flex items-center">
              <input
                type="search"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search..."
                className="border rounded px-3 py-1"
              />
            </div>
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]"></TableHead>
                <TableHead className="w-[100px]">Aksi</TableHead>
                <TableHead>Judul Peluang</TableHead>
                <TableHead>Nilai Investasi</TableHead>
                <TableHead>Luas Potensi</TableHead>
                <TableHead>Deskripsi</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {resPotensiInvestasi?.data &&
              resPotensiInvestasi?.data.length > 0 ? (
                resPotensiInvestasi?.data.map((item) => (
                  <>
                    <TableRow
                      key={item.id_kawasan_industri}
                      className="cursor-pointer hover:bg-gray-50"
                    >
                      <TableCell
                        // onClick={() => toggleRow(item.id_kawasan_industri)}
                        className="w-[50px]"
                      >
                        {/* <Button onClick={() => setPotensiInvestasi(item?.id_kawasan_industri)}>+ Potensi Investasi</Button> */}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <UpdatePotensiInvestasi
                            id={item.id_kawasan_industri_peluang}
                          />
                           <DeleteConfirm
                              variant="outline"
                              size="sm"
                              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                              onSubmit={() => {
                                handleDeletePotensiInvestasi(item?.id_kawasan_industri_peluang);
                              }}
                            />
                          {/* <Button
                            variant="outline"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                            onClick={() =>
                              handleDeletePotensiInvestasi(
                                item.id_kawasan_industri_peluang
                              )
                            }
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button> */}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-8 w-8 p-0"
                              >
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Export Data</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                      <TableCell>{item?.judul || "-"}</TableCell>
                      <TableCell>{item?.nilai_investasi || "-"}</TableCell>
                      <TableCell>{item?.luas_lahan || "-"}</TableCell>
                      <TableCell>{item?.deskripsi || "-"}</TableCell>
                      {/* <TableCell>
                                            <span
                                                className={`inline-flex px-2 py-1 rounded-full text-xs font-semibold ${item.status === "Approve"
                                                    ? "bg-green-100 text-green-800"
                                                    : "bg-red-100 text-red-800"
                                                    }`}
                                            >
                                                {item?.status_text || '-'}
                                            </span>
                                        </TableCell> */}
                    </TableRow>
                    {/* {expandedRow === item.id_kawasan_industri && (
                                        <TableRow key={item.id_kawasan_industri + 'collspan'}>
                                            <TableCell colSpan={8}>
                                                <div className="p-4 bg-gray-50">
                                                    <div className="grid grid-cols-2 gap-4">
                                                        <div className="bg-white p-2">
                                                            <span className="text-gray-600">Nama</span>
                                                            <span className="text-gray-800 ml-2">
                                                                : {item?.nama || '-'}
                                                            </span>
                                                        </div>
                                                        <div className="bg-white p-2">
                                                            <span className="text-gray-600">Kelas</span>
                                                            <span className="text-gray-800 ml-2">
                                                                : {item?.kelas || '-'}
                                                            </span>
                                                        </div>
                                                        <div className="bg-white p-2">
                                                            <span className="text-gray-600">Fungsi</span>
                                                            <span className="text-gray-800 ml-2">
                                                                : {item?.fungsi || '-'}
                                                            </span>
                                                        </div>
                                                        <div className="bg-white p-2">
                                                            <span className="text-gray-600">Provinsi</span>
                                                            <span className="text-gray-800 ml-2">
                                                                : {item?.tb_adm_kabkot?.nama_ibukota || '-'}
                                                            </span>
                                                        </div>
                                                        <div className="bg-white p-2">
                                                            <span className="text-gray-600">Kab/kota</span>
                                                            <span className="text-gray-800 ml-2">
                                                                : {item?.tb_adm_kabkot?.nama || '-'}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    )} */}
                  </>
                ))
              ) : (
                <TableRow>
                  {Array(7) // Number of columns
                    .fill(null)
                    .map((_, index) => (
                      <TableCell
                        key={index}
                        className={index === 3 ? "text-center py-4" : ""}
                      >
                        {index === 3 && ( // Center message in the middle column
                          <span className="text-center text-gray-600 block">
                            Tidak Ada Data Potensi Investasi di Kawasan Ini..
                          </span>
                        )}
                      </TableCell>
                    ))}
                </TableRow>
              )}
            </TableBody>
          </Table>
        </>
      )}
    </div>
  );
}
