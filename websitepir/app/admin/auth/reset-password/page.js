"use client"
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Image from "next/image";
import React, { useState, useEffect } from "react";
import logo from "@/public/logo-bkpms.png";
import { useRouter, useSearchParams } from "next/navigation";
import { resetPasswordService } from "@/services/AuthServices";
import Api from "@/utils/Api";

const ResetPassword = () => {
  const [loading, setLoading] = useState(false);
  const [token, setToken] = useState("");
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Mengambil token dari URL saat komponen dimount
    const tokenFromUrl = searchParams.get("token");
    if (tokenFromUrl) {
      setToken(tokenFromUrl);
    } else {
      // Redirect ke halaman login jika tidak ada token
      alert("Invalid reset password link.");
      router.push("/admin/auth/login");
    }
  }, [searchParams, router]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Pastikan token ada sebelum melanjutkan
    if (!token) {
      alert("Token is missing. Please try again with a valid reset link.");
      return;
    }

    const password = e.target.password.value;

    // Buat object data yang akan dikirim
    const data = {
      token: token,
      password: password,
    };

    try {
      setLoading(true);
      const response = await resetPasswordService(data);

      if (response?.success === true) {
        alert("Password has been reset successfully.");
        
        // Ambil email dari response
        const email = response?.data?.email;
        
        if (email) {
          try {
            // Ambil data user berdasarkan email
            const userResponse = await Api.get(`/user/getbyemail/${email}`);
            
            if (userResponse?.success === true && userResponse?.data) {
              const userData = userResponse.data;
              
              // Pastikan login_name tersedia
              if (userData.login_name) {
                try {
                  // Ambil token untuk akses geoportal
                  const userLogin = {
                    username: process.env.NEXT_PUBLIC_UNAME_GEOPORTAL,
                    password: process.env.NEXT_PUBLIC_PASS_GEOPORTAL,
                  };
                  
                  const tokenRes = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/iam/login`, {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify(userLogin),
                  });
                  
                  if (!tokenRes.ok) {
                    throw new Error(`Error ${tokenRes.status}: ${await tokenRes.text()}`);
                  }
                  
                  const tokenData = await tokenRes.json();
                  const accessToken = tokenData.accessToken;
                  
                  // Siapkan data untuk geoportal
                  const fullName = [userData.first_name, userData.middle_name, userData.last_name]
                    .filter(Boolean) // Menghapus string kosong agar tidak ada spasi berlebih
                    .join(" ");
                  
                  const geoportalData = {
                    login_name: userData.login_name,
                    full_name: fullName || userData.full_name,
                    email: userData.email,
                    password: password, // Gunakan password yang baru direset
                    role_uid: ["Iphae9kahxees2op", "Kuceejaigai9jieW"], // Set appropriate role_uid
                  };
                  
                  // Cek apakah user sudah ada di geoportal
                  const cekUser = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/gp-users/login_name/${geoportalData.login_name}`, {
                    method: "GET",
                    headers: {
                      "Content-Type": "application/json",
                    },
                  });
                  
                  const cekUserData = await cekUser.json();
                  
                  // Jika user belum ada, buat user baru di geoportal
                  if (!cekUserData.data) {
                    const createRes = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/adm/user/create`, {
                      method: "POST",
                      headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${accessToken}`,
                      },
                      body: JSON.stringify(geoportalData),
                    });
                    
                    if (!createRes.ok) {
                      const errorJson = await createRes.json().catch(() => ({}));
                      console.error("Error creating user in geoportal:", errorJson);
                    } else {
                      console.log("User successfully created on geoportal after password reset");
                    }
                  } else {
                    console.log("User already exists in geoportal system");
                  }
                } catch (error) {
                  console.error("Error creating user in geoportal:", error);
                }
              }
            }
          } catch (error) {
            console.error("Error saat mengambil data user:", error);
          }
        }
        
        router.push("/admin/auth/login");
      } else {
        alert("Failed to reset password. Please try again.");
      }
    } catch (error) {
      console.error("Password reset error:", error);
      alert("An error occurred. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="relative w-full h-screen">
      <Image
        src="/bg4.svg"
        alt="Background Image"
        layout="fill"
        objectFit="cover"
        className="absolute top-0 left-0 w-full h-full z-[-1] dark:brightness-[0.2] dark:grayscale"
      />

      <div className="flex items-center justify-end mr-40 h-full">
        <div
          className="w-full max-w-md p-8 rounded-lg shadow-lg"
          style={{
            background: "linear-gradient(to bottom right, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.1))",
            backdropFilter: "blur(10px)",
            WebkitBackdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.3)",
            boxShadow: `
              0 2px 10px rgba(0, 0, 0, 0.1),
              1px 2px 5px rgba(255, 255, 255, 0.3)
            `,
          }}
        >
          <div className="grid justify-center gap-2 mb-12 text-center">
            <Image unoptimized priority className="w-56" src={logo} alt="logo" />
          </div>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="password" className="text-white">New Password</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  placeholder="Enter your new password"
                  required
                />
              </div>
              <Button
                type="submit"
                className="w-full bg-[#FFA500] hover:bg-[#FF8C00] text-black"
                disabled={loading}
              >
                {loading ? "Resetting..." : "Reset Password"}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;