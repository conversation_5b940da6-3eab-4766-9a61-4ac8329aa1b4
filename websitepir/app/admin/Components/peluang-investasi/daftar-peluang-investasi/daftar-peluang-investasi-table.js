"use client";

import DataTable from "../../DataTable";
import SweetAlert from "../../SweetAlert";
import EditPeluangInvestasi from "./edit-peluang-investasi";
import FilterForm from "./filterForm";
import StatusButton from "./status-button";
import { Button } from "@/components/ui/button";
import { getReferensiService } from "@/services/AllService";
import {
  deletePeluangInvestasiService,
  getDataTablePeluangInvestasiService,
  getPeluangInvestasiService,
} from "@/services/PeluangInvestasiService";
import { Edit, Eye, Trash } from "lucide-react";
import { useRouter } from "next/navigation";
import React, { useEffect, useRef, useState, useMemo } from "react";
import useAuthStore from "@/store/AuthStore";

const stripHtmlTags = (html) => {
  const doc = new DOMParser().parseFromString(html, "text/html");
  return doc.body.textContent || "";
};

const truncateText = (text, wordLimit) => {
  if (!text) return "";
  const words = text.split(" ");
  return words.length > wordLimit
    ? words.slice(0, wordLimit).join(" ") + "..."
    : text;
};

const DaftarPeluangInvestasiTable = ({
  status,
  refreshTrigger,
  sumberDataOptions,
  kabkotOptions,
  peluangSektorOptions,
  peluangStatusOptions,
  jenisInsentifOptions,
  kategoriSektorOptions,
  peluangPrioritasOptions
}) => {
  const [localRefreshTrigger, setLocalRefreshTrigger] = useState(0);
  const [combinedTrigger, setCombinedTrigger] = useState(refreshTrigger);
  const router = useRouter();

  const containerRef = useRef(null);

  const [filters, setFilters] = useState({
    id_prioritas: "",
    project_status_enum: "",
    id_kategori_sektor: "",
  });

  useEffect(() => {
    setCombinedTrigger(refreshTrigger + localRefreshTrigger);
  }, [refreshTrigger, localRefreshTrigger]);

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    setLocalRefreshTrigger(prev => prev + 1);
  };

  const handleDelete = async (row) => {
    SweetAlert.confirm(
      "Konfirmasi",
      "Apakah Anda yakin ingin menghapus peluang investasi ini?",
      async () => {
        try {
          await deletePeluangInvestasiService(row.id_peluang_kabkot);
          SweetAlert.success(
            "Success",
            "Berhasil menghapus peluang investasi",
            () => {
              setLocalRefreshTrigger((prev) => prev + 1);
            }
          );
        } catch (error) {
          SweetAlert.error("Error", "Gagal menghapus peluang investasi", () => {
            console.error("Error deleting data:", error);
          });
        }
      }
    );
  };

  const handlePreview = (row) => {
    router.push(`detailed/${row.id_peluang_kabkot}`);
  };

  const columns = [
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <StatusButton
          status={row.original.status}
          peluangInvestasiDataEdit={row.original}
          onStatusChange={() => setLocalRefreshTrigger((prev) => prev + 1)}
        />
      ),
    },
    { accessorKey: "judul_peluang", header: "Judul Peluang", size: 600 },
    { accessorKey: "lokasi_kawasan", header: "Keterangan Lokasi Kawasan" },
    { accessorKey: "pulau", header: "Daerah" },
    { accessorKey: "provinsi", header: "Provinsi" },
    { accessorKey: "kabkot", header: "Kabupaten/Kota" },
    { accessorKey: "nama_Sektor", header: "Sektor Peluang" },
    { accessorKey: "prioritas", header: "Prioritas" },
    { accessorKey: "tahun", header: "Tahun" },
    {
      accessorKey: "keterangan",
      header: "Keterangan",
      cell: ({ row }) =>
        truncateText(stripHtmlTags(row.original.keterangan), 20),
    },
    { accessorKey: "status_ppi", header: "Status Peluang" },
  ];

  const ActionsComponent = ({ row, onDelete }) => {
    const roleId = useAuthStore((state) => state.roleId);
    const isDeleteEnabled = useMemo(() => roleId === 1 || roleId === 2, [roleId]);

    return (
      <div className="flex space-x-2">
        <EditPeluangInvestasi
          peluangId={row.id_peluang_kabkot}
          onStatusChange={() => setLocalRefreshTrigger((prev) => prev + 1)}
          sumberDataOptions={sumberDataOptions}
          kabkotOptions={kabkotOptions}
          peluangSektorOptions={peluangSektorOptions}
          peluangStatusOptions={peluangStatusOptions}
          jenisInsentifOptions={jenisInsentifOptions}
        />
        <Button
          variant="secondary"
          size="sm"
          className="bg-blue-500 hover:bg-blue-600 text-xs"
          onClick={() => handlePreview(row)}
        >
          <Eye className="mr-2 h-4 w-4" />
          Preview
        </Button>
        <Button
          variant="destructive"
          size="sm"
          className="bg-red-500 hover:bg-red-600 text-xs"
          onClick={() => onDelete(row)}
          disabled={!isDeleteEnabled}
        >
          <Trash className="mr-2 h-4 w-4" />
          Delete
        </Button>
      </div>
    );
  };

  const fetchWithStatus = async (page, pageSize, search, order, by) => {
    return await getDataTablePeluangInvestasiService(
      page,
      pageSize,
      search,
      order,
      by,
      status,
      filters.id_prioritas,
      filters.project_status_enum,
      filters.id_kategori_sektor,
    );
  };

  return (
    <div div className="p-4 space-y-4" ref={containerRef}>
      <FilterForm
        onFilterChange={handleFilterChange}
        containerRef={containerRef}
        peluangStatusOptions={peluangStatusOptions}
        kategoriSektorOptions={kategoriSektorOptions}
        peluangPrioritasOptions={peluangPrioritasOptions}
      />

      <DataTable
        columns={columns}
        pageSize={10}
        fetchDataService={fetchWithStatus}
        actionsComponent={({ row }) => (
          <ActionsComponent row={row} onDelete={handleDelete} />
        )}
        filterPlaceholder="Cari Peluang Investasi..."
        refreshTrigger={combinedTrigger}
        exportOptions={{
          orientation: "landscape",
          columnStyles: {
            1: { cellWidth: 20 },
            2: { cellWidth: 30 },
            9: { cellWidth: 70 },
          },
        }}
      />
    </div>
  );
};

export default DaftarPeluangInvestasiTable;
