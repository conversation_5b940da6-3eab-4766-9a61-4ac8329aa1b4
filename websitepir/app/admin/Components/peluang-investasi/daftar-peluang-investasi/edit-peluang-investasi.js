"use client";

import SumberDataDropdown from "../../SumberDataDropdown";
import SweetAlert from "../../SweetAlert";
import MapService from "../../beranda/headline-IKN/MapService";
import KontakTable from "./KontakTable";
import MapTag from "./MapTag";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import {
  changeStatusPeluangInvestasiService,
  editPeluangInvestasiService,
  editStatusPeluangInvestasiService,
  getByIdPeluangInvestasiService,
} from "@/services/PeluangInvestasiService";
import useKontakStore from "@/store/KontakStore";
import { useMapServiceStore } from "@/store/MapServiceStore";
import { ErrorMessage, FieldArray, Form, Formik } from "formik";
import {
  Check,
  ChevronsUpDown,
  Edit,
  PlusCircle,
  Trash,
  X,
  XCircle,
} from "lucide-react";
import dynamic from "next/dynamic";
import Image from "next/image";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
// Dynamically import Quill

// Make sure to import Quill styles
import "react-quill/dist/quill.snow.css";
// You can use other themes as well
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { urlToFile } from "@/app/helpers";

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });

// Define the Yup validation schema
// const createInvestmentSchema = Yup.object().shape({
//     daerah: Yup.string().required("Daerah harus diisi."),
//     sektor_peluang: Yup.string().required("Sektor peluang harus diisi."),
//     sumber_data: Yup.string().required("Sumber data harus diisi."),
//     is_ikn: Yup.boolean().required("Apakah IKN harus diisi."),
//     kode_kbli: Yup.string().required("Kode KBLI harus diisi."),
//     judul_peluang: Yup.string().required("Judul peluang harus diisi."),
//     status_peluang: Yup.string().required("Status peluang harus diisi."),
//     lokasi_kawasan: Yup.string().required("Lokasi kawasan harus diisi."),
//     tahun: Yup.string().required("Tahun harus diisi."),
//     prioritas: Yup.string().required("Prioritas harus diisi."),
//     nilai_investasi: Yup.number().required("Nilai investasi harus diisi."),
//     nilai_irr: Yup.number().required("Nilai IRR harus diisi."),
//     nilai_npv: Yup.number().required("Nilai NPV harus diisi."),
//     nilai_pp: Yup.number().required("Nilai PP harus diisi."),
//     keterangan: Yup.string().required("Keterangan harus diisi."),
//     images: Yup.mixed().required("Gambar harus diupload."),
//     infografis: Yup.mixed().required("Infografis harus diupload."),
//     document: Yup.mixed().required("Dokumen harus diupload."),
// });

const EditPeluangInvestasi = ({
  peluangId,
  onStatusChange,
  sumberDataOptions,
  kabkotOptions: daerahOptions,
  peluangSektorOptions: sektorOptions,
  peluangStatusOptions: statusPeluangOptions,
  jenisInsentifOptions: peluangInsentifOptions,
}) => {
  // Sesuaikan dengan store yang digunakan
  const [isLoading, setIsLoading] = useState(true); // Tambahkan state untuk loading

  const [openDaerah, setOpenDaerah] = useState(false);
  const [openSektor, setOpenSektor] = useState(false);
  const [openSumberData, setOpenSumberData] = useState(false);
  const [openStatusPeluang, setOpenStatusPeluang] = useState(false);
  const [openApakahIKN, setOpenApakahIKN] = useState(false);
  const [openTahun, setOpenTahun] = useState(false);
  const [openPrioritas, setOpenPrioritas] = useState(false);
  const [openLokasiKawasan, setOpenLokasiKawasan] = useState(false);
  const [showKontak, setShowKontak] = useState(false);
  const [openPeluangInsentif, setOpenPeluangInsentif] = useState(false);
  const [showTranslateForm, setShowTranslateForm] = useState(false);

  const [selectedDaerah, setSelectedDaerah] = useState("");
  const [selectedSektor, setSelectedSektor] = useState("");
  const [selectedSumberData, setSelectedSumberData] = useState("");
  const [selectedStatusPeluang, setSelectedStatusPeluang] = useState("");
  const [selectedApakahIKN, setSelectedApakahIKN] = useState("");
  const [selectedTahun, setSelectedTahun] = useState("");
  const [selectedPrioritas, setSelectedPrioritas] = useState("");
  const [selectedLokasiKawasan, setSelectedLokasiKawasan] = useState("");
  const [selectedPeluangInsentif, setSelectedPeluangInsentif] = useState("");
  const { previewLayer, setPreviewLayer } = useMapServiceStore();

  const containerRef = useRef(null);

  const [editorLoaded, setEditorLoaded] = useState(false);
  const editorRef = useRef();

  const [isEmailPopupOpen, setEmailPopupOpen] = useState(false); // State untuk popup email
  const [emailData, setEmailData] = useState({
    email: [],
    subjek: "",
    keterangan: "",
    project_status_enum: null,
    nama: "",
  });

  const handleEmailInputChange = (e) => {
    setEmailData((prev) => ({ ...prev, currentEmail: e.target.value }));
  };

  const addEmailIfValid = () => {
    const email = emailData.currentEmail.trim();
    if (email && isValidEmail(email) && !emailData.email?.includes(email)) {
      setEmailData((prev) => ({
        ...prev,
        email: [...prev.email, email],
        currentEmail: "",
      }));
      return true;
    }
    return false;
  };

  const handleEmailKeyDown = (e) => {
    if (e.key === "Enter" || e.key === ",") {
      e.preventDefault();
      addEmailIfValid();
    }
  };

  const handleEmailBlur = () => {
    addEmailIfValid();
  };

  const handleEmailTab = (e) => {
    if (e.key === "Tab") {
      const added = addEmailIfValid();
      if (added) {
        e.preventDefault(); // Prevent default tab behavior only if email was added
      }
    }
  };

  // const handleEmailKeyDown = (e) => {
  //   if (e.key === "Enter" || e.key === ",") {
  //     e.preventDefault();
  //     const email = emailData.currentEmail.trim();
  //     if (isValidEmail(email) && !emailData.email?.includes(email)) {
  //       setEmailData((prev) => ({
  //         ...prev,
  //         email: [...prev.email, email],
  //         currentEmail: "",
  //       }));
  //     }
  //   }
  // };

  const removeEmail = (emailToRemove) => {
    setEmailData((prev) => ({
      ...prev,
      email: prev.email.filter((email) => email !== emailToRemove),
    }));
  };

  const isValidEmail = (email) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const handleEmailChange = (e) => {
    const { name, value } = e.target;
    setEmailData((prev) => ({ ...prev, [name]: value }));
  };

  useEffect(() => {
    editorRef.current = {
      CKEditor: require("@ckeditor/ckeditor5-react").CKEditor,
      ClassicEditor: require("@ckeditor/ckeditor5-build-classic"),
    };
    setEditorLoaded(true);
  }, []);

  //data select
  const prioritasOptions = useCallback(
    () => [
      { id: 1, nama: "Prioritas" },
      { id: 3, nama: "Prioritas & IKN" },
    ],
    []
  );

  // const tahunOptions = [2020, 2021, 2022, 2023, 2024, 2025];
  const tahunOptions = useMemo(() => {
    const startYear = 2020;
    const currentYear = new Date().getFullYear();
    return Array.from(
      { length: currentYear - startYear + 1 },
      (_, i) => startYear + i
    );
  }, []);
  const apakahIKN = ["true", "false"];

  const [searchTerm, setSearchTerm] = useState("");
  const [imagePreview, setImagePreview] = useState([]);
  // const [infografisPreview, setInfografisPreview] = useState([]);
  // const [imagePreview, setImagesPreview] = useState([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dataEdit, setDataEdit] = useState(null);
  const [gambarInfografisPreview, setGambarInfografisPreview] = useState([]);
  const [gambarInfografisTrPreview, setGambarInfografisTrPreview] = useState(
    []
  );
  
    const [infografisValues, setInfografisValues] = useState([]);
      const [infografisTrValues, setInfografisTrValues] = useState([]);
  const [dokumenPreview, setDokumenPreview] = useState([]);

  const { selectedKontaks, addKontak, removeKontak, clearKontaks } =
    useKontakStore();

  // const {
  //   sumberDataOptions,
  //   kabkotOptions: daerahOptions,
  //   peluangSektorOptions: sektorOptions,
  //   peluangStatusOptions: statusPeluangOptions,
  //   jenisInsentifOptions: peluangInsentifOptions,
  //   fetchReferensiData,
  // } = useReferensiStore();

  // useEffect(() => {
  //   if (sumberDataOptions.length === 0) {
  //     fetchReferensiData(); // Fetch data only if not already loaded
  //   }
  // }, [
  //   fetchReferensiData,
  //   sumberDataOptions,
  //   daerahOptions,
  //   sektorOptions,
  //   statusPeluangOptions,
  //   peluangInsentifOptions,
  // ]);

  // const fetchPeluangData = async () => {
  //   try {
  //     setIsLoading(true);
  //     const response = await getByIdPeluangInvestasiService(peluangId);
  //     if (response.data) {
  //       setDataEdit(response.data);
  //       const image = response.data.tb_peluang_kabkot_file.images.map(
  //         (file) => file.path_file // Menampilkan path_file saja tanpa menambahkan link di depan
  //       );

  //       setImagePreview(image);

  //       const gambarInfografis =
  //         response.data.tb_peluang_kabkot_file.infografis.map(
  //           (file) => file.path_file
  //         );
  //       setGambarInfografisPreview(gambarInfografis);

  //       const gambarInfografisTr =
  //         response.data.tb_peluang_kabkot_file_tr.infografis.map(
  //           (file) => file.path_file
  //         );
  //       setGambarInfografisTrPreview(gambarInfografisTr);

  //       const dokumen = response.data.tb_peluang_kabkot_file.document.map(
  //         (file) => file.path_file
  //       );
  //       setDokumenPreview(dokumen);

  //       const kontakList = response.data.tb_peluang_kabkot_kontak;
  //       if (kontakList && kontakList.length > 0) {
  //         kontakList.forEach((kontak) => {
  //           addKontak({
  //             id_peluang_kontak: kontak.id_peluang_kontak,
  //             id_peluang_kabkot_kontak: kontak.id_peluang_kabkot_kontak,
  //             id_peluang_kabkot: kontak.id_peluang_kabkot,
  //           });
  //         });
  //       }

  //       console.log("data edit", response.data);

  //       // const daftarKontak = uniqueKontakList.map((item) =>
  //       //   Number(item.id_peluang_kontak)
  //       // );

  //       // Set the initial values for peluang_kontak in Formik or component state
  //       // setFieldValue("peluang_kontak", daftarKontak);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching sektor data:", error);
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };


  const fileFormatted = async (data) => {
      if (data?.length > 0) {
        const result = await Promise.all(
          data.map(async (val) => {
            let file = null;
            if (val.path_file) {
              file = await urlToFile(val.path_file, val.nama, 'image/jpeg');
            }
            return {
              judul: val.judul,
              nama: val.nama,
              file: file,
              path_file: val.path_file,
              keterangan: val.keterangan
            };
          })
        );
        return result;
      }
  
      return [];
    };

 const fetchPeluangData = async () => {
  try {
    setIsLoading(true);
    const response = await getByIdPeluangInvestasiService(peluangId);
    if (response.data) {
      setDataEdit(response.data);

      // Preview path seperti sebelumnya
      const image = response.data.tb_peluang_kabkot_file.images.map((file) => file.path_file);
      setImagePreview(image);

      const gambarInfografis = response.data.tb_peluang_kabkot_file.infografis.map((file) => file.path_file);
      setGambarInfografisPreview(gambarInfografis);

      const gambarInfografisTr = response.data.tb_peluang_kabkot_file_tr.infografis.map((file) => file.path_file);
      setGambarInfografisTrPreview(gambarInfografisTr);

      const dokumen = response.data.tb_peluang_kabkot_file.document.map((file) => file.path_file);
      setDokumenPreview(dokumen);

      // Format file untuk Formik state
     const formattedInfografis = await fileFormatted(response.data.tb_peluang_kabkot_file.infografis);
const onlyFilesInfografis = formattedInfografis
  .filter((item) => item.file) // pastikan file-nya tidak null
  .map((item) => item.file); // hanya ambil file-nya saja

setInfografisValues(onlyFilesInfografis);
  
const formattedInfografisTr = await fileFormatted(response.data.tb_peluang_kabkot_file_tr.infografis);
const onlyFilesInfografisTr = formattedInfografisTr
  .filter((item) => item.file) // pastikan file-nya tidak null
  .map((item) => item.file); // hanya ambil file-nya saja

setInfografisTrValues(onlyFilesInfografisTr);


// const formattedImages = await fileFormatted(response.data.tb_peluang_kabkot_file.images);
// const onlyFilesImages = formattedImages
//   .filter((item) => item.file)
//   .map((item) => item.file);
// setImageValues(onlyFilesImages);


      // Kontak
      const kontakList = response.data.tb_peluang_kabkot_kontak;
      if (kontakList && kontakList.length > 0) {
        kontakList.forEach((kontak) => {
          addKontak({
            id_peluang_kontak: kontak.id_peluang_kontak,
            id_peluang_kabkot_kontak: kontak.id_peluang_kabkot_kontak,
            id_peluang_kabkot: kontak.id_peluang_kabkot,
          });
        });
      }

      console.log("data edit", response.data);
    }
  } catch (error) {
    console.error("Error fetching peluang data:", error);
  } finally {
    setIsLoading(false);
  }
};


  const handleDialogOpenChange = (open) => {
    setIsDialogOpen(open);
    if (open) {
      fetchPeluangData();
    }
  };

  useEffect(() => {
    if (dataEdit) {
      // Temukan sektor berdasarkan id_sektor dari dataEdit
      const sektor = sektorOptions?.find(
        (sektor) => sektor.id_peluang_sektor === dataEdit.id_sektor
      );
      const sumberData = sumberDataOptions?.find(
        (sumberData) => sumberData.id_sumber_data === dataEdit.id_sumber_data
      );
      const daerah = daerahOptions?.find(
        (daerah) => daerah.id_adm_kabkot === dataEdit.id_adm_kabkot
      );
      const ikn = dataEdit.is_ikn === true ? "true" : "false";
      const prioritas = prioritasOptions().find(
        (prioritas) => prioritas.id === dataEdit.id_prioritas
      );
      const status = statusPeluangOptions?.find(
        (status) =>
          status.id_peluang_status === parseInt(dataEdit.project_status_enum)
      );

      setSelectedSektor(sektor);
      setSelectedSumberData(sumberData);
      setSelectedDaerah(daerah);
      setSelectedApakahIKN(ikn);
      setSelectedTahun(dataEdit.tahun);
      setSelectedPrioritas(prioritas);
      setSelectedStatusPeluang(status);

      setEmailData((prev) => ({
        ...prev,
        nama: dataEdit.nama || "", // Set `nama` from `dataEdit`
        project_status_enum: dataEdit.project_status_enum || null, // Set `project_status_enum` from `dataEdit`
      }));
    }
  }, [
    dataEdit,
    sumberDataOptions,
    daerahOptions,
    sektorOptions,
    statusPeluangOptions,
    prioritasOptions,
  ]);

  // console.log("info", dataEdit?.tb_peluang_kabkot_file?.infografis);
  // console.log("insentif opt",peluangInsentifOptions);

  const vidioEx = dataEdit?.tb_peluang_kabkot_file?.video?.[0]?.nama || "";
  const vidioInputRef = useRef(null);
  return (
    <Formik
      enableReinitialize={true}
      initialValues={{
        id_adm_kabkot: dataEdit?.id_adm_kabkot || "",
        id_sektor: dataEdit?.id_sektor || "",
        id_sumber_data: dataEdit?.id_sumber_data || "",
        is_ikn: dataEdit?.is_ikn || null,
        kode_kbli: dataEdit?.kode_kbli || "",
        nama: dataEdit?.nama || "",
        status: dataEdit?.status,
        lokasi_kawasan: dataEdit?.lokasi_kawasan || "",
        tahun: dataEdit?.tahun || "",
        id_prioritas: dataEdit?.id_prioritas || "",
        nilai_investasi: dataEdit?.nilai_investasi || null,
        nilai_irr: dataEdit?.nilai_irr || null,
        nilai_npv: dataEdit?.nilai_npv || null,
        nilai_pp: dataEdit?.nilai_pp || null,
        keterangan: dataEdit?.keterangan || "",
        // vidio: dataEdit?.tb_peluang_kabkot_file?.video?.[0]?.path_file || "",
        vidio: dataEdit?.tb_peluang_kabkot_file?.video?.[0]?.nama || "",
        //
        id_adm_kabkot_kantor: 1,
        shape: null,
        id_kontak: 1,
        nama_singkat: dataEdit?.nama_singkat || "",
        deskripsi_singkat: "",
        deskripsi: "",
        zoom_peta_default: 1,
        project_status_enum: null,
        peluang_kontak:
          dataEdit?.tb_peluang_kabkot_kontak.id_peluang_kontak || [],

        dokumen:
          dataEdit?.tb_peluang_kabkot_file?.document?.length > 0
            ? dataEdit.tb_peluang_kabkot_file.document
            : [""],
        images:
          dataEdit?.tb_peluang_kabkot_file?.images.length > 0
            ? dataEdit.tb_peluang_kabkot_file.images
            : [""],
        infografis:
          infografisValues?.length > 0 ? infografisValues : [{ judul: "", file: null, keterangan: "" }],

        infografis_tr:
          infografisTrValues?.length > 0 ? infografisTrValues : [{ judul: "", file: null, keterangan: "" }],

        peluang_insentif: dataEdit?.tb_peluang_kabkot_insentif
          ? dataEdit.tb_peluang_kabkot_insentif.map(
              (item) => item.id_jenis_insentif // Memetakan ke id_jenis_insentif
            )
          : [],

        map_service:
          dataEdit?.tb_peluang_layers?.length > 0
            ? dataEdit?.tb_peluang_layers.map((file) => ({
                id_pl: file.id_pl || null,
                layeruid: file.layeruid || "",
                keterangan: file.keterangan || "",
                is_active: file.is_active || false,
              }))
            : [
                {
                  id_pl: null,
                  layeruid: "",
                  keterangan: "",
                  is_active: false,
                },
              ],

        lon: dataEdit?.lon || null,
        lat: dataEdit?.lat || null,

        tr: {
          id_peluang_kabkot_tr:
            dataEdit?.tb_peluang_kabkot_tr?.[0]?.id_peluang_kabkot_tr || null,
          id_peluang_kabkot:
            dataEdit?.tb_peluang_kabkot_tr?.[0]?.id_peluang_kabkot || null,
          kd_bahasa: "en",
          nama: dataEdit?.tb_peluang_kabkot_tr?.[0]?.nama || "",
          keterangan: dataEdit?.tb_peluang_kabkot_tr?.[0]?.keterangan || "",
          nama_singkat: dataEdit?.nama_singkat || "",
          deskripsi_singkat:
            dataEdit?.tb_peluang_kabkot_tr?.[0]?.deskripsi_singkat || "",
          deskripsi: dataEdit?.tb_peluang_kabkot_tr?.[0]?.deskripsi || "",
          lokasi_kawasan:
            dataEdit?.tb_peluang_kabkot_tr?.[0]?.lokasi_kawasan || "",
        },
        kd_bahasa: "en",
        list_hapus_file: [],
        list_hapus_file_tr: [],
        is_ipro: dataEdit?.is_ipro || false,
      }}
      // // validationSchema={createInvestmentSchema}
      // onSubmit={async (values, { setSubmitting }) => {
      //   console.log("values", values);
      //   try {
      //     const response = await editPeluangInvestasiService(values, peluangId);
      //     setIsDialogOpen(false);
      //     SweetAlert.success(
      //       "Success",
      //       "Peluang investasi berhasil di edit!",
      //       () => {
      //         if (onStatusChange) {
      //           onStatusChange(); // Trigger refresh
      //         }
      //       }
      //     );
      //   } catch (error) {
      //     setIsDialogOpen(false);
      //     SweetAlert.error("Error", "Gagal edit peluang investasi.", () => {
      //       console.error("Error edit menu:", error);
      //     });
      //   } finally {
      //     setSubmitting(false);
      //   }
      // }}

      onSubmit={async (values, { setSubmitting, resetForm, setFieldError }) => {
        const nilaiInvestasi = String(values.nilai_investasi || "").replace(/\./g, "");
        const nilaiNpv = String(values.nilai_npv || "").replace(/\./g, "");

        // Check if video URL has changed and validate it
        if (values.vidio !== vidioEx) {
          // URL validation regex pattern
          const urlPattern = /^(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
          
          if (!urlPattern.test(values.vidio)) {
            setFieldError("vidio", "URL video tidak valid. Masukkan URL yang benar.");
            setSubmitting(false);
            // Set focus to the video input field
            if (vidioInputRef.current) {
              vidioInputRef.current.focus();
            }
            return;
          }
        }

        const payload = {
          ...values,
          nilai_investasi: Number(nilaiInvestasi),
          nilai_npv: Number(nilaiNpv),
        };

        // Check if map_service has any meaningful data
        const hasValidMapService = values.map_service.some(
          (service) =>
            service.layeruid.trim() !== "" || service.keterangan.trim() !== ""
        );

        // Only include map_service if it has valid data
        if (hasValidMapService) {
          payload.map_service = values.map_service;
        } else {
          // Remove map_service from payload if no valid data
          delete payload.map_service;
        }

        try {
          // Log the form values before submission
          // console.log("Form Values:", values);

          // Check if the email popup is triggered
          if (isEmailPopupOpen) {
            // Log the email data before sending
            // console.log("Email Data:", emailData);

            // First, submit the main form data
            const mainFormResponse = await editPeluangInvestasiService(
              payload,
              peluangId
            );
            // console.log("Main Form Response:", mainFormResponse);

            // Then, submit the email status change
            const emailStatusBody = {
              email: emailData.email,
              subjek: emailData.subjek,
              keterangan: emailData.keterangan,
              project_status_enum: emailData.project_status_enum || values.project_status_enum,
            };
            // console.log("Email Status Body:", emailStatusBody);

            const emailResponse = await changeStatusPeluangInvestasiService(
              peluangId,
              emailStatusBody
            );
            // console.log("Email Response:", emailResponse);

            await editStatusPeluangInvestasiService(peluangId, {
              id_peluang_kabkot: peluangId,
              status: 0, // Sesuaikan dengan status setelah edit
              keterangan: "Dokumen Diperbarui",
            });
            // Close dialogs and show success message
            SweetAlert.success(
              "Success",
              "Data berhasil disimpan dan email terkirim"
            );
            onStatusChange();
          } else {
            // Just submit the form data without email change
            const response = await editPeluangInvestasiService(
              payload,
              peluangId
            );
            // console.log("Response without email:", response);

            await editStatusPeluangInvestasiService(peluangId, {
              id_peluang_kabkot: peluangId,
              status: 0, // Sesuaikan dengan status setelah edit
              keterangan: "Dokumen Diperbarui",
            });

            SweetAlert.success("Success", "Peluang investasi berhasil diedit");
            onStatusChange();
          }

          // Reset the form and close dialogs
          setSubmitting(false);
          setIsDialogOpen(false);
          setEmailPopupOpen(false);
        } catch (error) {
          const errorMessage = error.errors
            ? error.errors.map((err) => `${err.message},`)
            : "Gagal menyimpan data.";
          SweetAlert.error("Error", errorMessage);

          // SweetAlert.error("Error", "Gagal membuat peluang investasi atau mengirim email");
          console.error("Error:", error);

          // Log error details
          console.error("Error Details:", error.response || error.message);

          setSubmitting(false); // Reset submission state even on error
        }
      }}
    >
      {({ setFieldValue, values, isSubmitting }) => {
        useEffect(() => {
          if (dataEdit) {
            setFieldValue(
              "nilai_investasi",
              dataEdit.nilai_investasi
                ? Number(dataEdit.nilai_investasi).toLocaleString("id-ID")
                : ""
            );
            setFieldValue(
              "nilai_npv",
              dataEdit.nilai_npv
                ? Number(dataEdit.nilai_npv).toLocaleString("id-ID")
                : ""
            );
          }
        }, [dataEdit, setFieldValue]);

        return (
          <Form>
            <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
              <DialogTrigger asChild>
                <Button
                  variant="secondary"
                  size="sm"
                  className="text-xs text-white"
                  style={{ backgroundColor: "#E2B93B", color: "white" }}
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Button>
              </DialogTrigger>

              <DialogContent className="max-w-6xl w-full max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Edit Peluang Investasi</DialogTitle>
                </DialogHeader>
                <form>
                  {/* Daerah Field */}
                  <div
                    ref={containerRef}
                    className="grid grid-cols-4 items-center gap-4"
                  >
                    <Label className="block text-xs font-medium ">Daerah
                    <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                    </Label>
                    <Popover
                      open={openDaerah}
                      onOpenChange={setOpenDaerah}
                      className="col-span-3 w-full"
                    >
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={openDaerah}
                          className="justify-between col-span-3 w-full text-xs"
                          disabled={true}
                        >
                          {selectedDaerah?.nama || "Pilih Daerah"}{" "}
                          {/* Hanya menampilkan nama daerah, bukan seluruh objek */}
                          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-full p-0"
                        container={containerRef.current}
                      >
                        <Command>
                          <CommandInput placeholder="Cari daerah..." />
                          <CommandList>
                            <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                            <CommandGroup>
                              {daerahOptions?.map((daerah) => (
                                <CommandItem
                                  key={daerah.id_adm_kabkot} // Menggunakan id unik sebagai key
                                  value={daerah.nama} // Nama daerah yang digunakan sebagai value
                                  onSelect={(currentValue) => {
                                    setFieldValue(
                                      "id_adm_kabkot",
                                      daerah.id_adm_kabkot
                                    ); // Menyimpan nama daerah ke form
                                    setSelectedDaerah(daerah); // Menyimpan seluruh objek daerah ke state
                                    setOpenDaerah(false); // Menutup popover setelah pemilihan
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      selectedDaerah?.nama === daerah.nama
                                        ? "opacity-100"
                                        : "opacity-0"
                                    )}
                                  />
                                  {daerah.nama}{" "}
                                  {/* Menampilkan hanya nama daerah */}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>

                    <ErrorMessage
                      name="daerah"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">
                      Sektor Peluang
                      <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                    </Label>
                    <Popover open={openSektor} onOpenChange={setOpenSektor}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          className="text-xs justify-between col-span-3 w-full"
                        >
                          {selectedSektor?.nama || "Pilih Sektor Peluang"}
                          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-full p-0"
                        container={containerRef.current}
                      >
                        <Command>
                          <CommandInput placeholder="Cari sektor..." />
                          <CommandList>
                            <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                            <CommandGroup>
                              {sektorOptions?.map((sektor) => (
                                <CommandItem
                                  key={sektor.id_peluang_sektor}
                                  value={sektor.nama}
                                  onSelect={() => {
                                    setFieldValue(
                                      "id_sektor",
                                      sektor.id_peluang_sektor
                                    );
                                    setSelectedSektor(sektor);
                                    setOpenSektor(false);
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      selectedSektor?.nama === sektor.nama
                                        ? "opacity-100"
                                        : "opacity-0"
                                    )}
                                  />
                                  {sektor.nama}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <ErrorMessage
                      name="sektor_peluang"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">
                      Sumber Data
                      <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                    </Label>
                    <SumberDataDropdown
                      onSelect={(selectedData) => {
                        setFieldValue(
                          "id_sumber_data",
                          selectedData.id_sumber_data
                        );
                      }}
                      className="col-span-3 w-full"
                      defaultValue={values.id_sumber_data}
                    />

                    <ErrorMessage
                      name="sumber_data"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">
                      Apakah IKN
                    </Label>
                    <Popover
                      open={openApakahIKN}
                      onOpenChange={setOpenApakahIKN}
                    >
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="text-xs justify-between col-span-3 w-full"
                        >
                          {selectedApakahIKN || "Apakah IKN?"}
                          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-full p-0"
                        container={containerRef.current}
                      >
                        <Command>
                          <CommandInput placeholder="Cari apakah IKN..." />
                          <CommandList>
                            <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                            <CommandGroup>
                              {apakahIKN.map((ikn) => (
                                <CommandItem
                                  key={ikn}
                                  value={ikn}
                                  onSelect={(currentValue) => {
                                    setFieldValue("is_ikn", true);
                                    setSelectedApakahIKN(currentValue);
                                    setOpenApakahIKN(false);
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      values.is_ikn === ikn
                                        ? "opacity-100"
                                        : "opacity-0"
                                    )}
                                  />
                                  {ikn}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <ErrorMessage
                      name="is_ikn"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">
                      Apakah IPRO?
                    </Label>
                    <Input
                      type="checkbox"
                      name="is_ipro"
                      checked={values.is_ipro === 1 || values.is_ipro === true} // Checkbox is checked if value is 1
                      onChange={(e) =>
                        setFieldValue("is_ipro", e.target.checked ? 1 : 0)
                      } // Set 1 for true, 0 for false
                      className="h-4 col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="is_ipro"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">
                      <span>Kode KBLI </span>
                      <span className="text-[10px] text-red-600">
                        Jika lebih dari satu dipisahkan dengan tanda koma (,)
                        atau Spasi _ 
                      </span>
                      <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                    </Label>
                    <Input
                      type="text"
                      name="kode_kbli"
                      placeholder="Kode KBLI..."
                      onChange={(e) =>
                        setFieldValue("kode_kbli", e.target.value)
                      }
                      value={values.kode_kbli}
                      className="justify-between col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="kode_kbli"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">
                      Judul Peluang
                      <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                    </Label>
                    <Input
                      type="text"
                      name="nama"
                      placeholder="Judul Peluang..."
                      onChange={(e) => setFieldValue("nama", e.target.value)}
                      value={values.nama}
                      className="justify-between col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="judul_peluang"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">
                      Status Peluang
                      {/* <span className="text-[12px] text-red-600">&nbsp;(*)</span> */}
                    </Label>
                    <Popover
                      open={openStatusPeluang}
                      onOpenChange={setOpenStatusPeluang}
                    >
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          className="text-xs justify-between col-span-3 w-full"
                        >
                          {selectedStatusPeluang?.nama ||
                            "Pilih Status Peluang"}
                          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-full p-0"
                        container={containerRef.current}
                      >
                        <Command>
                          <CommandInput placeholder="Cari status peluang..." />
                          <CommandList>
                            <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                            <CommandGroup>
                              {statusPeluangOptions?.map((status) => (
                                <CommandItem
                                  key={status.id_peluang_status}
                                  value={status.nama}
                                  onSelect={(currentValue) => {
                                    setFieldValue(
                                      "project_status_enum",
                                      status.id_peluang_status.toString()
                                    );
                                    setEmailData((prev) => ({
                                      ...prev,
                                      project_status_enum:
                                        status.id_peluang_status,
                                    }));
                                    setSelectedStatusPeluang(status);
                                    setOpenStatusPeluang(false);
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      selectedStatusPeluang?.nama ===
                                        status.nama
                                        ? // values?.status === status.nama
                                          "opacity-100"
                                        : "opacity-0"
                                    )}
                                  />
                                  {status.nama}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <ErrorMessage
                      name="status_peluang"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">
                      Lokasi Kawasan
                      <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                    </Label>
                    <Input
                      type="text"
                      name="lokasi_kawasan"
                      placeholder="Lokasi Kawasan..."
                      onChange={(e) =>
                        setFieldValue("lokasi_kawasan", e.target.value)
                      }
                      value={values.lokasi_kawasan}
                      className="justify-between col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="lokasi_kawasan"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">Tahun
                    <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                    </Label>
                    <Popover open={openTahun} onOpenChange={setOpenTahun}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="text-xs justify-between col-span-3 w-full"
                          disabled={true}
                        >
                          {selectedTahun || "Pilih Tahun"}
                          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-full p-0"
                        container={containerRef.current}
                      >
                        <Command>
                          <CommandInput placeholder="Cari Tahun..." />
                          <CommandList>
                            <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                            <CommandGroup>
                              {tahunOptions?.map((tahun) => (
                                <CommandItem
                                  key={tahun}
                                  value={tahun}
                                  onSelect={(currentValue) => {
                                    const tahunInteger = parseInt(currentValue);
                                    setFieldValue("tahun", tahunInteger);
                                    setSelectedTahun(tahun);
                                    setOpenTahun(false);
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      values.tahun === tahun
                                        ? "opacity-100"
                                        : "opacity-0"
                                    )}
                                  />
                                  {tahun}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <ErrorMessage
                      name="status_peluang"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">
                      Prioritas
                      <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                    </Label>
                    <Popover
                      open={openPrioritas}
                      onOpenChange={setOpenPrioritas}
                    >
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="text-xs justify-between col-span-3 w-full"
                          disabled={true}
                        >
                          {selectedPrioritas?.nama || "Pilih Prioritas"}
                          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-full p-0"
                        container={containerRef.current}
                      >
                        <Command>
                          <CommandInput placeholder="Cari Prioritas..." />
                          <CommandList>
                            <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                            <CommandGroup>
                              {prioritasOptions().map((prioritas) => (
                                <CommandItem
                                  key={prioritas.id}
                                  value={prioritas.nama}
                                  onSelect={(currentValue) => {
                                    setFieldValue("id_prioritas", prioritas.id);
                                    setSelectedPrioritas(prioritas);
                                    setOpenPrioritas(false);
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      selectedPrioritas?.nama === prioritas.nama
                                        ? "opacity-100"
                                        : "opacity-0"
                                    )}
                                  />
                                  {prioritas.nama}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <ErrorMessage
                      name="prioritas"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">
                      Nilai Investasi
                      <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                    </Label>
                    {/* <Input
                    type="number"
                    name="nilai_investasi"
                    placeholder="Nilai Investasi..."
                    onChange={(e) =>
                      setFieldValue("nilai_investasi", e.target.value)
                    }
                    value={values.nilai_investasi}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage
                    name="nilai_investasi"
                    component="div"
                    className="text-red-500"
                  /> */}
                    <Input
                      type="text"
                      name="nilai_investasi"
                      placeholder="Nilai Investasi..."
                      onChange={(e) => {
                        const rawValue = e.target.value.replace(/\D/g, ""); // ambil angka saja
                        const formatted =
                          Number(rawValue).toLocaleString("id-ID");
                        setFieldValue("nilai_investasi", formatted); // tampilkan terformat
                      }}
                      value={values.nilai_investasi}
                      className="justify-between col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="nilai_investasi"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    />

                    <Label className="block text-xs font-medium">
                      Nilai IRR
                      <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                    </Label>
                    <Input
                      name="nilai_irr"
                      type="number"
                      placeholder="Nilai IRR..."
                      onChange={(e) =>
                        setFieldValue("nilai_irr", e.target.value)
                      }
                      value={values.nilai_irr}
                      className="justify-between col-span-3 w-full text-xs"
                      step="any"
                    />
                    <ErrorMessage
                      name="nilai_irr"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">
                      Nilai NPV
                      <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                    </Label>
                    {/* <Input
                    name="nilai_npv"
                    type="number"
                    placeholder="Nilai NPV..."
                    onChange={(e) => setFieldValue("nilai_npv", e.target.value)}
                    value={values.nilai_npv}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage
                    name="nilai_npv"
                    component="div"
                    className="text-red-500"
                  /> */}
                    <Input
                      type="text"
                      name="nilai_npv"
                      placeholder="Nilai NPV..."
                      onChange={(e) => {
                        const rawValue = e.target.value.replace(/\D/g, ""); // hanya angka
                        const formatted =
                          Number(rawValue).toLocaleString("id-ID"); // format IDR
                        setFieldValue("nilai_npv", formatted);
                      }}
                      value={values.nilai_npv}
                      className="justify-between col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="nilai_npv"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    />

                    <Label className="block text-xs font-medium">
                      Nilai PP
                      <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                    </Label>
                    <Input
                      name="nilai_pp"
                      type="number"
                      placeholder="Nilai PP..."
                      onChange={(e) =>
                        setFieldValue("nilai_pp", e.target.value)
                      }
                      value={values.nilai_pp}
                      className="justify-between col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="nilai_pp"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">
                      Keterangan
                    </Label>

                    <div className="justify-between col-span-3 w-full text-xs">
                      <ReactQuill
                        value={values.keterangan} // Menetapkan nilai saat ini dari form
                        onChange={(content) =>
                          setFieldValue("keterangan", content)
                        } // Mengupdate nilai form
                        placeholder="Keterangan..."
                        className="w-full" // Mengatur lebar Quill editor
                      />
                    </div>
                    <ErrorMessage
                      name="keterangan"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">
                      Upload Foto{" "}
                      <span className="text-[10px] text-red-600">
                        Max 2Mb per file
                      </span>
                    </Label>
                    <FieldArray name="images">
                      {({ remove, push }) => (
                        <div className="justify-between col-span-3 w-full">
                          {values.images.map((image, index) => (
                            <div
                              key={index}
                              className="mb-4 flex items-center space-x-2"
                            >
                              <div className="w-full flex-row flex items-center">
                                <Input
                                  name={`images[${index}]`}
                                  type="file"
                                  accept="image/jpeg, image/jpg, image/png, image/webp, image/svg+xml"
                                  onChange={(event) => {
                                    const file = event.currentTarget.files[0];
                                    setFieldValue(`images[${index}]`, file);
                                    if (
                                      image &&
                                      image.id_peluang_kabkot_file
                                    ) {
                                      setFieldValue("list_hapus_file", [
                                        ...values.list_hapus_file,
                                        image.id_peluang_kabkot_file,
                                      ]);
                                    }
                                    const reader = new FileReader();
                                    // reader.onloadend = () => {
                                    //   const updatedPreviews = [...imagePreview];
                                    //   updatedPreviews[index] = reader.result;
                                    //   setImagePreview(updatedPreviews);
                                    // };
                                    reader.onloadend = () => {
                                      setImagePreview(
                                        (prevPreviews) => {
                                          const updatedPreviews = [
                                            ...prevPreviews,
                                          ];
                                          updatedPreviews[index] =
                                            reader.result;
                                          return updatedPreviews;
                                        }
                                      );
                                    };
                                    if (file) {
                                      reader.readAsDataURL(file);
                                    }
                                  }}
                                  className="justify-between col-span-1 w-full text-xs"
                                />
                                <div className="relative col-span-2 w-80 mr-auto">
                                  {imagePreview[index] ? (
                                    <Image
                                      src={imagePreview[index]}
                                      alt="Image Preview"
                                      width={320}
                                      height={0}
                                      layout="responsive"
                                      objectFit="cover"
                                      className="rounded"
                                    />
                                  ) : (
                                    <div className="w-full h-full bg-white rounded flex items-center justify-center"></div>
                                  )}
                                </div>
                                {index === values.images.length - 1 && (
                                  <Button
                                    type="button"
                                    onClick={() => {
                                      push("");
                                      setImagePreview(
                                        (prevPreviews) => [
                                          ...prevPreviews,
                                          null,
                                        ]
                                    ) }}
                                    className="text-xs hover:bg-blue-800"
                                    variant="ghost"
                                    size="icon"
                                  >
                                    <PlusCircle className="h-6 w-6" />
                                  </Button>
                                )}
                                {index !== 0 && (
                                  <Button
                                    type="button"
                                    onClick={() => {
                                      if (
                                        image &&
                                        image.id_peluang_kabkot_file
                                      ) {
                                        setFieldValue("list_hapus_file", [
                                          ...values.list_hapus_file,
                                          image.id_peluang_kabkot_file,
                                        ]);
                                      }
                                      remove(index);
                                      setImagePreview(
                                        (prevPreviews) =>
                                          prevPreviews.filter(
                                            (_, i) => i !== index
                                          )
                                      );
                                    }}
                                    className="text-red-500"
                                    variant="ghost"
                                    size="icon"
                                  >
                                    <XCircle className="h-6 w-6" />
                                  </Button>
                                )}
                              </div>
                              <ErrorMessage
                                name={`images[${index}]`}
                                component="div"
                                className="text-red-500"
                              />
                            </div>
                          ))}
                        </div>
                      )}
                    </FieldArray>

                    <ErrorMessage
                      name="images"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">
                      Upload Infografis{" "}
                      <span className="text-[10px] text-red-600">
                        Max 2MB per file, ukuran POTRAIT(1690x2590)
                      </span>
                    </Label>
                    <FieldArray name="infografis">
                      {({ remove, push }) => (
                        <div className="justify-between col-span-3 w-full">
                          {values.infografis.map((infografis, index) => (
                            <div
                              key={index}
                              className="mb-4 flex items-center space-x-2"
                            >
                              <div className="w-full flex-row flex items-center">
                                <Input
                                  name={`infografis[${index}]`}
                                  type="file"
                                  accept="image/jpeg, image/jpg, image/png, image/webp, image/svg+xml"
                                  onChange={(event) => {
                                    const file = event.currentTarget.files[0];
                                    setFieldValue(`infografis[${index}]`, file);
                                    if (
                                      infografis &&
                                      infografis.id_peluang_kabkot_file
                                    ) {
                                      setFieldValue("list_hapus_file", [
                                        ...values.list_hapus_file,
                                        infografis.id_peluang_kabkot_file,
                                      ]);
                                    }

                                    const reader = new FileReader();
                                    reader.onloadend = () => {
                                      setGambarInfografisPreview(
                                        (prevPreviews) => {
                                          const updatedPreviews = [
                                            ...prevPreviews,
                                          ];
                                          updatedPreviews[index] =
                                            reader.result;
                                          return updatedPreviews;
                                        }
                                      );
                                    };

                                    if (file) {
                                      reader.readAsDataURL(file);
                                    }
                                  }}
                                  className="justify-between col-span-1 w-full text-xs"
                                />
                                <div className="relative col-span-2 w-80 mr-auto">
                                  {gambarInfografisPreview[index] ? (
                                    <Image
                                      src={gambarInfografisPreview[index]}
                                      alt="Image Preview"
                                      width={320}
                                      height={0}
                                      layout="responsive"
                                      objectFit="cover"
                                      className="rounded"
                                    />
                                  ) : (
                                    <div className="w-full h-full bg-white rounded flex items-center justify-center"></div>
                                  )}
                                </div>
                                {index === values.infografis.length - 1 && (
                                  <Button
                                    type="button"
                                    // onClick={() => {
                                    //   push("");
                                    //   setGambarInfografisPreview(
                                    //     (prevPreviews) => [...prevPreviews, null]
                                    //   );
                                    // }}
                                    onClick={() => {
                                      push("");
                                      setFieldValue("infografis_tr", [
                                        ...values.infografis_tr,
                                        "",
                                      ]);
                                      setGambarInfografisPreview(
                                        (prevPreviews) => [
                                          ...prevPreviews,
                                          null,
                                        ]
                                      );
                                      setGambarInfografisTrPreview(
                                        (prevPreviews) => [
                                          ...prevPreviews,
                                          null,
                                        ]
                                      );
                                    }}
                                    className="text-xs hover:bg-blue-800"
                                    variant="ghost"
                                    size="icon"
                                  >
                                    <PlusCircle className="h-6 w-6" />
                                  </Button>
                                )}
                                {index !== 0 && (
                                  <Button
                                    type="button"
                                    // onClick={() => {
                                    //   if (
                                    //     infografis &&
                                    //     infografis.id_peluang_kabkot_file
                                    //   ) {
                                    //     setFieldValue("list_hapus_file", [
                                    //       ...values.list_hapus_file,
                                    //       infografis.id_peluang_kabkot_file,
                                    //     ]);
                                    //   }
                                    //   remove(index);
                                    //   setGambarInfografisPreview((prevPreviews) =>
                                    //     prevPreviews.filter((_, i) => i !== index)
                                    //   );
                                    // }}
                                    onClick={() => {
                                      if (
                                        infografis &&
                                        infografis.id_peluang_kabkot_file
                                      ) {
                                        setFieldValue("list_hapus_file", [
                                          ...values.list_hapus_file,
                                          infografis.id_peluang_kabkot_file,
                                        ]);
                                      }
                                      if (
                                        values.infografis_tr[index] &&
                                        values.infografis_tr[index]
                                          .id_peluang_kabkot_file_tr
                                      ) {
                                        setFieldValue("list_hapus_file_tr", [
                                          ...values.list_hapus_file_tr,
                                          values.infografis_tr[index]
                                            .id_peluang_kabkot_file_tr,
                                        ]);
                                      }
                                      remove(index);
                                      setFieldValue(
                                        "infografis_tr",
                                        values.infografis_tr.filter(
                                          (_, i) => i !== index
                                        )
                                      );
                                      setGambarInfografisPreview(
                                        (prevPreviews) =>
                                          prevPreviews.filter(
                                            (_, i) => i !== index
                                          )
                                      );
                                      setGambarInfografisTrPreview(
                                        (prevPreviews) =>
                                          prevPreviews.filter(
                                            (_, i) => i !== index
                                          )
                                      );
                                    }}
                                    className="text-red-500"
                                    variant="ghost"
                                    size="icon"
                                  >
                                    <XCircle className="h-6 w-6" />
                                  </Button>
                                )}
                              </div>
                              <ErrorMessage
                                name={`infografis[${index}]`}
                                component="div"
                                className="text-red-500"
                              />
                            </div>
                          ))}
                        </div>
                      )}
                    </FieldArray>
                    {/* <FieldArray name="infografis">
                    {({ remove, push }) => (
                      <div className="justify-between col-span-3 w-full">
                        {values.infografis.map((infografis, index) => (
                          <div
                            key={index}
                            className="mb-4 flex items-center space-x-2"
                          >
                            <div className="w-full flex-row flex items-center">
                              <Input
                                name={`infografis[${index}]`}
                                type="file"
                                accept="image/jpeg, image/jpg, image/png, image/webp, image/svg+xml"
                                onChange={(event) => {
                                  const file = event.currentTarget.files[0];
                                  setFieldValue(`infografis[${index}]`, file);

                                  const reader = new FileReader();
                                  reader.onloadend = () => {
                                    setGambarInfografisPreview(
                                      (prevPreviews) => {
                                        const updatedPreviews = [
                                          ...prevPreviews,
                                        ];
                                        updatedPreviews[index] = reader.result;
                                        return updatedPreviews;
                                      }
                                    );
                                  };

                                  if (file) {
                                    reader.readAsDataURL(file);
                                  }
                                }}
                                className="justify-between col-span-1 w-full text-xs"
                              />
                              <div className="relative col-span-2 w-80 mr-auto">
                                {gambarInfografisPreview[index] ? (
                                  <Image
                                    src={gambarInfografisPreview[index]}
                                    alt="Image Preview"
                                    width={320}
                                    height={0}
                                    layout="responsive"
                                    objectFit="cover"
                                    className="rounded"
                                  />
                                ) : (
                                  <div className="w-full h-full bg-white rounded flex items-center justify-center"></div>
                                )}
                              </div>
                              {index === values.infografis.length - 1 && (
                                <Button
                                  type="button"
                                  onClick={() => {
                                    push("");
                                    setGambarInfografisPreview(
                                      (prevPreviews) => [...prevPreviews, null]
                                    );
                                  }}
                                  className="text-xs hover:bg-blue-800"
                                  variant="ghost"
                                  size="icon"
                                >
                                  <PlusCircle className="h-6 w-6" />
                                </Button>
                              )}
                              {index !== 0 && (
                                <Button
                                  type="button"
                                  onClick={() => {
                                    if (
                                      infografis &&
                                      infografis.id_peluang_kabkot_file
                                    ) {
                                      setFieldValue("list_hapus_file", [
                                        ...values.list_hapus_file,
                                        infografis.id_peluang_kabkot_file,
                                      ]);
                                    }
                                    remove(index);
                                    setGambarInfografisPreview((prevPreviews) =>
                                      prevPreviews.filter((_, i) => i !== index)
                                    );
                                  }}
                                  className="text-red-500"
                                  variant="ghost"
                                  size="icon"
                                >
                                  <XCircle className="h-6 w-6" />
                                </Button>
                              )}
                            </div>
                            <ErrorMessage
                              name={`infografis[${index}]`}
                              component="div"
                              className="text-red-500"
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </FieldArray> */}

                    <ErrorMessage
                      name="infografis"
                      component="div"
                      className="text-red-500"
                    />
                    <Label className="block text-xs font-medium">
                      Upload Dokumen{" "}
                      <span className="text-[10px] text-red-600">
                        Max 20Mb per file
                      </span>
                    </Label>
                    <FieldArray name="dokumen">
                      {({ remove, push }) => (
                        <div className="justify-between col-span-3 w-full">
                          {(values.dokumen?.length === 0
                            ? [""]
                            : values.dokumen
                          )?.map((dokumen, index) => (
                            <div
                              key={index}
                              className="mb-4 flex items-center space-x-2"
                            >
                              <div className="w-full flex-col flex items-center">
                                <Input
                                  name={`dokumen[${index}]`}
                                  type="file"
                                  accept="application/pdf"
                                  onChange={(event) => {
                                    const file = event.currentTarget.files[0];

                                    if (file) {
                                      const tempUrl = URL.createObjectURL(file);
                                      setFieldValue(`dokumen[${index}]`, file);
                                      setFieldValue(
                                        `dokumen_detail[${index}].fileName`,
                                        file.name
                                      );

                                      const updatedPreviews = [
                                        ...dokumenPreview,
                                      ];
                                      updatedPreviews[index] = tempUrl;
                                      setDokumenPreview(updatedPreviews);
                                    }
                                  }}
                                  className="justify-between col-span-3 w-full text-xs"
                                />
                                <div className="relative col-span-2 w-80 mr-auto">
                                  {dokumenPreview[index] ? (
                                    <div className="p-2 bg-gray-100 rounded text-xs flex items-center">
                                      <span className="truncate">
                                        {dokumenPreview[index].includes("blob:")
                                          ? values.dokumen.nama
                                          : dokumenPreview[index]}
                                      </span>
                                      <a
                                        href={dokumenPreview[index]}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="ml-2 text-blue-600 underline text-xs"
                                      >
                                        Lihat
                                      </a>
                                    </div>
                                  ) : (
                                    <div className="w-full h-full bg-gray-100 rounded flex items-center justify-center">
                                      <span className="text-gray-500 text-xs">
                                        Belum ada dokumen
                                      </span>
                                    </div>
                                  )}
                                </div>
                                {index === values.dokumen.length - 1 && (
                                  <Button
                                    type="button"
                                    onClick={() => push("")}
                                    className="text-xs hover:bg-blue-800"
                                    variant="ghost"
                                    size="icon"
                                  >
                                    <PlusCircle className="h-6 w-6" />
                                  </Button>
                                )}
                                {index !== 0 && (
                                  <Button
                                    type="button"
                                    onClick={() => {
                                      if (
                                        dokumen &&
                                        dokumen.id_peluang_kabkot_file
                                      ) {
                                        setFieldValue("list_hapus_file", [
                                          ...values.list_hapus_file,
                                          dokumen.id_peluang_kabkot_file,
                                        ]);
                                      }
                                      remove(index);
                                      setDokumenPreview((prevPreviews) =>
                                        prevPreviews.filter(
                                          (_, i) => i !== index
                                        )
                                      );
                                    }}
                                    className="text-red-500"
                                    variant="ghost"
                                    size="icon"
                                  >
                                    <XCircle className="h-6 w-6" />
                                  </Button>
                                )}
                              </div>
                              <ErrorMessage
                                name={`dokumen[${index}]`}
                                component="div"
                                className="text-red-500"
                              />
                            </div>
                          ))}
                        </div>
                      )}
                    </FieldArray>

                    <ErrorMessage
                      name="dokumen"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">
                      Video Url
                    </Label>
                    <Input
                      name="vidio"
                      type="text"
                      value={values.vidio}
                      onChange={(e) => setFieldValue("vidio", e.target.value)}
                      placeholder="Video url..."
                      className="justify-between col-span-3 w-full text-xs"
                      ref={vidioInputRef}
                    />
                     <Label className="block text-xs font-medium">
                    </Label>
                    <div className="justify-between  text-xs">
                    {dataEdit?.tb_peluang_kabkot_file?.video?.[0]?.path_file ? (
                                    <div className="p-2 bg-gray-100 rounded text-xs flex items-center">
                                      <span className="truncate">
                                        {dataEdit?.tb_peluang_kabkot_file?.video?.[0]?.path_file}
                                      </span>
                                      <a
                                        href={dataEdit?.tb_peluang_kabkot_file?.video?.[0]?.path_file}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="ml-2 text-blue-600 underline text-xs"
                                      >
                                        Lihat
                                      </a>
                                    </div>
                      ) : (
                        <div className="w-full h-full bg-gray-100 rounded flex items-center justify-center">
                          <span className="text-gray-500 text-xs">
                            Belum ada vidio
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="col-span-2"></div>
                    <Label className="block text-xs font-medium">
                    </Label>
                    <div className="col-span-3">
                    <ErrorMessage
                      name="vidio"
                      component="div"
                      className="text-red-500"
                    />
                    </div>
                    
                    <Label className="block text-xs font-medium">
                      Map Service
                    </Label>
                    <FieldArray name="map_service">
                      {({ push, remove }) => (
                        <div className="justify-between col-span-3 w-full">
                          {values.map_service.map((input, index) => (
                            <div
                              key={index}
                              className="mb-4 grid grid-cols-12 items-center gap-4"
                            >
                              {/* Tombol untuk Set Active */}
                              <div className="col-span-2">
                                <Button
                                  type="button"
                                  onClick={() => {
                                    const newStatus =
                                      !!values.map_service[index].is_active;
                                    setFieldValue(
                                      `map_service[${index}].is_active`,
                                      !newStatus
                                    );
                                  }}
                                  className={`text-xs ${
                                    !!values.map_service[index].is_active
                                      ? "text-green-500"
                                      : "text-gray-500"
                                  }`}
                                  variant="ghost"
                                >
                                  {!!values.map_service[index].is_active
                                    ? "Active"
                                    : "Inactive"}
                                </Button>
                              </div>

                              {/* Tombol Preview */}
                              <div className="col-span-2">
                                <Button
                                  type="button"
                                  variant="secondary"
                                  onClick={() => {
                                    if (previewLayer === input.layeruid) {
                                      // Jika sudah di-preview, reset previewLayer
                                      setPreviewLayer(null);
                                    } else {
                                      // Jika belum di-preview, set layeruid sebagai previewLayer
                                      setPreviewLayer(input.layeruid);
                                    }
                                  }}
                                  className={`text-xs py-2 px-4 ${
                                    previewLayer === input.layeruid
                                      ? "bg-blue-500"
                                      : "bg-gray-500"
                                  } hover:bg-gray-700 text-white`}
                                >
                                  {previewLayer === input.layeruid
                                    ? "Unpreview"
                                    : "Preview"}
                                </Button>
                              </div>

                              {/* Field untuk Layer UID */}
                              <div className="col-span-3">
                                <Input
                                  name={`map_service[${index}].layeruid`}
                                  type="text"
                                  placeholder="Layer UID"
                                  value={input.layeruid || ""}
                                  onChange={(event) =>
                                    setFieldValue(
                                      `map_service[${index}].layeruid`,
                                      event.currentTarget.value
                                    )
                                  }
                                  className="w-full text-xs"
                                />
                                <ErrorMessage
                                  name={`map_service[${index}].layeruid`}
                                  component="div"
                                  className="text-red-500 text-xs"
                                />
                              </div>

                              {/* Field untuk Keterangan */}
                              <div className="col-span-4">
                                <Input
                                  name={`map_service[${index}].keterangan`}
                                  type="text"
                                  placeholder="Keterangan"
                                  value={input.keterangan || ""}
                                  onChange={(event) =>
                                    setFieldValue(
                                      `map_service[${index}].keterangan`,
                                      event.currentTarget.value
                                    )
                                  }
                                  className="w-full text-xs"
                                />
                                <ErrorMessage
                                  name={`map_service[${index}].keterangan`}
                                  component="div"
                                  className="text-red-500 text-xs"
                                />
                              </div>

                              {/* Tombol Tambah/Hapus */}
                              <div className="col-span-1 flex justify-end">
                                {index === 0 && (
                                  <Button
                                    type="button"
                                    onClick={() =>
                                      push({ layeruid: "", keterangan: "" })
                                    }
                                    className="text-xs hover:bg-blue-800"
                                    variant="ghost"
                                    size="icon"
                                  >
                                    <PlusCircle className="h-6 w-6" />
                                  </Button>
                                )}
                                {index !== 0 && (
                                  <Button
                                    type="button"
                                    onClick={() => remove(index)}
                                    className="text-red-500"
                                    variant="ghost"
                                    size="icon"
                                  >
                                    <XCircle className="h-6 w-6" />
                                  </Button>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </FieldArray>
                    <ErrorMessage
                      name="map_service"
                      component="div"
                      className="text-red-500"
                    />
                    {previewLayer && (
                      <div className="col-span-3 col-start-2">
                        <h3 className="text-sm font-bold mb-4">Preview Peta</h3>
                        <div className="w-full h-80 border border-gray-300 rounded-md">
                          <MapService />
                        </div>
                      </div>
                    )}
                  </div>

                  <DialogHeader className="inline-block bg-orange-500 text-white py-2 px-4 rounded font-semibold">
                    <DialogTitle className="inline">Peluang Kontak</DialogTitle>
                  </DialogHeader>
                  <KontakTable setFieldValue={setFieldValue} />

                  <DialogHeader className="inline-block bg-orange-500 text-white py-2 px-4 rounded font-semibold">
                    <DialogTitle className="inline">
                      Peluang Insentif
                    </DialogTitle>
                  </DialogHeader>

                  <div
                    ref={containerRef}
                    className="grid grid-cols-4 items-center gap-4"
                  >
                    <Label className="block text-xs font-medium">
                      Jenis Insentif
                    </Label>

                    <FieldArray name="peluang_insentif">
                      {({ push, remove }) => (
                        <Popover
                          modal={true}
                          open={openPeluangInsentif}
                          onOpenChange={setOpenPeluangInsentif}
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="justify-between col-span-2 w-full text-xs"
                            >
                              {/* {values.peluang_insentif.length > 0
                                                        ? values.peluang_insentif.map(id => {
                                                            const insentif = peluangInsentifOptions.find(option => option.id_sektor_nasional_insentif === id);
                                                            return insentif ? insentif.nama : '';
                                                        }).join(', ')
                                                        : "Pilih Peluang Insentif"} */}
                              Pilih Peluang Insentif
                              <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-full max-w-[400px] p-0">
                            <Command>
                              <CommandInput placeholder="Cari peluang insentif..." />
                              <CommandList>
                                <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                                {peluangInsentifOptions?.map((insentif) => (
                                  <CommandItem
                                    key={insentif.id_jenis_insentif}
                                    onSelect={() => {
                                      const selectedIndex =
                                        values.peluang_insentif.indexOf(
                                          insentif.id_jenis_insentif
                                        );
                                      if (selectedIndex === -1) {
                                        // Menambahkan insentif yang dipilih
                                        push(insentif.id_jenis_insentif);
                                      } else {
                                        // Menghapus insentif yang sudah dipilih
                                        remove(selectedIndex);
                                      }
                                    }}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        values.peluang_insentif.includes(
                                          insentif.id_jenis_insentif
                                        )
                                          ? "opacity-100"
                                          : "opacity-0"
                                      )}
                                    />
                                    {insentif.nama}
                                  </CommandItem>
                                ))}
                              </CommandList>
                            </Command>
                          </PopoverContent>

                          {/* Display selected incentives below the dropdown */}
                          {/* {values.peluang_insentif.length > 0 && (
                                                <div className="justify-between col-span-3 w-full text-xs">
                                                    <h4 className="font-semibold">Peluang Insentif Terpilih:</h4>
                                                    <ul>
                                                        {values.peluang_insentif.map(id => {
                                                            const insentif = peluangInsentifOptions.find(option => option.id_jenis_insentif === id);
                                                            return insentif ? (
                                                                <li key={insentif.id_jenis_insentif} className="flex justify-between items-center py-1">
                                                                    <span>{insentif.nama}</span>
                                                                    <button onClick={() => remove(values.peluang_insentif.indexOf(id))} className="text-red-500">Hapus</button>
                                                                </li>
                                                            ) : null;
                                                        })}
                                                    </ul>
                                                </div>
                                            )} */}
                          {values.peluang_insentif.length > 0 && (
                            <div className="justify-between col-span-3 w-full text-xs">
                              <h4 className="font-semibold">
                                Peluang Insentif Terpilih:
                              </h4>
                              <ul>
                                {values.peluang_insentif.map((id) => {
                                  const insentif = peluangInsentifOptions?.find(
                                    (option) => option.id_jenis_insentif === id
                                  );
                                  return insentif ? (
                                    <li
                                      key={insentif.id_jenis_insentif}
                                      className="flex justify-between items-center py-1"
                                    >
                                      <span>{insentif.nama}</span>
                                      <Button
                                        variant="ghost"
                                        onClick={() =>
                                          remove(
                                            values.peluang_insentif.indexOf(id)
                                          )
                                        }
                                        className="text-red-500"
                                      >
                                        <Trash className="h-4 w-4" />
                                      </Button>
                                    </li>
                                  ) : null;
                                })}
                              </ul>
                            </div>
                          )}
                        </Popover>
                      )}
                    </FieldArray>

                    <ErrorMessage
                      name="sektor_peluang"
                      component="div"
                      className="text-red-500"
                    />

                    <div className="col-span-4">
                      <Button
                        type="button"
                        onClick={() => setShowTranslateForm((prev) => !prev)}
                        className="text-xs text-white hover:bg-blue-800  mb-4 mt-2"
                      >
                        {showTranslateForm ? "Hide Translate" : "Translate EN"}
                      </Button>

                      {showTranslateForm && (
                        <div
                          ref={containerRef}
                          className="grid grid-cols-4 items-center gap-4"
                        >
                          {/* <Label className="block text-xs font-medium">
                          Judul Peluang
                        </Label>
                        <Input
                          name="tr[0]?.nama"
                          type="text"
                          onChange={(e) =>
                            setFieldValue("tr?.nama", e.target.value)
                          }
                          value={values.tr?.nama}
                          placeholder="Ketikkan judul peluang EN..."
                          className="justify-between col-span-3 w-full text-xs"
                        />
                        <ErrorMessage
                          name="tr?.nama"
                          component="div"
                          className="text-red-500"
                        />

                        <Label className="block text-xs font-medium">
                          Keterangan Lokasi Kawasan
                        </Label>
                        <Input
                          name="tr?.lokasi_kawasan"
                          type="text"
                          placeholder="Ketikkan Lokasi Kawasan EN..."
                          onChange={(e) =>
                            setFieldValue("tr?.lokasi_kawasan", e.target.value)
                          }
                          value={values.tr?.lokasi_kawasan}
                          className="justify-between col-span-3 w-full text-xs"
                        />
                        <ErrorMessage
                          name="tr?.lokasi_kawasan"
                          component="div"
                          className="text-red-500"
                        /> */}

                          <Label className="block text-xs font-medium">
                            Judul Peluang
                            <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                          </Label>
                          <Input
                            name="tr.nama"
                            type="text"
                            onChange={(e) =>
                              setFieldValue("tr.nama", e.target.value)
                            } // Pastikan path sesuai
                            value={values.tr?.nama || ""} // Cek apakah values.tr[0] ada dan aman
                            placeholder="Ketikkan judul peluang EN..."
                            className="justify-between col-span-3 w-full text-xs"
                          />
                          <ErrorMessage
                            name="tr.nama"
                            component="div"
                            className="text-red-500"
                          />

                          <Label className="block text-xs font-medium">
                            Keterangan Lokasi Kawasan
                          </Label>
                          <Input
                            name="tr.lokasi_kawasan"
                            type="text"
                            placeholder="Ketikkan Lokasi Kawasan EN..."
                            onChange={(e) =>
                              setFieldValue("tr.lokasi_kawasan", e.target.value)
                            } // Pastikan path sesuai
                            value={values.tr?.lokasi_kawasan || ""} // Cek apakah values.tr[0] ada dan aman
                            className="justify-between col-span-3 w-full text-xs"
                          />
                          <ErrorMessage
                            name="tr.lokasi_kawasan"
                            component="div"
                            className="text-red-500"
                          />

                          <Label className="block text-xs font-medium">
                            Keterangan
                          </Label>

                          <div className="justify-between col-span-3 w-full text-xs">
                            <ReactQuill
                              value={values.tr?.keterangan} // Menetapkan nilai saat ini dari form
                              onChange={(content) =>
                                setFieldValue("tr.keterangan", content)
                              } // Mengupdate nilai form
                              placeholder="Keterangan en..."
                              className="w-full" // Mengatur lebar Quill editor
                            />
                          </div>
                          <ErrorMessage
                            name="tr.keterangan"
                            component="div"
                            className="text-red-500"
                          />

                          <Label className="block text-xs font-medium">
                            Tagline
                          </Label>
                          <Input
                            name="tr.deskripsi_singkat"
                            type="text"
                            placeholder="Ketikkan tagline EN..."
                            onChange={(e) =>
                              setFieldValue(
                                "tr.deskripsi_singkat",
                                e.target.value
                              )
                            }
                            value={values.tr?.deskripsi_singkat}
                            className="justify-between col-span-3 w-full text-xs"
                          />
                          <ErrorMessage
                            name="tr.deskripsi_singkat"
                            component="div"
                            className="text-red-500"
                          />

                          <Label className="block text-xs font-medium">
                            Deskripsi Singkat
                          </Label>
                          <Input
                            name="tr.deskripsi"
                            type="text"
                            placeholder="Ketikkan deskripsi singkat EN..."
                            onChange={(e) =>
                              setFieldValue("tr.deskripsi", e.target.value)
                            }
                            value={values.tr?.deskripsi}
                            className="justify-between col-span-3 w-full text-xs"
                          />
                          <ErrorMessage
                            name="tr.deskripsi"
                            component="div"
                            className="text-red-500"
                          />

                          <Label className="block text-xs font-medium">
                            Upload Infografis En{" "}
                            <span className="text-[10px] text-red-600">
                              Max 2MB per file, ukuran POTRAIT(1690x2590)
                            </span>
                          </Label>
                          {/* <FieldArray name="infografis_tr">
                            {({ remove, push }) => (
                              <div className="justify-between col-span-3 w-full">
                                {values.infografis_tr.map(
                                  (infografis_tr, index) => (
                                    <div
                                      key={index}
                                      className="mb-4 flex items-center space-x-2"
                                    >
                                      <div className="w-full flex-row flex items-center">
                                        <Input
                                          name={`infografis_tr[${index}]`}
                                          type="file"
                                          accept="image/jpeg, image/jpg, image/png, image/webp, image/svg+xml"
                                          onChange={(event) => {
                                            const file =
                                              event.currentTarget.files[0];
                                            setFieldValue(
                                              `infografis_tr[${index}]`,
                                              file
                                            );
                                            if (
                                              infografis_tr &&
                                              infografis_tr.id_peluang_kabkot_file_tr
                                            ) {
                                              setFieldValue(
                                                "list_hapus_file_tr",
                                                [
                                                  ...values.list_hapus_file_tr,
                                                  infografis_tr.id_peluang_kabkot_file_tr,
                                                ]
                                              );
                                            }

                                            const reader = new FileReader();
                                            reader.onloadend = () => {
                                              setGambarInfografisTrPreview(
                                                (prevPreviews) => {
                                                  const updatedPreviews = [
                                                    ...prevPreviews,
                                                  ];
                                                  updatedPreviews[index] =
                                                    reader.result;
                                                  return updatedPreviews;
                                                }
                                              );
                                            };

                                            if (file) {
                                              reader.readAsDataURL(file);
                                            }
                                          }}
                                          className="justify-between col-span-1 w-full text-xs"
                                        />
                                        <div className="relative col-span-2 w-80 mr-auto">
                                          {gambarInfografisTrPreview[index] ? (
                                            <Image
                                              src={
                                                gambarInfografisTrPreview[index]
                                              }
                                              alt="Image Preview"
                                              width={320}
                                              height={0}
                                              layout="responsive"
                                              objectFit="cover"
                                              className="rounded"
                                            />
                                          ) : (
                                            <div className="w-full h-full bg-white rounded flex items-center justify-center"></div>
                                          )}
                                        </div>
                                      </div>
                                      <ErrorMessage
                                        name={`infografis_tr[${index}]`}
                                        component="div"
                                        className="text-red-500"
                                      />
                                    </div>
                                  )
                                )}
                              </div>
                            )}
                          </FieldArray> */}
                          <FieldArray name="infografis_tr">
                          {({ remove, push }) => (
                            <div className="justify-between col-span-3 w-full">
                              {values.infografis_tr.map((infografis_tr, index) => (
                                <div
                                  key={index}
                                  className="mb-4 flex items-center space-x-2"
                                >
                                  <div className="w-full flex-row flex items-center">
                                    <Input
                                      name={`infografis_tr[${index}]`}
                                      type="file"
                                      accept="image/jpeg, image/jpg, image/png, image/webp, image/svg+xml"
                                      onChange={(event) => {
                                        const file = event.currentTarget.files[0];
                                        setFieldValue(`infografis_tr[${index}]`, file);

                                        const reader = new FileReader();
                                        reader.onloadend = () => {
                                          setGambarInfografisTrPreview(
                                            (prevPreviews) => {
                                              const updatedPreviews = [
                                                ...prevPreviews,
                                              ];
                                              updatedPreviews[index] = reader.result;
                                              return updatedPreviews;
                                            }
                                          );
                                        };

                                        if (file) {
                                          reader.readAsDataURL(file);
                                        }
                                      }}
                                      className="justify-between col-span-1 w-full text-xs"
                                    />
                                    <div className="relative col-span-2 w-80 mr-auto">
                                      {gambarInfografisTrPreview[index] ? (
                                        <Image
                                          src={gambarInfografisTrPreview[index]}
                                          alt="Image Preview"
                                          width={320}
                                          height={0}
                                          layout="responsive"
                                          objectFit="cover"
                                          className="rounded"
                                        />
                                      ) : (
                                        <div className="w-full h-full bg-white rounded flex items-center justify-center"></div>
                                      )}
                                    </div>
                                    {index === values.infografis_tr.length - 1 && (
                                      <Button
                                        type="button"
                                        onClick={() => {
                                          push("");
                                          setGambarInfografisTrPreview(
                                            (prevPreviews) => [...prevPreviews, null]
                                          );
                                        }}
                                        className="text-xs hover:bg-blue-800"
                                        variant="ghost"
                                        size="icon"
                                      >
                                        <PlusCircle className="h-6 w-6" />
                                      </Button>
                                    )}
                                    {index !== 0 && (
                                      <Button
                                        type="button"
                                        onClick={() => {
                                          if (
                                            infografis_tr &&
                                            infografis_tr.id_peluang_kabkot_file_tr
                                          ) {
                                            setFieldValue("list_hapus_file_tr", [
                                              ...values.list_hapus_file_tr,
                                              infografis_tr.id_peluang_kabkot_file_tr,
                                            ]);
                                          }
                                          remove(index);
                                          setGambarInfografisTrPreview((prevPreviews) =>
                                            prevPreviews.filter((_, i) => i !== index)
                                          );
                                        }}
                                        className="text-red-500"
                                        variant="ghost"
                                        size="icon"
                                      >
                                        <XCircle className="h-6 w-6" />
                                      </Button>
                                    )}
                                  </div>
                                  <ErrorMessage
                                    name={`infografis_tr[${index}]`}
                                    component="div"
                                    className="text-red-500"
                                  />
                                </div>
                              ))}
                            </div>
                          )}
                        </FieldArray>

                          <ErrorMessage
                            name="infografis"
                            component="div"
                            className="text-red-500"
                          />
                        </div>
                      )}
                    </div>

                    <Label className="block text-xs font-medium">
                      Latitude
                    </Label>
                    <Input
                      type="number"
                      step="0.000001"
                      placeholder="Masukkan latitude"
                      name="lat"
                      value={values.lat}
                      onChange={(e) => setFieldValue("lat", e.target.value)}
                      className="justify-between col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="lat"
                      component="div"
                      className="text-red-500"
                    />

                    <Label className="block text-xs font-medium">
                      Longitude
                    </Label>
                    <Input
                      type="number"
                      step="0.000001"
                      placeholder="Masukkan longitude"
                      name="lon"
                      value={values.lon}
                      onChange={(e) => setFieldValue("lon", e.target.value)}
                      className="justify-between col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="lon"
                      component="div"
                      className="text-red-500"
                    />
                    <div className="col-span-4">
                      <MapTag
                        setValue={(name, value) => setFieldValue(name, value)}
                        latFormName="lat"
                        lngFormName="lon"
                        values={values}
                        defaultValue={{ lat: values.lat, lng: values.lon }}
                      />
                    </div>
                  </div>

                  {/* Submit button */}
                  <DialogFooter>
                    <Button
                      type="button"
                      onClick={() => setEmailPopupOpen(true)} // Menampilkan popup email
                      className="text-xs bg-blue-500 hover:bg-blue-700"
                      size="sm"
                    >
                      Email
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="text-xs bg-blue-500 hover:bg-blue-700"
                      size="sm"
                    >
                      {isSubmitting ? "Submitting..." : "Simpan"}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>

            {isEmailPopupOpen && (
              <Dialog
                open={isEmailPopupOpen}
                onOpenChange={(open) => setEmailPopupOpen(open)}
              >
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Kirim Email</DialogTitle>
                  </DialogHeader>
                  {/* <form onSubmit={handleEmailSubmit}> */}
                  <form onSubmit={(e) => e.preventDefault()}>
                    <div className="mb-4">
                      <label
                        htmlFor="subjek"
                        className="block text-sm font-medium"
                      >
                        Subjek
                      </label>
                      <Select
                        name="subjek"
                        value={emailData.subjek} // Ensure this is an integer
                        onValueChange={(value) =>
                          setEmailData((prev) => ({
                            ...prev,
                            subjek: parseInt(value, 10), // Convert to integer before updating the state
                          }))
                        }
                      >
                        <SelectTrigger>
                          {/* Use the integer value in the display logic */}
                          <SelectValue placeholder="Pilih status">
                            {emailData.subjek === 1 && "Nota Dinas"}{" "}
                            {/* Compare with integer */}
                            {emailData.subjek === 2 && "Informasi"}{" "}
                            {/* Compare with integer */}
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          {/* Ensure the value passed to SelectItem is an integer */}
                          <SelectItem value={1}>Nota Dinas</SelectItem>{" "}
                          {/* Use integer value */}
                          <SelectItem value={2}>Informasi</SelectItem>{" "}
                          {/* Use integer value */}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="mb-4">
                      <label
                        htmlFor="email"
                        className="block text-sm font-medium"
                      >
                        Email Penerima
                      </label>
                      <div className="flex flex-wrap gap-2 p-2 border rounded-md min-h-[42px] mb-2">
                        {emailData?.email?.map((email, index) => (
                          <div
                            key={index}
                            className="flex items-center gap-1 bg-blue-100 px-2 py-1 rounded-md"
                          >
                            <span className="text-sm">{email}</span>
                            <button
                              type="button"
                              onClick={() => removeEmail(email)}
                              className="text-gray-500 hover:text-gray-700"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </div>
                        ))}
                        <Input
                          type="email"
                          value={emailData.currentEmail}
                          onChange={handleEmailInputChange}
                          onKeyDown={handleEmailKeyDown}
                          onBlur={handleEmailBlur}
                          onKeyUp={handleEmailTab}
                          placeholder="Ketik email dan tekan Enter"
                          className="border-none focus:ring-0 flex-grow min-w-[200px]"
                        />
                      </div>
                      <p className="text-xs text-red-500">
                        Tekan Enter atau koma untuk menambahkan email
                      </p>
                    </div>
                    <div className="mb-4">
                      <label
                        htmlFor="nama"
                        className="block text-sm font-medium"
                      >
                        Nama
                      </label>
                      <Input
                        type="text"
                        name="nama"
                        value={emailData.nama}
                        readOnly
                        className="bg-gray-100 cursor-not-allowed text-sm"
                      />
                    </div>
                    {/* <div className="mb-4">
                      <label
                        htmlFor="project_status_enum"
                        className="block text-sm font-medium"
                      >
                        Status
                      </label>
                      <Select
                        name="project_status_enum"
                        value={emailData.project_status_enum}
                        disabled
                      >
                        <SelectTrigger>
                          <SelectValue>
                            {statusPeluangOptions.find(
                              (opt) =>
                                opt.id_peluang_status ===
                                emailData.project_status_enum
                            )?.nama || "Pilih status"}
                          </SelectValue>
                        </SelectTrigger>
                      </Select>
                    </div> */}
                    <div className="mb-4">
                      <label
                        htmlFor="keterangan"
                        className="block text-sm font-medium"
                      >
                        Keterangan
                      </label>
                      <Textarea
                        name="keterangan"
                        value={emailData.keterangan}
                        onChange={handleEmailChange}
                        placeholder="Tulis pesan Anda"
                        required
                      />
                    </div>
                    <DialogFooter>
                      <Button
                        type="submit"
                        className="text-xs bg-green-500 hover:bg-green-700"
                        size="sm"
                        // disabled={isSubmitting}
                        disabled={
                          isSubmitting || 
                          !emailData.email ||
                          !emailData.subjek ||      
                          !emailData.keterangan }
                        >
                        {isSubmitting ? "Menyimpan..." : "Simpan & Email"}
                      </Button>
                      <Button
                        type="button"
                        onClick={() => setEmailPopupOpen(false)}
                        className="text-xs bg-red-500 hover:bg-red-700"
                        size="sm"
                      >
                        Batal
                      </Button>
                    </DialogFooter>
                  </form>
                </DialogContent>
              </Dialog>
            )}
          </Form>
        );
      }}
    </Formik>
  );
};

export default EditPeluangInvestasi;
