"use client";

import SumberDataDropdown from "../../SumberDataDropdown";
import SweetAlert from "../../SweetAlert";
import MapService from "../../beranda/headline-IKN/MapService";
import KontakTable from "./KontakTable";
import MapTag from "./MapTag";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import {
  changeStatusPeluangInvestasiService,
  createPeluangInvestasiService,
  editStatusPeluangInvestasiService,
} from "@/services/PeluangInvestasiService";
import useKontakStore from "@/store/KontakStore";
// const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });
// import 'react-quill/dist/quill.snow.css';

// Sesuaikan dengan store yang Anda gunakan
import { useMapServiceStore } from "@/store/MapServiceStore";
import { ErrorMessage, FieldArray, Form, Formik } from "formik";
import {
  Check,
  ChevronsUpDown,
  PlusCircle,
  Trash,
  X,
  XCircle,
} from "lucide-react";
import dynamic from "next/dynamic";
import Image from "next/image";
import { useEffect, useMemo, useRef, useState } from "react";
import * as Yup from "yup";

// Define the Yup validation schema
const createInvestmentSchema = Yup.object().shape({
  id_sumber_data: Yup.string().required("Sumber data wajib diisi"),
  keterangan: Yup.string().required("Keterangan wajib diisi"),
  nilai_investasi: Yup.string().required("Nilai Investasi wajib diisi"),
  nilai_irr: Yup.string().required("Nilai IRR wajib diisi"),
  nilai_npv: Yup.string().required("Nilai NPV wajib diisi"),
  nilai_pp: Yup.string().required("Nilai PP wajib diisi"),
  lon: Yup.number()
    .required("Longitude wajib diisi")
    .typeError("Longitude harus berupa angka"),
  lat: Yup.number()
    .required("Latitude wajib diisi")
    .typeError("Latitude harus berupa angka"),
  lokasi_kawasan: Yup.string().required("Lokasi Kawasan wajib diisi"),
  kode_kbli: Yup.string().required("Kode KBLI wajib diisi"),
  nama_tr: Yup.string().required("Judul En wajib diisi"),
  // project_status_enum: Yup.string().required("Status Peluang wajib dipilih"),
  is_ikn: Yup.boolean().required("Wajib memilih apakah IKN"),
});

const FormDialogFull = ({
  onDataChange,
  sumberDataOptions,
  kabkotOptions,
  peluangSektorOptions,
  peluangStatusOptions,
  jenisInsentifOptions,
}) => {
  const containerRef = useRef(null);

  const [isEmailPopupOpen, setEmailPopupOpen] = useState(false); // State untuk popup email
  const [emailData, setEmailData] = useState({
    email: [],
    subjek: "",
    keterangan: "",
    project_status_enum: null,
    nama: "",
  });

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [openDaerah, setOpenDaerah] = useState(false);
  const [openSektor, setOpenSektor] = useState(false);
  const [openSumberData, setOpenSumberData] = useState(false);
  const [openStatusPeluang, setOpenStatusPeluang] = useState(false);
  const [openApakahIKN, setOpenApakahIKN] = useState(false);
  const [openTahun, setOpenTahun] = useState(false);
  const [openPrioritas, setOpenPrioritas] = useState(false);
  const [openLokasiKawasan, setOpenLokasiKawasan] = useState(false);
  const [showKontak, setShowKontak] = useState(false);
  const [openPeluangInsentif, setOpenPeluangInsentif] = useState(false);
  const [showTranslateForm, setShowTranslateForm] = useState(false);

  const [selectedDaerah, setSelectedDaerah] = useState("");
  const [selectedSektor, setSelectedSektor] = useState("");
  const [selectedSumberData, setSelectedSumberData] = useState("");
  const [selectedStatusPeluang, setSelectedStatusPeluang] = useState("");
  const [selectedApakahIKN, setSelectedApakahIKN] = useState("");
  const [selectedTahun, setSelectedTahun] = useState("");
  const [selectedPrioritas, setSelectedPrioritas] = useState("");
  const [peluangInsentifOptions, setPeluangInsentifOptions] = useState([]);

  const { previewLayer, setPreviewLayer } = useMapServiceStore();
  //data select
  const prioritasOptions = [
    { id: 1, nama: "Prioritas" },
    { id: 3, nama: "Prioritas & IKN" },
  ];
  // const tahunOptions = [2020, 2021, 2022, 2023, 2024, 2025];
  const tahunOptions = useMemo(() => {
    const startYear = 2020;
    const currentYear = new Date().getFullYear();
    return Array.from(
      { length: currentYear - startYear + 1 },
      (_, i) => currentYear - i
    );
  }, []);
  const apakahIKN = ["true", "false"];

  const [searchTerm, setSearchTerm] = useState("");
  const [imagePreview, setImagePreview] = useState([]);
  const [infografisPreview, setInfografisPreview] = useState([]);
  const [infografisTrPreview, setInfografisTrPreview] = useState([]);
  const [editorLoaded, setEditorLoaded] = useState(false);
  const editorRef = useRef();
  const { CKEditor, ClassicEditor } = editorRef.current || {};

  useEffect(() => {
    editorRef.current = {
      CKEditor: require("@ckeditor/ckeditor5-react").CKEditor,
      ClassicEditor: require("@ckeditor/ckeditor5-build-classic"),
    };
    setEditorLoaded(true);
  }, []);

  const { selectedKontaks, addKontak, removeKontak, clearKontaks } =
    useKontakStore();

  const handleEmailInputChange = (e) => {
    setEmailData((prev) => ({ ...prev, currentEmail: e.target.value }));
  };

  const addEmailIfValid = () => {
    const email = emailData.currentEmail.trim();
    if (email && isValidEmail(email) && !emailData.email?.includes(email)) {
      setEmailData((prev) => ({
        ...prev,
        email: [...prev.email, email],
        currentEmail: "",
      }));
      return true;
    }
    return false;
  };

  const handleEmailKeyDown = (e) => {
    if (e.key === "Enter" || e.key === ",") {
      e.preventDefault();
      addEmailIfValid();
    }
  };

  const handleEmailBlur = () => {
    addEmailIfValid();
  };

  const handleEmailTab = (e) => {
    if (e.key === "Tab") {
      const added = addEmailIfValid();
      if (added) {
        e.preventDefault(); // Prevent default tab behavior only if email was added
      }
    }
  };

  // const handleEmailKeyDown = (e) => {
  //   if (e.key === "Enter" || e.key === ",") {
  //     e.preventDefault();
  //     const email = emailData.currentEmail.trim();
  //     if (isValidEmail(email) && !emailData.email?.includes(email)) {
  //       setEmailData((prev) => ({
  //         ...prev,
  //         email: [...prev.email, email],
  //         currentEmail: "",
  //       }));
  //     }
  //   }
  // };

  const removeEmail = (emailToRemove) => {
    setEmailData((prev) => ({
      ...prev,
      email: prev.email.filter((email) => email !== emailToRemove),
    }));
  };

  const isValidEmail = (email) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const handleEmailChange = (e) => {
    const { name, value } = e.target;
    setEmailData((prev) => ({ ...prev, [name]: value }));
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setSelectedDaerah("");
    setSelectedSektor("");
    setSelectedStatusPeluang("");
    setSelectedApakahIKN("");
    setSelectedTahun("");
    setSelectedPrioritas("");
    setImagePreview([]);
    setInfografisPreview([]);
    setInfografisTrPreview([]);
    setEmailPopupOpen(false);
  };

  const handleFormSubmit = async (
    values,
    { setSubmitting, resetForm },
    isEmailSubmit = false
  ) => {
    try {
      // Submit the main form data first
      const mainFormResponse = await createPeluangInvestasiService(values);
      console.log("mainFormResponse", mainFormResponse);

      if (isEmailSubmit) {
        // Then submit the email status change
        const emailStatusBody = {
          email: emailData.email,
          subjek: emailData.subjek,
          keterangan: emailData.keterangan,
          project_status_enum: emailData.project_status_enum,
        };
        console.log("email body", emailStatusBody);

        await changeStatusPeluangInvestasiService(
          mainFormResponse.data.id_peluang_kabkot,
          emailStatusBody
        );
        SweetAlert.success(
          "Success",
          "Data berhasil disimpan dan email terkirim"
        );
      } else {
        SweetAlert.success("Success", "Peluang investasi berhasil dibuat");
      }

      onDataChange();
      handleDialogClose();
    } catch (error) {
      SweetAlert.error("Error", error);
      console.error("Error:", error);
    } finally {
      setSubmitting(false);
      resetForm();
    }
  };

  return (
    <Formik
      initialValues={{
        nama: "",
        nama_tr: "",
        keterangan: "-",
        keterangan_tr: "-",
        status: "99",
        nama_singkat: "",
        nama_singkat_tr: "",
        deskripsi_singkat: "",
        deskripsi_singkat_tr: "",
        deskripsi: "",
        deskripsi_tr: "",
        lokasi_kawasan: "",
        lokasi_kawasan_tr: "",
        project_status_enum: null,
        kode_kbli: "",
        kd_bahasa: "en",
        id_adm_kabkot: "",
        id_sektor: "",
        tahun: "",
        id_sumber_data: "",
        id_prioritas: "",
        id_adm_kabkot_kantor: 1,
        id_kontak: 1,
        zoom_peta_default: 1,
        nilai_investasi: "",
        nilai_irr: "",
        nilai_npv: "",
        nilai_pp: "",
        vidio: "",
        lon: 1,
        lat: 1,
        peluang_kontak: [],
        peluang_insentif: [],

        is_ikn: true,
        shape: "",

        map_service: [
          {
            layeruid: "",
            keterangan: "",
            is_active: true,
          },
        ],

        images: [""],
        infografis: [""],
        infografis_tr: [""],
        dokumen: [""],
        is_ipro: false,
      }}
      validationSchema={createInvestmentSchema}
      // onSubmit={async (values, { setSubmitting, resetForm }) => {
      //   console.log("values", values);
      //   try {
      //     const response = await createPeluangInvestasiService(values);
      //     console.log("API Response:", response);
      //     setIsDialogOpen(false);
      //     SweetAlert.success(
      //       "Success",
      //       "Peluang investasi berhasil dibuat",
      //       () => {
      //         // // window.location.reload();
      //       }
      //     );
      //     onDataChange();
      //     handleDialogClose();
      //   } catch (error) {
      //     SweetAlert.error("Error", "Gagal membuat peluang investasi.", () => {
      //       console.error("Error creating peluang investasi:", error);
      //     });
      //   } finally {
      //     setSubmitting(false);
      //     resetForm({
      //       values: {
      //         nama: "",
      //         nama_tr: "",
      //         keterangan: "-",
      //         keterangan_tr: "-",
      //         status: 0,
      //         nama_singkat: "",
      //         nama_singkat_tr: "",
      //         deskripsi_singkat: "",
      //         deskripsi_singkat_tr: "",
      //         deskripsi: "",
      //         deskripsi_tr: "",
      //         lokasi_kawasan: "",
      //         lokasi_kawasan_tr: "",
      //         project_status_enum: 1,
      //         kode_kbli: "",
      //         kd_bahasa: "en",
      //         id_adm_kabkot: "",
      //         id_sektor: "",
      //         tahun: "",
      //         id_sumber_data: "",
      //         id_prioritas: "",
      //         id_adm_kabkot_kantor: 1,
      //         id_kontak: 1,
      //         zoom_peta_default: 1,
      //         nilai_investasi: null,
      //         nilai_irr: null,
      //         nilai_npv: null,
      //         nilai_pp: null,
      //         vidio: "",
      //         lon: 1,
      //         lat: 1,
      //         peluang_kontak: [],
      //         peluang_insentif: [],

      //         is_ikn: true,
      //         shape: null,

      //         map_service: [
      //           {
      //             layeruid: "",
      //             keterangan: "",
      //             is_active: true,
      //           },
      //         ],

      //         images: [""],
      //         infografis: [""],
      //         infografis_tr: [""],
      //         dokumen: [""],
      //         is_ipro: false,
      //       },
      //     });

      //   }
      // }}
      // onSubmit={async (values, { setSubmitting, resetForm }) => {
      //   await handleFormSubmit(values, { setSubmitting, resetForm }, false);
      // }}
      onSubmit={async (values, { setSubmitting, resetForm }) => {
        const nilaiInvestasi = values.nilai_investasi.replace(/\./g, ""); // hilangkan titik
        const nilaiNpv = values.nilai_npv.replace(/\./g, ""); // hilangkan titik untuk NPV

        const payload = {
          ...values,
          nilai_investasi: Number(nilaiInvestasi),
          nilai_npv: Number(nilaiNpv),
        };

        // Check if map_service has any meaningful data
        const hasValidMapService = values.map_service.some(
          (service) =>
            service.layeruid.trim() !== "" || service.keterangan.trim() !== ""
        );

        // Only include map_service if it has valid data
        if (hasValidMapService) {
          payload.map_service = values.map_service;
        } else {
          // Remove map_service from payload if no valid data
          delete payload.map_service;
        }

        try {
          let response;
          // Check if the email popup is triggered
          if (isEmailPopupOpen) {
            // First, submit the main form data
            // const mainFormResponse = await createPeluangInvestasiService(values);
            response = await createPeluangInvestasiService(payload);
            // Then, submit the email status change
            const emailStatusBody = {
              email: emailData.email,
              subjek: emailData.subjek,
              keterangan: emailData.keterangan,
              // project_status_enum: emailData.project_status_enum,
              project_status_enum:
                emailData.project_status_enum || values.project_status_enum,
            };

            await changeStatusPeluangInvestasiService(
              response.data.id_peluang_kabkot,
              emailStatusBody
            );

            await editStatusPeluangInvestasiService(
              response.data.id_peluang_kabkot,
              {
                id_peluang_kabkot: response.data.id_peluang_kabkot,
                status: 0, // Sesuaikan dengan status yang diinginkan
                keterangan: "Dokumen Baru",
              }
            );

            // Close dialogs and show success message
            SweetAlert.success(
              "Success",
              "Data berhasil disimpan dan email terkirim"
            );
            onDataChange();
          } else {
            // Just submit the form data without email change
            response = await createPeluangInvestasiService(payload);

            await editStatusPeluangInvestasiService(
              response.data.id_peluang_kabkot,
              {
                id_peluang_kabkot: response.data.id_peluang_kabkot,
                status: 0,
                keterangan: "Dokumen Baru",
              }
            );

            SweetAlert.success("Success", "Peluang investasi berhasil dibuat");
            onDataChange();
          }

          // Reset the form and close dialogs
          setSubmitting(false);
          resetForm({
            values: {
              nama: "",
              nama_tr: "",
              keterangan: "-",
              keterangan_tr: "-",
              status: 0,
              nama_singkat: "",
              nama_singkat_tr: "",
              deskripsi_singkat: "",
              deskripsi_singkat_tr: "",
              deskripsi: "",
              deskripsi_tr: "",
              lokasi_kawasan: "",
              lokasi_kawasan_tr: "",
              project_status_enum: null,
              kode_kbli: "",
              kd_bahasa: "en",
              id_adm_kabkot: "",
              id_sektor: "",
              tahun: "",
              id_sumber_data: "",
              id_prioritas: "",
              id_adm_kabkot_kantor: 1,
              id_kontak: 1,
              zoom_peta_default: 1,
              nilai_investasi: null,
              nilai_irr: null,
              nilai_npv: null,
              nilai_pp: null,
              vidio: "",
              lon: 1,
              lat: 1,
              peluang_kontak: [],
              peluang_insentif: [],

              is_ikn: true,
              shape: null,

              map_service: [
                {
                  layeruid: "",
                  keterangan: "",
                  is_active: true,
                },
              ],

              images: [""],
              infografis: [""],
              infografis_tr: [""],
              dokumen: [""],
              is_ipro: false,
            },
          });
          setIsDialogOpen(false);
          setEmailPopupOpen(false);
        } catch (error) {
          console.error("Error:", error);

          // Cek apakah error memiliki `errors` dan apakah itu array
          let errorMessage = error.errors
            ? error.errors.map((err) => `${err.message},`)
            : "Gagal Menyimpan.";
            if(Array.isArray(error.message)){
              errorMessage = `File ${error.message[0].clientName} ${error.message[0].message}`;
            }
          SweetAlert.error("Error", errorMessage);
          setSubmitting(false); // Reset submission state even on error
        }
      }}
    >
      {({ setFieldValue, values, isSubmitting, handleChange }) => (
        <Form>
          <Dialog
            open={isDialogOpen}
            onOpenChange={(open) => {
              if (!open) {
                handleDialogClose();
              }
              setIsDialogOpen(open);
            }}
          >
            <DialogTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                className="text-xs text-white"
              >
                Tambah Peluang
              </Button>
            </DialogTrigger>

            <DialogContent className="max-w-6xl w-full max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Tambah Peluang Investasi</DialogTitle>
              </DialogHeader>
              <form>
                {/* Daerah Field */}
                <div
                  ref={containerRef}
                  className="grid grid-cols-4 items-center gap-4"
                >
                  <Label className="block text-xs font-medium ">Daerah
                  <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                  </Label>
                  <Popover
                    modal={true}
                    open={openDaerah}
                    onOpenChange={setOpenDaerah}
                    className="col-span-3 w-full"
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={openDaerah}
                        className="justify-between col-span-3 w-full text-xs"
                      >
                        {selectedDaerah?.nama || "Pilih Daerah"}{" "}
                        {/* Hanya menampilkan nama daerah, bukan seluruh objek */}
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent
                      className="w-full p-0 overflow-visible"
                      container={containerRef.current}
                    >
                      <Command>
                        <CommandInput placeholder="Cari daerah..." />
                        <CommandList>
                          <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                          <CommandGroup>
                            {kabkotOptions?.map((daerah) => (
                              <CommandItem
                                key={daerah.id_adm_kabkot} // Menggunakan id unik sebagai key
                                value={daerah.nama} // Nama daerah yang digunakan sebagai value
                                onSelect={(currentValue) => {
                                  setFieldValue(
                                    "id_adm_kabkot",
                                    daerah.id_adm_kabkot
                                  ); // Menyimpan nama daerah ke form
                                  setSelectedDaerah(daerah); // Menyimpan seluruh objek daerah ke state
                                  setOpenDaerah(false); // Menutup popover setelah pemilihan
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    selectedDaerah?.nama === daerah.nama
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {daerah.nama}{" "}
                                {/* Menampilkan hanya nama daerah */}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>

                  <ErrorMessage
                    name="daerah"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Sektor Peluang
                    <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                  </Label>
                  <Popover
                    modal={true}
                    open={openSektor}
                    onOpenChange={setOpenSektor}
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        className="text-xs justify-between col-span-3 w-full"
                      >
                        {selectedSektor?.nama || "Pilih Sektor Peluang"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent
                      className="w-full p-0"
                      container={containerRef.current}
                    >
                      <Command>
                        <CommandInput placeholder="Cari sektor..." />
                        <CommandList>
                          <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                          <CommandGroup>
                            {peluangSektorOptions?.map((sektor) => (
                              <CommandItem
                                key={sektor.id_peluang_sektor}
                                value={sektor.nama}
                                onSelect={() => {
                                  setFieldValue(
                                    "id_sektor",
                                    sektor.id_peluang_sektor
                                  );
                                  setSelectedSektor(sektor);
                                  setOpenSektor(false);
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    selectedSektor?.nama === sektor.nama
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {sektor.nama}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <ErrorMessage
                    name="sektor_peluang"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Sumber Data
                    <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                  </Label>
                  {/* <SumberDataDropdown
                    onSelect={(selectedData) => {
                      setFieldValue(
                        "id_sumber_data",
                        selectedData.id_sumber_data
                      );
                    }}
                    className="col-span-3 w-full"
                    defaultValue={values.id_sumber_data}
                  />
                 
                  <ErrorMessage
                    name="sumber_data"
                    component="div"
                    className="text-red-500"
                  /> */}
                  <div className="col-span-3 w-full flex flex-col">
                    <SumberDataDropdown
                      onSelect={(selectedData) => {
                        setFieldValue(
                          "id_sumber_data",
                          selectedData.id_sumber_data
                        );
                      }}
                      className="w-full"
                      defaultValue={values.id_sumber_data}
                    />
                    <ErrorMessage
                      name="id_sumber_data"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    />
                  </div>

                  <Label className="block text-xs font-medium">
                    Apakah IKN
                  </Label>
                  <div className="col-span-3 w-full flex flex-col">
                    <Popover
                      modal={true}
                      open={openApakahIKN}
                      onOpenChange={setOpenApakahIKN}
                    >
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="text-xs justify-between col-span-3 w-full"
                        >
                          {selectedApakahIKN || "Apakah IKN?"}
                          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-full p-0"
                        container={containerRef.current}
                      >
                        <Command>
                          <CommandInput placeholder="Cari apakah IKN..." />
                          <CommandList>
                            <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                            <CommandGroup>
                              {apakahIKN.map((ikn) => (
                                <CommandItem
                                  key={ikn}
                                  value={ikn}
                                  onSelect={(currentValue) => {
                                    setFieldValue("is_ikn", true);
                                    setSelectedApakahIKN(currentValue);
                                    setOpenApakahIKN(false);
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      values.is_ikn === ikn
                                        ? "opacity-100"
                                        : "opacity-0"
                                    )}
                                  />
                                  {ikn}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <ErrorMessage
                      name="is_ikn"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    />
                  </div>

                  <Label className="block text-xs font-medium">
                    Apakah IPRO?
                  </Label>
                  <div className="col-span-3 w-full flex flex-col">
                    <Input
                      type="checkbox"
                      name="is_ipro"
                      checked={values.is_ipro === 1 || values.is_ipro === true} // Checkbox is checked if value is 1
                      onChange={(e) =>
                        setFieldValue("is_ipro", e.target.checked ? 1 : 0)
                      } // Set 1 for true, 0 for false
                      className="h-4 col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="is_ipro"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    />
                  </div>

                  <Label className="block text-xs font-medium">
                    <span>Kode KBLI </span>
                    <span className="text-[10px] text-red-600">
                      Jika lebih dari satu dipisahkan dengan tanda koma (,) atau
                      Spasi _
                    </span> 
                    <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                  </Label>
                  {/* <Input
                    type="text"
                    name="kode_kbli"
                    placeholder="Kode KBLI..."
                    onChange={(e) => setFieldValue("kode_kbli", e.target.value)}
                    value={values.kode_kbli}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage
                    name="kode_kbli"
                    component="div"
                    className="text-red-500"
                  /> */}
                  <div className="col-span-3 w-full flex flex-col">
                    <Input
                      type="text"
                      name="kode_kbli"
                      placeholder="Kode KBLI..."
                      onChange={(e) =>
                        setFieldValue("kode_kbli", e.target.value)
                      }
                      value={values.kode_kbli}
                      className="text-xs w-full"
                    />
                    <ErrorMessage
                      name="kode_kbli"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    />
                  </div>

                  <Label className="block text-xs font-medium">
                    Judul Peluang
                    <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                  </Label>
                  <Input
                    type="text"
                    name="nama"
                    placeholder="Judul Peluang..."
                    onChange={(e) => {
                      // Formik akan mengatur nilai dengan `setFieldValue`
                      handleChange(e);

                      // Sinkronisasi dengan emailData
                      const { value } = e.target;
                      setEmailData((prev) => ({ ...prev, nama: value }));
                    }}
                    value={values.nama}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage
                    name="judul_peluang"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Status Peluang
                    {/* <span className="text-[12px] text-red-600">&nbsp;(*)</span> */}
                  </Label>
                  <div className="col-span-3 w-full flex flex-col">
                    <Popover
                      open={openStatusPeluang}
                      onOpenChange={setOpenStatusPeluang}
                    >
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="text-xs justify-between col-span-3 w-full"
                        >
                          {selectedStatusPeluang?.nama ||
                            "Pilih Status Peluang"}
                          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-full p-0"
                        container={containerRef.current}
                      >
                        <Command>
                          <CommandInput placeholder="Cari status peluang..." />
                          <CommandList>
                            <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                            <CommandGroup>
                              {peluangStatusOptions?.map((status) => (
                                <CommandItem
                                  key={status.id_peluang_status}
                                  value={status.nama}
                                  onSelect={(currentValue) => {
                                    setFieldValue(
                                      "project_status_enum",
                                      status.id_peluang_status.toString()
                                    );
                                    setEmailData((prev) => ({
                                      ...prev,
                                      project_status_enum:
                                        status.id_peluang_status,
                                    }));
                                    setSelectedStatusPeluang(status);
                                    setOpenStatusPeluang(false);
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      selectedStatusPeluang?.nama ===
                                        status.nama
                                        ? // values?.status === status.nama
                                          "opacity-100"
                                        : "opacity-0"
                                    )}
                                  />
                                  {status.nama}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    {/* <ErrorMessage
                      name="project_status_enum"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    /> */}
                  </div>

                  <Label className="block text-xs font-medium">
                    Lokasi Kawasan
                    <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                  </Label>
                  <div className="col-span-3 w-full flex flex-col">
                    <Input
                      type="text"
                      name="lokasi_kawasan"
                      placeholder="Lokasi Kawasan..."
                      onChange={(e) =>
                        setFieldValue("lokasi_kawasan", e.target.value)
                      }
                      value={values.lokasi_kawasan}
                      className="justify-between col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="lokasi_kawasan"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    />
                  </div>

                  <Label className="block text-xs font-medium">Tahun
                  <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                  </Label>
                  <Popover
                    modal={true}
                    open={openTahun}
                    onOpenChange={setOpenTahun}
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="text-xs justify-between col-span-3 w-full"
                      >
                        {selectedTahun || "Pilih Tahun"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Command>
                        <CommandInput placeholder="Cari Tahun..." />
                        <CommandList>
                          <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                          <CommandGroup>
                            {tahunOptions?.map((tahun) => (
                              <CommandItem
                                key={tahun}
                                value={tahun}
                                onSelect={(currentValue) => {
                                  const tahunInteger = parseInt(currentValue);
                                  setFieldValue("tahun", tahunInteger);
                                  setSelectedTahun(tahun);
                                  setOpenTahun(false);
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    values.tahun === tahun
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {tahun}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <ErrorMessage
                    name="status_peluang"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">Prioritas
                  <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                  </Label>
                  <Popover
                    modal={true}
                    open={openPrioritas}
                    onOpenChange={setOpenPrioritas}
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="text-xs justify-between col-span-3 w-full"
                      >
                        {selectedPrioritas?.nama || "Pilih Prioritas"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Command>
                        <CommandInput placeholder="Cari Prioritas..." />
                        <CommandList>
                          <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                          <CommandGroup>
                            {prioritasOptions?.map((prioritas) => (
                              <CommandItem
                                key={prioritas.id}
                                value={prioritas.nama}
                                onSelect={(currentValue) => {
                                  setFieldValue("id_prioritas", prioritas.id);
                                  setSelectedPrioritas(prioritas);
                                  setOpenPrioritas(false);
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    selectedPrioritas?.nama === prioritas.nama
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {prioritas.nama}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <ErrorMessage
                    name="prioritas"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    <div className="flex items-center">
                      <span>Nilai Investasi</span>
                      <p className="text-[10px] text-red-600 ml-1">Rp</p>
                      <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                    </div>
                  </Label>
                  {/* <div className="col-span-3 w-full flex flex-col">
                    <Input
                      type="number"
                      name="nilai_investasi"
                      placeholder="Nilai Investasi..."
                      onChange={(e) =>
                        setFieldValue("nilai_investasi", e.target.value)
                      }
                      value={values.nilai_investasi}
                      className="justify-between col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="nilai_investasi"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    />
                  </div> */}
                  <div className="col-span-3 w-full flex flex-col">
                    <Input
                      type="text"
                      name="nilai_investasi"
                      placeholder="Nilai Investasi..."
                      onChange={(e) => {
                        const rawValue = e.target.value.replace(/\D/g, ""); // ambil angka saja
                        const formatted =
                          Number(rawValue).toLocaleString("id-ID");
                        setFieldValue("nilai_investasi", formatted); // tampilkan terformat
                      }}
                      value={values.nilai_investasi}
                      className="justify-between col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="nilai_investasi"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    />
                  </div>

                  <Label className="block text-xs font-medium">
                    <div className="flex items-center">
                      <span>Nilai IRR</span>
                      <p className="text-[10px] text-red-600 ml-1">(%)</p>
                      <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                    </div>
                  </Label>
                  <div className="col-span-3 w-full flex flex-col">
                    <Input
                      name="nilai_irr"
                      type="number"
                      placeholder="Nilai IRR..."
                      onChange={(e) =>
                        setFieldValue("nilai_irr", e.target.value)
                      }
                      value={values.nilai_irr}
                      className="justify-between col-span-3 w-full text-xs"
                      step="any"
                    />
                    <ErrorMessage
                      name="nilai_irr"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    />
                  </div>

                  {/* <Label className="block text-xs font-medium">
                    <div className="flex items-center">
                      <span>Nilai NPV</span>
                      <p className="text-[10px] text-red-600 ml-1">Rp</p>
                    </div>
                  </Label>
                  <div className="col-span-3 w-full flex flex-col">
                    <Input
                      name="nilai_npv"
                      type="number"
                      placeholder="Nilai NPV..."
                      onChange={(e) =>
                        setFieldValue("nilai_npv", e.target.value)
                      }
                      value={values.nilai_npv}
                      className="justify-between col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="nilai_npv"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    />
                  </div> */}
                  <Label className="block text-xs font-medium">
                    <div className="flex items-center">
                      <span>Nilai NPV</span>
                      <p className="text-[10px] text-red-600 ml-1">Rp</p>
                      <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                    </div>
                  </Label>
                  <div className="col-span-3 w-full flex flex-col">
                    <Input
                      type="text"
                      name="nilai_npv"
                      placeholder="Nilai NPV..."
                      onChange={(e) => {
                        const rawValue = e.target.value.replace(/\D/g, ""); // hanya angka
                        const formatted =
                          Number(rawValue).toLocaleString("id-ID"); // format IDR
                        setFieldValue("nilai_npv", formatted);
                      }}
                      value={values.nilai_npv}
                      className="justify-between col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="nilai_npv"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    />
                  </div>

                  <Label className="block text-xs font-medium">
                    <div className="flex items-center">
                      <span>Nilai PP</span>
                      <p className="text-[10px] text-red-600 ml-1">Tahun</p>
                      <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                    </div>
                  </Label>
                  <div className="col-span-3 w-full flex flex-col">
                    <Input
                      name="nilai_pp"
                      type="number"
                      placeholder="Nilai PP..."
                      onChange={(e) =>
                        setFieldValue("nilai_pp", e.target.value)
                      }
                      value={values.nilai_pp}
                      className="justify-between col-span-3 w-full text-xs"
                    />
                    <ErrorMessage
                      name="nilai_pp"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    />
                  </div>

                  <Label className="block text-xs font-medium">
                    Keterangan
                  </Label>
                  <div className="justify-between col-span-3 w-full text-xs">
                    <CKEditor
                      editor={ClassicEditor}
                      data={values.keterangan} // Menetapkan nilai saat ini dari form
                      onChange={(event, editor) => {
                        const data = editor.getData();
                        setFieldValue("keterangan", data); // Mengupdate nilai form
                      }}
                      config={{
                        placeholder: "Keterangan...",
                      }}
                      className="w-full" // Mengatur lebar CKEditor
                    />
                    <ErrorMessage
                      name="keterangan"
                      component="div"
                      className="text-xs text-red-500 mt-1"
                    />
                  </div>
                  {/* <div className="justify-between col-span-3 w-full text-xs">
                    <ReactQuill
                      value={values.deskripsi} // Menetapkan nilai saat ini dari form
                      onChange={(content) => setFieldValue('deskripsi', content)} // Mengupdate nilai form
                      placeholder="Keterangan..."
                      className="w-full" // Mengatur lebar Quill editor
                    />
                  </div> */}

                  <Label className="block text-xs font-medium">
                    Upload Foto{" "}
                    <span className="text-[10px] text-red-600">
                      Max 2Mb per file
                    </span>
                  </Label>
                  <FieldArray name="images">
                    {({ remove, push }) => (
                      <div className="justify-between col-span-3 w-full">
                        {values.images?.map((_, index) => (
                          <div
                            key={index}
                            className="mb-4 flex items-center space-x-2"
                          >
                            <div className="w-full flex-row flex items-center">
                              <Input
                                name={`images[${index}]`}
                                type="file"
                                accept="image/jpeg, image/jpg, image/png, image/webp, image/svg+xml"
                                onChange={(event) => {
                                  const file = event.currentTarget.files[0];
                                  setFieldValue(`images[${index}]`, file); // Set file to potensi
                                  const reader = new FileReader();
                                  reader.onloadend = () => {
                                    // Create a copy of the current previews
                                    const updatedPreviews = [...imagePreview];
                                    updatedPreviews[index] = reader.result; // Assign the image preview to the corresponding index
                                    setImagePreview(updatedPreviews); // Update the state
                                    console.log(
                                      "Updated previews:",
                                      updatedPreviews
                                    ); // Debugging line
                                  };
                                  if (file) {
                                    reader.readAsDataURL(file);
                                    // Set fileName in potensi_detail
                                  }
                                }}
                                className="justify-between col-span-1 w-full text-xs"
                              />
                              <div className="relative col-span-2 w-80 mr-auto">
                                {imagePreview[index] ? (
                                  <Image
                                    src={imagePreview[index]}
                                    alt="Image Preview"
                                    width={320}
                                    height={0}
                                    layout="responsive"
                                    objectFit="cover"
                                    className="rounded"
                                  />
                                ) : (
                                  <div className="w-full h-full bg-white rounded flex items-center justify-center">
                                    {/* Placeholder text */}
                                  </div>
                                )}
                              </div>
                              {index === values.images?.length - 1 && (
                                <Button
                                  type="button"
                                  onClick={() => push("")} // Menambah input file baru
                                  className="text-xs hover:bg-blue-800"
                                  variant="ghost"
                                  size="icon"
                                >
                                  <PlusCircle className="h-6 w-6" />
                                </Button>
                              )}

                              {index !== 0 && (
                                <Button
                                  type="button"
                                  onClick={() => remove(index)} // Menghapus input file
                                  className="text-red-500"
                                  variant="ghost"
                                  size="icon"
                                >
                                  <XCircle className="h-6 w-6" />
                                </Button>
                              )}
                            </div>

                            <ErrorMessage
                              name={`images[${index}]`}
                              component="div"
                              className="text-red-500"
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </FieldArray>

                  <ErrorMessage
                    name="images"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Upload Infografis{" "}
                    <span className="text-[10px] text-red-600">
                      Max 2MB per file, ukuran POTRAIT(1690x2590)
                    </span>
                  </Label>
                  <FieldArray name="infografis">
                    {({ remove, push }) => (
                      <div className="justify-between col-span-3 w-full">
                        {values.infografis?.map((_, index) => (
                          <div
                            key={index}
                            className="mb-4 flex items-center space-x-2"
                          >
                            <div className="w-full flex-row flex items-center">
                              <Input
                                name={`infografis[${index}]`}
                                type="file"
                                accept="image/jpeg, image/jpg, image/png, image/webp, image/svg+xml"
                                onChange={(event) => {
                                  const file = event.currentTarget.files[0];
                                  setFieldValue(`infografis[${index}]`, file); // Set file to potensi
                                  const reader = new FileReader();
                                  reader.onloadend = () => {
                                    // Create a copy of the current previews
                                    const updatedPreviews = [
                                      ...infografisPreview,
                                    ];
                                    updatedPreviews[index] = reader.result; // Assign the image preview to the corresponding index
                                    setInfografisPreview(updatedPreviews); // Update the state
                                  };
                                  if (file) {
                                    reader.readAsDataURL(file);
                                    // Set fileName in potensi_detail
                                  }
                                }}
                                className="justify-between col-span-1 w-full text-xs"
                              />
                              <div className="relative col-span-2 w-80 mr-auto">
                                {infografisPreview[index] ? (
                                  <Image
                                    src={infografisPreview[index]}
                                    alt="Image Preview"
                                    width={320}
                                    height={0}
                                    layout="responsive"
                                    objectFit="cover"
                                    className="rounded"
                                  />
                                ) : (
                                  <div className="w-full h-full bg-white rounded flex items-center justify-center">
                                    {/* Placeholder text */}
                                  </div>
                                )}
                              </div>
                              {index === values.infografis?.length - 1 && (
                                <Button
                                  type="button"
                                  // onClick={() => push("")} // Menambah input file baru
                                  onClick={() => {
                                    push(""); // Tambah field baru di Indonesia
                                    setFieldValue("infografis_tr", [
                                      ...values.infografis_tr,
                                      "", // Tambahkan juga field kosong di translate
                                    ]);
                                  }}
                                  className="text-xs hover:bg-blue-800"
                                  variant="ghost"
                                  size="icon"
                                >
                                  <PlusCircle className="h-6 w-6" />
                                </Button>
                              )}

                              {index !== 0 && (
                                <Button
                                  type="button"
                                  // onClick={() => remove(index)} // Menghapus input file
                                  onClick={() => {
                                    remove(index); // Hapus field di Indonesia
                                    const updatedTranslate = [
                                      ...values.infografis_tr,
                                    ];
                                    updatedTranslate.splice(index, 1); // Hapus juga field di translate
                                    setFieldValue(
                                      "infografis_tr",
                                      updatedTranslate
                                    );
                                  }}
                                  className="text-red-500"
                                  variant="ghost"
                                  size="icon"
                                >
                                  <XCircle className="h-6 w-6" />
                                </Button>
                              )}
                            </div>

                            <ErrorMessage
                              name={`infografis[${index}]`}
                              component="div"
                              className="text-red-500"
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </FieldArray>

                  <ErrorMessage
                    name="infografis"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Upload Dokumen{" "}
                    <span className="text-[10px] text-red-600">
                      Max 20Mb per file
                    </span>
                  </Label>
                  <FieldArray name="dokumen">
                    {({ remove, push }) => (
                      <div className="justify-between col-span-3 w-full">
                        {values.dokumen?.map((_, index) => (
                          <div
                            key={index}
                            className="mb-4 flex items-center space-x-2"
                          >
                            <div className="w-full flex-row flex items-center">
                              <Input
                                name={`dokumen[${index}]`}
                                type="file"
                                accept="application/pdf"
                                onChange={(event) =>
                                  setFieldValue(
                                    `dokumen[${index}]`,
                                    event.currentTarget.files[0]
                                  )
                                }
                                className="justify-between col-span-3 w-full text-xs"
                              />
                              {index === values.dokumen?.length - 1 && (
                                <Button
                                  type="button"
                                  onClick={() => push("")} // Menambahkan input file baru
                                  className="text-xs hover:bg-blue-800"
                                  variant="ghost"
                                  size="icon"
                                >
                                  <PlusCircle className="h-6 w-6" />
                                </Button>
                              )}
                              {index !== 0 && (
                                <Button
                                  type="button"
                                  onClick={() => remove(index)} // Menghapus input file
                                  className="text-red-500"
                                  variant="ghost"
                                  size="icon"
                                >
                                  <XCircle className="h-6 w-6" />
                                </Button>
                              )}
                            </div>
                            <ErrorMessage
                              name={`dokumen[${index}]`}
                              component="div"
                              className="text-red-500"
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </FieldArray>
                  <ErrorMessage
                    name="dokumen"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">Video Url</Label>
                  <Input
                    name="vidio"
                    type="text"
                    placeholder="Video url..."
                    onChange={(e) => setFieldValue("vidio", e.target.value)}
                    value={values.vidio}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage
                    name="vidio"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Map Service
                  </Label>
                  <FieldArray name="map_service">
                    {({ push, remove }) => (
                      <div className="justify-between col-span-3 w-full">
                        {values.map_service?.map((input, index) => (
                          <div
                            key={index}
                            className="mb-4 grid grid-cols-12 items-center gap-4"
                          >
                            {/* Tombol untuk Set Active */}
                            <div className="col-span-2">
                              <Button
                                type="button"
                                onClick={() => {
                                  const newStatus =
                                    !!values.map_service[index].is_active;
                                  setFieldValue(
                                    `map_service[${index}].is_active`,
                                    !newStatus
                                  );
                                }}
                                className={`text-xs ${
                                  !!values.map_service[index].is_active
                                    ? "text-green-500"
                                    : "text-gray-500"
                                }`}
                                variant="ghost"
                              >
                                {!!values.map_service[index].is_active
                                  ? "Active"
                                  : "Inactive"}
                              </Button>
                            </div>

                            {/* Tombol Preview */}
                            <div className="col-span-2">
                              <Button
                                type="button"
                                variant="secondary"
                                onClick={() => {
                                  if (previewLayer === input.layeruid) {
                                    // Jika sudah di-preview, reset previewLayer
                                    setPreviewLayer(null);
                                  } else {
                                    // Jika belum di-preview, set layeruid sebagai previewLayer
                                    setPreviewLayer(input.layeruid);
                                  }
                                }}
                                className={`text-xs py-2 px-4 ${
                                  previewLayer === input.layeruid
                                    ? "bg-blue-500"
                                    : "bg-gray-500"
                                } hover:bg-gray-700 text-white`}
                              >
                                {previewLayer === input.layeruid
                                  ? "Unpreview"
                                  : "Preview"}
                              </Button>
                            </div>

                            {/* Field untuk Layer UID */}
                            <div className="col-span-3">
                              <Input
                                name={`map_service[${index}].layeruid`}
                                type="text"
                                placeholder="Layer UID"
                                value={input.layeruid || ""}
                                onChange={(event) =>
                                  setFieldValue(
                                    `map_service[${index}].layeruid`,
                                    event.currentTarget.value
                                  )
                                }
                                className="w-full text-xs"
                              />
                              <ErrorMessage
                                name={`map_service[${index}].layeruid`}
                                component="div"
                                className="text-red-500 text-xs"
                              />
                            </div>

                            {/* Field untuk Keterangan */}
                            <div className="col-span-4">
                              <Input
                                name={`map_service[${index}].keterangan`}
                                type="text"
                                placeholder="Keterangan"
                                value={input.keterangan || ""}
                                onChange={(event) =>
                                  setFieldValue(
                                    `map_service[${index}].keterangan`,
                                    event.currentTarget.value
                                  )
                                }
                                className="w-full text-xs"
                              />
                              <ErrorMessage
                                name={`map_service[${index}].keterangan`}
                                component="div"
                                className="text-red-500 text-xs"
                              />
                            </div>

                            {/* Tombol Tambah/Hapus */}
                            <div className="col-span-1 flex justify-end">
                              {index === 0 && (
                                <Button
                                  type="button"
                                  onClick={() =>
                                    push({ layeruid: "", keterangan: "" })
                                  }
                                  className="text-xs hover:bg-blue-800"
                                  variant="ghost"
                                  size="icon"
                                >
                                  <PlusCircle className="h-6 w-6" />
                                </Button>
                              )}
                              {index !== 0 && (
                                <Button
                                  type="button"
                                  onClick={() => remove(index)}
                                  className="text-red-500"
                                  variant="ghost"
                                  size="icon"
                                >
                                  <XCircle className="h-6 w-6" />
                                </Button>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </FieldArray>
                  <ErrorMessage
                    name="map_service"
                    component="div"
                    className="text-red-500"
                  />
                  {previewLayer && (
                    <div className="col-span-3 col-start-2">
                      <h3 className="text-sm font-bold mb-4">Preview Peta</h3>
                      <div className="w-full h-80 border border-gray-300 rounded-md">
                        <MapService />
                      </div>
                    </div>
                  )}
                </div>

                <DialogHeader className="inline-block bg-orange-500 text-white py-2 px-4 rounded font-semibold">
                  <DialogTitle className="inline">Peluang Kontak</DialogTitle>
                </DialogHeader>
                <KontakTable
                  setFieldValue={setFieldValue}
                  // onDataChange={onDataChange}
                />

                <DialogHeader className="inline-block bg-orange-500 text-white py-2 px-4 rounded font-semibold">
                  <DialogTitle className="inline">Peluang Insentif</DialogTitle>
                </DialogHeader>

                <div
                  ref={containerRef}
                  className="grid grid-cols-4 items-center gap-4"
                >
                  <Label className="block text-xs font-medium">
                    Jenis Insentif
                  </Label>

                  <FieldArray name="peluang_insentif">
                    {({ push, remove }) => (
                      <Popover
                        modal={true}
                        open={openPeluangInsentif}
                        onOpenChange={setOpenPeluangInsentif}
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="justify-between col-span-2 w-full text-xs"
                          >
                            {/* {values.peluang_insentif.length > 0
                                                        ? values.peluang_insentif.map(id => {
                                                            const insentif = peluangInsentifOptions.find(option => option.id_sektor_nasional_insentif === id);
                                                            return insentif ? insentif.nama : '';
                                                        }).join(', ')
                                                        : "Pilih Peluang Insentif"} */}
                            Pilih Peluang Insentif
                            <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full max-w-[400px] p-0">
                          <Command>
                            <CommandInput placeholder="Cari peluang insentif..." />
                            <CommandList>
                              <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                              {jenisInsentifOptions
                                ?.filter(insentif => insentif.status == 99)
                                .map((insentif) => (
                                  <CommandItem
                                    key={insentif.id_jenis_insentif}
                                    onSelect={() => {
                                      const selectedIndex = values.peluang_insentif.indexOf(
                                        insentif.id_jenis_insentif
                                      );
                                      if (selectedIndex === -1) {
                                        // Menambahkan insentif yang dipilih
                                        push(insentif.id_jenis_insentif);
                                      } else {
                                        // Menghapus insentif yang sudah dipilih
                                        remove(selectedIndex);
                                      }
                                    }}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        values.peluang_insentif?.includes(
                                          insentif.id_jenis_insentif
                                        )
                                          ? "opacity-100"
                                          : "opacity-0"
                                      )}
                                    />
                                    {insentif.nama}
                                  </CommandItem>
                                ))}
                            </CommandList>
                          </Command>
                        </PopoverContent>

                        {/* Display selected incentives below the dropdown */}
                        {/* {values.peluang_insentif.length > 0 && (
                                                <div className="justify-between col-span-3 w-full text-xs">
                                                    <h4 className="font-semibold">Peluang Insentif Terpilih:</h4>
                                                    <ul>
                                                        {values.peluang_insentif.map(id => {
                                                            const insentif = peluangInsentifOptions.find(option => option.id_jenis_insentif === id);
                                                            return insentif ? (
                                                                <li key={insentif.id_jenis_insentif} className="flex justify-between items-center py-1">
                                                                    <span>{insentif.nama}</span>
                                                                    <button onClick={() => remove(values.peluang_insentif.indexOf(id))} className="text-red-500">Hapus</button>
                                                                </li>
                                                            ) : null;
                                                        })}
                                                    </ul>
                                                </div>
                                            )} */}
                        {values.peluang_insentif?.length > 0 && (
                          <div className="justify-between col-span-3 w-full text-xs">
                            <h4 className="font-semibold">
                              Peluang Insentif Terpilih:
                            </h4>
                            <ul>
                              {values.peluang_insentif?.map((id) => {
                                const insentif = jenisInsentifOptions?.find(
                                  (option) => option.id_jenis_insentif === id
                                );
                                return insentif ? (
                                  <li
                                    key={insentif.id_jenis_insentif}
                                    className="flex justify-between items-center py-1"
                                  >
                                    <span>{insentif.nama}</span>
                                    <Button
                                      variant="ghost"
                                      onClick={() =>
                                        remove(
                                          values.peluang_insentif.indexOf(id)
                                        )
                                      }
                                      className="text-red-500"
                                    >
                                      <Trash className="h-4 w-4" />
                                    </Button>
                                  </li>
                                ) : null;
                              })}
                            </ul>
                          </div>
                        )}
                      </Popover>
                    )}
                  </FieldArray>

                  <ErrorMessage
                    name="sektor_peluang"
                    component="div"
                    className="text-red-500"
                  />

                  <div className="col-span-4">
                    <Button
                      type="button"
                      onClick={() => setShowTranslateForm((prev) => !prev)}
                      className="text-xs text-white hover:bg-blue-800 mb-4 mt-2"
                    >
                      {showTranslateForm ? "Hide Translate" : "Translate EN"}
                    </Button>

                    {showTranslateForm && (
                      <div
                        ref={containerRef}
                        className="grid grid-cols-4 items-center gap-4"
                      >
                        <Label className="block text-xs font-medium">
                          Judul Peluang
                          <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                        </Label>
                        <div className="col-span-3 w-full flex flex-col">
                          <Input
                            name="nama_tr"
                            type="text"
                            placeholder="Ketikkan judul peluang EN..."
                            onChange={(e) =>
                              setFieldValue("nama_tr", e.target.value)
                            }
                            value={values.nama_tr}
                            className="justify-between col-span-3 w-full text-xs"
                          />
                          <ErrorMessage
                            name="nama_tr"
                            component="div"
                            className="text-xs text-red-500 mt-1"
                          />
                        </div>
                        <Label className="block text-xs font-medium">
                          Keterangan Lokasi Kawasan
                        </Label>
                        <Input
                          name="lokasi_kawasan_tr"
                          type="text"
                          placeholder="Ketikkan keterangan EN..."
                          onChange={(e) =>
                            setFieldValue("lokasi_kawasan_tr", e.target.value)
                          }
                          value={values.lokasi_kawasan_tr}
                          className="justify-between col-span-3 w-full text-xs"
                        />
                        <ErrorMessage
                          name="lokasi_kawasan_tr"
                          component="div"
                          className="text-red-500"
                        />

                        <Label className="block text-xs font-medium">
                          Keterangan
                        </Label>
                        <div className="justify-between col-span-3 w-full text-xs">
                          <CKEditor
                            editor={ClassicEditor}
                            data={values.keterangan_tr} // Menetapkan nilai saat ini dari form
                            onChange={(event, editor) => {
                              const data = editor.getData();
                              setFieldValue("keterangan_tr", data); // Mengupdate nilai form
                            }}
                            config={{
                              placeholder: "keterangan_tr EN...",
                            }}
                            className="w-full" // Mengatur lebar CKEditor
                          />
                        </div>
                        {/* <div className="justify-between col-span-3 w-full text-xs">
                          <ReactQuill
                            value={values.deskripsi_tr} // Menetapkan nilai saat ini dari form
                            onChange={(content) => setFieldValue('deskripsi_tr', content)} // Mengupdate nilai form
                            placeholder="Keterangan en..."
                            className="w-full" // Mengatur lebar Quill editor
                          />
                        </div> */}
                        <ErrorMessage
                          name="keterangan_tr"
                          component="div"
                          className="text-red-500"
                        />

                        <Label className="block text-xs font-medium">
                          Tagline
                        </Label>
                        <Input
                          name="deskripsi_singkat_tr"
                          type="text"
                          placeholder="Ketikkan tagline EN..."
                          onChange={(e) =>
                            setFieldValue(
                              "deskripsi_singkat_tr",
                              e.target.value
                            )
                          }
                          value={values.deskripsi_singkat_tr}
                          className="justify-between col-span-3 w-full text-xs"
                        />
                        <ErrorMessage
                          name="deskripsi_singkat_tr"
                          component="div"
                          className="text-red-500"
                        />

                        <Label className="block text-xs font-medium">
                          Deskripsi Singkat
                        </Label>
                        <Input
                          name="deskripsi_tr"
                          type="text"
                          placeholder="Ketikkan deskripsi singkat EN..."
                          onChange={(e) =>
                            setFieldValue("deskripsi_tr", e.target.value)
                          }
                          value={values.deskripsi_tr}
                          className="justify-between col-span-3 w-full text-xs"
                        />
                        <ErrorMessage
                          name="deskripsi_tr"
                          component="div"
                          className="text-red-500"
                        />

                        <Label className="block text-xs font-medium">
                          Upload Infografis En{" "}
                          <span className="text-[10px] text-red-600">
                            Max 2MB per file, ukuran POTRAIT(1690x2590)
                          </span>
                        </Label>
                        <FieldArray name="infografis_tr">
                          {({ remove, push }) => (
                            <div className="justify-between col-span-3 w-full">
                              {values.infografis_tr?.map((_, index) => (
                                <div
                                  key={index}
                                  className="mb-4 flex items-center space-x-2"
                                >
                                  <div className="w-full flex-row flex items-center">
                                    <Input
                                      name={`infografis_tr[${index}]`}
                                      type="file"
                                      accept="image/jpeg, image/jpg, image/png, image/webp, image/svg+xml"
                                      onChange={(event) => {
                                        const file =
                                          event.currentTarget.files[0];
                                        setFieldValue(
                                          `infografis_tr[${index}]`,
                                          file
                                        ); // Set file to potensi
                                        const reader = new FileReader();
                                        reader.onloadend = () => {
                                          // Create a copy of the current previews
                                          const updatedPreviews = [
                                            ...infografisTrPreview,
                                          ];
                                          updatedPreviews[index] =
                                            reader.result; // Assign the image preview to the corresponding index
                                          setInfografisTrPreview(
                                            updatedPreviews
                                          );
                                        };
                                        if (file) {
                                          reader.readAsDataURL(file);
                                          // Set fileName in potensi_detail
                                        }
                                      }}
                                      className="justify-between col-span-1 w-full text-xs"
                                    />
                                    <div className="relative col-span-2 w-80 mr-auto">
                                      {infografisTrPreview[index] ? (
                                        <Image
                                          src={infografisTrPreview[index]}
                                          alt="Image Preview"
                                          width={320}
                                          height={0}
                                          layout="responsive"
                                          objectFit="cover"
                                          className="rounded"
                                        />
                                      ) : (
                                        <div className="w-full h-full bg-white rounded flex items-center justify-center">
                                          {/* Placeholder text */}
                                        </div>
                                      )}
                                    </div>
                                  </div>

                                  <ErrorMessage
                                    name={`infografis_tr[${index}]`}
                                    component="div"
                                    className="text-red-500"
                                  />
                                </div>
                              ))}
                            </div>
                          )}
                        </FieldArray>

                        <ErrorMessage
                          name="infografis_tr"
                          component="div"
                          className="text-red-500"
                        />
                      </div>
                    )}
                  </div>

                  <Label className="block text-xs font-medium">Latitude</Label>
                  <Input
                    type="number"
                    step="0.000001"
                    placeholder="Masukkan latitude"
                    name="lat"
                    value={values.lat}
                    onChange={(e) => setFieldValue("lat", e.target.value)}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage
                    name="lat"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">Longitude</Label>
                  <Input
                    type="number"
                    step="0.000001"
                    placeholder="Masukkan longitude"
                    name="lon"
                    value={values.lon}
                    onChange={(e) => setFieldValue("lon", e.target.value)}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage
                    name="lon"
                    component="div"
                    className="text-red-500"
                  />

                  <div className="col-span-4">
                    <MapTag
                      setValue={(name, value) => setFieldValue(name, value)}
                      latFormName="lat"
                      lngFormName="lon"
                      defaultValue={{ lat: values.lat, lng: values.lon }}
                    />
                  </div>
                </div>

                {/* <Map /> */}

                {/* Submit button */}
                <DialogFooter>
                  <Button
                    type="button"
                    onClick={() => setEmailPopupOpen(true)} // Menampilkan popup email
                    className="text-xs bg-blue-500 hover:bg-blue-700"
                    size="sm"
                  >
                    Email
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="text-xs bg-blue-500 hover:bg-blue-700"
                    size="sm"
                  >
                    {isSubmitting ? "Submitting..." : "Simpan"}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>

          {/* Popup Kirim Email */}
          {isEmailPopupOpen && (
            <Dialog
              open={isEmailPopupOpen}
              onOpenChange={(open) => setEmailPopupOpen(open)}
            >
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Kirim Email</DialogTitle>
                </DialogHeader>
                {/* <form onSubmit={handleEmailSubmit}> */}
                <form onSubmit={(e) => e.preventDefault()}>
                  <div className="mb-4">
                    <label
                      htmlFor="subjek"
                      className="block text-sm font-medium"
                    >
                      Subjek
                    </label>
                    <Select
                      name="subjek"
                      value={emailData.subjek} // Ensure this is an integer
                      onValueChange={(value) =>
                        setEmailData((prev) => ({
                          ...prev,
                          subjek: parseInt(value, 10), // Convert to integer before updating the state
                        }))
                      }
                    >
                      <SelectTrigger>
                        {/* Use the integer value in the display logic */}
                        <SelectValue placeholder="Pilih status">
                          {emailData.subjek === 1 && "Nota Dinas"}{" "}
                          {/* Compare with integer */}
                          {emailData.subjek === 2 && "Informasi"}{" "}
                          {/* Compare with integer */}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        {/* Ensure the value passed to SelectItem is an integer */}
                        <SelectItem value={1}>Nota Dinas</SelectItem>{" "}
                        {/* Use integer value */}
                        <SelectItem value={2}>Informasi</SelectItem>{" "}
                        {/* Use integer value */}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="mb-4">
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium"
                    >
                      Email Penerima
                    </label>
                    <div className="flex flex-wrap gap-2 p-2 border rounded-md min-h-[42px] mb-2">
                      {emailData?.email?.map((email, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-1 bg-blue-100 px-2 py-1 rounded-md"
                        >
                          <span className="text-sm">{email}</span>
                          <button
                            type="button"
                            onClick={() => removeEmail(email)}
                            className="text-gray-500 hover:text-gray-700"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </div>
                      ))}
                      <Input
                        type="email"
                        value={emailData.currentEmail}
                        onChange={handleEmailInputChange}
                        onKeyDown={handleEmailKeyDown}
                        onBlur={handleEmailBlur}
                        onKeyUp={handleEmailTab}
                        placeholder="Ketik email dan tekan Enter"
                        className="border-none focus:ring-0 flex-grow min-w-[200px]"
                      />
                    </div>
                    <p className="text-xs text-red-500">
                      Tekan Enter atau koma untuk menambahkan email
                    </p>
                  </div>
                  <div className="mb-4">
                    <label htmlFor="nama" className="block text-sm font-medium">
                      Nama
                    </label>
                    <Input
                      type="text"
                      name="nama"
                      value={emailData.nama}
                      readOnly
                      className="bg-gray-100 cursor-not-allowed text-sm"
                    />
                  </div>
                  {/* <div className="mb-4">
                    <label
                      htmlFor="project_status_enum"
                      className="block text-sm font-medium"
                    >
                      Status
                    </label>
                    <Select
                      name="project_status_enum"
                      value={emailData.project_status_enum}
                      disabled
                    >
                      <SelectTrigger>
                        <SelectValue>
                          {peluangStatusOptions.find(
                            (opt) =>
                              opt.id_peluang_status ===
                              emailData.project_status_enum
                          )?.nama || "Pilih status"}
                        </SelectValue>
                      </SelectTrigger>
                    </Select>
                  </div> */}
                  <div className="mb-4">
                    <label
                      htmlFor="keterangan"
                      className="block text-sm font-medium"
                    >
                      Keterangan
                    </label>
                    <Textarea
                      name="keterangan"
                      value={emailData.keterangan}
                      onChange={handleEmailChange}
                      placeholder="Tulis pesan Anda"
                      required
                    />
                  </div>
                  <DialogFooter>
                    <Button
                      type="submit"
                      className="text-xs bg-green-500 hover:bg-green-700"
                      size="sm"
                      // disabled={isSubmitting}
                      disabled={
                        isSubmitting ||
                        !emailData.email ||
                        !emailData.subjek ||
                        !emailData.keterangan
                      }
                    >
                      {isSubmitting ? "Menyimpan..." : "Simpan & Email"}
                    </Button>
                    <Button
                      type="button"
                      onClick={() => setEmailPopupOpen(false)}
                      className="text-xs bg-red-500 hover:bg-red-700"
                      size="sm"
                    >
                      Batal
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          )}
        </Form>
      )}
    </Formik>
  );
};

export default FormDialogFull;
