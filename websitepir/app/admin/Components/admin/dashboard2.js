import { getDataInfrastrukturDashboardService } from "@/services/Dashboard";
import React, { useEffect, useState } from "react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from "recharts";

const Dashboard2 = () => {
  const [data, setData] = useState(null);
  const [legendState, setLegendState] = useState({
    update: { active: true, opacity: 1 },
    belum_update: { active: true, opacity: 1 }
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getDataInfrastrukturDashboardService();
        setData(response);
      } catch (error) {
        console.error("Error fetching data", error);
      }
    };
    fetchData();
  }, []);

  if (!data) {
    return <div>Loading...</div>;
  }

  const formatNumber = (number) => {
    return new Intl.NumberFormat('id-ID').format(number);
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const updateValue = payload[0]?.value || 0;
      const belumUpdateValue = payload[1]?.value || 0;
      return (
        <div className="bg-white p-4 border rounded shadow">
          <p className="font-medium">{`${label}`}</p>
          <p className="text-sm">{`Update: ${formatNumber(updateValue)}`}</p>
          <p className="text-sm">{`Belum Update: ${formatNumber(belumUpdateValue)}`}</p>
        </div>
      );
    }
    return null;
  };

  const barData = data.map((item) => ({
    name: item.nama,
    update: item.update,
    belum_update: item.belum_update,
    total: item.update + item.belum_update
  }));

  const handleLegendClick = (dataKey) => {
    setLegendState(prev => ({
      ...prev,
      [dataKey]: {
        active: !prev[dataKey].active,
        opacity: prev[dataKey].active ? 0.3 : 1
      }
    }));
  };

  const renderLegend = (props) => {
    const { payload } = props;
    return (
      <div className="flex justify-center space-x-4 mt-4">
        {payload.map((entry, index) => {
          const dataKey = entry.dataKey;
          const isActive = legendState[dataKey].active;
          return (
            <div
              key={`item-${index}`}
              className={`flex items-center cursor-pointer`}
              onClick={() => handleLegendClick(dataKey)}
            >
              <div
                className="w-4 h-4 mr-2"
                style={{
                  backgroundColor: entry.color,
                  opacity: legendState[dataKey].opacity
                }}
              />
              <span
                style={{
                  opacity: legendState[dataKey].opacity,
                  fontWeight: isActive ? 'normal' : 'normal'
                }}
              >
                {dataKey === 'update' ? 'Update' : 'Belum Update'}
              </span>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="dashboard flex flex-col gap-8 p-6">
      {barData.length > 0 ? (
        <>
          <div className="bg-white p-6 rounded-lg shadow w-full">
            {/* <h3 className="text-xl font-semibold mb-6 text-center">
            {`Status Data Infrastruktur Tahun ${new Date().getFullYear()}`}
          </h3> */}
            <ResponsiveContainer width="100%" height={500}>
              <BarChart
                data={barData}
                layout="vertical"
                margin={{ top: 20, right: 30, left: 10, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" tickFormatter={formatNumber} />
                <YAxis
                  type="category"
                  dataKey="name"
                  angle={0}
                  width={160}
                  tick={{ fontSize: 12 }}
                // tick={{ fontSize: 10, fontWeight: 'bold', fill: 'black' }}
                />
                <Tooltip content={CustomTooltip} />
                <Legend
                  content={renderLegend}
                  wrapperStyle={{ marginTop: '20px' }}
                />
                <Bar
                  dataKey="update"
                  stackId="a"
                  fill="#87CEFA"
                  opacity={legendState.update.opacity}
                  hide={!legendState.update.active}
                  name="Update"
                  label={legendState.update.active ? {
                    position: 'insideRight',
                    formatter: formatNumber,
                    fill: '#333',
                    fontSize: 11
                  } : null}
                />
                <Bar
                  dataKey="belum_update"
                  stackId="a"
                  fill="#4f7ec2"
                  opacity={legendState.belum_update.opacity}
                  hide={!legendState.belum_update.active}
                  name="Belum Update"
                  label={legendState.belum_update.active ? {
                    position: 'right',
                    formatter: formatNumber,
                    fill: '#333',
                    fontSize: 11
                  } : null}
                />
              </BarChart>
            </ResponsiveContainer>

            <div className="overflow-x-auto mt-8">
              <table className="min-w-full divide-y divide-gray-200 border border-gray-300">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      
                    </th>
                    <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                    <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Update
                    </th>
                    <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Belum Update
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {barData.map((item, index) => (
                    <tr key={`row-${index}`} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-6 py-4 whitespace-nowrap text-xs text-center font-medium text-gray-900">
                        {item.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-xs text-center text-gray-500 ">
                        {formatNumber(item.total)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-xs text-center text-gray-500 ">
                        {formatNumber(item.update)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-xs text-center text-gray-500 ">
                        {formatNumber(item.belum_update)}
                      </td>
                    </tr>
                  ))}

                </tbody>
              </table>
            </div>
          </div>

      
        </>
      ) : (
        <div className="bg-white p-6 rounded-lg shadow w-full text-center">
          No Data Available
        </div>
      )}
    </div>
  );
};

export default Dashboard2;