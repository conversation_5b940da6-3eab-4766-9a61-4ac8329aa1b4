"use client";

import { But<PERSON> } from "@/components/ui/button";
import { flagProvinsi, getByIdPICDataService, unflagProvinsi } from "@/services/Dashboard";
import useSelectedDataStore from "@/store/cms/SelectedDataStore";
import { Check, Clock4, Flag } from "lucide-react";
import React, { useState } from "react";
import {
  PieChart,
  Pie,
  Cell,
  Tooltip,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  Legend,
  ResponsiveContainer,
} from "recharts";

// Warna untuk Pie Chart
const COLORS = ["#44cf6e", "#d64040"];

const DetailPengguna = ({ data, user }) => {
  const [filter, setFilter] = useState("ada");
  const [isLoading, setIsLoading] = useState({});
  const { selectedUserId, setSelectedData } = useSelectedDataStore();
  console.log("Dataaaaa", data);
  console.log("Dataaaaa udr", user);
  const pieData = [
    { name: "Update", value: data.total_ada, percentage: data.persentase_ada },
    {
      name: "Belum Update",
      value: data.total_tidak_ada,
      percentage: data.persentase_tidak_ada,
    },
  ];

  // Filter data berdasarkan pilihan pengguna
  const filteredData = filter === "ada" ? data.ada : data.tidak_ada;

  // Data untuk Bar Chart menggunakan indikator_prov
  const barData = data.indikator_prov.map((prov) => ({
    name: prov.nama_prov,
    Update: prov.ada,
    "Belum Update": prov.tidak_ada,
  }));

  // Custom label for pie chart that shows percentage
  const renderCustomizedLabel = ({
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    percent,
    index,
  }) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos((-midAngle * Math.PI) / 180);
    const y = cy + radius * Math.sin((-midAngle * Math.PI) / 180);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor="middle"
        dominantBaseline="central"
        fontSize="11"
        fontWeight="medium"
      >
        {`${pieData[index].percentage}%`}
      </text>
    );
  };

  const refreshData = async () => {
    try {
      const response = await getByIdPICDataService(selectedUserId);
      if (response.success) {
        setSelectedData(response.data);
      } else {
        console.error("Failed to refresh data");
      }
    } catch (error) {
      console.error("Error refreshing data:", error);
    }
  };

  // Handle flag/unflag action
  const handleFlagToggle = async (item) => {
    setIsLoading({ ...isLoading, [item.id_mapping_indikator_pic]: true });

    try {
      if (item.flag) {
        // If already flagged, unflag it
        await unflagProvinsi(item.id_mapping_indikator_pic);
      } else {
        // If not flagged, flag it
        const body = {
          id_user: selectedUserId,
          kd_prov: item.kd_prov,
          id_indikator_pic: item.id_indikator_pic,
          tahun: item.tahun || new Date().getFullYear(),
        };
        await flagProvinsi(body);
      }
      await refreshData();
    //   item.flag = !item.flag;
    } catch (error) {
      console.error("Error toggling flag:", error);
    } finally {
      setIsLoading({ ...isLoading, [item.id_mapping_indikator_pic]: false });
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-4">
      {/* Pie Chart */}
      <div className="bg-white p-4 shadow-md rounded-lg">
        <div className="bg-[#2D3A96] text-white w-full p-4 rounded-md shadow-md mb-8">
          <h1 className="text-lg font-semibold mb-2">Persentase Laporan PIC</h1>
          <h2 className="text-sm mb-2">
            {user.nama} - {user.jabatan}
          </h2>
        </div>

        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={pieData}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              outerRadius={80}
              labelLine={false}
              label={renderCustomizedLabel}
            >
              {pieData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={COLORS[index % COLORS.length]}
                />
              ))}
            </Pie>
            <Tooltip
              formatter={(value, name, props) => [
                `${props.payload.percentage}% (${value})`,
                name,
              ]}
            />
          </PieChart>
        </ResponsiveContainer>

        {/* Legend */}
        <div className="flex justify-center gap-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-[#44cf6e] rounded"></div>
            <span>Update</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-[#d64040] rounded"></div>
            <span>Belum Update</span>
          </div>
        </div>
      </div>

      {/* Tabel Data */}
      <div className="bg-white p-4 shadow-md rounded-lg">
        <div className="bg-[#2D3A96] text-white w-full p-4 rounded-md shadow-md mb-8">
          <h1 className="text-lg font-semibold mb-2">
            Status Data Daerah Provinsi Laporan PIC
          </h1>
          <h2 className="text-sm mb-2">
            {user.nama} - {user.jabatan}
          </h2>
        </div>
        <div className="flex space-x-2 mb-4">
          <Button
            onClick={() => setFilter("ada")}
            variant={filter === "ada" ? "default" : "white"}
            className={filter !== "ada" ? "text-[#2D3A96]" : ""}
          >
            <Check className="mr-1 h-4 w-4" /> Update
          </Button>
          <Button
            onClick={() => setFilter("tidak_ada")}
            variant={filter === "tidak_ada" ? "destructive" : "white"}
            className={filter !== "tidak_ada" ? "text-[#2D3A96]" : ""}
          >
            <Clock4 className="mr-1 h-4 w-4" /> Belum Update
          </Button>
        </div>
        <div className="overflow-auto max-h-64">
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-200">
                <th className="border px-4 py-2 text-sm">Data Indikator</th>
                <th className="border px-4 py-2 text-sm">Provinsi</th>
                {filter === "tidak_ada" && (
                  <th className="border px-4 py-2 text-sm">Aksi</th>
                )}
              </tr>
            </thead>
            <tbody>
              {filteredData.map((item, index) => (
                <tr key={index} className="hover:bg-gray-100">
                  <td className="border px-4 py-2 text-xs">{item.indikator}</td>
                  <td className="border px-4 py-2 text-xs">{item.nama_prov}</td>
                  {filter === "tidak_ada" && (
                    <td className="border px-4 py-2 text-xs">
                      {item.id_indikator_pic == 11 ? (
                      <Button
                        size="sm"
                        className={`text-xs px-2 py-1 h-auto ${
                          item.flag
                            ? "bg-red-600 hover:bg-red-700 text-white"
                            : "bg-[#44cf6e] hover:bg-green-700 text-white"
                        }`}
                        onClick={() => handleFlagToggle(item)}
                        disabled={isLoading[item.id_mapping_indikator_pic]}
                      >
                        <Flag className="mr-1 h-3 w-3" />
                        {isLoading[item.id_mapping_indikator_pic]
                          ? "Loading..."
                          : item.flag
                          ? "Batalkan Data Nihil"
                          : "Data Nihil"}
                      </Button>
                      ) : (
                        ""
                      )}
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Bar Chart */}
      <div className="col-span-2 bg-white p-4 shadow-md rounded-lg">
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={barData} barCategoryGap="30%">
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="Update" stackId="a" fill="#44cf6e" />
            <Bar dataKey="Belum Update" stackId="a" fill="#DC3545" />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default DetailPengguna;
