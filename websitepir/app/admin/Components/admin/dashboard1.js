"use client";

import { getDataDashboardService } from "@/services/Dashboard";
import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";

const Dashboard1 = ({ startDate, endDate }) => {
  const [data, setData] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getDataDashboardService({
          startDate: startDate || "",
          endDate: endDate || "",
        });
        setData(response.data);
      } catch (error) {
        console.error("Error fetching data", error);
      }
    };

    fetchData();
  }, [startDate, endDate]);

  if (!data) {
    return <div>Loading...</div>;
  }

  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"];

  const formatNumber = (number) => {
    return new Intl.NumberFormat("id-ID").format(number);
  };

  const CustomTooltip = ({ active, payload, nameKey }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 border rounded shadow">
          <p className="font-medium">{payload[0].name}</p>
          <p className="text-sm">{`Jumlah: ${formatNumber(
            payload[0].value
          )}`}</p>
        </div>
      );
    }
    return null;
  };

  const pieData = data.pengunjung_menu.map((item) => ({
    name: item.nama,
    value: item.jumlah_pengunjung,
  }));

  const barProvinsiData = [...data.pengunjung_provinsi]
    .sort((a, b) => b.jumlah - a.jumlah)
    .map((item) => ({
      name: item.nama,
      value: item.jumlah,
    }));

  const barPeluangData = [...data.pengunjung_peluang]
    .sort((a, b) => b.jumlah - a.jumlah)
    .map((item) => ({
      name: item.nama,
      value: item.jumlah,
    }));

  const barUnduhData = [...data.unduh_data]
    .sort((a, b) => b.jumlah - a.jumlah)
    .map((item) => ({
      name: item.negara,
      value: item.jumlah,
    }));

  const barProvinsiAllData = [...data.pengunjung_provinsi_all]
    .sort((a, b) => b.jumlah - a.jumlah)
    .map((item) => ({
      name: item.nama,
      value: item.jumlah,
    }));

  return (
    <div className="dashboard flex flex-col gap-8 p-6">
      {/* Pie Chart - Pengunjung Menu */}
      {pieData.length > 0 ? (
        <div className="bg-white p-6 rounded-lg shadow w-full">
          <h3 className="text-xl font-semibold mb-6 text-center">
            Pengunjung Menu
          </h3>
          <ResponsiveContainer width="100%" height={400}>
            <PieChart>
              <Pie
                data={pieData}
                dataKey="value"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={150}
                label={({ name, percent, value }) =>
                  `${name} ${formatNumber(value)} (${(percent * 100).toFixed(0)}%)`
                }
              >
                {pieData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                  />
                ))}
              </Pie>
              <Tooltip content={CustomTooltip} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
      ) : (
        <div className="bg-white p-6 rounded-lg shadow w-full text-center"></div>
      )}

      {/* Bar Chart - Semua Pengunjung Provinsi */}
      {barProvinsiAllData.length > 0 ? (
        <div className="bg-white p-6 rounded-lg shadow w-full">
          <h3 className="text-xl font-semibold mb-6 text-center">
            Pengunjung Provinsi (Total Keseluruhan)
          </h3>
          <ResponsiveContainer width="100%" height={Math.max(barProvinsiAllData.length * 30, 400)}>
            <BarChart
              data={barProvinsiAllData}
              layout="vertical"
              margin={{ left: 10, right: 100 }}
              barCategoryGap={2}  // Jarak antar kategori (semakin kecil, semakin rapat)
              barGap={2}  
            >
              <CartesianGrid strokeDasharray="3 3" />
              <YAxis
                type="category"
                dataKey="name"
                width={180}
                tick={{ fontSize: 12 }}
              />
              <XAxis
                type="number"
                tick={{ fontSize: 12 }}
                tickFormatter={formatNumber}
              />
              <Tooltip content={CustomTooltip} />
              <Bar 
                dataKey="value" 
                fill="#8884d8" 
                radius={[0, 4, 4, 0]} 
                barSize={10}
                label={{
                  position: 'right',
                  formatter: formatNumber,
                  fill: '#333',
                  fontSize: 12
                }}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      ) : (
        <div className="bg-white p-6 rounded-lg shadow w-full text-center"></div>
      )}

      {/* Bar Chart - Pengunjung Provinsi */}
      {barProvinsiData.length > 0 ? (
        <div className="bg-white p-6 rounded-lg shadow w-full">
          <h3 className="text-xl font-semibold mb-6 text-center">
            Pengunjung Provinsi
          </h3>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart
              data={barProvinsiData}
              layout="vertical"
              margin={{ left: 10, right: 100 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <YAxis
                type="category"
                dataKey="name"
                width={180}
                tick={{ fontSize: 12 }}
              />
              <XAxis
                type="number"
                tick={{ fontSize: 12 }}
                tickFormatter={formatNumber}
              />
              <Tooltip content={CustomTooltip} />
              <Bar 
                dataKey="value" 
                fill="#00C49F" 
                radius={[0, 4, 4, 0]}
                label={{
                  position: 'right',
                  formatter: formatNumber,
                  fill: '#333',
                  fontSize: 12
                }}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      ) : (
        <div className="bg-white p-6 rounded-lg shadow w-full text-center"></div>
      )}

      {/* Bar Chart - Pengunjung Peluang */}
      {barPeluangData.length > 0 ? (
        <div className="bg-white p-6 rounded-lg shadow w-full">
          <h3 className="text-xl font-semibold mb-6 text-center">
            Pengunjung Peluang
          </h3>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart
              data={barPeluangData}
              layout="vertical"
              margin={{ left: 10, right: 100 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <YAxis
                type="category"
                dataKey="name"
                width={230}
                tick={{ fontSize: 12 }}
              />
              <XAxis
                type="number"
                tick={{ fontSize: 12 }}
                tickFormatter={formatNumber}
              />
              <Tooltip content={CustomTooltip} />
              <Bar 
                dataKey="value" 
                fill="#0088FE" 
                radius={[0, 4, 4, 0]}
                label={{
                  position: 'right',
                  formatter: formatNumber,
                  fill: '#333',
                  fontSize: 12
                }}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      ) : (
        <div className="bg-white p-6 rounded-lg shadow w-full text-center"></div>
      )}

      {/* Bar Chart - Unduh Data */}
      {barUnduhData.length > 0 ? (
        <div className="bg-white p-6 rounded-lg shadow w-full">
          <h3 className="text-xl font-semibold mb-6 text-center">Unduh Data</h3>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart
              data={barUnduhData}
              layout="vertical"
              margin={{ left: 10, right: 100 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <YAxis
                type="category"
                dataKey="name"
                width={130}
                tick={{ fontSize: 12 }}
              />
              <XAxis
                type="number"
                tick={{ fontSize: 12 }}
                tickFormatter={formatNumber}
              />
              <Tooltip content={CustomTooltip} />
              <Bar 
                dataKey="value" 
                fill="#FFBB28" 
                radius={[0, 4, 4, 0]}
                label={{
                  position: 'right',
                  formatter: formatNumber,
                  fill: '#333',
                  fontSize: 12
                }}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      ) : (
        <div className="bg-white p-6 rounded-lg shadow w-full text-center"></div>
      )}
    </div>
  );
};

export default Dashboard1;
