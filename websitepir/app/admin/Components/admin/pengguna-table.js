"use client";

import React, { useCallback, useEffect, useState } from "react";
import { getByIdPICDataService, getDataUserDashboardService } from "@/services/Dashboard";
import DataTableDashboard from "./DataTableDashboard";
import { Check } from "lucide-react";
import useSelectedDataStore from "@/store/cms/SelectedDataStore";

const PenggunaTable = ({user}) => {
    const [data, setData] = useState([]);

    const modifiedFetchDataService = useCallback(
        async () => {
            try {
                const response = await getDataUserDashboardService();

                if (response.success && Array.isArray(response.data)) {
                    // Update the data state
                    setData(response.data);
                }

                return response;
            } catch (error) {
                console.error("Error in modified fetch service:", error);
                throw error;
            }
        },
        []
    );

    // Function to handle the button click
    const handlePilih = async (id, nama, jabatan) => {
        try {
            // Call the service here
            const response = await getByIdPICDataService(id);
            if (response.success) {
                useSelectedDataStore.getState().setSelectedData(response.data);
                useSelectedDataStore.getState().setSelectedUser({ nama, jabatan });
                useSelectedDataStore.getState().setSelectedUserId(id); 

                useSelectedDataStore.getState().setShouldScrollToDetail(true);
                
            } else {
                alert("Aksi gagal. Silakan coba lagi.");
            }
        } catch (error) {
            console.error("Error in pilih action:", error);
            alert("Terjadi kesalahan saat melakukan aksi.");
        }
    };

    useEffect(() => {
        const fetchPICData = async () => {
            if (user && user.roleId === 14) {
                try {
                    console.log("Mengambil data untuk user roleId 14:", user);
                    const response = await getByIdPICDataService(user.id);
                    console.log("Data PIC ditemukan:", response);
                    
                    if (response && response.success) {
                        // Gunakan nama dan jabatan dari user jika tersedia
                        const nama = user.first_name ? user.first_name : "" + " " + user.middle_name ? user.middle_name : "" + " " + user.last_name ? user.last_name : "";
                        const jabatan = user.jabatan || user.role_name || "PIC";
                        
                        // Panggil handlePilih dengan data yang sudah diambil
                        handlePilih(user.id, nama, jabatan);
                    }
                } catch (error) {
                    console.error("Gagal mengambil data PIC:", error);
                }
            }
        };
        
        fetchPICData();
    }, [user]);

    const columns = [
        {
            accessorKey: "aksi",
            header: "Aksi",
            cell: ({ row }) => (
                <button
                    onClick={() => handlePilih(row.original.id, row.original.nama, row.original.jabatan)}
                    className="bg-[#2D3A96] text-white px-2 py-2 rounded flex items-center space-x-1 hover:bg-[#1B2878] transition-colors duration-300"
                >
                    <Check className="h-4 w-4" />
                    <span>Pilih</span>
                </button>
            ),
        },
        { accessorKey: "nama", header: "Nama" },
        { accessorKey: "jabatan", header: "Jabatan" },
        { accessorKey: "role_name", header: "Role" },
        {
            accessorKey: "provinsi",
            header: "PIC Provinsi",
            cell: ({ getValue }) => {
                const value = getValue();

                // Jika value kosong atau null, tampilkan teks pengganti atau return null
                if (!value || value.trim() === "") {
                    return null;
                }

                // Pisahkan nilai berdasarkan koma untuk multi-provinsi
                const provinsiList = value.split(',').map(item => item.trim());

                return (
                    <div className="flex flex-wrap">
                        {provinsiList.map((provinsi, index) => (
                            <span
                                key={index}
                                className="inline-flex items-center px-3 py-1 bg-green-500 text-white rounded-full mr-2"
                            >
                                {provinsi}
                            </span>
                        ))}
                    </div>
                );
            },
        },
        // { accessorKey: "kawasan", header: "PIC Kawasan Industri" },
        {
            accessorKey: "kawasan",
            header: "PIC Kawasan Industri",
            cell: ({ getValue }) => {
                const value = getValue();

                // Jika value kosong atau null, tidak menampilkan badge
                if (!value || value.trim() === "") {
                    return null;
                }

                // Pisahkan nilai berdasarkan koma
                const kawasanList = value.split(',').map(item => item.trim());

                return (
                    <div className="flex flex-wrap">
                        {kawasanList.map((kawasan, index) => (
                            <span
                                key={index}
                                className="inline-flex items-center px-3 py-1 bg-blue-500 text-white rounded-full mr-2"
                            >
                                {kawasan}
                            </span>
                        ))}
                    </div>
                );
            },
        },
    ];

    return (
        <>
            {user && user.roleId === 1 && (
                <section className="title-section">
                <DataTableDashboard
                    columns={columns}
                    fetchDataService={modifiedFetchDataService}
                    filterPlaceholder="Cari Peluang IKN..."
                />
                </section>
            )}

        </>
    );
};

export default PenggunaTable;
