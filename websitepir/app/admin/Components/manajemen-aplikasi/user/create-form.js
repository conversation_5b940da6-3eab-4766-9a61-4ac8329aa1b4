'use client';

import React, { useEffect, useRef, useState } from "react";
import { Formik, Form, ErrorMessage } from "formik";
import * as Yup from "yup";
import { createUserService } from "@/services/UserServices";
import { Plus } from "lucide-react";
import useRoleStore from "@/store/RoleStore";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Button } from "@/components/ui/button";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import FormDialog from "../../FormDialog";
import SweetAlert from "../../SweetAlert";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import useReferensiStore2 from "@/store/cms/ReferensiStore2";
import Api from "@/utils/Api";

const createUserSchema = Yup.object().shape({
  login_name: Yup.string().required("Login Name is required"),
  password: Yup.string().min(8, "Password must be at least 8 characters long").required("Password is required"),
  email: Yup.string().email("Invalid email address").required("Email is required"),
  full_name: Yup.string().required("Full Name is required"),
  role_id: Yup.string().required("Role is required"),
});

const CreateForm = ({ onDataChange }) => {
  const { roles, getRoles } = useRoleStore();
  const [open, setOpen] = useState(false);
  const [selectedRoleName, setSelectedRoleName] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const [openRole, setOpenRole] = useState(false);

  const [selectedProvinsi, setSelectedProvinsi] = useState("");
  const [selectedDaerah, setSelectedDaerah] = useState("");
  const [selectedKI, setSelectedKI] = useState("");

  const [selectedPicProvinsi, setSelectedPicProvinsi] = useState([]);
  const [openPicProvinsi, setOpenPicProvinsi] = useState(false);
  const [showPicProvinsi, setShowPicProvinsi] = useState(false);

  const [selectedPicKawasan, setSelectedPicKawasan] = useState([]);
  const [openPicKawasan, setOpenPicKawasan] = useState(false);
  const [showPicKawasan, setShowPicKawasan] = useState(false);

  const [selectedJabatan, setSelectedJabatan] = useState(""); // Tambahan baru
  const [openJabatan, setOpenJabatan] = useState(false);     // Tambahan baru
  const [showJabatan, setShowJabatan] = useState(false);     // Tambahan baru


  const [openProvinsi, setOpenProvinsi] = useState(false);
  const [openDaerah, setOpenDaerah] = useState(false);
  const [openKI, setOpenKI] = useState(false);
  const [filteredDaerahOptions, setFilteredDaerahOptions] = useState([]);
  const [showProvinsi, setShowProvinsi] = useState(false);
  const [showDaerah, setShowDaerah] = useState(false);
  const [showKI, setShowKI] = useState(false);

  useEffect(() => {
    getRoles();
  }, [getRoles]);

  const getToken = async () => {
    try {
      const userLogin = {
        username: process.env.NEXT_PUBLIC_UNAME_GEOPORTAL,
        password: process.env.NEXT_PUBLIC_PASS_GEOPORTAL,
      };

      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/iam/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(userLogin),
      });

      if (!res.ok) {
        const errorMessage = await res.text();
        throw new Error(`Error ${res.status}: ${errorMessage}`);
      }

      const data = await res.json();
      console.log("Token successfully obtained:", data.accessToken);
      return data.accessToken;
    } catch (error) {
      console.error("Error obtaining token:", error);
      throw error;
    }
  };

  const createUserOnSecondSystem = async (formData2, accessToken) => {
    try {
      const cek = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/gp-users/login_name/${formData2.login_name}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      })      
      if (cek.success) {
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/adm/user/create`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(formData2),
      });

      if (!res.ok) {
        const errorJson = await res.json().catch(() => ({}));
        const errorMessage = errorJson.detail || "Gagal membuat pengguna di sistem kedua.";
  
        throw {
          message: errorMessage,
          status: res.status,
        };
      }

      const data = await res.json();
      console.log("User successfully created on second system:", data);
      return data;
      }
      } catch (error) {
      console.error("Error creating user on second system:", error);
      throw error;
    }
  };

  const containerRef = useRef(null);


  const {
    provinsiOptions,
    kabkotOptions: daerahOptions,
    kawasanIndustriOptions,
    fetchKawasanIndustri,
    fetchProvinsi,
    fetchKabkot,
    fetchUserInternalJabatan,
    userInternalJabatanOptions,
    fetchUserInternalProvinsi,
    userInternalProvinsiOptions,
    fetchUserInternalKawasan,
    userInternalKawasanOptions,
  } = useReferensiStore2();

  useEffect(() => {
    if (provinsiOptions.length === 0) {
      fetchProvinsi();
    }
    if (daerahOptions.length === 0) {
      fetchKabkot();
    }
    if (kawasanIndustriOptions.length === 0) {
      fetchKawasanIndustri();
    }
    if (userInternalJabatanOptions.length === 0) {
      fetchUserInternalJabatan();
    }
    if (userInternalProvinsiOptions.length === 0) {
      fetchUserInternalProvinsi();
    }
    if (userInternalKawasanOptions.length === 0) {
      fetchUserInternalKawasan();
    }
  }, [fetchKabkot, daerahOptions, fetchProvinsi, provinsiOptions, kawasanIndustriOptions, fetchKawasanIndustri, userInternalJabatanOptions, fetchUserInternalJabatan]);
  useEffect(() => {
    if (selectedProvinsi) {
      const filteredDaerah = daerahOptions.filter(
        (daerah) => daerah.id_adm_provinsi === selectedProvinsi.id_adm_provinsi
      );
      setFilteredDaerahOptions(filteredDaerah);
    } else {
      setFilteredDaerahOptions([]); // Clear filtered options if no province is selected
    }
  }, [selectedProvinsi, daerahOptions]); // Run this effect when selectedProvinsi or daerahOptions change


  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setSelectedDaerah("");
    setSelectedKI("");
    setSelectedProvinsi("");
    setSelectedPicProvinsi([]);
    setSelectedPicKawasan([]);
    setSelectedJabatan("");  // Tambahan baru
    setSelectedRoleName("");
  };

  return (
    <Formik
      initialValues={{
        login_name: "",
        password: "",
        email: "",
        first_name: "",
        middle_name: "",
        last_name: "",
        role_id: "",
        mobile_number: "",
        phone_number: "",
        id_adm_provinsi: "",
        id_adm_kabkot: "",
        id_kawasan_industri: "",
        pic_prov:[],
        kawasan_prov:[],
        jabatan:""
      }}
      // validationSchema={createUserSchema}
      // onSubmit={async (values, { setSubmitting }) => {
      //   try {
      //     await createUserService(values);
      //     console.log("User successfully created on first system");

      //     const accessToken = await getToken();
      //     // console.log("access tokena", accessToken);
      //     const fullName = [values.first_name, values.middle_name, values.last_name]
      //     .filter(Boolean) // Menghapus string kosong agar tidak ada spasi berlebih
      //     .join(" ");

      //     const formData2 = {
      //       login_name: values.login_name,
      //       full_name: fullName,
      //       email: values.email,
      //       password: values.password,
      //       role_uid: ["Iphae9kahxees2op", "Kuceejaigai9jieW"], // Set appropriate role_uid
      //     };

      //     await createUserOnSecondSystem(formData2, accessToken);
      //     console.log("User successfully created on geoportal");
      //     SweetAlert.success("Success", "Pengguna berhasil di buat!", () => {
      //       // // window.location.reload();
      //     });
      //     onDataChange();
      //     handleDialogClose();
      //   } catch (error) {
      //     SweetAlert.error("Error", "Gagal membuat pengguna.", () => {
      //       console.error("Error edit menu:", error);
      //     });
      //   } finally {
      //     setSubmitting(false);
      //   }
      // }}
      onSubmit={async (values, { setSubmitting,resetForm }) => {
        try {
          const accessToken = await getToken();
          if (!accessToken) {
            throw new Error("Gagal mendapatkan token.");
          }
      
          const fullName = [values.first_name, values.middle_name, values.last_name]
            .filter(Boolean) // Menghapus string kosong agar tidak ada spasi berlebih
            .join(" ");
      
          await createUserService(values);
          console.log("User successfully created on first system");
      
          const formData2 = {
            login_name: values.login_name,
            full_name: fullName,
            email: values.email,
            password: values.password,
            role_uid: [process.env.NEXT_PUBLIC_ROLE_UID_1, process.env.NEXT_PUBLIC_ROLE_UID_2, process.env.NEXT_PUBLIC_ROLE_UID_3], // Set appropriate role_uid
          };
      
          await createUserOnSecondSystem(formData2, accessToken);
          console.log("User successfully created on geoportal");
      
          SweetAlert.success("Success", "Pengguna berhasil dibuat!", () => {
            // window.location.reload();
          });
          onDataChange();
          handleDialogClose();
        } 
        // catch (error) {
        //   // SweetAlert.error("Error", error.errors.message || "Gagal membuat pengguna.");
        //   // console.error("Error creating user:", error);
        //   if (Array.isArray(error.errors)) {
        //     const errorMessages = error.errors.map(err => `Field: ${err.field} - ${err.message}`).join("\n");
        //     SweetAlert.error("Error", errorMessages);
        //   } else {
        //     SweetAlert.error("Error", error.errors.message || "Gagal membuat pengguna.");
        //   }
        //   console.error("Error creating user:", error.errors);
        //   console.log("Error creating user:", error.errors);
        // } finally {
        //   setSubmitting(false);
        //   resetForm();
        // }
        catch (error) {
          const defaultMessage = "Gagal membuat pengguna.";
        
          if (Array.isArray(error.errors)) {
            const errorMessages = error.errors.map(err =>
              `Field: ${err.field || "-"} - ${err.message || "-"}`
            ).join("\n");
            SweetAlert.error("Error", errorMessages);
          } else if (typeof error.message === "string") {
            SweetAlert.error("Error", error.message || defaultMessage);
          } else if (error.detail) {
            SweetAlert.error("Error", error.detail);
          } else {
            SweetAlert.error("Error", defaultMessage);
          }
        
          console.error("Error creating user:", error);
        }
      }}      
    >
      {({ setFieldValue, values, isSubmitting }) => (
        
        <Form >
          <Dialog open={isDialogOpen} onOpenChange={(open) => {
            if (!open) {
              handleDialogClose();
            }
            setIsDialogOpen(open);
          }}>
            <DialogTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                className="text-xs text-white"
              >
                Tambah Pengguna
              </Button>
            </DialogTrigger>

            <DialogContent className="sm:max-w-[900px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Tambah Pengguna</DialogTitle>
              </DialogHeader>
              <form>
                <div className="grid grid-cols-4 items-start gap-4" ref={containerRef}>
                  <Label className="block text-xs font-medium">Username <span className="text-[10px] text-red-600">*</span></Label>
                  <Input
                    type="text"
                    placeholder="Enter Username"
                    name="login_name"
                    value={values.login_name}
                    onChange={(e) => setFieldValue("login_name", e.target.value)}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage name="login_name" component="div" className="text-red-500" />

                  <Label className="block text-xs font-medium">Password <span className="text-[10px] text-red-600">*</span></Label>
                  <Input
                    type="password"
                    placeholder="Enter Password"
                    name="password"
                    value={values.password}
                    onChange={(e) => setFieldValue("password", e.target.value)}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage name="password" component="div" className="text-red-500" />


                  <Label className="block text-xs font-medium">Daftar Sebagai <span className="text-[10px] text-red-600">*</span></Label>
                  <Popover open={openRole} onOpenChange={setOpenRole} className="col-span-3 w-full">
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={openRole}
                        className="justify-between col-span-3 w-full text-xs"
                      >
                        {selectedRoleName || "Pilih Nama Role"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0" container={containerRef.current}>
                      <Command>
                        <CommandInput placeholder="Cari Role..." />
                        <CommandList>
                          <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                          <CommandGroup>
                            {roles.map((role) => (
                              <CommandItem
                                key={role.id}
                                value={role.role_name}
                                onSelect={() => {
                                  setFieldValue("role_id", role.id);
                                  setSelectedRoleName(role.role_name);
                                  setOpenRole(false);

                                  // Reset values for provinsi, daerah, dan kawasan industri
                                  setFieldValue("id_adm_provinsi", null);
                                  setFieldValue("id_adm_kabkot", null);
                                  setFieldValue("id_kawasan_industri", null);
                                  setSelectedProvinsi(null);
                                  setSelectedDaerah(null);
                                  setSelectedKI(null);

                                  // Atur kondisi untuk menampilkan provinsi dan daerah
                                  if (role.id === 3) {
                                    setShowProvinsi(true);
                                    setShowDaerah(false);
                                    setShowKI(false);
                                    setShowPicProvinsi(false);
                                    setShowPicKawasan(false);
                                  } else if (role.id === 4) {
                                    setShowProvinsi(true);
                                    setShowDaerah(true);
                                    setShowKI(false);
                                    setShowPicProvinsi(false);
                                    setShowPicKawasan(false);
                                  } else if (role.id === 5) {
                                    setShowProvinsi(false);
                                    setShowDaerah(false);
                                    setShowKI(true);
                                    setShowPicProvinsi(false);
                                    setShowPicKawasan(false);
                                  } else if (role.id == 14) {
                                    console.log('Role 14 selected in dropdown');
                                    setShowProvinsi(false);
                                    setShowDaerah(false);
                                    setShowKI(false);
                                    setShowPicProvinsi(true);
                                    setShowPicKawasan(true);
                                    setShowJabatan(true);  // Tambahan baru
                                    // Reset nilai pic_prov jika ada
                                    setFieldValue("pic_prov", []);
                                    setSelectedPicProvinsi([]);
                                    setFieldValue("kawasan_pic", []);
                                    setSelectedPicKawasan([]);
                                    setFieldValue("id_jabatan", "");  // Tambahan baru
                                    setSelectedJabatan("");  // Tambahan baru
                                  } else {
                                    setShowProvinsi(false);
                                    setShowDaerah(false);
                                    setShowKI(false);
                                    setShowPicProvinsi(false);
                                    setShowPicKawasan(false);
                                    setShowJabatan(false);  // Tambahan baru
                                  }
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    selectedRoleName === role.role_name ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                {role.role_name}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <ErrorMessage name="role_id" component="div" className="text-red-500" />

                  {/* Kondisi untuk menampilkan pilihan Provinsi */}
                  {showProvinsi && (
                    <>
                      <Label className="block text-xs font-medium">Provinsi</Label>
                      <Popover
                        modal={true}
                        open={openProvinsi}
                        onOpenChange={setOpenProvinsi}
                        className="col-span-3 w-full"
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openProvinsi}
                            className="justify-between col-span-3 w-full text-xs"
                          >
                            {selectedProvinsi?.nama || "Pilih Nama Provinsi"}
                            <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0" container={containerRef.current}>
                          <Command>
                            <CommandInput placeholder="Cari nama sektor..." />
                            <CommandList>
                              <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                              <CommandGroup>
                                {provinsiOptions?.length > 0 &&
                                  provinsiOptions.map((provinsi) => (
                                    <CommandItem
                                      key={provinsi.id_adm_provinsi}
                                      value={provinsi.nama}
                                      onSelect={() => {
                                        setFieldValue("id_adm_provinsi", provinsi.id_adm_provinsi);
                                        setSelectedProvinsi(provinsi);
                                        setOpenProvinsi(false);
                                      }}
                                    >
                                      <Check
                                        className={cn(
                                          "mr-2 h-4 w-4",
                                          selectedProvinsi?.nama === provinsi.nama ? "opacity-100" : "opacity-0"
                                        )}
                                      />
                                      {provinsi.nama}
                                    </CommandItem>
                                  ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <ErrorMessage name="id_adm_provinsi" component="div" className="text-red-500" />
                    </>
                  )}

                  {/* Kondisi untuk menampilkan pilihan Daerah */}
                  {showDaerah && (
                    <>
                      <Label className="block text-xs font-medium">Pilih Daerah</Label>
                      <Popover
                        modal={true}
                        open={openDaerah}
                        onOpenChange={setOpenDaerah}
                        className="col-span-3 w-full"
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openDaerah}
                            className="justify-between col-span-3 w-full text-xs"
                          >
                            {selectedDaerah?.nama || "Pilih Daerah"}
                            <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0" container={containerRef.current}>
                          <Command>
                            <CommandInput placeholder="Cari daerah..." />
                            <CommandList>
                              <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                              <CommandGroup>
                                {filteredDaerahOptions.map((daerah) => (
                                  <CommandItem
                                    key={daerah.id_adm_kabkot}
                                    value={daerah.nama}
                                    onSelect={() => {
                                      setFieldValue("id_adm_kabkot", daerah.id_adm_kabkot);
                                      setSelectedDaerah(daerah);
                                      setOpenDaerah(false);
                                    }}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        selectedDaerah?.nama === daerah.nama ? "opacity-100" : "opacity-0"
                                      )}
                                    />
                                    {daerah.nama}
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <ErrorMessage name="id_adm_kabkot" component="div" className="text-red-500" />
                    </>
                  )}

                  {showKI && (
                    <>
                      <Label className="block text-xs font-medium">Pilih Kawasan Industri</Label>
                      <Popover
                        modal={true}
                        open={openKI}
                        onOpenChange={setOpenKI}
                        className="col-span-3 w-full"
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openKI}
                            className="justify-between col-span-3 w-full text-xs"
                          >
                            {selectedKI?.nama || "Pilih Kawasan"}
                            <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0" container={containerRef.current}>
                          <Command>
                            <CommandInput placeholder="Cari daerah..." />
                            <CommandList>
                              <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                              <CommandGroup>
                                {kawasanIndustriOptions.map((kawasan) => (
                                  <CommandItem
                                    key={kawasan.id_kawasan_industri}
                                    value={kawasan.nama}
                                    onSelect={() => {
                                      setFieldValue("id_kawasan_industri", kawasan.id_kawasan_industri);
                                      setSelectedKI(kawasan);
                                      setOpenKI(false);
                                    }}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        selectedDaerah?.nama === kawasan.nama ? "opacity-100" : "opacity-0"
                                      )}
                                    />
                                    {kawasan.nama}
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <ErrorMessage name="id_kawasan_industri" component="div" className="text-red-500" />
                    </>
                  )}

                  {/* Kondisi untuk menampilkan pilihan Jabatan untuk PIC (role 14) */}
                  {showJabatan && (
                    <>
                      <Label className="block text-xs font-medium">Jabatan <span className="text-[10px] text-red-600">*</span></Label>
                      <Popover
                        open={openJabatan}
                        onOpenChange={setOpenJabatan}
                        className="col-span-3 w-full"
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openJabatan}
                            className="justify-between col-span-3 w-full text-xs"
                          >
                            {selectedJabatan ? selectedJabatan.nama : "Pilih Jabatan"}
                            <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0" container={containerRef.current}>
                          <Command>
                            <CommandInput placeholder="Cari jabatan..." />
                            <CommandList>
                              <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                              <CommandGroup>
                                {userInternalJabatanOptions?.length > 0 &&
                                  userInternalJabatanOptions.map((jabatan) => (
                                    <CommandItem
                                      key={jabatan.id_jabatan}
                                      value={jabatan.nama}
                                      onSelect={() => {
                                        setSelectedJabatan(jabatan);
                                        setFieldValue("id_jabatan", jabatan.id_jabatan);
                                        setOpenJabatan(false);
                                      }}
                                    >
                                      <Check
                                        className={cn(
                                          "mr-2 h-4 w-4",
                                          selectedJabatan?.id_jabatan === jabatan.id_jabatan
                                            ? "opacity-100"
                                            : "opacity-0"
                                        )}
                                      />
                                      {jabatan.nama}
                                    </CommandItem>
                                  ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <ErrorMessage name="id_jabatan" component="div" className="text-red-500" />
                    </>
                  )}
                  {/* Kondisi untuk menampilkan pilihan Multi Provinsi untuk PIC (role 14) */}
                  {showPicProvinsi && (
                    <>
                      <Label className="block text-xs font-medium">Provinsi PIC <span className="text-[10px] text-red-600">*</span></Label>
                      <Popover
                        modal={true}
                        open={openPicProvinsi}
                        onOpenChange={setOpenPicProvinsi}
                        className="col-span-3 w-full"
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openPicProvinsi}
                            className="justify-between col-span-3 w-full text-xs"
                          >
                            {selectedPicProvinsi.length > 0 
                              ? `${selectedPicProvinsi.length} provinsi dipilih` 
                              : "Pilih Provinsi PIC"}
                            <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0" container={containerRef.current}>
                          <Command>
                            <CommandInput placeholder="Cari provinsi..." />
                            <CommandList>
                              <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                              <CommandGroup>
                                {provinsiOptions?.length > 0 &&
                                  provinsiOptions.map((provinsi) => {
                                    const isSelected = selectedPicProvinsi.some(
                                      p => p.id_adm_provinsi === provinsi.id_adm_provinsi
                                    );
                                    const isDisabled = userInternalProvinsiOptions.some(
                                      p => p.id_adm_provinsi === provinsi.id_adm_provinsi
                                    );
                                    // Close popover when selecting
                                    const handleSelect = () => {
                                      if (isDisabled) return;
                                      let updatedSelection;
                                      if (isSelected) {
                                        updatedSelection = selectedPicProvinsi.filter(
                                          p => p.id_adm_provinsi !== provinsi.id_adm_provinsi
                                        );
                                      } else {
                                        updatedSelection = [...selectedPicProvinsi, provinsi];
                                      }
                                      setSelectedPicProvinsi(updatedSelection);
                                      const provinsiIds = updatedSelection.map(p => p.id_adm_provinsi);
                                      setFieldValue("pic_prov", provinsiIds);
                                      // Keep popover open for multiple selections
                                    };
                                    return (
                                      <CommandItem
                                        key={provinsi.id_adm_provinsi}
                                        value={provinsi.nama}
                                        onSelect={handleSelect}
                                      >
                                        <div className={cn(
                                          "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                                          isSelected
                                            ? "bg-primary text-primary-foreground"
                                            : "opacity-50"
                                        )}>
                                          {isSelected && <Check className="h-3 w-3" />}
                                        </div>
                                        <span className={isDisabled ? "text-gray-400" : ""}>
                                          {provinsi.nama}
                                          {isDisabled && " "}
                                        </span>
                                      </CommandItem>
                                    );
                                  })}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <ErrorMessage name="pic_prov" component="div" className="text-red-500" />
                    </>
                  )}

                  {/* Kondisi untuk menampilkan pilihan Multi Kawasan Industri untuk PIC (role 14) */}
                  {showPicKawasan && (
                    <>
                      <Label className="block text-xs font-medium">Kawasan Industri PIC <span className="text-[10px] text-red-600">*</span></Label>
                      <Popover
                        modal={true}
                        open={openPicKawasan}
                        onOpenChange={setOpenPicKawasan}
                        className="col-span-3 w-full"
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openPicKawasan}
                            className="justify-between col-span-3 w-full text-xs"
                          >
                            {selectedPicKawasan.length > 0 
                              ? `${selectedPicKawasan.length} kawasan industri dipilih` 
                              : "Pilih Kawasan Industri PIC"}
                            <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0" container={containerRef.current}>
                          <Command>
                            <CommandInput placeholder="Cari kawasan industri..." />
                            <CommandList>
                              <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                              <CommandGroup>
                                {kawasanIndustriOptions?.length > 0 &&
                                  kawasanIndustriOptions.map((kawasan) => {
                                    const isSelected = selectedPicKawasan.some(
                                      k => k.id_kawasan_industri === kawasan.id_kawasan_industri
                                    );
                                    const isDisabled = userInternalKawasanOptions.some(
                                      k => k.id_kawasan_industri === kawasan.id_kawasan_industri
                                    );
                                    // Close popover when selecting
                                    const handleSelect = () => {
                                      if (isDisabled) return;
                                      let updatedSelection;
                                      if (isSelected) {
                                        updatedSelection = selectedPicKawasan.filter(
                                          k => k.id_kawasan_industri !== kawasan.id_kawasan_industri
                                        );
                                      } else {
                                        updatedSelection = [...selectedPicKawasan, kawasan];
                                      }
                                      setSelectedPicKawasan(updatedSelection);
                                      const kawasanIds = updatedSelection.map(k => k.id_kawasan_industri);
                                      setFieldValue("kawasan_pic", kawasanIds);
                                      // Keep popover open for multiple selections
                                    };
                                    return (
                                      <CommandItem
                                        key={kawasan.id_kawasan_industri}
                                        value={kawasan.nama}
                                        onSelect={handleSelect}
                                      >
                                        <div className={cn(
                                          "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                                          isSelected
                                            ? "bg-primary text-primary-foreground"
                                            : "opacity-50"
                                        )}>
                                          {isSelected && <Check className="h-3 w-3" />}
                                        </div>
                                        <span className={isDisabled ? "text-gray-400" : ""}>
                                          {kawasan.nama}
                                          {isDisabled && " "}
                                        </span>
                                      </CommandItem>
                                    );
                                  })}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <ErrorMessage name="kawasan_pic" component="div" className="text-red-500" />
                    </>
                  )}

                  <Label className="block text-xs font-medium">Email <span className="text-[10px] text-red-600">*</span></Label>
                  <Input
                    type="email"
                    placeholder="Enter Email"
                    name="email"
                    value={values.email}
                    onChange={(e) => setFieldValue("email", e.target.value)}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage name="email" component="div" className="text-red-500" />

                  <Label className="block text-xs font-medium">Nama Lengkap <span className="text-[10px] text-red-600">*</span></Label>
                  <div className="grid grid-cols-3 gap-4 col-span-3 w-full">
                    <Input
                      type="text"
                      placeholder="First Name"
                      name="first_name"
                      value={values.first_name}
                      onChange={(e) => setFieldValue("first_name", e.target.value)}
                      className="w-full text-xs"
                    />
                    <Input
                      type="text"
                      placeholder="Middle Name"
                      name="middle_name"
                      value={values.middle_name}
                      onChange={(e) => setFieldValue("middle_name", e.target.value)}
                      className="w-full text-xs"
                    />
                    <Input
                      type="text"
                      placeholder="Last Name"
                      name="last_name"
                      value={values.last_name}
                      onChange={(e) => setFieldValue("last_name", e.target.value)}
                      className="w-full text-xs"
                    />
                  </div>

                  <Label className="block text-xs font-medium">Nomor HP</Label>
                  <Input
                    type="number"
                    placeholder="Enter Mobile Number"
                    name="mobile_number"
                    value={values.mobile_number}
                    onChange={(e) => setFieldValue("mobile_number", e.target.value)}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage name="mobile_number" component="div" className="text-red-500" />

                  <Label className="block text-xs font-medium">Nomor Telpon</Label>
                  <Input
                    type="number"
                    placeholder="Enter Phone Number"
                    name="phone_number"
                    value={values.phone_number}
                    onChange={(e) => setFieldValue("phone_number", e.target.value)}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage name="phone_number" component="div" className="text-red-500" />
                </div>
                <DialogFooter>
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={isSubmitting}
                    className="bg-blue-500 hover:bg-blue-700 text-white text-xs py-2 px-4 mt-6"
                    onClick={() => {
                      console.log("Submit button clicked");
                    }}
                  >
                    Simpan
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </Form>

      )}
    </Formik>
  );
};

export default CreateForm;
