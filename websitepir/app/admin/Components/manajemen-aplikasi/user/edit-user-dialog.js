"use client";

import <PERSON><PERSON><PERSON><PERSON> from "../../SweetAlert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { getAllRolesService } from "@/services/RoleServices";
import { editUser, getUserById, updatePassword } from "@/services/UserServices";
import useRoleStore from "@/store/RoleStore";
import useReferensiStore2 from "@/store/cms/ReferensiStore2";
import { Formik, ErrorMessage, Form } from "formik";
import { Edit } from "lucide-react";
import { ChevronsUpDown, Check } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import * as Yup from "yup";

const editUserSchema = Yup.object().shape({
  full_name: Yup.string().required("Full name is required."),
  login_name: Yup.string().required("Login name is required."),
  email: Yup.string()
    .email("Invalid email format")
    .required("Email is required."),
  role_id: Yup.string().required("Role is required"),
});

const EditUserDialog = ({ userId, onStatusChange }) => {
  const [dataEdit, setDataEdit] = useState(null);
  const [selectedRoleName, setSelectedRoleName] = useState("");
  const [roleOptions, setRoleOptions] = useState([]);
  const [openRole, setOpenRole] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const [selectedProvinsi, setSelectedProvinsi] = useState("");
  const [selectedDaerah, setSelectedDaerah] = useState("");
  const [selectedKI, setSelectedKI] = useState("");

  // State untuk role 14
  const [selectedJabatan, setSelectedJabatan] = useState("");
  const [openJabatan, setOpenJabatan] = useState(false);
  const [showJabatan, setShowJabatan] = useState(false);

  const [selectedPicProvinsi, setSelectedPicProvinsi] = useState([]);
  const [openPicProvinsi, setOpenPicProvinsi] = useState(false);
  const [showPicProvinsi, setShowPicProvinsi] = useState(false);

  const [selectedPicKawasan, setSelectedPicKawasan] = useState([]);
  const [openPicKawasan, setOpenPicKawasan] = useState(false);
  const [showPicKawasan, setShowPicKawasan] = useState(false);

  const [openProvinsi, setOpenProvinsi] = useState(false);
  const [openDaerah, setOpenDaerah] = useState(false);
  const [openKI, setOpenKI] = useState(false);
  const [filteredDaerahOptions, setFilteredDaerahOptions] = useState([]);
  const [showProvinsi, setShowProvinsi] = useState(false);
  const [showDaerah, setShowDaerah] = useState(false);
  const [showKI, setShowKI] = useState(false);

  const {
    provinsiOptions,
    kabkotOptions: daerahOptions,
    kawasanIndustriOptions,
    userInternalJabatanOptions,
    userInternalProvinsiOptions,
    userInternalKawasanOptions,
    fetchKawasanIndustri,
    fetchProvinsi,
    fetchKabkot,
    fetchUserInternalJabatan,
    fetchUserInternalProvinsi,
    fetchUserInternalKawasan,
  } = useReferensiStore2();

  useEffect(() => {
    if (provinsiOptions.length === 0) {
      fetchProvinsi();
    }
    if (daerahOptions.length === 0) {
      fetchKabkot();
    }
    if (kawasanIndustriOptions.length === 0) {
      fetchKawasanIndustri();
    }
    if (userInternalJabatanOptions?.length === 0) {
      fetchUserInternalJabatan();
    }
    if (userInternalProvinsiOptions?.length === 0) {
      fetchUserInternalProvinsi();
    }
    if (userInternalKawasanOptions?.length === 0) {
      fetchUserInternalKawasan();
    }
  }, [
    fetchKabkot,
    daerahOptions,
    fetchProvinsi,
    provinsiOptions,
    kawasanIndustriOptions,
    fetchKawasanIndustri,
    userInternalJabatanOptions,
    fetchUserInternalJabatan,
  ]);

  const fetchUserData = async () => {
    try {
      const response = await getUserById(userId);
      if (response.data) {
        setDataEdit(response.data);
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  };

  const handleDialogOpenChange = (open) => {
    setIsDialogOpen(open);
    if (open) {
      // Re-fetch user data first
      fetchUserData().then(() => {
        // Clear store data
        useReferensiStore2.setState({
          userInternalProvinsiOptions: [],
          userInternalKawasanOptions: []
        });
        
        // After getting user data, fetch dropdowns excluding current user's assignments
        fetchUserInternalProvinsi(userId);
        fetchUserInternalKawasan(userId);
      });
      
      // Reset all selections
      setSelectedPicProvinsi([]);
      setSelectedPicKawasan([]);
      setSelectedJabatan("");
      setSelectedRoleName("");
      setSelectedProvinsi("");
      setSelectedDaerah("");
      setSelectedKI("");
      
      // Reset all popover states
      setOpenPicProvinsi(false);
      setOpenPicKawasan(false);
      setOpenJabatan(false);
      setOpenRole(false);
      setOpenProvinsi(false);
      setOpenDaerah(false);
      setOpenKI(false);
    }
  };

  useEffect(() => {
    async function fetchRoleData() {
      const response = await getAllRolesService();
      setRoleOptions(response.data.data);
    }
    fetchRoleData();
  }, []); // Panggil hanya sekali ketika komponen di-mount

  useEffect(() => {
    if (dataEdit) {
      // Handle role selection
      const roleData = roleOptions.find(
        (roleData) => roleData.id === dataEdit.role_id
      );
      if (roleData) {
        setSelectedRoleName(roleData.role_name);

        // Set visibility based on role_id
        if (dataEdit.role_id === 3) {
          setShowProvinsi(true);
          setShowDaerah(false);
          setShowKI(false);
          setShowJabatan(false);
          setShowPicProvinsi(false);
          setShowPicKawasan(false);
        } else if (dataEdit.role_id === 4) {
          setShowProvinsi(true);
          setShowDaerah(true);
          setShowKI(false);
          setShowJabatan(false);
          setShowPicProvinsi(false);
          setShowPicKawasan(false);
        } else if (dataEdit.role_id === 5) {
          setShowProvinsi(false);
          setShowDaerah(false);
          setShowKI(true);
          setShowJabatan(false);
          setShowPicProvinsi(false);
          setShowPicKawasan(false);
        } else if (dataEdit.role_id === 14) {
          setShowProvinsi(false);
          setShowDaerah(false);
          setShowKI(false);
          setShowJabatan(true);
          setShowPicProvinsi(true);
          setShowPicKawasan(true);
          
          // Set jabatan if exists
          if (dataEdit.id_jabatan) {
            const selectedJabatanOption = userInternalJabatanOptions?.find(
              (option) => option.id_jabatan === dataEdit.id_jabatan
            );
            setSelectedJabatan(selectedJabatanOption || "");
            
            // setFieldValue akan dipanggil di useEffect lain yang memiliki akses ke fungsi tersebut
          }
          
          // Set pic_prov from tb_adm_user
          if (dataEdit.tb_adm_user && dataEdit.tb_adm_user.length > 0) {
            // Ekstrak id_adm_provinsi dari tb_adm_user
            const picProvIds = dataEdit.tb_adm_user.map(item => item.id_adm_provinsi);
            
            // pic_prov akan diatur di initialValues Formik
            
            // Set selected options untuk UI
            const selectedPicProvOptions = picProvIds.map(id => {
              return provinsiOptions.find(prov => prov.id_adm_provinsi === id);
            }).filter(Boolean);
            setSelectedPicProvinsi(selectedPicProvOptions);
          }
          
          // Set kawasan_pic from tb_kawasan_user
          if (dataEdit.tb_kawasan_user && dataEdit.tb_kawasan_user.length > 0) {
            // Ekstrak id_kawasan_industri dari tb_kawasan_user
            const kawasanPicIds = dataEdit.tb_kawasan_user.map(item => item.id_kawasan_industri);
            
            // kawasan_pic akan diatur di initialValues Formik
            
            // Set selected options untuk UI
            const selectedPicKawasanOptions = kawasanPicIds.map(id => {
              return kawasanIndustriOptions.find(kawasan => kawasan.id_kawasan_industri === id);
            }).filter(Boolean);
            setSelectedPicKawasan(selectedPicKawasanOptions);
          }
        } else {
          setShowProvinsi(false);
          setShowDaerah(false);
          setShowKI(false);
          setShowJabatan(false);
          setShowPicProvinsi(false);
          setShowPicKawasan(false);
        }
      }

      // Set provinsi if exists
      if (dataEdit.tb_adm_user?.[0]) {
        const selectedProvOption = provinsiOptions.find(
          (option) =>
            option.id_adm_provinsi === dataEdit.tb_adm_user[0].id_adm_provinsi
        );
        setSelectedProvinsi(selectedProvOption || null);
      }

      // Set daerah if exists
      if (dataEdit.tb_adm_user?.[0]) {
        const selectedDaerahOption = daerahOptions.find(
          (option) =>
            option.id_adm_kabkot === dataEdit.tb_adm_user[0].id_adm_kabkot
        );
        setSelectedDaerah(selectedDaerahOption || null);
      }

      // Set kawasan industri if exists
      if (dataEdit.tb_kawasan_user?.[0]) {
        const selectedKIOption = kawasanIndustriOptions.find(
          (option) =>
            option.id_kawasan_industri ===
            dataEdit.tb_kawasan_user[0].id_kawasan_industri
        );
        setSelectedKI(selectedKIOption || null);
      }
    }
  }, [
    dataEdit,
    roleOptions,
    provinsiOptions,
    daerahOptions,
    kawasanIndustriOptions,
  ]);
  useEffect(() => {
    if (selectedProvinsi && showDaerah) {
      const filteredDaerah = daerahOptions.filter(
        (daerah) => daerah.id_adm_provinsi === selectedProvinsi.id_adm_provinsi
      );
      setFilteredDaerahOptions(filteredDaerah);
    } else {
      setFilteredDaerahOptions([]);
    }
  }, [selectedProvinsi, daerahOptions, showDaerah]);

  useEffect(() => {
    if (selectedProvinsi) {
      const filteredDaerah = daerahOptions.filter(
        (daerah) => daerah.id_adm_provinsi === selectedProvinsi.id_adm_provinsi
      );
      setFilteredDaerahOptions(filteredDaerah);
    } else {
      setFilteredDaerahOptions([]); // Clear filtered options if no province is selected
    }
  }, [selectedProvinsi, daerahOptions]); // Run this effect when selectedProvinsi or daerahOptions change

  
  // --- Ensure selectedPicProvinsi and selectedPicKawasan are always array of objects (not array of id) ---
  useEffect(() => {
    // console.log("selectedPicProvinsi", selectedPicProvinsi);

    // Only run when dataEdit and options are loaded
    if (dataEdit && provinsiOptions.length) {
      setSelectedPicProvinsi(
        provinsiOptions.filter(opt => (dataEdit.pic_prov || []).includes(opt.id_adm_provinsi))
      );
      // console.log("selectedPicProvinsi", dataEdit.pic_prov);
    // console.log("selectedPicProvinsiNew", selectedPicProvinsi);
  }
    
    if (dataEdit && kawasanIndustriOptions.length) {
      setSelectedPicKawasan(
        kawasanIndustriOptions.filter(opt => (dataEdit.kawasan_pic || []).includes(opt.id_kawasan_industri))
      );
    }
  }, [dataEdit, provinsiOptions, kawasanIndustriOptions]);

  const containerRef = useRef(null);

  return (
    <Formik
      enableReinitialize={true}
      initialValues={{
        login_name: dataEdit?.login_name || "",
        password: "",
        email: dataEdit?.email || "",
        role_id: dataEdit?.role_id || "",
        first_name: dataEdit?.first_name || "",
        middle_name: dataEdit?.middle_name || "",
        last_name: dataEdit?.last_name || "",
        mobile_number: dataEdit?.mobile_number || "",
        phone_number: dataEdit?.phone_number || "",
        id_jabatan: dataEdit?.id_jabatan || "",
        user_kawasan: {
          id_kawasan_industri:
            dataEdit?.tb_kawasan_user[0]?.id_kawasan_industri || null,
          id_kawasan_user:
            dataEdit?.tb_kawasan_user[0]?.id_kawasan_user || null,
        },
        user_kabkot: {
          id_adm_kabkot: dataEdit?.tb_adm_user[0]?.id_adm_kabkot || null,
          id_adm_user: dataEdit?.tb_adm_user[0]?.id_adm_user || null,
        },
        user_provinsi: {
          id_adm_user: dataEdit?.tb_adm_user[0]?.id_adm_user || null,
          id_adm_provinsi: dataEdit?.tb_adm_user[0]?.id_adm_provinsi || null,
        },
        // Field untuk role 14
        id_jabatan: dataEdit?.id_jabatan || "",
        pic_prov: dataEdit?.pic_prov ? dataEdit.pic_prov : [],
        kawasan_pic: dataEdit?.kawasan_pic ? dataEdit.kawasan_pic : [],
      }}
      // validationSchema={editUserSchema}
      onSubmit={async (values, { setSubmitting }) => {
        try {
          // Format data for API
          const userData = { ...values };
          
          // Handle role 14 specific data
          if (parseInt(values.role_id) === 14) {
            userData.id_jabatan = values.id_jabatan;
            
            // Format pic_prov data
            if (values.pic_prov && values.pic_prov.length > 0) {
              userData.tb_adm_user = values.pic_prov.map(provinsiId => ({
                id_adm_provinsi: provinsiId
              }));
            }
            
            // Format kawasan_pic data
            if (values.kawasan_pic && values.kawasan_pic.length > 0) {
              userData.tb_kawasan_user = values.kawasan_pic.map(kawasanId => ({
                id_kawasan_industri: kawasanId
              }));
            }
          }

          const response = await editUser(userData, userId);
          if (response.success) {
            SweetAlert.success("Success", "Pengguna berhasil di edit!", () => {
              onStatusChange();
            });
            setIsDialogOpen(false);
            
            // Immediately refresh dropdown data after successful save
            useReferensiStore2.setState({
              userInternalProvinsiOptions: [],
              userInternalKawasanOptions: []
            });
            await Promise.all([
              fetchUserInternalProvinsi(),
              fetchUserInternalKawasan()
            ]);
          }
        } catch (error) {
          const defaultMessage = "Gagal mengedit pengguna.";
          if (Array.isArray(error.errors)) {
            const errorMessages = error.errors.map(err =>
              `Field: ${err.field || "-"} - ${err.message || "-"}`
            ).join("\n");
            SweetAlert.error("Error", errorMessages);
          } else if (typeof error.message === "string") {
            SweetAlert.error("Error", error.message || defaultMessage);
          } else if (error.detail) {
            SweetAlert.error("Error", error.detail);
          } else {
            SweetAlert.error("Error", defaultMessage);
          }
          
        } finally {
          setSubmitting(false);
        }
      }}
    >
      {({ setFieldValue, values, isSubmitting }) => (
        <Form>
          <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
            <DialogTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                className="text-xs text-white"
                style={{ backgroundColor: "#E2B93B", color: "white" }}
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </DialogTrigger>

            <DialogContent className="sm:max-w-[900px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Edit Pengguna</DialogTitle>
              </DialogHeader>
              <form>
                <div
                  className="grid grid-cols-4 items-start gap-4"
                  ref={containerRef}
                >
                  <Label className="block text-xs font-medium">Username</Label>
                  <Input
                    type="text"
                    placeholder="Masukkan nama"
                    name="login_name"
                    value={values.login_name}
                    onChange={(e) =>
                      setFieldValue("login_name", e.target.value)
                    }
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage
                    name="login_name"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">Password</Label>
                  <Input
                    type="password"
                    placeholder="Masukkan password baru (jika ingin ubah)"
                    name="password"
                    value={values.password}
                    onChange={(e) => setFieldValue("password", e.target.value)}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage
                    name="password"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Daftar Sebagai{" "}
                    <span className="text-[10px] text-red-600">*</span>
                  </Label>
                  <Popover
                    open={openRole}
                    onOpenChange={setOpenRole}
                    className="col-span-3 w-full"
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={openRole}
                        className="justify-between col-span-3 w-full text-xs"
                      >
                        {selectedRoleName || "Pilih Nama Role"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent
                      className="w-full p-0"
                      container={containerRef.current}
                    >
                      <Command>
                        <CommandInput placeholder="Cari Role..." />
                        <CommandList>
                          <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                          <CommandGroup>
                            {roleOptions.map((role) => (
                              <CommandItem
                                key={role.id}
                                value={role.role_name}
                                onSelect={() => {
                                  setFieldValue("role_id", role.id);
                                  setSelectedRoleName(role.role_name);
                                  setOpenRole(false);

                                  // Atur kondisi untuk menampilkan provinsi dan daerah
                                  if (role.id === 3) {
                                    setShowProvinsi(true);
                                    setShowDaerah(false);
                                    setShowKI(false);
                                    setShowJabatan(false);
                                    setShowPicProvinsi(false);
                                    setShowPicKawasan(false);
                                  } else if (role.id === 4) {
                                    setShowProvinsi(true);
                                    setShowDaerah(true);
                                    setShowKI(false);
                                    setShowJabatan(false);
                                    setShowPicProvinsi(false);
                                    setShowPicKawasan(false);
                                  } else if (role.id === 5) {
                                    setShowProvinsi(false);
                                    setShowDaerah(false);
                                    setShowKI(true);
                                    setShowJabatan(false);
                                    setShowPicProvinsi(false);
                                    setShowPicKawasan(false);
                                  } else if (role.id === 14) {
                                    setShowProvinsi(false);
                                    setShowDaerah(false);
                                    setShowKI(false);
                                    setShowJabatan(true);
                                    setShowPicProvinsi(true);
                                    setShowPicKawasan(true);
                                    
                                    // Reset nilai dropdown
                                    setSelectedJabatan("");
                                    setSelectedPicProvinsi([]);
                                    setSelectedPicKawasan([]);
                                    setFieldValue("id_jabatan", "");
                                    setFieldValue("pic_prov", []);
                                    setFieldValue("kawasan_pic", []);
                                  } else {
                                    setShowProvinsi(false);
                                    setShowDaerah(false);
                                    setShowKI(false);
                                    setShowJabatan(false);
                                    setShowPicProvinsi(false);
                                    setShowPicKawasan(false);
                                  }
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    selectedRoleName === role.role_name
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {role.role_name}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <ErrorMessage
                    name="role_id"
                    component="div"
                    className="text-red-500"
                  />

                  {/* Kondisi untuk menampilkan pilihan Provinsi */}
                  {showProvinsi && (
                    <>
                      <Label className="block text-xs font-medium">
                        Provinsi
                      </Label>
                      <Popover
                        modal={true}
                        open={openProvinsi}
                        onOpenChange={setOpenProvinsi}
                        className="col-span-3 w-full"
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openProvinsi}
                            className="justify-between col-span-3 w-full text-xs"
                          >
                            {selectedProvinsi?.nama || "Pilih Nama Provinsi"}
                            <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-full p-0"
                          container={containerRef.current}
                        >
                          <Command>
                            <CommandInput placeholder="Cari nama sektor..." />
                            <CommandList>
                              <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                              <CommandGroup>
                                {provinsiOptions?.length > 0 &&
                                  provinsiOptions.map((provinsi) => (
                                    <CommandItem
                                      key={provinsi.id_adm_provinsi}
                                      value={provinsi.nama}
                                      onSelect={() => {
                                        setFieldValue(
                                          "user_provinsi.id_adm_provinsi",
                                          provinsi.id_adm_provinsi
                                        );
                                        setSelectedProvinsi(provinsi);
                                        setOpenProvinsi(false);
                                      }}
                                    >
                                      <Check
                                        className={cn(
                                          "mr-2 h-4 w-4",
                                          selectedProvinsi?.nama ===
                                            provinsi.nama
                                            ? "opacity-100"
                                            : "opacity-0"
                                        )}
                                      />
                                      {provinsi.nama}
                                    </CommandItem>
                                  ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <ErrorMessage
                        name="id_adm_provinsi"
                        component="div"
                        className="text-red-500"
                      />
                    </>
                  )}

                  {/* Kondisi untuk menampilkan pilihan Daerah */}
                  {showDaerah && (
                    <>
                      <Label className="block text-xs font-medium">
                        Pilih Daerah
                      </Label>
                      <Popover
                        modal={true}
                        open={openDaerah}
                        onOpenChange={setOpenDaerah}
                        className="col-span-3 w-full"
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openDaerah}
                            className="justify-between col-span-3 w-full text-xs"
                          >
                            {selectedDaerah?.nama || "Pilih Daerah"}
                            <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-full p-0"
                          container={containerRef.current}
                        >
                          <Command>
                            <CommandInput placeholder="Cari daerah..." />
                            <CommandList>
                              <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                              <CommandGroup>
                                {filteredDaerahOptions.map((daerah) => (
                                  <CommandItem
                                    key={daerah.id_adm_kabkot}
                                    value={daerah.nama}
                                    onSelect={() => {
                                      setFieldValue(
                                        "user_kabkot.id_adm_kabkot",
                                        daerah.id_adm_kabkot
                                      );
                                      setSelectedDaerah(daerah);
                                      setOpenDaerah(false);
                                    }}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        selectedDaerah?.nama === daerah.nama
                                          ? "opacity-100"
                                          : "opacity-0"
                                      )}
                                    />
                                    {daerah.nama}
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <ErrorMessage
                        name="id_adm_kabkot"
                        component="div"
                        className="text-red-500"
                      />
                    </>
                  )}

                  {showKI && (
                    <>
                      <Label className="block text-xs font-medium">
                        Pilih Kawasan Industri
                      </Label>
                      <Popover
                        modal={true}
                        open={openKI}
                        onOpenChange={setOpenKI}
                        className="col-span-3 w-full"
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openKI}
                            className="justify-between col-span-3 w-full text-xs"
                          >
                            {selectedKI?.nama || "Pilih Kawasan"}
                            <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-full p-0"
                          container={containerRef.current}
                        >
                          <Command>
                            <CommandInput placeholder="Cari daerah..." />
                            <CommandList>
                              <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                              <CommandGroup>
                                {kawasanIndustriOptions.map((kawasan) => (
                                  <CommandItem
                                    key={kawasan.id_kawasan_industri}
                                    value={kawasan.nama}
                                    onSelect={() => {
                                      setFieldValue(
                                        "user_kawasan.id_kawasan_industri",
                                        kawasan.id_kawasan_industri
                                      );
                                      setSelectedKI(kawasan);
                                      setOpenKI(false);
                                    }}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        selectedKI?.nama === kawasan.nama
                                          ? "opacity-100"
                                          : "opacity-0"
                                      )}
                                    />
                                    {kawasan.nama}
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <ErrorMessage
                        name="id_kawasan_industri"
                        component="div"
                        className="text-red-500"
                      />
                    </>
                  )}

                  {/* Dropdown Jabatan untuk Role 14 */}
                  {showJabatan && (
                    <>
                      <Label className="block text-xs font-medium">
                        Jabatan
                      </Label>
                      <Popover
                        modal={true}
                        open={openJabatan}
                        onOpenChange={setOpenJabatan}
                        className="col-span-3 w-full"
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openJabatan}
                            className="justify-between col-span-3 w-full text-xs"
                          >
                            {selectedJabatan?.nama || "Pilih Jabatan"}
                            <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-full p-0"
                          container={containerRef.current}
                        >
                          <Command>
                            <CommandInput placeholder="Cari jabatan..." />
                            <CommandList>
                              <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                              <CommandGroup>
                                {userInternalJabatanOptions?.map((jabatan) => (
                                  <CommandItem
                                    key={jabatan.id_jabatan}
                                    value={jabatan.nama}
                                    onSelect={() => {
                                      setFieldValue(
                                        "id_jabatan",
                                        jabatan.id_jabatan
                                      );
                                      setSelectedJabatan(jabatan);
                                      setOpenJabatan(false);
                                    }}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        selectedJabatan?.id_jabatan === jabatan.id_jabatan
                                          ? "opacity-100"
                                          : "opacity-0"
                                      )}
                                    />
                                    {jabatan.nama}
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <ErrorMessage
                        name="id_jabatan"
                        component="div"
                        className="text-red-500"
                      />
                    </>
                  )}

                  {/* Multi-select PIC Provinsi untuk Role 14 */}
                  {showPicProvinsi && (
                    <>
                      <Label className="block text-xs font-medium">
                        PIC Provinsi
                      </Label>
                      <Popover
                        modal={true}
                        open={openPicProvinsi}
                        onOpenChange={setOpenPicProvinsi}
                        className="col-span-3 w-full"
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openPicProvinsi}
                            className="justify-between col-span-3 w-full text-xs"
                          >
                            {selectedPicProvinsi.length > 0
                              ? `${selectedPicProvinsi.length} provinsi dipilih`
                              : "Pilih Provinsi..."}
                            <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-full p-0"
                          container={containerRef.current}
                        >
                          <Command>
                            <CommandInput placeholder="Cari provinsi..." />
                            <CommandList>
                              <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                              <CommandGroup>
                                {provinsiOptions.map((provinsi) => {
                                  const isSelected = selectedPicProvinsi.some(
                                    (selected) => selected.id_adm_provinsi === provinsi.id_adm_provinsi
                                  );
                                  
                                  // Check if province is assigned to another user
                                  // Exclude current user's provinces from the check
                                  const isDisabled = !isSelected && userInternalProvinsiOptions.some(
                                    (p) => p.id_adm_provinsi === provinsi.id_adm_provinsi && 
                                          !dataEdit.pic_prov?.includes(p.id_adm_provinsi)
                                  );

                                  const handleSelect = () => {
                                    if (isDisabled) return;
                                    let newSelected = [];
                                    if (isSelected) {
                                      newSelected = selectedPicProvinsi.filter(
                                        (item) => item.id_adm_provinsi !== provinsi.id_adm_provinsi
                                      );
                                    } else {
                                      newSelected = [...selectedPicProvinsi, provinsi];
                                    }
                                    setSelectedPicProvinsi(newSelected);
                                    setFieldValue(
                                      "pic_prov",
                                      newSelected.map((item) => item.id_adm_provinsi)
                                    );
                                  };
                                  return (
                                    <CommandItem
                                      key={provinsi.id_adm_provinsi}
                                      value={provinsi.nama}
                                      onSelect={handleSelect}
                                    >
                                      <div className="flex items-center">
                                        <input
                                          type="checkbox"
                                          checked={isSelected}
                                          className="mr-2"
                                          readOnly
                                        />
                                        <span className={isDisabled ? "text-gray-400" : ""}>
                                          {provinsi.nama}
                                          {isDisabled && " "}
                                        </span>
                                      </div>
                                    </CommandItem>
                                  );
                                })}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <ErrorMessage
                        name="pic_prov"
                        component="div"
                        className="text-red-500"
                      />
                    </>
                  )}

                  {/* Multi-select PIC Kawasan untuk Role 14 */}
                  {showPicKawasan && (
                    <>
                      <Label className="block text-xs font-medium">
                        PIC Kawasan Industri
                      </Label>
                      <Popover
                        modal={true}
                        open={openPicKawasan}
                        onOpenChange={setOpenPicKawasan}
                        className="col-span-3 w-full"
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openPicKawasan}
                            className="justify-between col-span-3 w-full text-xs"
                          >
                            {selectedPicKawasan.length > 0
                              ? `${selectedPicKawasan.length} kawasan dipilih`
                              : "Pilih Kawasan Industri..."}
                            <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-full p-0"
                          container={containerRef.current}
                        >
                          <Command>
                            <CommandInput placeholder="Cari kawasan industri..." />
                            <CommandList>
                              <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                              <CommandGroup>
                                {kawasanIndustriOptions.map((kawasan) => {

                                  const isSelected = selectedPicKawasan.some(
                                    (selected) => selected.id_kawasan_industri === kawasan.id_kawasan_industri
                                  );
                                  // Check if kawasan is assigned to another user, but allow if it's currently selected
                                  const isDisabled = !isSelected && userInternalKawasanOptions.some(
                                    (k) => k.id_kawasan_industri === kawasan.id_kawasan_industri
                                  );
                                  // Close popover when selecting
                                  const handleSelect = () => {
                                    if (isDisabled) return;
                                    let newSelected = [];
                                    if (isSelected) {
                                      newSelected = selectedPicKawasan.filter(
                                        (item) => item.id_kawasan_industri !== kawasan.id_kawasan_industri
                                      );
                                    } else {
                                      newSelected = [...selectedPicKawasan, kawasan];
                                    }
                                    setSelectedPicKawasan(newSelected);
                                    setFieldValue(
                                      "kawasan_pic",
                                      newSelected.map((item) => item.id_kawasan_industri)
                                    );
                                    // Keep popover open for multiple selections
                                  };
                                  return (
                                    <CommandItem
                                      key={kawasan.id_kawasan_industri}
                                      value={kawasan.nama}
                                      onSelect={handleSelect}
                                    >
                                      <div className="flex items-center">
                                        <input
                                          type="checkbox"
                                          checked={isSelected}
                                          className="mr-2"
                                          readOnly
                                        />
                                        <span className={isDisabled ? "text-gray-400" : ""}>
                                          {kawasan.nama}
                                          {isDisabled && " "}
                                        </span>
                                      </div>
                                    </CommandItem>
                                  );
                                })}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <ErrorMessage
                        name="kawasan_pic"
                        component="div"
                        className="text-red-500"
                      />
                    </>
                  )}

                  <Label className="block text-xs font-medium">Email</Label>
                  <Input
                    type="text"
                    placeholder="Masukkan Email"
                    name="email"
                    value={values.email}
                    onChange={(e) => setFieldValue("email", e.target.value)}
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage
                    name="email"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Nama Lengkap{" "}
                    <span className="text-[10px] text-red-600">*</span>
                  </Label>
                  <div className="grid grid-cols-3 gap-4 col-span-3 w-full">
                    <Input
                      type="text"
                      placeholder="First Name"
                      name="first_name"
                      value={values.first_name}
                      onChange={(e) =>
                        setFieldValue("first_name", e.target.value)
                      }
                      className="w-full text-xs"
                    />
                    <Input
                      type="text"
                      placeholder="Middle Name"
                      name="middle_name"
                      value={values.middle_name}
                      onChange={(e) =>
                        setFieldValue("middle_name", e.target.value)
                      }
                      className="w-full text-xs"
                    />
                    <Input
                      type="text"
                      placeholder="Last Name"
                      name="last_name"
                      value={values.last_name}
                      onChange={(e) =>
                        setFieldValue("last_name", e.target.value)
                      }
                      className="w-full text-xs"
                    />
                  </div>

                  <Label className="block text-xs font-medium">Nomor HP</Label>
                  <Input
                    type="number"
                    placeholder="Enter Mobile Number"
                    name="mobile_number"
                    value={values.mobile_number}
                    onChange={(e) =>
                      setFieldValue("mobile_number", e.target.value)
                    }
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage
                    name="mobile_number"
                    component="div"
                    className="text-red-500"
                  />

                  <Label className="block text-xs font-medium">
                    Nomor Telpon
                  </Label>
                  <Input
                    type="number"
                    placeholder="Enter Phone Number"
                    name="phone_number"
                    value={values.phone_number}
                    onChange={(e) =>
                      setFieldValue("phone_number", e.target.value)
                    }
                    className="justify-between col-span-3 w-full text-xs"
                  />
                  <ErrorMessage
                    name="phone_number"
                    component="div"
                    className="text-red-500"
                  />

                  {/* <Label className="block text-xs font-medium">Role</Label>
                  <Popover
                    open={openRole}
                    onOpenChange={setOpenRole}
                    className="col-span-3 w-full"
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={openRole}
                        className="justify-between col-span-3 w-full text-xs"
                      >
                        {selectedRoleName || "Pilih Nama Role"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0" container={containerRef.current}>
                      <Command>
                        <CommandInput placeholder="Cari nama role..." />
                        <CommandList>
                          <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                          <CommandGroup>
                            {roleOptions?.map((role) => (
                              <CommandItem
                                key={role.id}
                                value={role.role_name}
                                onSelect={() => {
                                  setFieldValue("role_id", role.id);
                                  setSelectedRoleName(role.role_name);
                                  setOpenRole(false);
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    selectedRoleName === role.role_name
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {role.role_name}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <ErrorMessage
                    name="role_id"
                    component="div"
                    className="text-red-500"
                  /> */}
                </div>
                <DialogFooter className="text-right">
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={isSubmitting}
                    className="bg-blue-500 hover:bg-blue-700 text-white text-xs py-2 px-4 mt-6"
                  >
                    Simpan
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </Form>
      )}
    </Formik>
  );
};

export default EditUserDialog;
