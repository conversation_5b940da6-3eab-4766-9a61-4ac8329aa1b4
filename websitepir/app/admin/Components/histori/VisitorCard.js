// import { useState } from 'react';
// import { Button } from '@/components/ui/button';

// const VisitorCard = ({ nama, jumlah, onToggle, isVisible }) => {
//   return (
//     <div className="bg-white shadow-md rounded-lg p-4 w-full md:w-1/3">
//       <h3 className="text-lg font-semibold">{nama}</h3>
//       <p className="text-2xl font-bold">{jumlah.toLocaleString()}</p>
//       <Button className="mt-2 w-full" onClick={onToggle}>
//         {isVisible ? 'Hide' : 'Show'}
//       </Button>
//     </div>
//   );
// };

// export default VisitorCard;


import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Home, CreditCard } from 'lucide-react'; // Sesuaikan ikon
import useVisitorIdStore from '@/store/UseVisitorIdStore';

const VisitorCard = ({ id, nama, jumlah, color, icon: Icon }) => {
  const { visibleId, setVisibleId } = useVisitorIdStore();

  // Mapping ID ke key yang sesuai
  const mapIdToKey = (id) => {
    const idMap = {
      1: "get_home",
      2: "get_kawasan",
      3: "get_kawasan_detail",
      4: "get_daerah",
      5: "get_daerah_detail",
      6: "get_peluang",
      7: "get_ppi",
      8: "get_pid",
      9: "get_hilirisasi",
      10: "get_kajian",
    };
    return idMap[id] || `unknown_${id}`;
  };

  return (
    <div className={`relative p-4 rounded-lg text-white shadow-md w-60 bg-blue-500`}>
      <div className="flex items-center justify-between">
        <p className="text-3xl font-bold">{jumlah.toLocaleString()}</p>
      </div>
      <p className="text-lg">{nama}</p>
      <Button
        className="mt-6 flex items-center justify-center w-full text-sm bg-white text-gray-800 px-3 py-2 rounded shadow hover:bg-gray-100"
        onClick={() => setVisibleId(mapIdToKey(id))}
      >
        ➤ Show / Hide
      </Button>
    </div>
  );
};

export default VisitorCard;
