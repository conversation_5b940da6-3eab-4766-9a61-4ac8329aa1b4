'use client';
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import DataTable from '../../../DataTable';
import SweetAlert from '../../../SweetAlert';
import { Edit, Trash } from 'lucide-react';
import CreateProfilForm from './create-profil-form';
import DataTableDaerah from '../../../DataTableDaerah';
import useAuthStore from '@/store/AuthStore';


// Define columns for Kantor Provinsi table
const columns = [
  { accessorKey: 'nama_provinsi', header: 'Provinsi' },
  { accessorKey: 'nama', header: 'Kabupaten' },
  { accessorKey: 'nama_ibukota', header: 'Ibu Kota' },
  { accessorKey: 'luas_wilayah', header: '<PERSON><PERSON>ilayah' },
  { accessorKey: 'alamat', header: 'Alamat' },
];

// Actions component for edit and delete buttons

// const fetchDataService = async (page, pageSize, filter) => {
//   try {
//     const response = await fetch(
//       `${process.env.NEXT_PUBLIC_BASE_URL}/admin/kabupaten_profil/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(filter || "")}`
//     );
//     const result = await response.json();
//     console.log(result);


//     if (response.ok && result.success) {
//       return {
//         success: true,
//         data: result.data,
//         totalPage: result.pagination.total_pages,
//         totalRecords: result.pagination.total_count,
//       };
//     }

//     throw new Error(result.message || "Failed to fetch data");
//   } catch (error) {
//     return {
//       success: false,
//       data: [],
//       totalPage: 0,
//       totalRecords: 0,
//       message: error.message,
//     };
//   }
// };
// const fetchDataService = async (page, pageSize, filter) => {
//   try {
//     const { id_adm_provinsi, id_adm_kabkot } = useAuthStore.getState(); // Ambil nilai dari Zustand store
//     console.log("prov", id_adm_provinsi);
//     console.log("kab", id_adm_kabkot);
//     let url = `${process.env.NEXT_PUBLIC_BASE_URL}/admin/kabupaten_profil/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(filter || "")}`;

//     // Tambahkan query parameter berdasarkan data user yang login
//     if (id_adm_provinsi) {
//       url += `&id_adm_provinsi=${id_adm_provinsi}`;
//     } else if (id_adm_kabkot) {
//       url += `&id_adm_kabkot=${id_adm_kabkot}`;
//     }

//     const response = await fetch(url);
//     const result = await response.json();
//     console.log("result api", result);

//     if (response.ok && result.success) {
//       return {
//         success: true,
//         data: result.data || [],
//         totalPage: result.pagination.total_pages,
//         totalRecords: result.pagination.total_count,
//       };
//     }

//     throw new Error(result.message || "Failed to fetch data");
//   } catch (error) {
//     return {
//       success: false,
//       data: [],
//       totalPage: 0,
//       totalRecords: 0,
//       message: error.message,
//     };
//   }
// };
const fetchDataService = async (page, pageSize, filter, order = null, by = null) => {
  try {
    const { id_adm_provinsi, id_adm_kabkot,roleId,id } = useAuthStore.getState(); // Ambil nilai dari Zustand store
    console.log("prov", id_adm_provinsi);
    console.log("kab", id_adm_kabkot);

    let url = `${process.env.NEXT_PUBLIC_BASE_URL}/admin/kabupaten_profil/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(filter || "")}`;

    if (roleId == 14) {
      url += `&pic=${id}`;
    }
    // Tambahkan query parameter berdasarkan data user yang login
    if (id_adm_provinsi) {
      url += `&id_adm_provinsi=${id_adm_provinsi}`;
    } else if (id_adm_kabkot) {
      url += `&id_adm_kabkot=${id_adm_kabkot}`;
    }

    // Tambahkan parameter sorting jika ada
    if (order && by) {
      url += `&order=${order}&by=${by}`;
    }

    const response = await fetch(url);
    const result = await response.json();
    console.log("result api", result);

    
    if (response.ok && result.success) {
      const formattedData = result.data.map(item => ({
        ...item,
        nama_provinsi: item.tb_adm_provinsi.nama, // Flattening the nested structure
    }));
      return {
        success: true,
        data: formattedData,
        totalPage: result.pagination.total_pages,
        totalRecords: result.pagination.total_count,
      };
    }

    throw new Error(result.message || "Failed to fetch data");
  } catch (error) {
    return {
      success: false,
      data: [],
      totalPage: 0,
      totalRecords: 0,
      message: error.message,
    };
  }
};




const ProfilKabkotTable = () => {
  const [localRefreshTrigger, setLocalRefreshTrigger] = useState(0);

  const ActionsComponent = ({ row, onEdit, onDelete }) => (
    <div className="flex space-x-2">
      <CreateProfilForm id={row.id_adm_kabkot} onStatusChange={() => setLocalRefreshTrigger((prev) => prev + 1)} /> </div>
  );
  return (
    <>
      <DataTableDaerah
        columns={columns}
        fetchDataService={fetchDataService}
        pageSize={10}
        actionsComponent={({ row }) => (
          <ActionsComponent row={row} />
        )}
        filterPlaceholder="Cari..."
      />
    </>
  );
};

export default ProfilKabkotTable;
