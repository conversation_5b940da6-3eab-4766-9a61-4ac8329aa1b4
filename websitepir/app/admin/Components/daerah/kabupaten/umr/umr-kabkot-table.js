"use client";

import DataTable from "../../../DataTable";
import DataTableDaerah from "../../../DataTableDaerah";
import <PERSON>Alert from "../../../SweetAlert";
import StatusButton from "../../provinsi/status-button";
import { Button } from "@/components/ui/button";
import {
  deleteUMRKabkotService,
  statusUMRKabkotService,
} from "@/services/KabkotCMSService";
import { deleteEksporImporProvinsiService } from "@/services/ProvinsiCMSService";
import useAuthStore from "@/store/AuthStore";
import { Edit, Trash } from "lucide-react";
import React, { useEffect, useState } from "react";

// Define columns for Kantor Provinsi table

// Actions component for edit and delete buttons
const ActionsComponent = ({ row, onEdit, onDelete }) => (
  <div className="flex space-x-2">
    {/* <EditSektorNasionalRef id={row.id_sektor_nasional_ref}/> */}
    <Button
      variant="secondary"
      size="sm"
      style={{ backgroundColor: "#E2B93B", color: "white" }}
      className="text-xs"
      onClick={() => {
        console.log("Edit clicked, row data:", row); // Logging row data
        onEdit(row);
      }}
    >
      {" "}
      <Edit className="mr-2 h-4 w-4" /> Edit
    </Button>
    <Button
      variant="destructive"
      size="sm"
      className="bg-red-500 hover:bg-red-600 text-xs"
      onClick={() => onDelete(row)}
    >
      {" "}
      <Trash className="mr-2 h-4 w-4" /> Delete
    </Button>
  </div>
);

// const fetchDataService = async (page, pageSize, filter) => {
//   try {
//     const response = await fetch(
//       `${process.env.NEXT_PUBLIC_BASE_URL}/admin/kabupaten_umr/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(filter || "")}`
//     );
//     const result = await response.json();
//     console.log(result);

//     if (response.ok && result.success) {
//       return {
//         success: true,
//         data: result.data,
//         totalPage: result.pagination.total_pages,
//         totalRecords: result.pagination.total_count,
//       };
//     }

//     throw new Error(result.message || "Failed to fetch data");
//   } catch (error) {
//     return {
//       success: false,
//       data: [],
//       totalPage: 0,
//       totalRecords: 0,
//       message: error.message,
//     };
//   }
// };
// const fetchDataService = async (page, pageSize, filter) => {
//   try {
//     const { id_adm_provinsi, id_adm_kabkot } = useAuthStore.getState(); // Ambil nilai dari Zustand store
//     console.log("prov", id_adm_provinsi);
//     console.log("kab", id_adm_kabkot);
//     let url = `${process.env.NEXT_PUBLIC_BASE_URL}/admin/kabupaten_umr/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(filter || "")}`;

//     // Tambahkan query parameter berdasarkan data user yang login
//     if (id_adm_provinsi) {
//       url += `&id_adm_provinsi=${id_adm_provinsi}`;
//     } else if (id_adm_kabkot) {
//       url += `&id_adm_kabkot=${id_adm_kabkot}`;
//     }

//     const response = await fetch(url);
//     const result = await response.json();
//     console.log("result api", result);

//     if (response.ok && result.success) {
//       return {
//         success: true,
//         data: result.data || [],
//         totalPage: result.pagination.total_pages,
//         totalRecords: result.pagination.total_count,
//       };
//     }

//     throw new Error(result.message || "Failed to fetch data");
//   } catch (error) {
//     return {
//       success: false,
//       data: [],
//       totalPage: 0,
//       totalRecords: 0,
//       message: error.message,
//     };
//   }
// };

const UMRKabkotTable = ({ status, onEditUMR, refreshTrigger }) => {
  const [localRefreshTrigger, setLocalRefreshTrigger] = useState(0);
  const [combinedTrigger, setCombinedTrigger] = useState(refreshTrigger);

  useEffect(() => {
    setCombinedTrigger(refreshTrigger + localRefreshTrigger);
  }, [refreshTrigger, localRefreshTrigger]);

  const fetchDataService = async (
    page,
    pageSize,
    filter,
    order = null,
    by = null
  ) => {
    try {
      const { id_adm_provinsi, id_adm_kabkot,roleId,id } = useAuthStore.getState(); // Ambil nilai dari Zustand store
      console.log("prov", id_adm_provinsi);
      console.log("kab", id_adm_kabkot);

      let url = `${
        process.env.NEXT_PUBLIC_BASE_URL
      }/admin/kabupaten_umr/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(
        filter || ""
      )}`;

      if (roleId == 14) {
        url += `&pic=${id}`;
      }
      // Tambahkan query parameter berdasarkan data user yang login
      if (id_adm_provinsi) {
        url += `&id_adm_provinsi=${id_adm_provinsi}`;
      } else if (id_adm_kabkot) {
        url += `&id_adm_kabkot=${id_adm_kabkot}`;
      }

      // Tambahkan filter status jika tersedia
      if (status !== undefined && status !== null) {
        url += `&status=${status}`;
      }

      // Tambahkan parameter sorting jika ada
      if (order && by) {
        url += `&order=${order}&by=${by}`;
      }

      const response = await fetch(url);
      const result = await response.json();
      console.log("result api", result);

      if (response.ok && result.success) {
        const formattedData = result.data.map((item) => ({
          ...item,
          provinsi: item.tb_adm_kabkot.tb_adm_provinsi.nama,
          kabupaten: item.tb_adm_kabkot.nama, // Flattening the nested structure
        }));
        return {
          success: true,
          data: formattedData,
          totalPage: result.pagination.total_pages,
          totalRecords: result.pagination.total_count,
        };
      }

      throw new Error(result.message || "Failed to fetch data");
    } catch (error) {
      return {
        success: false,
        data: [],
        totalPage: 0,
        totalRecords: 0,
        message: error.message,
      };
    }
  };

  const handleDelete = async (row) => {
    SweetAlert.confirm(
      "Konfirmasi",
      "Apakah Anda yakin ingin menghapus data ini?",
      async () => {
        try {
          await deleteUMRKabkotService(row.id_umr_kabkot);
          SweetAlert.success("Success", "Berhasil menghapus data", () => {
            setLocalRefreshTrigger((prev) => prev + 1);
          });
        } catch (error) {
          SweetAlert.error("Error", "Gagal menghapus data", () => {
            console.error("Error deleting data:", error);
          });
        }
      }
    );
  };
  const columns = [
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <div className="flex h-full">
          <StatusButton
            status={row.original.status}
            dataEdit={row.original}
            id={row.original.id_umr_kabkot}
            onStatusChange={() => setLocalRefreshTrigger((prev) => prev + 1)} // Trigger refresh
            service={statusUMRKabkotService}
          />
        </div>
      ),
    },
    { accessorKey: "provinsi", header: "Provinsi" },
    { accessorKey: "kabupaten", header: "Kabupaten" },
    { accessorKey: "tahun", header: "Tahun" },
    { accessorKey: "nilai", header: "Nilai" },
    { accessorKey: "keterangan", header: "Keterangan" },
  ];

  return (
    <>
      <DataTableDaerah
        columns={columns}
        fetchDataService={fetchDataService}
        pageSize={10}
        actionsComponent={({ row }) => (
          <ActionsComponent
            row={row}
            onEdit={onEditUMR}
            onDelete={handleDelete}
          />
        )}
        filterPlaceholder="Cari..."
        refreshTrigger={combinedTrigger}
      />
    </>
  );
};

export default UMRKabkotTable;
