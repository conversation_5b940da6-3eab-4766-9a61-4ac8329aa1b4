'use client';
import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Edit, Trash } from 'lucide-react';
import DataTable from '../../../DataTable';
import { deleteArtikelService, statusArtikelService } from '@/services/InformasiService';
import SweetAlert from '../../../SweetAlert';
import DataTableDaerah from '../../../DataTableDaerah';
import useAuthStore from '@/store/AuthStore';
import StatusButton from '../../provinsi/status-button';



const ActionsComponent = ({ row, onEdit,onDelete }) => (
  <div className="flex space-x-2">
   <Button variant="secondary" size="sm" style={{ backgroundColor: '#E2B93B', color: 'white' }} className="text-xs"  onClick={() => onEdit(row)}> <Edit className="mr-2 h-4 w-4" /> Edit</Button>
   <Button variant="destructive" size="sm" className="bg-red-500 hover:bg-red-600 text-xs" onClick={() => onDelete(row)}> <Trash className="mr-2 h-4 w-4" /> Delete</Button>
    {/* <Button variant="destructive" size="sm">Delete Permanen</Button> */}
  </div>
);

// const fetchDataService = async (page, pageSize, filter) => {
//   try {
//     const response = await fetch(
//       `${process.env.NEXT_PUBLIC_BASE_URL}/admin/artikel_input_data/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(
//         filter || ""
//       )}`
//     );
//     const result = await response.json();
//     console.log(result);

//     if (response.ok && result.success) {
//       return {
//         success: true,
//         data: result.data,
//         totalPage: result.pagination.total_pages,
//         totalRecords: result.pagination.total_count,
//       };
//     }

//     throw new Error(result.message || "Failed to fetch data");
//   } catch (error) {
//     return {
//       success: false,
//       data: [],
//       totalPage: 0,
//       totalRecords: 0,
//       message: error.message,
//     };
//   }
// };

// const fetchDataService = async (page, pageSize, filter) => {
//   try {
//     const { id_adm_provinsi, id_adm_kabkot } = useAuthStore.getState(); // Ambil nilai dari Zustand store
// console.log("prov", id_adm_provinsi);
// console.log("kab", id_adm_kabkot);
//     let url = `${process.env.NEXT_PUBLIC_BASE_URL}/admin/artikel_input_data/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(filter || "")}&kategori=potensi`;

//     // Tambahkan query parameter berdasarkan data user yang login
//     if (id_adm_kabkot) {
//       url += `&id_adm_kabkot=${id_adm_kabkot}`;
//     } else if (id_adm_kabkot === null) {
//       url += `&kabkot=true`;
//     }

//     const response = await fetch(url);
//     const result = await response.json();
//     console.log("result api", result);

//     if (response.ok && result.success) {
//       return {
//         success: true,
//         data: result.data || [],
//         totalPage: result.pagination.total_pages,
//         totalRecords: result.pagination.total_count,
//       };
//     }

//     throw new Error(result.message || "Failed to fetch data");
//   } catch (error) {
//     return {
//       success: false,
//       data: [],
//       totalPage: 0,
//       totalRecords: 0,
//       message: error.message,
//     };
//   }
// };
// Helper function untuk menunggu state auth tersedia
const waitForAuthState = async (maxAttempts = 10, interval = 300) => {
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const state = useAuthStore.getState();
      
      if (state && (state.id_adm_provinsi !== undefined || state.id_adm_kabkot !== undefined)) {
          return { state, success: true };
      }
      
      await new Promise(resolve => setTimeout(resolve, interval));
  }
  
  return { state: null, success: false };
};

// export const fetchDataService = async (page, pageSize, filter) => {
//   try {
//       // Tunggu sampai auth state tersedia
//       const { state, success } = await waitForAuthState();
      
//       if (!success) {
//           throw new Error('Tidak dapat memuat data auth setelah beberapa percobaan');
//       }

//       const { id_adm_provinsi, id_adm_kabkot } = state;
      
//       // Buat URL dengan URLSearchParams untuk handling yang lebih aman
//       const baseURL = `${process.env.NEXT_PUBLIC_BASE_URL}/admin/artikel_input_data/lists`;
//       const queryParams = new URLSearchParams({
//           page: page.toString(),
//           per_page: pageSize.toString(),
//           q: filter || "",
//           kategori: "potensi"
//       });

//       // Tambahkan query parameter berdasarkan data user yang login
//       if (id_adm_kabkot) {
//           queryParams.set('id_adm_kabkot', id_adm_kabkot);
//       } else if (id_adm_kabkot === null) {
//           queryParams.set('kabkot', 'true');
//       }

//       const url = `${baseURL}?${queryParams.toString()}`;

      
//       const response = await fetch(url);
//       const result = await response.json();
      

//       if (!response.ok) {
//           throw new Error(result.message || `HTTP error! status: ${response.status}`);
//       }

//       if (!result.success) {
//           throw new Error(result.message || "API returned unsuccessful response");
//       }

//       return {
//           success: true,
//           data: result.data || [],
//           totalPage: result.pagination.total_pages,
//           totalRecords: result.pagination.total_count,
//       };

//   } catch (error) {
//       console.error('Error in fetchDataService:', error);
//       return {
//           success: false,
//           data: [],
//           totalPage: 0,
//           totalRecords: 0,
//           message: error.message || "Terjadi kesalahan saat mengambil data",
//       };
//   }
// };
export const fetchDataService = async (page, pageSize, filter, order = null, by = null) => {
  try {
    // Tunggu sampai auth state tersedia
    const { state, success } = await waitForAuthState();
    
    if (!success) {
      throw new Error('Tidak dapat memuat data auth setelah beberapa percobaan');
    }

    const { id_adm_provinsi, id_adm_kabkot,roleId,id } = state;
    
    // Buat URL dengan URLSearchParams untuk handling yang lebih aman
    const baseURL = `${process.env.NEXT_PUBLIC_BASE_URL}/admin/artikel_input_data/lists`;
    const queryParams = new URLSearchParams({
      page: page.toString(),
      per_page: pageSize.toString(),
      q: filter || "",
      kategori: "potensi"
    });

    // Tambahkan query parameter berdasarkan data user yang login
    if (id_adm_kabkot) {
      queryParams.set('id_adm_kabkot', id_adm_kabkot);
    } else if (id_adm_kabkot === null) {
      queryParams.set('kabkot', 'true');
    }

    // Tambahkan parameter sorting jika ada
    if (order && by) {
      queryParams.set('order', order);
      queryParams.set('by', by);
    }

    if (roleId == 14) {
      queryParams.set('pic', id);
    }
    const url = `${baseURL}?${queryParams.toString()}`;

    const response = await fetch(url);
    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || `HTTP error! status: ${response.status}`);
    }

    if (!result.success) {
      throw new Error(result.message || "API returned unsuccessful response");
    }

    const formattedData = result.data.map(item => ({
      ...item,
      provinsi: item.tb_adm_provinsi.nama,
      kabupaten: item.tb_adm_kabkot.nama// Flattening the nested structure
     }));

    return {
      success: true,
      data: formattedData,
      totalPage: result.pagination.total_pages,
      totalRecords: result.pagination.total_count,
    };

  } catch (error) {
    console.error('Error in fetchDataService:', error);
    return {
      success: false,
      data: [],
      totalPage: 0,
      totalRecords: 0,
      message: error.message || "Terjadi kesalahan saat mengambil data",
    };
  }
};

const ArtikelTable = ({onEditArtikel,refreshTrigger}) => {
  const [localRefreshTrigger, setLocalRefreshTrigger] = useState(0);
  const [combinedTrigger, setCombinedTrigger] = useState(refreshTrigger);

  useEffect(() => {
    setCombinedTrigger(refreshTrigger + localRefreshTrigger);
  }, [refreshTrigger, localRefreshTrigger]);


  const columns = [
    { accessorKey: 'status', header: 'Status',
      cell: ({ row }) => (
        <div className="flex h-full items-center justify-center">
          <StatusButton
            status={row.original.status}
            dataEdit={row.original}
            id={row.original.id}
            onStatusChange={() => setLocalRefreshTrigger((prev) => prev + 1)} // Trigger refresh
            service={statusArtikelService}
          />
        </div>
      )
     },
    { accessorKey: 'provinsi', header: 'Provinsi' },
    { accessorKey: 'kabupaten', header: 'Kabupaten/Kota' },
    { accessorKey: 'judul', header: 'Judul' },
    { accessorKey: 'deskripsi_singkat', header: 'Deskripsi Singkat' }
  ];

  const handleDelete = async (row) => {
    SweetAlert.confirm(
      "Konfirmasi",
      "Apakah Anda yakin ingin menghapus data ini?",
      async () => {
        try {
          await deleteArtikelService(row.id); 
          SweetAlert.success("Success", "Berhasil menghapus data", () => {
            setLocalRefreshTrigger((prev) => prev + 1);
          });
        } catch (error) {
          SweetAlert.error("Error", "Gagal menghapus data", () => {
            console.error("Error deleting data:", error);
          });
        }
      }
    );
  };
  return (
    <DataTableDaerah
      columns={columns}
      fetchDataService={fetchDataService}
      pageSize={10}
        actionsComponent={({ row }) => (
          <ActionsComponent
            row={row}
            onEdit={onEditArtikel}
            onDelete={handleDelete}
          />
        )}
        filterPlaceholder="Cari..."
        refreshTrigger={combinedTrigger}
    />
  );
};

export default ArtikelTable;
