import React, { useEffect, useState } from 'react';
import usePeluangInvestasiDaerahStore from "@/store/PeluangInvestasiDaerah";
import { useForm } from 'react-hook-form';
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
  } from "@/components/ui/command";
import { Label } from "@/components/ui/label";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
  } from "@/components/ui/popover";
import { Check, ChevronsUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import ApiForm from '@/utils/ApiForm';
import { cn } from '@/lib/utils';
import { Input } from "@/components/ui/input";
import Api from '@/utils/Api';
import SweetAlert from "../../SweetAlert";
import useAuthStore from "@/store/AuthStore";

const MasterForm = () => {

    const { formValues, step, setStep, updatePeluangInvestasi } = usePeluangInvestasiDaerahStore();
    const {
        register,
        handleSubmit,
        watch,
        setValue,
        getValues,
        formState: { errors },
      } = useForm({
        defaultValues: {
            provinsi: null,
            kabkot: null,
            sektor_daerah: null,
            sub_sektor_daerah: null,
            pdrb_year: null,
            title: null
        }
      });
    
    const onSubmit = async (data) => {
      try {
        // const res = await Api.post(
        //   "admin/peluang_investasi_daerah/create",
        //   {
        //     id_adm_provinsi: data?.provinsi?.id,
        //     id_adm_kabkot: data?.kabkot?.id,
        //     id_sektor_daerah: data?.sektor_daerah?.id,
        //     id_sub_sektor_daerah: data?.sub_sektor_daerah?.id,
        //     tahun: data?.pdrb_year,
        //     judul: data?.title,
        //     status_peluang: 1,
        //     status: 99,
        //   }
        // );

        // const resData =  res?.data;
        // resData.id_sektor_daerah = data?.sektor_daerah?.id;
        // const dataMaster = {
        //     id_adm_provinsi: data?.provinsi?.id,
        //     id_adm_kabkot: data?.kabkot?.id,
        //     id_sektor_daerah: data?.sektor_daerah?.id,
        //     id_sub_sektor_daerah: data?.sub_sektor_daerah?.id,
        //     tahun: data?.pdrb_year,
        //     judul: data?.title,
        // }
        
        updatePeluangInvestasi(data);
        setStep(2);
        // updatePeluangInvestasi(res?.data);
      } catch (error) {
        SweetAlert.error("Error", "Terjadi kesalahan", () => {
          console.error("Error:", error);
        }, {
          timer: 2000
        });
      }
    }

    const [openSelectProvinsi, setOpenSelectProvinsi] = useState(false);
    const selectedProvinsi = watch("provinsi");
    
    const [openSelectKabkot, setOpenSelectKabkot] = useState(false);
    const selectedKabkot = watch("kabkot");

    const [openSelectSektorDaerah, setOpenSelectSektorDaerah] = useState(false);
    const selectedSektorDaerah = watch("sektor_daerah");

    const [openSelectSubSektorDaerah, setOpenSelectSubSektorDaerah] = useState(false);
    const selectedSubSektorDaerah = watch("sub_sektor_daerah");
    
    const [openPDRBYear, setOpenPDRBYear] = useState(false);
    const selectedPDRBYear = watch("pdrb_year");

    const titleValue = watch("title");

    const [allOption, setAllOption] = useState({
      sektorDaerahOptions: [],
      subSektorDaerahOptions: [],
      PDRBYearOptions: [2025, 2024, 2023, 2022, 2021, 2020],
      provinsiOptions: [],
      kabkotOptions: [],
    });

    const { user, roleId } = useAuthStore.getState();

    const { isLoading: resProvinsiIsLoading,
            error: resProvinsiError,
            data: resProvinsi,
            isFetched: resProvinsiIsFetched,
            status: resProvinsiStatus,
            refetch: resProvinsiRefetch } = useQuery({
            queryKey: ['provinsi'],
            queryFn: async () => {
                const response = await ApiForm.get('/global/getReferensi/tb_adm_provinsi');
                let provinsiData = response?.data;
                // Filter based on role
                if (roleId === 14 && user?.pic_prov_arr?.length > 0) {
                  const picProvIds = user.pic_prov_arr;
                  provinsiData = provinsiData.filter(item => picProvIds.includes(item.id_adm_provinsi));
                }
                if (roleId === 3 && user?.id_adm_provinsi) {
                  provinsiData = provinsiData.filter(item => item.id_adm_provinsi === user.id_adm_provinsi);
                }
                if (roleId === 4 && user?.id_adm_kabkot) {
                  provinsiData = provinsiData.filter(item => item.id_adm_provinsi === user.id_adm_kabkot.slice(0, 2));
                }

                return provinsiData;
            },
            keepPreviousData: true,
            enabled: false,
    });

    const { isLoading: resKabkotIsLoading,
            error: resKabkotError,
            data: resKabkot,
            status: resKabkotStatus,
            refetch: resKabkotRefetch } = useQuery({
            queryKey: ['kabkot', 'provinsi_'+selectedProvinsi?.id],
            queryFn: async () => {
                const response = await ApiForm.get('/global/getReferensi/tb_adm_kabkot');
                return response?.data;
            },
            keepPreviousData: true,
            enabled: false,
    });

    const { isLoading: resSektorDaerahIsLoading,
            error: resSektorDaerahError,
            data: resSektorDaerah,
            status: resSektorDaerahStatus,
            refetch: resSektorDaerahRefetch } = useQuery({
            queryKey: ['sektor_daerah', 'provinsi_'+selectedProvinsi?.id, 'kabkot_'+selectedKabkot?.id],
            queryFn: async () => {
                const response = await Api.get('/admin/sud_sektor_daerah/lists?is_simple=true', {
                  params: {
                    id_adm_provinsi: selectedProvinsi?.id,
                    id_adm_kabkot: selectedKabkot?.id
                  }
                });
                return response?.data;
            },
            keepPreviousData: true,
            enabled: false,
    });
    
    const { isLoading: resSubSektorDaerahIsLoading,
            error: resSubSektorDaerahError,
            data: resSubSektorDaerah,
            status: resSubSektorDaerahStatus,
            refetch: resSubSektorDaerahRefetch } = useQuery({
            queryKey: ['sub_sektor_daerah', 'sektor_daerah_'+selectedSektorDaerah?.id],
            queryFn: async () => {
              const response = await Api.get('/admin/sud_sub_sektor_daerah/lists?is_simple=true', {
                params: {
                  id_sektor_daerah: selectedSektorDaerah?.id
                }
              });
              return response?.data;
            },
            keepPreviousData: true,
            enabled: false,
    });
    
    const resProvinsiOptionFormat = (data) => {
        return data?.map((item) => {
            return {
                id: item.id_adm_provinsi,
                title: item.nama
            }
        });
    }

    const resKabkotOptionFormat = (data, provinsiID) => {
      return data?.filter((item) => item?.id_adm_provinsi === provinsiID)?.map((item) => {
          return {
              id: item.id_adm_kabkot,
              title: item.nama
          }
      });
    }

    const resSektorDaerahOptionFormat = (data) => {
      return data?.map((item) => {
          return {
              id: item.id_sektor_daerah,
              title: item.sektor_nasional_ref?.nama
          }
      });
    }

    const resSubSektorDaerahOptionFormat = (data) => {
      return data?.map((item) => {
          return {
              id: item.id_sub_sektor_daerah,
              title: item.tb_sub_sektor_nasional?.sub_sektor_ref?.nama
          }
      });
    }

    useEffect(() => {
      if (openSelectProvinsi === true) {
        resProvinsiRefetch().then(({data}) => {
            const resProvinsiOptionFormatted = resProvinsiOptionFormat(data);
            const newOption = {
                ...allOption,
                provinsiOptions: resProvinsiOptionFormatted
            }
            setAllOption(newOption);
        });
      }
    }, [openSelectProvinsi]);

    useEffect(() => {
        setValue("kabkot", null);
        if (selectedProvinsi) {
            resKabkotRefetch().then(({data}) => {
              const resKabkotOptionFormatted = resKabkotOptionFormat(data, selectedProvinsi?.id);
              const newOption = {
                  ...allOption,
                  kabkotOptions: resKabkotOptionFormatted
              }
              setAllOption(newOption);
            });
        }
    }, [selectedProvinsi]);

    useEffect(() => {
      setValue("sektor_daerah", null);
      if (selectedKabkot?.id && selectedKabkot?.id) {
        resSektorDaerahRefetch().then(({data}) => {
          const _resSektorDaerahOptionFormat = resSektorDaerahOptionFormat(data);
          const newOption = {
              ...allOption,
              sektorDaerahOptions: _resSektorDaerahOptionFormat
          }
          setAllOption(newOption);
        });
      }
    }, [selectedProvinsi, selectedKabkot]);

    useEffect(() => {
      setValue("sub_sektor_daerah", null);
      if (selectedSektorDaerah) {
        resSubSektorDaerahRefetch().then(({data}) => {
          const _resSubSektorDaerahOptionFormat = resSubSektorDaerahOptionFormat(data);
          console.log(_resSubSektorDaerahOptionFormat);
          const newOption = {
            ...allOption,
            subSektorDaerahOptions: _resSubSektorDaerahOptionFormat
          }
          setAllOption(newOption);
        })
      }
    }, [selectedSektorDaerah]);

    const isCanContinueNextStep = () => {
      return Boolean( selectedProvinsi && 
                      selectedKabkot && 
                      selectedSektorDaerah &&
                      selectedSubSektorDaerah &&
                      selectedPDRBYear &&
                      titleValue
                    );
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <div
              className="grid grid-cols-4 items-center gap-4"
            >
              {/* Provinsi Field */}
              <Label className="block text-xs font-medium">Provinsi
              <span className="text-[12px] text-red-600">&nbsp;(*)</span>
              </Label>
              <Popover
                open={openSelectProvinsi}
                onOpenChange={setOpenSelectProvinsi}
                className="col-span-3"
              >
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    className="justify-between w-full text-xs col-span-3"
                  >
                    {selectedProvinsi?.title || "Pilih Provinsi"}
                    <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-full p-0"
                >
                  <Command>
                    <CommandInput placeholder="Cari provinsi..." />
                    <CommandList>
                      <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                      <CommandGroup>
                        {allOption?.provinsiOptions?.map((provinsi) => (
                          <CommandItem
                            key={provinsi.id}
                            value={provinsi.id}
                            onSelect={(val) => {
                              setValue("provinsi", provinsi);
                              setOpenSelectProvinsi(false);
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                selectedProvinsi?.id ===
                                  provinsi.id
                                  ? "opacity-100"
                                  : "opacity-0"
                              )}
                            />
                            {provinsi.id} - {provinsi.title}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>

              {selectedProvinsi && (
                <>
                  <Label className="block text-xs font-medium">Kabkot
                  <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                  </Label>
                  <Popover
                    open={openSelectKabkot}
                    onOpenChange={setOpenSelectKabkot}
                    className="col-span-3"
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        className="justify-between w-full text-xs col-span-3"
                      >
                        {selectedKabkot?.title || "Pilih Kabkot"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent
                      className="w-full p-0"
                    >
                      <Command>
                        <CommandInput placeholder="Cari provinsi..." />
                        <CommandList>
                          <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                          <CommandGroup>
                            {allOption?.kabkotOptions?.map((kabkot) => {
                                return (
                              <CommandItem
                                key={kabkot.id}
                                value={kabkot.id}
                                onSelect={() => {
                                  setValue("kabkot", kabkot);
                                  setOpenSelectKabkot(false);
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    selectedKabkot?.id ===
                                      kabkot.id
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {kabkot.title}
                              </CommandItem>
                            )})}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </>
              )}

              {selectedProvinsi && selectedKabkot && (
              <>
                <Label className="block text-xs font-medium">Sektor Daerah
                  <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                  </Label>
                <Popover
                  open={openSelectSektorDaerah}
                  onOpenChange={setOpenSelectSektorDaerah}
                  className="col-span-3"
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      className="justify-between w-full text-xs col-span-3"
                    >
                      {selectedSektorDaerah?.title || "Pilih Sektor Daerah"}
                      <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent
                    className="w-full p-0"
                  >
                    <Command>
                      <CommandInput placeholder="Cari Sektor Daerah..." />
                      <CommandList>
                        <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                        <CommandGroup>
                          {allOption?.sektorDaerahOptions?.map((item) => (
                            <CommandItem
                              key={item.id}
                              value={item.id}
                              onSelect={() => {
                                setValue("sektor_daerah", item);
                                setOpenSelectSektorDaerah(false);
                              }}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  selectedSektorDaerah?.id ===
                                    item.id
                                    ? "opacity-100"
                                    : "opacity-0"
                                )}
                              />
                              {item?.title}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </>
              )}

              {selectedSektorDaerah && (
              <>
                <Label className="block text-xs font-medium">Sub Sektor Daerah
                <span className="text-[12px] text-red-600">&nbsp;(*)</span>
                </Label>
                <Popover
                  open={openSelectSubSektorDaerah}
                  onOpenChange={setOpenSelectSubSektorDaerah}
                  className="col-span-3"
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      className="justify-between w-full text-xs col-span-3"
                    >
                      {selectedSubSektorDaerah?.title || "Pilih Sub Sektor Daerah"}
                      <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent
                    className="w-full p-0"
                  >
                    <Command>
                      <CommandInput placeholder="Cari Sub Sektor Daerah..." />
                      <CommandList>
                        <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                        <CommandGroup>
                          {allOption?.subSektorDaerahOptions?.map((item) => {
                              return (
                            <CommandItem
                              key={item.id}
                              value={item.id}
                              onSelect={() => {
                                setValue("sub_sektor_daerah", item);
                                setOpenSelectSubSektorDaerah(false);
                              }}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  selectedSubSektorDaerah?.id ===
                                    item.id
                                    ? "opacity-100"
                                    : "opacity-0"
                                )}
                              />
                              {item?.title}
                            </CommandItem>
                          )})}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </>
              )}

              <Label className="block text-xs font-medium">Tahun
              <span className="text-[12px] text-red-600">&nbsp;(*)</span>
              </Label>
              <Popover open={openPDRBYear} onOpenChange={setOpenPDRBYear}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="text-xs justify-between col-span-3 w-full"
                  >
                    {selectedPDRBYear || "Pilih Tahun"}
                    <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-full p-0"
                >
                  <Command>
                    <CommandInput placeholder="Cari Tahun..." />
                    <CommandList>
                      <CommandEmpty>Tidak ditemukan.</CommandEmpty>
                      <CommandGroup>
                        {allOption.PDRBYearOptions.map((year) => (
                          <CommandItem
                            key={year}
                            value={year}
                            onSelect={() => {
                              setValue("pdrb_year", year);
                              setOpenPDRBYear(false);
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                selectedPDRBYear === year
                                  ? "opacity-100"
                                  : "opacity-0"
                              )}
                            />
                            {year}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>

              <Label className="block text-xs font-medium">Judul Peluang
              <span className="text-[12px] text-red-600">&nbsp;(*)</span>
              </Label>
              <Input
                type="text"
                placeholder="Judul Peluang"
                name="judul"
                value={getValues("title")}
                onChange={(e) => setValue("title", e.target.value)}
                className="justify-between col-span-3 w-full text-xs"
              />
            </div>
            <div className="flex justify-end">
                  <Button
                    type="submit"
                    disabled={!isCanContinueNextStep()}
                    className="text-xs mt-4"
                  >
                    Lanjut
                  </Button>
            </div>
        </form>
    );
}

export default MasterForm;
