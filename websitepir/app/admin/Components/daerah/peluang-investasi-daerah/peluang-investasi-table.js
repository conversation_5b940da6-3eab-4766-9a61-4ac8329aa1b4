"use client";

import DataTable from "../../DataTable";
import SweetAlert from "../../SweetAlert";
import EditPeluangInvestasiDaerah from "./EditPeluangInvestasiDaerah";
import StatusButtonFrom from "@/components/ui/StatusButtonFrom";
import DynamicTable from "@/components/ui/TableCRUD";
import { Button } from "@/components/ui/button";
import { getDataTablePeluangInvestasiDaerahService } from "@/services/PeluangInvestasiDaerahService";
import useAuthStore from "@/store/AuthStore";
import Api, { APICORS } from "@/utils/Api";
import { formatNumber } from "@/utils/formatNumber";
import { mapStatus } from "@/utils/status";
import { Edit, Eye, Trash } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const API_ENDPOINTS = {
  LIST: "/admin/peluang_investasi_daerah/lists?is_get_table_admin=true",
  DELETE: "/admin/peluang_investasi_daerah/delete",
};

const PeluangInvestasiTable = ({ onEdit, refreshTrigger }) => {
  const [combinedTrigger, setCombinedTrigger] = useState(refreshTrigger);
  const [localRefreshTrigger, setLocalRefreshTrigger] = useState(0);
  const router = useRouter();

  const { user, isLoading: userLoading } = useAuthStore();

  useEffect(() => {
    setCombinedTrigger(refreshTrigger + localRefreshTrigger);
  }, [refreshTrigger, localRefreshTrigger]);

  // Define columns for Peluang Investasi Daerah table
  const peluangInvestasiColumns = [
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ cell, row }) => {
        console.log(row);

        return (
          <div className="flex h-full items-center justify-center">
            <StatusComponent
              status={row.original.status}
              dataEdit={row.original}
              id={row.original.id_peluang_daerah}
              onStatusChange={() => setLocalRefreshTrigger((prev) => prev + 1)}
              service={statusService}
            />
          </div>
        );
      },
    },
    { accessorKey: "judul", header: "Judul Peluang" },
    { accessorKey: "lokasi", header: "Lokasi" },
    { accessorKey: "nama_sektor_peluang", header: "Sektor Peluang" },
    { accessorKey: "nama_provinsi", header: "Provinsi" },
    { accessorKey: "nama_kabkot", header: "Kabupaten Kota" },
    { accessorKey: "prioritas", header: "Prioritas" },
    { accessorKey: "tahun", header: "Tahun" },
    {
      accessorKey: "initial_invesment",
      header: "Initial Investment (IDR)",
      cell: ({ cell }) => formatNumber(cell.getValue())?.toString() ?? "",
    },
    {
      accessorKey: "irr",
      header: "IRR (%)",
      cell: ({ cell }) => formatNumber(cell.getValue())?.toString() ?? "",
    },
    {
      accessorKey: "npv",
      header: "NPV (IDR)",
      cell: ({ cell }) => formatNumber(cell.getValue())?.toString() ?? "",
    },
    {
      accessorKey: "pp",
      header: "Payback Period (Tahun)",
      cell: ({ cell }) => formatNumber(cell.getValue())?.toString() ?? "",
    },
    {
      accessorKey: "fileKelayakan",
      header: "File Kelayakan",
      customButton: true,
    },
    { accessorKey: "keteranganProses", header: "Keterangan Proses" },
  ];

  const deleteService = async (id) => {
    return await Api.delete(`${API_ENDPOINTS.DELETE}/${id}`);
  };

  const statusService = async (body, id) => {
    return await APICORS.patch(
      `/admin/peluang_investasi_daerah/toggle_status/${id}`,
      body
    );
  };

  const DynamicSektorDaerah = ({ data }) => {
    return (
      <EditPeluangInvestasiDaerah
        id={data.id_peluang_daerah}
        onSuccess={() => {
          setLocalRefreshTrigger((prev) => prev + 1);
        }}
      />
    );
  };

  const StatusComponent = ({
    status,
    dataEdit,
    id,
    onStatusChange,
    service,
  }) => {
    return (
      <StatusButtonFrom
        status={status}
        dataEdit={dataEdit}
        id={id}
        onStatusChange={onStatusChange}
        service={service}
      />
    );
  };

  const handlePreview = (row) => {
    router.push(
      `/admin/daerah/peluang-investasi-daerah/detail/${row.id_peluang_daerah}`
    );
  };

  const ActionsComponent = ({ row, onDelete }) => (
    <div className="flex space-x-2">
      <DynamicSektorDaerah data={row} />
      {/* <EditPeluangInvestasi peluangId={row.id_peluang_kabkot} onStatusChange={() => setLocalRefreshTrigger((prev) => prev + 1)} sumberDataOptions={sumberDataOptions} kabkotOptions={kabkotOptions} peluangSektorOptions={peluangSektorOptions} peluangStatusOptions={peluangStatusOptions} jenisInsentifOptions={jenisInsentifOptions}/> */}
      <Button
        variant="secondary"
        size="sm"
        className="bg-blue-500 hover:bg-blue-600 text-xs"
        onClick={() => handlePreview(row)}
      >
        <Eye className="mr-2 h-4 w-4" />
        Preview
      </Button>
      <Button
        variant="destructive"
        size="sm"
        className="bg-red-500 hover:bg-red-600 text-xs"
        onClick={() => onDelete(row)}
      >
        <Trash className="mr-2 h-4 w-4" />
        Delete
      </Button>
    </div>
  );

  const handleDelete = async (row) => {
    SweetAlert.confirm(
      "Konfirmasi",
      "Apakah Anda yakin ingin menghapus peluang investasi daerah ini?",
      async () => {
        try {
          await deleteService(row.id_peluang_daerah);
          SweetAlert.success(
            "Success",
            "Berhasil menghapus peluang investasi daerah",
            () => {
              setLocalRefreshTrigger((prev) => prev + 1);
            }
          );
        } catch (error) {
          SweetAlert.error(
            "Error",
            "Gagal menghapus peluang investasi daerah",
            () => {
              console.error("Error deleting data:", error);
            }
          );
        }
      }
    );
  };

  // const getPeluangInvestasiDaerahServiceFormatted = async (id_adm_kabkot, id_adm_provinsi, pageIndex, pageSize, globalFilter) => {

  //   const queryParams = {
  //     id_adm_kabkot: id_adm_kabkot,
  //     id_adm_provinsi: id_adm_provinsi,
  //     page: pageIndex,
  //     per_page: pageSize,
  //     q: globalFilter
  //   }

  //   if (!globalFilter) {
  //     delete queryParams.q;
  //   }

  //   const res = await getDataTablePeluangInvestasiDaerahService(queryParams);

  //   res.totalPage = res?.pagination?.total_pages;
  //   res.totalRecords = res?.pagination?.total_count;

  //   return res;
  // }
  const getPeluangInvestasiDaerahServiceFormatted = async (
    id_adm_kabkot,
    id_adm_provinsi,
    pageIndex,
    pageSize,
    globalFilter,
    order = null,
    by = null,
    pic = null
  ) => {
    const queryParams = {
      id_adm_kabkot: id_adm_kabkot,
      id_adm_provinsi: id_adm_provinsi,
      page: pageIndex,
      per_page: pageSize,
      q: globalFilter,
      status: status
    };


    if (user?.roleId === 14) {
      queryParams.pic = user.id;
    } else if (pic) {
      queryParams.pic = pic;
    }

    // Tambahkan parameter sorting jika ada
    if (order && by) {
      queryParams.order = order;
      queryParams.by = by;
    }

    if (!globalFilter) {
      delete queryParams.q;
    }

    const res = await getDataTablePeluangInvestasiDaerahService(queryParams);
    res.totalPage = res?.pagination?.total_pages;
    res.totalRecords = res?.pagination?.total_count;
    return res;
  };
  console.log('usernya' ,user);
  
  return (
    <>
      {/* <DynamicTable
        idKey='id_peluang_daerah'
        fetchDataUrl={API_ENDPOINTS.LIST}
        deleteService={deleteService}
        statusService={statusService}
        columns={peluangInvestasiColumns}
        showEdit={false}
        onEdit={onEdit}
        EditComponent={DynamicSektorDaerah}
        StatusComponent={StatusComponent}
        refreshTrigger={refreshTrigger}
        filterPlaceholder="Cari Sektor..."
        alertService={SweetAlert} // If you're using SweetAlert
      /> */}
      {userLoading === false && user ? (
        <DataTable
          columns={peluangInvestasiColumns}
          pageSize={10}
          fetchDataService={async (
            pageIndex,
            pageSize,
            globalFilter,
            order,
            by,
            status
          ) => {
            return await getPeluangInvestasiDaerahServiceFormatted(
              user?.id_adm_kabkot,
              user?.id_adm_provinsi,
              pageIndex,
              pageSize,
              globalFilter,
              order,
              by
            );
          }} // Kirimkan fungsi, bukan langsung datanya
          actionsComponent={({ row }) => {
            return <ActionsComponent row={row} onDelete={handleDelete} />;
          }}
          filterPlaceholder="Cari Peluang Investasi..."
          refreshTrigger={combinedTrigger}
          exportOptions={{
            orientation: "landscape",
            columnStyles: {
            },
          }}
        />
      ) : (
        ""
      )}
    </>
  );
};

export default PeluangInvestasiTable;
