"use client";

import <PERSON><PERSON><PERSON><PERSON> from "../../../SweetAlert";
import SubSektorDaerahEdit from "./EditSubSektorDaerah";
import { HtmlRenderer } from "@/app/helpers";
import StatusButtonFrom from "@/components/ui/StatusButtonFrom";
import DynamicTable from "@/components/ui/TableCRUD";
import useAuthStore from "@/store/AuthStore";
import Api, { APICORS } from "@/utils/Api";
import { mapStatus } from "@/utils/status";

const API_ENDPOINTS = {
  LIST: "/admin/sud_sub_sektor_daerah/lists?is_get_table_admin=true",
  DELETE: "/admin/sud_sub_sektor_daerah/delete",
};

// Main component
const SubSektorTable = ({ status, onEdit, refreshTrigger }) => {
  const deleteService = async (id) => {
    return await Api.delete(`${API_ENDPOINTS.DELETE}/${id}`);
  };

  const statusService = async (body, id) => {
    return await APICORS.patch(
      `/admin/sud_sub_sektor_daerah/toggle_status/${id}`,
      body
    );
  };

  const { user, isLoading: userLoading } = useAuthStore();

  // Column definitions
  const columns = [
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ cell }) => mapStatus(cell.getValue()),
    },
    { accessorKey: "sektor", header: "Sektor Daerah" },
    { accessorKey: "sub_sektor", header: "Sub Sektor Daerah" },
    { accessorKey: "provinsi", header: "Provinsi" },
    { accessorKey: "kabkota", header: "Kabupaten" },
    {
      accessorKey: "deskripsi",
      header: "Deskripsi",
      cell: ({ cell }) => {
        return <HtmlRenderer htmlString={cell.getValue()} /> ?? "";
      },
    },
  ];

  const DynamicSektorDaerah = ({ data }) => {
    return <SubSektorDaerahEdit id={data.id_sub_sektor_daerah} />;
  };

  const StatusComponent = ({
    status,
    dataEdit,
    id,
    onStatusChange,
    service,
  }) => {
    return (
      <StatusButtonFrom
        status={status}
        dataEdit={dataEdit}
        id={id}
        onStatusChange={onStatusChange}
        service={service}
      />
    );
  };

  // const getDataURL = () => {
  //   return  API_ENDPOINTS.LIST + (userLoading === false && user && user.id_adm_kabkot ? `&id_adm_kabkot=${user.id_adm_kabkot}` : '') + (userLoading === false && user && user.id_adm_provinsi ? `&id_adm_provinsi=${user.id_adm_provinsi}` : '');
  // }

  const getDataURL = () => {
    let url = API_ENDPOINTS.LIST;

    if (userLoading === false && user?.roleId === 14) {
      url += `&pic=${user.id}`;
    } 

    if (userLoading === false && user?.id_adm_kabkot) {
      url += `&id_adm_kabkot=${user.id_adm_kabkot}`;
    }

    if (userLoading === false && user?.id_adm_provinsi) {
      url += `&id_adm_provinsi=${user.id_adm_provinsi}`;
    }

    if (status !== undefined && status !== null && status !== "") {
      url += `&status=${status}`;
    }

    return url;
  };

  return (
    <>
      {!userLoading ? (
        <DynamicTable
          idKey="id_sub_sektor_daerah"
          status={status}
          fetchDataUrl={getDataURL()}
          deleteService={deleteService}
          statusService={statusService}
          columns={columns}
          showEdit={false}
          onEdit={onEdit}
          EditComponent={DynamicSektorDaerah}
          StatusComponent={StatusComponent}
          refreshTrigger={refreshTrigger}
          filterPlaceholder="Cari Sektor..."
          alertService={SweetAlert} // If you're using SweetAlert
        />
      ) : (
        ""
      )}
    </>
  );
};

export default SubSektorTable;
