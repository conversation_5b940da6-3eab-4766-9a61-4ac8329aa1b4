"use client";

import <PERSON><PERSON>ler<PERSON> from "../../../SweetAlert";
import SektorDaerahEdit from "./edit-sektor-daerah";
import { HtmlRenderer } from "@/app/helpers";
import StatusButtonFrom from "@/components/ui/StatusButtonFrom";
import DynamicTable from "@/components/ui/TableCRUD";
import useAuthStore from "@/store/AuthStore";
import Api, { APICORS } from "@/utils/Api";
import { formatNumber } from "@/utils/formatNumber";
import { mapStatus } from "@/utils/status";

const API_ENDPOINTS = {
  LIST: "/admin/sud_sektor_daerah/lists?is_get_table_admin=true",
  DELETE: "/admin/sud_sektor_daerah/delete",
};

// Main component
const TableLayout = ({ status, onEdit, refreshTrigger }) => {
  const deleteService = async (id) => {
    return await Api.delete(`${API_ENDPOINTS.DELETE}/${id}`);
  };

  const statusService = async (body, id) => {
    return await APICORS.patch(
      `/admin/sud_sektor_daerah/toggle_status/${id}`,
      body
    );
  };

  const { user, isLoading: userLoading } = useAuthStore();

  // Column definitions
  const columns = [
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ cell }) => mapStatus(cell.getValue()),
    },
    { accessorKey: "nama_sektor", header: "Nama Sektor" },
    { accessorKey: "nama_provinsi", header: "Provinsi" },
    { accessorKey: "nama_kabkot", header: "Kabupaten/Kota" },
    {
      accessorKey: "tahun",
      header: "Tahun",
      cell: ({ cell }) => cell.getValue()?.toString() ?? "",
    },
    {
      accessorKey: "nilai_lq",
      header: "Nilai LQ",
      cell: ({ cell }) => formatNumber(cell.getValue())?.toString() ?? "",
    },
    {
      accessorKey: "pdb_sektor",
      header: "PDB Sektor",
      cell: ({ cell }) => formatNumber(cell.getValue())?.toString() ?? "",
    },
    {
      accessorKey: "pdb_total",
      header: "PDB Total",
      cell: ({ cell }) => formatNumber(cell.getValue())?.toString() ?? "",
    },
    {
      accessorKey: "pdrb_sektor",
      header: "PDRB Sektor",
      cell: ({ cell }) => formatNumber(cell.getValue())?.toString() ?? "",
    },
    {
      accessorKey: "pdrb_total",
      header: "PDRB Total",
      cell: ({ cell }) => formatNumber(cell.getValue())?.toString() ?? "",
    },
    { accessorKey: "deskripsi_singkat", header: "Deskripsi Singkat" },
    {
      accessorKey: "potensi_pasar",
      header: "Potensi Pasar",
      cell: ({ cell }) => {
        return <HtmlRenderer htmlString={cell.getValue()} /> ?? "";
      },
    },
    { accessorKey: "keterangan", header: "Keterangan" },
  ];

  const DynamicSektorDaerah = ({ data }) => {
    return <SektorDaerahEdit id_sektor_daerah={data.id_sektor_daerah} />;
  };

  const StatusComponent = ({
    status,
    dataEdit,
    id,
    onStatusChange,
    service,
  }) => {
    return (
      <StatusButtonFrom
        status={status}
        dataEdit={dataEdit}
        id={id}
        onStatusChange={onStatusChange}
        service={service}
      />
    );
  };

  // const getDataURL = () => {
  //   return (
  //     API_ENDPOINTS.LIST +
  //     (userLoading === false && user && user.id_adm_kabkot
  //       ? `&id_adm_kabkot=${user.id_adm_kabkot}`
  //       : "") +
  //     (userLoading === false && user && user.id_adm_provinsi
  //       ? `&id_adm_provinsi=${user.id_adm_provinsi}`
  //       : "")
  //   );
  // };

  const getDataURL = () => {
    let url = API_ENDPOINTS.LIST;

    if (userLoading === false && user?.roleId === 14) {
      url += `&pic=${user.id}`;
    } 
    
    if (userLoading === false && user?.id_adm_kabkot) {
      url += `&id_adm_kabkot=${user.id_adm_kabkot}`;
    }

    if (userLoading === false && user?.id_adm_provinsi) {
      url += `&id_adm_provinsi=${user.id_adm_provinsi}`;
    }

    if (status !== undefined && status !== null && status !== "") {
      url += `&status=${status}`;
    }

    return url;
  };

  return (
    <>
      {!userLoading ? (
        <DynamicTable
          idKey="id_sektor_daerah"
          status={status}
          fetchDataUrl={getDataURL()}
          deleteService={deleteService}
          statusService={statusService}
          columns={columns}
          showEdit={false}
          onEdit={onEdit}
          EditComponent={DynamicSektorDaerah}
          StatusComponent={StatusComponent}
          refreshTrigger={refreshTrigger}
          filterPlaceholder="Cari Sektor..."
          alertService={SweetAlert} // If you're using SweetAlert
          exportOptions={{
            orientation: "landscape",
            columnStyles: {
              5: { cellWidth: 20 },
              10: { cellWidth: 30 },
              11: { cellWidth: 50 },
            },
            textOptions: {
              headFontSize: 10, // Ukuran teks header tabel
              bodyFontSize: 8, // Ukuran teks isi tabel
            },
          }}
        />  
      ) : (
        ""
      )}
    </>
  );
};

export default TableLayout;
