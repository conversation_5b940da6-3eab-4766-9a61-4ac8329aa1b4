import { useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button"; // Button from ShadCN UI
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"; // Modal components from ShadCN UI
import { Textarea } from "@/components/ui/textarea";
import { Form, Formik } from "formik";
import { editStatusIKNLayerService } from "@/services/IKNCmsService";
import SweetAlert from "../../SweetAlert";
import useAuthStore from "@/store/AuthStore";

const StatusButton = ({ status, dataEdit ,id , onStatusChange, service }) => {
  const [showDialog, setShowDialog] = useState(false);
  const roleId = useAuthStore((state) => state.roleId);
  const isDisabled = useMemo(() => roleId !== 1 && roleId !== 14, [roleId]);
  
  const getStatusColor = (status) => {
    switch (status) {
      case 99:
        return "bg-[#4ca95e] text-white hover:bg-[#45984f]";
      case -1:
        return "bg-red-500 text-white hover:bg-red-600";
      case 0:
        return "bg-yellow-500 text-white hover:bg-yellow-600";
      default:
        return "bg-gray-500 text-white hover:bg-gray-600";
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 99:
        return "Approved";
      case -1:
        return "Rejected";
      case 0:
        return "Pending";
      case -99:
          return "Deleted";
      default:
        return "Change Status";
    }
  };

  return (
    <Formik
      enableReinitialize={true}
      initialValues={{
        id: id,
        status: dataEdit?.status || 0,
        keterangan: "-",
      }}
      onSubmit={async (values, { setSubmitting, resetForm }) => {
        console.log("value",values);
        console.log("id",id);
        
        try {
          const response = await service(values, id);
          console.log("Full Response:", response);
          
          if (response.status === 200 || response.status === "success") {
            resetForm();
            setShowDialog(false);
            SweetAlert.success("Success", "Status berhasil diubah", () => {
              if (onStatusChange) {
                onStatusChange(); // Trigger refresh
              }
            });
          } else {
            SweetAlert.error("Error", `Gagal mengubah status. Pesan: ${response.message}`);
          }
        } catch (error) {
          SweetAlert.error("Error", "Gagal mengubah status.");
          console.error("Error updating status:", error);
        }
        
      }}
    >
      {({ setFieldValue, handleSubmit }) => (
        <Form>
          <Dialog open={showDialog} onOpenChange={setShowDialog}>
        {dataEdit?.status === -99 ? (
                <span className="text-red-500">Deleted</span>
              ) : (
            <DialogTrigger asChild>
              <Button
                size="sm"
                className={`${getStatusColor(
                  dataEdit?.status
                )} text-xs flex items-center`}
                variant="outline"
                disabled={isDisabled} 
              >
                {getStatusText(dataEdit?.status)}
              </Button>
            </DialogTrigger>
              )}

            <DialogContent>
              <DialogHeader>
                <DialogTitle>Ubah Status</DialogTitle>
                <DialogDescription>
                  {dataEdit?.status === 0
                    ? "Status saat ini pending. Silahkan pilih untuk menyetujui atau menolak."
                    : dataEdit?.status === 99
                    ? "Status saat ini sudah disetujui. Anda hanya dapat menolak status."
                    : "Status saat ini belum disetujui atau ditolak. Anda dapat memilih untuk menyetujui."}
                </DialogDescription>
              </DialogHeader>
              
             
                <Textarea
                  onChange={(e) => setFieldValue("keterangan", e.target.value)}
                  placeholder="Tulis alasan"
                />
            
              
              <DialogFooter className="space-x-2">
                <Button variant="outline" onClick={() => setShowDialog(false)}>
                  Cancel
                </Button>

                {dataEdit?.status === 0 && (
                  <>
                    <Button
                      variant="danger"
                      type="button"
                      className="bg-red-500 hover:bg-red-600 text-white"
                      onClick={() => {
                        setFieldValue("status", -1);
                        handleSubmit();
                      }}
                    >
                      Reject
                    </Button>
                    <Button
                      variant="success"
                      type="button"
                      className="bg-green-500 hover:bg-green-600 text-white"
                      onClick={() => {
                        setFieldValue("status", 99);
                        handleSubmit();
                      }}
                    >
                      Approve
                    </Button>
                  </>
                )}

                {dataEdit?.status === 99 && (
                  <Button
                    variant="danger"
                    type="button"
                    className="bg-red-500 hover:bg-red-600 text-white"
                    onClick={() => {
                      setFieldValue("status", -1);
                      handleSubmit();
                    }}
                  >
                    Reject
                  </Button>
                )}

                {dataEdit?.status === -1 && (
                  <Button
                    variant="success"
                    type="button"
                    className="bg-green-500 hover:bg-green-600 text-white"
                    onClick={() => {
                      setFieldValue("status", 99);
                      handleSubmit();
                    }}
                  >
                    Approve
                  </Button>
                )}
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </Form>
      )}
    </Formik>
  );
};

export default StatusButton;
