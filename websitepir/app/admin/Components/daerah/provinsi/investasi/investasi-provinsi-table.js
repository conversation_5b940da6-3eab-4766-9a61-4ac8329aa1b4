"use client";

import DataTable from "../../../DataTable";
import SweetAlert from "../../../SweetAlert";
import { Button } from "@/components/ui/button";
import { deleteInvestasiProvinsiService, statusInvestasiProvinsiService } from "@/services/ProvinsiCMSService";
import { Edit, Trash } from "lucide-react";
import React, { useEffect, useState } from "react";
import StatusButton from "../status-button";
import DataTableDaerah from "../../../DataTableDaerah";
import useAuthStore from "@/store/AuthStore";


// Define columns for Kantor Provinsi table


// Actions component for edit and delete buttons
const ActionsComponent = ({ row, onEdit, onDelete }) => (
  <div className="flex space-x-2">
    {/* <EditSektorNasionalRef id={row.id_sektor_nasional_ref}/> */}
    <Button
      variant="secondary"
      size="sm"
      style={{ backgroundColor: "#E2B93B", color: "white" }}
      className="text-xs"
      onClick={() => onEdit(row)}
    >
      {" "}
      <Edit className="mr-2 h-4 w-4" /> Edit
    </Button>
    <Button
      variant="destructive"
      size="sm"
      className="bg-red-500 hover:bg-red-600 text-xs"
      onClick={() => onDelete(row)}
    >
      {" "}
      <Trash className="mr-2 h-4 w-4" /> Delete
    </Button>
  </div>
);

// const fetchDataService = async (page, pageSize, filter) => {
//   try {
//     const response = await fetch(
//       `${process.env.NEXT_PUBLIC_BASE_URL}/admin/provinsi_investasi/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(
//         filter || ""
//       )}`
//     );
//     const result = await response.json();
    
//     if (response.ok && result.success) {
//       return {
//         success: true,
//         data: result.data,
//         totalPage: result.pagination.total_pages,
//         totalRecords: result.pagination.total_count,
//       };
//     }

//     throw new Error(result.message || "Failed to fetch data");
//   } catch (error) {
//     return {
//       success: false,
//       data: [],
//       totalPage: 0,
//       totalRecords: 0,
//       message: error.message,
//     };
//   }
// };
// const fetchDataService = async (page, pageSize, filter) => {
//   try {
//     const { id_adm_provinsi, id_adm_kabkot } = useAuthStore.getState();
//     let url = `${process.env.NEXT_PUBLIC_BASE_URL}/admin/provinsi_investasi/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(filter || "")}`;

//     // Tambahkan query parameter berdasarkan data user yang login
//     if (id_adm_provinsi) {
//       url += `&id_adm_provinsi=${id_adm_provinsi}`;
//     } else if (id_adm_kabkot) {
//       url += `&id_adm_kabkot=${id_adm_kabkot}`;
//     }

//     const response = await fetch(url);
//     const result = await response.json();
//     console.log("result api", result);

//     if (response.ok && result.success) {
//       return {
//         success: true,
//         data: result.data || [],
//         totalPage: result.pagination.total_pages,
//         totalRecords: result.pagination.total_count,
//       };
//     }

//     throw new Error(result.message || "Failed to fetch data");
//   } catch (error) {
//     return {
//       success: false,
//       data: [],
//       totalPage: 0,
//       totalRecords: 0,
//       message: error.message,
//     };
//   }
// };
const fetchDataService = async (page, pageSize, filter, order = null, by = null) => {
  try {
    const { id_adm_provinsi, id_adm_kabkot,roleId,id } = useAuthStore.getState();
    let url = `${process.env.NEXT_PUBLIC_BASE_URL}/admin/provinsi_investasi/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(filter || "")}`;

    if(roleId === 14){
      url += `&pic=${id}`;
    }
    // Tambahkan query parameter berdasarkan data user yang login
    if (id_adm_provinsi) {
      url += `&id_adm_provinsi=${id_adm_provinsi}`;
    } else if (id_adm_kabkot) {
      url += `&id_adm_kabkot=${id_adm_kabkot}`;
    }

    // Tambahkan parameter sorting jika ada
    if (order && by) {
      url += `&order=${order}&by=${by}`;
    }

    const response = await fetch(url);
    const result = await response.json();
    console.log("result api", result);

    if (response.ok && result.success) {
      const formattedData = result.data.map(item => ({
        ...item,
        provinsi: item.tb_adm_provinsi.nama,
        sektor: item.tb_investasi_sektor.nama,
        jenis: item.tb_investasi_jenis.nama
    }));
      return {
        success: true,
        data: formattedData,
        totalPage: result.pagination.total_pages,
        totalRecords: result.pagination.total_count,
      };
    }

    throw new Error(result.message || "Failed to fetch data");
  } catch (error) {
    return {
      success: false,
      data: [],
      totalPage: 0,
      totalRecords: 0,
      message: error.message,
    };
  }
};


const InvestasiProvinsiTable = ({ onEditInvestasi, refreshTrigger }) => {
  const [localRefreshTrigger, setLocalRefreshTrigger] = useState(0);
  const [combinedTrigger, setCombinedTrigger] = useState(refreshTrigger);

  useEffect(() => {
    setCombinedTrigger(refreshTrigger + localRefreshTrigger);
  }, [refreshTrigger, localRefreshTrigger]);

  
const handleDelete = async (row) => {
  SweetAlert.confirm(
    "Konfirmasi",
    "Apakah Anda yakin ingin menghapus data ini?",
    async () => {
      try {
        await deleteInvestasiProvinsiService(row.id_investasi_provinsi);
        SweetAlert.success("Success", "Berhasil menghapus data", () => {
          setLocalRefreshTrigger((prev) => prev + 1); 
        });
      } catch (error) {
        SweetAlert.error("Error", "Gagal menghapus data", () => {
          console.error("Error deleting data:", error);
        });
      }
    }
  );
};

const columns = [
  { accessorKey: 'status', header: 'Status',
    cell: ({ row }) => (
      <div className="flex h-full">
        <StatusButton
          status={row.original.status}
          dataEdit={row.original}
          id={row.original.id_investasi_provinsi}
          onStatusChange={() => setLocalRefreshTrigger((prev) => prev + 1)} // Trigger refresh
          service={statusInvestasiProvinsiService}
        />
      </div>
    )
   },
  { accessorKey: "provinsi", header: "Provinsi" },
  { accessorKey: "tahun", header: "Tahun" },
  { accessorKey: "sektor", header: "Sektor" },
  { accessorKey: "jenis", header: "Jenis" },
  { accessorKey: "tb_sumber_data.tb_sumber_data_judul.judul", header: "Sumber Data" },
  { accessorKey: "tb_investasi_jenis_data.nama", header: "Jenis Data" },
  { accessorKey: "jumlah_proyek", header: "Jumlah Proyek" },
  { accessorKey: "jumlah_investasi", header: "Jumlah Investasi" },
];

  return (
    <>
      <DataTableDaerah
        columns={columns}
        fetchDataService={fetchDataService}
        pageSize={10}
        actionsComponent={({ row }) => (
          <ActionsComponent
            row={row}
            onEdit={onEditInvestasi}
            onDelete={handleDelete}
          />
        )}
        filterPlaceholder="Cari..."
        refreshTrigger={combinedTrigger}
      />
    </>
  );
};

export default InvestasiProvinsiTable;
