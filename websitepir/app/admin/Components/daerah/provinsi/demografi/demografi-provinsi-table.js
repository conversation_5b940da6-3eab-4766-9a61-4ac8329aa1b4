"use client";

import DataTable from "../../../DataTable";
import DataTableDaerah from "../../../DataTableDaerah";
import <PERSON>Alert from "../../../SweetAlert";
import StatusButton from "../status-button";
import { getDataTableDaerahDemografiService } from "@/services/DaerahService";
import { Button } from "@/components/ui/button";
import {
  deleteDemografiProvinsiService,
  deleteEksporImporProvinsiService,
  statusDemografiProvinsiService,
} from "@/services/ProvinsiCMSService";
import useAuthStore from "@/store/AuthStore";
import { Edit, Trash } from "lucide-react";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

// Define columns for Kantor Provinsi table

// Actions component for edit and delete buttons
const ActionsComponent = ({ row, onEdit, onDelete }) => (
  <div className="flex space-x-2">
    <Button
      variant="secondary"
      size="sm"
      style={{ backgroundColor: "#E2B93B", color: "white" }}
      className="text-xs"
      onClick={() => onEdit(row)}
    >
      {" "}
      <Edit className="mr-2 h-4 w-4" /> Edit
    </Button>
    <Button
      variant="destructive"
      size="sm"
      className="bg-red-500 hover:bg-red-600 text-xs"
      onClick={() => onDelete(row)}
    >
      {" "}
      <Trash className="mr-2 h-4 w-4" /> Delete
    </Button>
  </div>
);
// const fetchDataService = async (page, pageSize, filter) => {
//   try {
//     const response = await fetch(
//       `${process.env.NEXT_PUBLIC_BASE_URL}/admin/provinsi_demografi/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(
//         filter || ""
//       )}`
//     );
//     const result = await response.json();
//     console.log("result api",result)

//     if (response.ok && result.success) {
//       return {
//         success: true,
//         data: result.data || [],
//         totalPage: result.pagination.total_pages,
//         totalRecords: result.pagination.total_count,
//       };
//     }

//     throw new Error(result.message || "Failed to fetch data");
//   } catch (error) {
//     return {
//       success: false,
//       data: [],
//       totalPage: 0,
//       totalRecords: 0,
//       message: error.message,
//     };
//   }
// };
// const fetchDataService = async (page, pageSize, filter) => {
//   try {
//     const { id_adm_provinsi, id_adm_kabkot } = useAuthStore.getState(); // Ambil nilai dari Zustand store
//     console.log("prov", id_adm_provinsi);
//     console.log("kab", id_adm_kabkot);
//     let url = `${
//       process.env.NEXT_PUBLIC_BASE_URL
//     }/admin/provinsi_demografi/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(
//       filter || ""
//     )}`;

//     // Tambahkan query parameter berdasarkan data user yang login
//     if (id_adm_provinsi) {
//       url += `&id_adm_provinsi=${id_adm_provinsi}`;
//     } else if (id_adm_kabkot) {
//       url += `&id_adm_kabkot=${id_adm_kabkot}`;
//     }

//     const response = await fetch(url);
//     const result = await response.json();
//     console.log("result api", result);

//     if (response.ok && result.success) {
//       return {
//         success: true,
//         data: result.data || [],
//         totalPage: result.pagination.total_pages,
//         totalRecords: result.pagination.total_count,
//       };
//     }

//     throw new Error(result.message || "Failed to fetch data");
//   } catch (error) {
//     return {
//       success: false,
//       data: [],
//       totalPage: 0,
//       totalRecords: 0,
//       message: error.message,
//     };
//   }
// };
// const fetchDataService = async (page, pageSize, filter, order = null, by = null) => {
//   try {
//     const { id_adm_provinsi, id_adm_kabkot } = useAuthStore.getState(); // Ambil nilai dari Zustand store
//     console.log("prov", id_adm_provinsi);
//     console.log("kab", id_adm_kabkot);

//     let url = `${process.env.NEXT_PUBLIC_BASE_URL}/admin/provinsi_demografi/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(filter || "")}`;

//     // Tambahkan query parameter berdasarkan data user yang login
//     if (id_adm_provinsi) {
//       url += `&id_adm_provinsi=${id_adm_provinsi}`;
//     } else if (id_adm_kabkot) {
//       url += `&id_adm_kabkot=${id_adm_kabkot}`;
//     }

//     // Tambahkan parameter sorting jika ada
//     if (order && by) {
//       url += `&order=${order}&by=${by}`;
//     }

//     const response = await fetch(url);
//     const result = await response.json();
//     console.log("result api", result);

//     if (response.ok && result.success) {
//       return {
//         success: true,
//         data: result.data || [],
//         totalPage: result.pagination.total_pages,
//         totalRecords: result.pagination.total_count,
//       };
//     }

//     throw new Error(result.message || "Failed to fetch data");
//   } catch (error) {
//     return {
//       success: false,
//       data: [],
//       totalPage: 0,
//       totalRecords: 0,
//       message: error.message,
//     };
//   }
// };


const DemografiProvinsiTable = ({
  status, onEditDemografi, refreshTrigger }) => {
  const [localRefreshTrigger, setLocalRefreshTrigger] = useState(0);
  const [combinedTrigger, setCombinedTrigger] = useState(refreshTrigger);
  const router = useRouter();

  const { user, isLoading: userLoading } = useAuthStore();

  useEffect(() => {
    setCombinedTrigger(refreshTrigger + localRefreshTrigger);
  }, [refreshTrigger, localRefreshTrigger]);

  const columns = [
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <div className="flex h-full items-center justify-center">
          <StatusButton
            status={row.original.status}
            dataEdit={row.original}
            id={row.original.id_demografi_provinsi}
            onStatusChange={() => setLocalRefreshTrigger((prev) => prev + 1)} // Trigger refresh
            service={statusDemografiProvinsiService}
          />
        </div>
      ),
    },
    { accessorKey: "provinsi", header: "Provinsi" },
    { accessorKey: "tahun", header: "Tahun" },
    { accessorKey: "tb_demografi_kategori.nama", header: "Kategori" },
    {
      accessorKey: "tb_adm_provinsi.jumlah_penduduk",
      header: "Jumlah Penduduk",
    },
    { accessorKey: "jumlah_pria", header: "Jumlah Pria" },
    { accessorKey: "jumlah_wanita", header: "Jumlah Wanita" },
    { accessorKey: "kepadatan_penduduk", header: "Kepadatan Penduduk" },
    { accessorKey: "pertumbuhan_penduduk", header: "Laju Pertumbuhan" },
  ];

  const handleDelete = async (row) => {
    SweetAlert.confirm(
      "Konfirmasi",
      "Apakah Anda yakin ingin menghapus data ini?",
      async () => {
        try {
          await deleteDemografiProvinsiService(row.id_demografi_provinsi);
          SweetAlert.success("Success", "Berhasil menghapus data", () => {
            setLocalRefreshTrigger((prev) => prev + 1);
          });
        } catch (error) {
          SweetAlert.error("Error", "Gagal menghapus data", () => {
            console.error("Error deleting data:", error);
          });
        }
      }
    );
  };

  const getProvinsiDaerahServiceFormatted = async (
    id_adm_kabkot,
    id_adm_provinsi,
    pageIndex,
    pageSize,
    globalFilter,
    order = null,
    by = null
  ) => {
    const queryParams = {
      id_adm_kabkot: id_adm_kabkot,
      id_adm_provinsi: id_adm_provinsi,
      page: pageIndex,
      per_page: pageSize,
      q: globalFilter,
      status,
    };

    // Add pic parameter for role 14 users
    if (user?.roleId === 14) {
      queryParams.pic = user.id;
    }

    if (status !== null && status !== undefined) {
      queryParams.status = status;
    }

    // Tambahkan parameter sorting jika ada
    if (order && by) {
      queryParams.order = order;
      queryParams.by = by;
    }

    if (!globalFilter) {
      delete queryParams.q;
    }

    
    console.log("Fetching data with params:", queryParams);

    const res = await getDataTableDaerahDemografiService(queryParams);
    
    if (res.success) {
      const formattedData = res.data.map(item => ({
        ...item,
        provinsi: item.tb_adm_provinsi.nama, // Flattening the nested structure
    }));

       console.log("formattedData",formattedData);
      return {
          success: true,
          data: formattedData,
          totalPage: res.pagination.total_pages,
          totalRecords: res.pagination.total_count,
      };
  } else {
      console.error("Error fetching data:", res.message);
      return {
          success: false,
          data: [],
          totalPage: 0,
          totalRecords: 0,
      };
  }
  };

  return (
    <>
      {userLoading === false && user ? (
      <DataTableDaerah
        columns={columns}
        // fetchDataService={fetchDataService}
        pageSize={10}
        fetchDataService={async (
          pageIndex,
          pageSize,
          globalFilter,
          order,
          by
        ) => {
          return await getProvinsiDaerahServiceFormatted(
            user?.id_adm_kabkot,
            user?.id_adm_provinsi,
            pageIndex,
            pageSize,
            globalFilter,
            order,
            by
          );
        }} 
        actionsComponent={({ row }) => (
          <ActionsComponent
            row={row}
            onEdit={onEditDemografi}
            onDelete={handleDelete}
          />
        )}
        filterPlaceholder="Cari..."
        refreshTrigger={combinedTrigger}
        exportOptions={{
          orientation: "landscape",
          columnStyles: {
          },
        }}
        status={status} 
      />
    ) : (
      ""
    )}
    </>
  );
};

export default DemografiProvinsiTable;
