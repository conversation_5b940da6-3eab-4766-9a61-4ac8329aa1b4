'use client';
import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { deleteKantorProvinsiService, statusKantorProvinsiService } from '@/services/ProvinsiCMSService';
import DataTable from '../../../DataTable';
import SweetAlert from '../../../SweetAlert';
import { Edit, Trash } from 'lucide-react';
import StatusButton from '../status-button';
import DataTableDaerah from '../../../DataTableDaerah';
import useAuthStore from '@/store/AuthStore';




// Define columns for Kantor Provinsi table


// Actions component for edit and delete buttons
const ActionsComponent = ({ row, onEdit,onDelete  }) => (
  <div className="flex space-x-2">
    <Button variant="secondary" size="sm" style={{ backgroundColor: '#E2B93B', color: 'white' }} className="text-xs"  onClick={() => onEdit(row)}> <Edit className="mr-2 h-4 w-4" /> Edit</Button>
    <Button variant="destructive" size="sm" className="bg-red-500 hover:bg-red-600 text-xs" onClick={() => onDelete(row)}> <Trash className="mr-2 h-4 w-4" /> Delete</Button>
  </div>
);
// const fetchDataService = async (page, pageSize, filter) => {
//   try {
//     const response = await fetch(
//       `${process.env.NEXT_PUBLIC_BASE_URL}/admin/provinsi_kantor/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(filter || "")}`
//     );
//     const result = await response.json();

//     if (response.ok && result.success) {
//       return {
//         success: true,
//         data: result.data,
//         totalPage: result.pagination.total_pages,
//         totalRecords: result.pagination.total_count,
//       };
//     }

//     throw new Error(result.message || "Failed to fetch data");
//   } catch (error) {
//     return {
//       success: false,
//       data: [],
//       totalPage: 0,
//       totalRecords: 0,
//       message: error.message,
//     };
//   }
// };
// const fetchDataService = async (page, pageSize, filter) => {
//   try {
//     const { id_adm_provinsi, id_adm_kabkot } = useAuthStore.getState();
//     let url = `${process.env.NEXT_PUBLIC_BASE_URL}/admin/provinsi_kantor/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(filter || "")}`;

//     // Tambahkan query parameter berdasarkan data user yang login
//     if (id_adm_provinsi) {
//       url += `&id_adm_provinsi=${id_adm_provinsi}`;
//     } else if (id_adm_kabkot) {
//       url += `&id_adm_kabkot=${id_adm_kabkot}`;
//     }

//     const response = await fetch(url);
//     const result = await response.json();
//     console.log("result api", result);

//     if (response.ok && result.success) {
//       return {
//         success: true,
//         data: result.data || [],
//         totalPage: result.pagination.total_pages,
//         totalRecords: result.pagination.total_count,
//       };
//     }

//     throw new Error(result.message || "Failed to fetch data");
//   } catch (error) {
//     return {
//       success: false,
//       data: [],
//       totalPage: 0,
//       totalRecords: 0,
//       message: error.message,
//     };
//   }
// };


const KantorProvinsiTable = ({status,onEditKantor, refreshTrigger}) => {
  const [localRefreshTrigger, setLocalRefreshTrigger] = useState(0);
  const [combinedTrigger, setCombinedTrigger] = useState(refreshTrigger);

  useEffect(() => {
    setCombinedTrigger(refreshTrigger + localRefreshTrigger);
  }, [refreshTrigger, localRefreshTrigger]);

  
  
const fetchDataService = async (page, pageSize, filter, order = null, by = null) => {
  try {
    const { id_adm_provinsi, id_adm_kabkot,roleId,id } = useAuthStore.getState();
    let url = `${process.env.NEXT_PUBLIC_BASE_URL}/admin/provinsi_kantor/lists?page=${page}&per_page=${pageSize}&q=${encodeURIComponent(filter || "")}`;

    if(roleId === 14){
      url += `&pic=${id}`;
    }
    // Tambahkan query parameter berdasarkan data user yang login
    if (id_adm_provinsi) {
      url += `&id_adm_provinsi=${id_adm_provinsi}`;
    } else if (id_adm_kabkot) {
      url += `&id_adm_kabkot=${id_adm_kabkot}`;
    }

      // Tambahkan filter status jika tersedia
      if (status !== undefined && status !== null) {
        url += `&status=${status}`;
      }

    // Tambahkan parameter sorting jika ada
    if (order && by) {
      url += `&order=${order}&by=${by}`;
    }

    const response = await fetch(url);
    const result = await response.json();
    // console.log("result api", result);

    if (response.ok && result.success) {
      const formattedData = result.data.map(item => ({
        ...item,
        provinsi: item.tb_adm_provinsi.nama, // Flattening the nested structure
    }));

      return {
        success: true,
        data: formattedData,
        totalPage: result.pagination.total_pages,
        totalRecords: result.pagination.total_count,
      };
    }

    throw new Error(result.message || "Failed to fetch data");
  } catch (error) {
    return {
      success: false,
      data: [],
      totalPage: 0,
      totalRecords: 0,
      message: error.message,
    };
  }
};



const handleDelete = async (row) => {
  SweetAlert.confirm(
    "Konfirmasi",
    "Apakah Anda yakin ingin menghapus data ini?",
    async () => {
      try {
        await deleteKantorProvinsiService(row.id_adm_provinsi_kantor); 
        SweetAlert.success("Success", "Berhasil menghapus data", () => {
          //window.location.reload();
          setLocalRefreshTrigger((prev) => prev + 1);
        });
      } catch (error) {
        SweetAlert.error("Error", "Gagal menghapus data", () => {
          console.error("Error deleting data:", error);
        });
      }
    }
  );
};

const kantorColumns = [
  { accessorKey: 'status', header: 'Status',
    cell: ({ row }) => (
      <div className="flex h-full">
        <StatusButton
          status={row.original.status}
          dataEdit={row.original}
          id={row.original.id_adm_provinsi_kantor}
          onStatusChange={() => setLocalRefreshTrigger((prev) => prev + 1)} // Trigger refresh
          service={statusKantorProvinsiService}
        />
      </div>
    )
   },
  { accessorKey: 'provinsi', header: 'Provinsi' },
  { accessorKey: 'nama', header: 'Nama Kantor' },
];

  return (
    <>
      <DataTableDaerah
        columns={kantorColumns}
        fetchDataService={fetchDataService}
        pageSize={10}
        actionsComponent={({row}) => (
          <ActionsComponent row={row} onEdit={onEditKantor} onDelete={handleDelete}/>
        )}
        filterPlaceholder="Cari..."
        refreshTrigger={combinedTrigger}
      />
    </>
  );
};

export default KantorProvinsiTable;
