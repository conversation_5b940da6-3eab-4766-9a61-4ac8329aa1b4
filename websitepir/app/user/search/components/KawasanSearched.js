"use client";

import useLanguageStore from "../../stores/languageStore";
import { Button } from "@/components/ui/button";
import { Calendar } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React from "react";

const KawasanSearched = ({
  id_kawasan_industri,
  title,
  keterangan
}) => {
  const router = useRouter();

  const { language } = useLanguageStore();

  // Option 2: Arrow function
  const handleClickedKawasanInvestasiCard = (id_kawasan_industri) => {
    router.push(`/daerah/kawasan/${id_kawasan_industri}`);
  };

  const removeHtmlTags = (html) => {
    if (!html) return "";

    // Create a temporary div element
    const temp = document.createElement("div");
    temp.innerHTML = html;

    // Get text content (this removes HTML tags)
    let text = temp.textContent || temp.innerText;

    // Remove extra whitespace
    text = text.replace(/\s+/g, " ").trim();

    return text;
  };

 

  // Clean the descriptions
  const cleanKeterangan = removeHtmlTags(keterangan);
  if (title === null) {
    return <></>;
  }

  return (
    <div className="m-4 border-b-2 border-gray-300">
        <div className="text-xl font-bold font-montserrat">
          {title || "Budidaya Udang Terintegrasi"}
        </div>
        <div className="pt-5">
          {cleanKeterangan ||
            "-"}
        </div>
        <div className="flex justify-end gap-3">
          <div className="flex pt-4">
            
          </div>
          <div className="flex pt-4">
            {title == null ? (
              <></>
            ) : (
              <Button
                className="hover:bg-[#2D3A96] bg-[#4CA85F] mb-10 font-montserrat font-bold text-sm"
                onClick={() =>
                  handleClickedKawasanInvestasiCard(id_kawasan_industri, )
                }
              >
                {language === "en" ? "Learn More" : "Selengkapnya"}
              </Button>
            )}
          </div>
        </div>
      </div>
);
};

export default KawasanSearched;
