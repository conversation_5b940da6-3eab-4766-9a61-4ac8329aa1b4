"use client";

import useLanguageStore from "../../stores/languageStore";
import { Button } from "@/components/ui/button";
import { Calendar } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React from "react";

const SearchedInvestasiCard = ({
  id_peluang,
  title,
  prioritas,
  sektor,
  tahun,
  deskripsi,
  logo,
  no_data,
  dokumen,
  status,
  jml,
}) => {
  const router = useRouter();

  const { language } = useLanguageStore();

  // Option 2: Arrow function
  const handleClickedPeluangInvestasiCard = (id_adm_kabkot, status) => {
    const routes = {
      IPRO: `/peluang_investasi/ipro/${id_adm_kabkot}`,
      PID: `/daerah/peluang_investasi/${id_adm_kabkot}`,
      default: `/peluang_investasi/detailed/${id_adm_kabkot}`,
    };

    router.push(routes[status] || routes.default);
  };
  if (title === null) {
    return <></>;
  }
  return (
    <div className="m-4">
      <div className="text-xl font-bold font-montserrat">
        {title || "Budidaya Udang Terintegrasi"}
      </div>
      <div className="flex gap-3 mt-2">
        <div
          className={`rounded-xl text-white font-montserrat  ${
            prioritas === "ipro" ? "bg-[#FF9500] px-2 py-1" : ""
          }`}
        >
          {prioritas == "ipro" ? "IPRO" : <div className="bg-none"></div>}
        </div>
        <div className="flex gap-2">
          <Image
            src={logo}
            width={20}
            height={20}
            alt="L"
            className="drop-shadow-md "
          />
          <div className="flex bg-[#FF9500] px-2 py-1 text-white rounded-lg content-center justify-center items-center">
            <p className="text-sm font-medium font-montserrat">{status}</p>
          </div>
          <span className="flex font-montserrat text-[#2D3A96] justify-center items-center">
            {sektor || "Sektor"}
          </span>
        </div>
        <div className="flex items-center justify-center gap-2">
          <Calendar color="#2D3A96" size={20} />
          <span className="flex items-center text-[#2D3A96] justify-center font-montserrat">
            {tahun || "2020"}
          </span>
        </div>
      </div>
      <div className="pt-5">
        {deskripsi ||
          "-"}
      </div>
      <div className="flex justify-end gap-3">
        <div className="flex pt-4">
          {dokumen?.length > 0 ? (
            <Link
              href={`${dokumen[0].file}`}
              target="_blank"
              rel="noopener noreferrer"
            >
              <Button className="hover:bg-[#4CA85F] bg-[#2D3A96] font-montserrat font-bold text-sm">
                {language === "id" ? "Unduh Dokumen" : "Download Document"}
              </Button>
            </Link>
          ) : (
            <></>
          )}
        </div>
        <div className="flex pt-4">
          {no_data ? (
            <></>
          ) : (
            <Button
              className="hover:bg-[#2D3A96] bg-[#4CA85F] font-montserrat font-bold text-sm"
              onClick={() =>
                handleClickedPeluangInvestasiCard(id_peluang, status)
              }
            >
              {language === "en" ? "Learn More" : "Selengkapnya"}
            </Button>
          )}
        </div>
      </div>
    </div>
  
  );
};

export default SearchedInvestasiCard;
