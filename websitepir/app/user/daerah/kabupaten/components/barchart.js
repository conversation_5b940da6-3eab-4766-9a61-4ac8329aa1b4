"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import React, { useEffect } from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  Rectangle,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
} from "recharts";
import useLanguageStore from "@/app/user/stores/languageStore";

const formatNumber = (num) => {
  return new Intl.NumberFormat("id-ID").format(num);
};

const Barchart = ({ data, type, selectedYear }) => {
  const { language } = useLanguageStore();
  useEffect(() => {
    console.log("Incoming data:", data);
    console.log("Selected type:", type);
    console.log("Selected year:", selectedYear);
  }, [data, type, selectedYear]);

  const transformData = () => {
    if (!data) {
      console.warn("Data is undefined or null.");
      return [];
    }

    let transformedData = [];

    switch (type) {
      case "komoditas":
        if (!data || data.length === 0) {
          console.warn("Komoditas data is empty or missing.");
          return [];
        }
        // Handle direct komoditas data array
        transformedData = data.map((item, index) => ({
          name: item.nama_komoditi,
          value: item.nilai_produksi,
          fill: getColor(index),
        }));
        break;
      case "pdrb":
        if (!data.pdrb || !data.pdrb[selectedYear]) {
          console.warn("PDRB data for the selected year is missing.");
          return [];
        }
        transformedData = data.pdrb[selectedYear].map((item, index) => ({
          name: item.nama_sektor,
          value: item.nilai / 1000000000,
          fill: getColor(index),
        }));
        break;
      case "umr":
        if (!data.umr) {
          console.warn("UMR data is missing.");
          return [];
        }
        transformedData = data.umr
          .filter((item) => item.tahun.toString() === selectedYear)
          .map((item, index) => ({
            name: "UMR",
            value: item.nilai,
            fill: getColor(index),
          }));
        break;
      case "penduduk":
        if (!data.penduduk) {
          console.warn("Penduduk data is missing.");
          return [];
        }
        transformedData = data.penduduk
          .filter((item) => item.tahun.toString() === selectedYear)
          .map((item, index) => ({
            name: "Penduduk",
            Pria: item.jumlah_pria,
            Wanita: item.jumlah_wanita,
            fill: getColor(index),
          }));
        break;
      case "investasi":
        if (!data.investasi) {
          console.warn("Investasi data is missing.");
          return [];
        }
        const yearData = data.investasi.by_year.find(
          (item) => item.tahun.toString() === selectedYear
        );
        transformedData =
          yearData?.data?.map((item, index) => ({
            name: item.jenis,
            value: item.total_investasi,
            fill: getColor(index),
          })) || [];
        break;
      default:
        console.warn("Type is not recognized:", type);
        return [];
    }

    console.log("Transformed data:", transformedData);
    return transformedData;
  };

  const getColor = (index) => {
    const colors = [
      "#2D3B96", "#4CA85F", "#FF6F61", "#FFA500", "#6A5ACD",
      "#FF1493", "#1E90FF", "#32CD32", "#FFD700", "#FF4500",
    ];
    return colors[index % colors.length]; // Cycle through the colors
  };

  const getChartTitle = () => {
    switch (type) {
      case "komoditas":
        return language === "id" ? "Produksi Komoditas (dalam Ton)" : "Production of Commodities (in Ton)";
      case "pdrb":
        return language === "id" ? "PDRB (dalam Miliar Rupiah)" : "PDRB (in Billion Rupiah)";
      case "umr":
        return language === "id" ? "UMR (dalam Rupiah)" : "UMR (in Rupiah)";
      case "penduduk":
        return language === "id" ? "Jumlah Penduduk (Jiwa)" : "Population (People)";
      case "investasi":
        return language === "id" ? "Realisasi Investasi (dalam Juta Rupiah)" : "Realized Investment (in Juta Rupiah)";
      default:
        return "";
    }
  };

  const formatTooltipValue = (value, type) => {
    if (value === undefined || value === null) return "-";
    switch (type) {
      case "komoditas":
        return language === "id" ? `${formatNumber(value)} Ton` : `${formatNumber(value)} Ton`;
      case "pdrb":
        return language === "id" ? `${formatNumber(value)} Miliar` : `${formatNumber(value)} Billion`;
      case "umr":
        return language === "id" ? `Rp ${formatNumber(value)}` : `Rp ${formatNumber(value)}`;
      case "penduduk":
        return language === "id" ? `${formatNumber(value)} Jiwa` : `${formatNumber(value)} People`;
      case "investasi":
        return language === "id" ? `${formatNumber(value)} Juta` : `${formatNumber(value)} Million`;
      default:
        return formatNumber(value);
    }
  };

  const chartData = transformData();

  if (chartData.length === 0) {
    console.warn("Chart data is empty.");
    return (
      <Card className="flex-1 h-full mt-10 mb-10 ml-10 mr-10 shrink-0">
        <CardHeader>
          <CardTitle className="font-montserrat text-[#2D3A96]">
            {getChartTitle()}
          </CardTitle>
          <CardContent className="h-[500px] flex items-center justify-center">
            <p className="text-gray-500">{language === "id" ? "Tidak ada data tersedia" : "No data available"}</p>
          </CardContent>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className="flex-1 h-full mt-10 mb-10 ml-10 mr-10 shrink-0">
      <CardHeader>
        <CardTitle className="font-montserrat text-[#2D3A96]">
          {getChartTitle()}
        </CardTitle>
      </CardHeader>
      <CardContent className="h-[500px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            layout="vertical"
            data={chartData}
            margin={{
              top: 20,
              right: 30,
              left: 120,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              type="number"
              tickFormatter={formatNumber}
              domain={[0, "dataMax"]}
            />
            <YAxis type="category" dataKey="name" width={120} />
            <Tooltip formatter={(value) => formatTooltipValue(value, type)} />
            <Legend />
            {type === "penduduk" ? (
              <>
                <Bar dataKey={language === "id" ? "Pria" : "Male"} fill="#2D3B96" barSize={20} name={language === "id" ? "Pria" : "Male"} />
                <Bar
                  dataKey={language === "id" ? "Wanita" : "Female"}
                  fill="#4CA85F"
                  barSize={20}
                  name={language === "id" ? "Wanita" : "Female"}
                />
              </>
            ) : (
              <Bar
                dataKey="value"
                name={
                  type === "komoditas"
                    ? language === "id" ? "Nilai Produksi (Ton)" : "Production Value (Ton)"
                    : type === "pdrb"
                      ? language === "id" ? "Nilai (Miliar Rupiah)" : "Value (Billion Rupiah)"
                      : type === "umr"
                        ? language === "id" ? "Nilai UMR" : "UMR Value"
                        : type === "investasi"
                          ? language === "id" ? "Nilai Investasi" : "Investment Value"
                          : language === "id" ? "Nilai" : "Value"
                }
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.fill} />
                ))}
              </Bar>
            )}
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default Barchart;