"use client";
import React, { useEffect, useState } from "react";
import { getReferensiService } from "@/services/AllService";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import Image from "next/image";
import { useRouter } from "next/navigation"; // Import useRouter from next/navigation

const CardTitle = ({ data, idProvinsi }) => {
  const [filteredKabkot, setFilteredKabkot] = useState([]);
  const [selectedKabupaten, setSelectedKabupaten] = useState("");
  const router = useRouter(); // Initialize router

  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       const response = await getReferensiService("tb_adm_kabkot");
  //       if (response && response.data) {
  //         console.log("Data dari API:", response.data);

  //         if (idProvinsi) {
  //           // Convert id to string for comparison if needed
  //           const stringId = idProvinsi.toString();

  //           const filtered = response.data.filter(
  //             (kabkot) => kabkot.id_adm_provinsi.toString() === stringId
  //           );

  //           console.log("Data terfilter:", filtered);
  //           setFilteredKabkot(filtered);
  //         } else {
  //           setFilteredKabkot([]);
  //         }
  //       }
  //     } catch (error) {
  //       console.error("Error fetching data:", error);
  //       setFilteredKabkot([]);
  //     }
  //   };

  //   fetchData();
  // }, [idProvinsi]);

  // const handleKabupatenChange = (value) => {
  //   console.log('value',value);

  //   setSelectedKabupaten(value);
  //   if (value) {
  //     // Redirect to the specific route when a kabupaten is selected
  //     router.push(`/user/daerah/kabupaten/${value}`);
  //   }
  // };


  return (
    <div className="flex flex-col items-center justify-center">
      <div className="pt-12 rounded-lg text-center">
        <h1 className="font-bold text-[40px] text-white pt-4">
          {data?.nama_kabkot}
        </h1>

        <div className="relative w-full h-[240px]">
          <Image
            src={data?.icon}
            alt="image"
            layout="fill"
            className="rounded-xl object-contain"
          />
        </div>

        {/* <div className="mt-4 w-full max-w-xs px-6">
          <Select
            value={selectedKabupaten}
            onValueChange={handleKabupatenChange} // Use the new handler here
            className="w-full font-montserrat rounded-lg bg-blue-900 text-white font-bold py-2"
          >
            <SelectTrigger className="w-full font-montserrat rounded-lg bg-blue-900 text-white font-bold py-2">
              <SelectValue placeholder="Select Kabupaten/Kota" />
            </SelectTrigger>
            <SelectContent>
              {filteredKabkot.map((kabupaten) => (
                <SelectItem key={kabupaten?.id_adm_kabkot} value={kabupaten?.id_adm_kabkot} className="font-montserrat">
                  {kabupaten?.nama}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div> */}

        {/* <div className="mt-4">
          <span className="bg-white text-blue-800 font-montserrat font-bold px-6 py-2 rounded-lg">
            {data?.luas}
          </span>
        </div> */}
      </div>
    </div>
  );
};

export default CardTitle;
