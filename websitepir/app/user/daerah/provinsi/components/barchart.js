"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import React, { useEffect } from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  Rectangle,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
} from "recharts";
import useLanguageStore from "@/app/user/stores/languageStore";
const formatNumber = (num) => {
  return new Intl.NumberFormat("id-ID").format(num);
};

const Barchart = ({ data, type, selectedYear }) => {
  const { language } = useLanguageStore();
  useEffect(() => {
    // console.log("Incoming data:", data);
    // console.log("Selected type:", type);
    // console.log("Selected year:", selectedYear);
  }, [data, type, selectedYear]);

  const transformData = () => {
    if (!data) {
      console.warn("Data is undefined or null.");
      return [];
    }

    let transformedData = [];

    switch (type) {
      case "komoditas":
        if (!data || !Array.isArray(data) || data.length === 0) {
          console.warn("Komoditas data is empty or missing.");
          return [];
        }
        transformedData = data.map((item, index) => ({
          name: item.nama_komoditi || "Unknown",
          value: item.nilai_produksi || 0,
          fill: getColor(index),
        }));
        break;
      case "pdrb":
        if (!data.pdrb || !data.pdrb[selectedYear]) {
          console.warn("PDRB data for the selected year is missing.");
          return [];
        }
        transformedData = data.pdrb[selectedYear].map((item,index) => ({
          name: item.nama_sektor,
          value: item.nilai / 1000000000,
          fill: getColor(index), 
        }));
        console.log('transformedData pdrb',transformedData);

        break;
      case "umr":
        if (!data.umr) {
          console.warn("UMR data is missing.");
          return [];
        }

        transformedData = data.umr.map((entry, index) => ({
          name: entry.tahun.toString(),
          value: entry.nilai || 0,
          fill: getColor(index)
        }));
       
        break;
      case "penduduk":
        if (!data.penduduk) {
          console.warn("Penduduk data is missing.");
          return [];
        }

        transformedData = data.penduduk.map((entry, index) => ({
          name: entry.tahun.toString(),
          pria: entry.jumlah_pria || 0,
          wanita: entry.jumlah_wanita || 0,
          fill: getColor(index)
        }));

      
        break;
      case "investasi":
        if (!data.investasi) {
          console.warn("Investasi data is missing.");
          return [];
        }
      
      
        // transformedData = data.investasi.by_sektor.map((sektor, index) => {
        //   // Ekstrak nilai PMA (id_jenis: 1) dan PMDN (id_jenis: 2)
        //   const pma = sektor.find(entry => entry.id_jenis === 1)?.jumlah_investasi || 0;
        //   const pmdn = sektor.find(entry => entry.id_jenis === 2)?.jumlah_investasi || 0;
      
        //   return {
        //     name: sektor[0].nama, // Nama sektor diambil dari entry pertama
        //     pma: pma,
        //     pmdn: pmdn,
        //     fill: getColor(index) // Warna berdasarkan index
        //   };
        // });
        const yearData = data.investasi.by_year.find(
          (year) => year.tahun.toString() === selectedYear.toString()
        );
        
        if (!yearData) {
          console.warn(`Investment data for year ${selectedYear} is missing.`);
          return [];
        }
        
        // // Create a single data point with PMA and PMDN values
        // const pmaEntry = yearData.data.find(entry => entry.id_jenis === 1);
        // const pmdnEntry = yearData.data.find(entry => entry.id_jenis === 2);
        // console.log('yeardata',yearData);
        
        transformedData = yearData.data.map((entry, index) => ({
          name: entry.jenis.toString(),
          value: entry.total_investasi || 0,
          fill: getColor(index)
        }));
        console.log('transformedData investasi',transformedData);
        
       
        // transformedData = [
        //   {
        //     name: selectedYear.toString(),
        //     pma: pmaEntry ? pmaEntry.total_investasi : 0,
        //     pmdn: pmdnEntry ? pmdnEntry.total_investasi : 0,
        //     fill: getColor(0)
        //   }
        // ];
        break;
      default:
        console.warn("Type is not recognized:", type);
        return [];
    }

    console.log("Transformed data:", transformedData);
    return transformedData;
  };

  const getColor = (index) => {
    const colors = [
      "#2D3B96", "#4CA85F", "#FF6F61", "#FFA500", "#6A5ACD",
      "#FF1493", "#1E90FF", "#32CD32", "#FFD700", "#FF4500",
    ];
    return colors[index % colors.length]; // Cycle through the colors
  };

  const getChartTitle = () => {
    if(language === "en"){
      switch (type) {
        case "komoditas":
          return "Production of Commodities (in Ton)";
        case "pdrb":
          return "PDRB (in Billions of Rupiah)";
        case "umr":
          return "UMR (in Rupiah)";
        case "penduduk":
          return "Population (People)";
        case "investasi":
          return "Realisasi Investasi (in Billions of Rupiah)";
        default:
          return "";
      }
    }else{
      switch (type) {
        case "komoditas":
          return "Produksi Komoditas (dalam Ton)";
        case "pdrb":
          return "PDRB (dalam Miliar Rupiah)";
        case "umr":
          return "UMR (dalam Rupiah)";
        case "penduduk":
          return "Jumlah Penduduk (Jiwa)";
        case "investasi":
          return "Realisasi Investasi (dalam Juta Rupiah)";
        default:
          return "";
      }
    }
  };
  const formatTooltipValue = (value, type) => {
    if (value === undefined || value === null) return "-";
    switch (type) {
      case "komoditas":
        return language === "id" ? `${formatNumber(value)} Ton` : `${formatNumber(value)} Ton`;
      case "pdrb":
        return language === "id" ? `${formatNumber(value)} Miliar` : `${formatNumber(value)} Billion`;
      case "umr":
        return language === "id" ? `Rp ${formatNumber(value)}` : `Rp ${formatNumber(value)}`;
      case "penduduk":
        return language === "id" ? `${formatNumber(value)} Jiwa` : `${formatNumber(value)} People`;
      case "investasi":
        return language === "id" ? `${formatNumber(value)} Juta` : `${formatNumber(value)} Million`;
      default:
        return formatNumber(value);
    }
  };

  const chartData = transformData();

  if (chartData.length === 0) {
    console.warn("Chart data is empty.");
    return (
      <Card className="flex-1 h-full mt-10 mb-10 ml-10 mr-10 shrink-0">
        <CardHeader>
          <CardTitle className="font-montserrat text-[#2D3A96]">
            {getChartTitle()}
          </CardTitle>
          <CardContent className="h-[500px] flex items-center justify-center">
            <p className="text-gray-500">{language === "id" ? "Tidak ada data tersedia" : "Data Not Available"}</p>
          </CardContent>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className="flex-1 h-full mt-10 mb-10 ml-10 mr-10 shrink-0">
      <CardHeader>
        <CardTitle className="font-montserrat text-[#2D3A96]">
          {getChartTitle()}
        </CardTitle>
      </CardHeader>
      <CardContent className="h-[700px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            layout="vertical"
            data={chartData}
            margin={{
              top: 20,
              right: 30,
              left: 5,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              type="number"
              tickFormatter={formatNumber}
              domain={[0, "dataMax"]}
            />
            <YAxis 
              type="category" 
              dataKey="name" 
              width={300}
              tick={{ 
                angle: 0, 
                textAnchor: 'end', 
                fontSize: 12,
                dy: 5,  // Add vertical spacing
                dx: 5   // Add horizontal spacing
              }}
              interval={0} // Ensure all labels are shown
            />
            <Tooltip formatter={(value) => formatTooltipValue(value, type)} />
            <Legend />
            
            {(() => {
              // if (type === "investasi") {
              //   return (
              //     <>
              //       <Bar dataKey="pma" fill="#2D3B96" barSize={50} name="PMA" />
              //       <Bar
              //         dataKey="pmdn"
              //         fill="#4CA85F"
              //         barSize={50}
              //         name="PMDN"
              //       />
              //     </>
              //   );
              if (type === "investasi") {
                return (
                  <>
                    <Bar dataKey="value" fill="#2D3B96" barSize={50} name="Investasi" />
                  </>
                );
              } else if (type === "penduduk") {
                return (
                  <>
                    <Bar dataKey="pria" fill="#2D3B96" barSize={50} name="PRIA" />
                    <Bar
                      dataKey="wanita"
                      fill="#4CA85F"
                      barSize={50}
                      name="Wanita"
                    />
                  </>
                );
              } else if (type === "penduduk") {
                return (
                  <>
                    <Bar dataKey="Pria" fill="#2D3B96" barSize={20} name="Pria" />
                    <Bar
                      dataKey="Wanita"
                      fill="#4CA85F"
                      barSize={20}
                      name="Wanita"
                    />
                  </>
                );
              } else {
                return (
                  <Bar
                    dataKey="value"
                    name={
                      type === "komoditas"
                        ? "Nilai Produksi (Ton)"
                        : type === "pdrb"
                        ? "Nilai (Miliar Rupiah)"
                        : type === "umr"
                        ? "Nilai UMR"
                        : type === "investasi"
                        ? "Nilai Investasi"
                        : "Nilai"
                    }
                  >
                    {chartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.fill} />
                    ))}
                  </Bar>
                );
              }
            })()}
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default Barchart;