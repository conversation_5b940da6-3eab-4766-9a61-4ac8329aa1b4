"use client";

import useLanguageStore from "@/app/user/stores/languageStore";
import Barchart from "./barchart";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { getKomoditasDaerahProv } from "@/services/DaerahService";
import React, { useState, useEffect } from "react";

const DaerahDetil = ({ daerahDetil, id }) => {
  const translations = {
    id: {
      daerahDetil: "Daerah Detail",
      tabs: {
        pdrb: "PDRB",
        umr: "UMR",
        penduduk: "Pertumbuhan Penduduk",
        investasi: "Realisasi Investasi",
        komoditas: "Komoditas Daerah",
      },
    },
    en: {
      daerahDetil: "Regional Detail",
      tabs: {
        pdrb: "PDRB",
        umr: "UMR",
        penduduk: "Population",
        investasi: "Investment Realization",
        komoditas: "Regional Commodities",
      },
    },
  };

  const { language } = useLanguageStore();
  const t = translations[language]; // Pilih terjemahan sesuai bahasa aktif

  const yearnow = new Date().getFullYear();
  const theyear = yearnow;

  const [activeTab, setActiveTab] = useState("pdrb");
  const [selectedYear, setSelectedYear] = useState(theyear);
  const [selectedYear2, setSelectedYear2] = useState(theyear);
  const [selectedSektor, setSelectedSektor] = useState("");
  const [selectedSubSektor, setSelectedSubSektor] = useState("");
  const [komoditasData, setKomoditasData] = useState([]);

  const tabs = ["pdrb", "investasi", "umr", "penduduk", "komoditas"];
  
  //make back years 4 year from now
  const backYears = Array.from({ length: 5 }, (_, i) => theyear - i);
  // console.log('array years', backYears)
  //const oldyears = theyear - 4; //array of years

  const komoditasYears = backYears;


  // const komoditasYears = ["2020", "2021", "2022", "2023", "2024"];

  // const getAvailableYears = () => {
  //   switch (activeTab) {
  //     case "pdrb":
  //       return Object.keys(daerahDetil?.pdrb || {});
  //     case "umr":
  //       return (
  //         [
  //           ...new Set(daerahDetil?.umr?.map((item) => item.tahun.toString())),
  //         ] || []
  //       );
  //     case "penduduk":
  //       return (
  //         [
  //           ...new Set(
  //             daerahDetil?.penduduk?.map((item) => item.tahun.toString())
  //           ),
  //         ] || []
  //       );
  //     case "investasi":
  //       return (
  //         [
  //           ...new Set(
  //             daerahDetil?.investasi?.by_year?.map((item) =>
  //               item.tahun.toString()
  //             )
  //           ),
  //         ] || []
  //       );
  //     case "komoditas":
  //       return komoditasYears;
  //     default:
  //       return [];
  //   }
  // };

  const getAvailableYears = () => {
    switch (activeTab) {
      case "pdrb":
        case "komoditas":
          case "investasi":
              return komoditasYears;
      default:
        return [];
    }
  };


  const years = getAvailableYears();
  const sectors = daerahDetil?.daerahDetil?.komoditi?.ref_sektor || [];

  const handleFetchKomoditas = async (subSektor, year) => {
    if (id && subSektor && year) {
      try {
        const response = await getKomoditasDaerahProv(
          id,
          subSektor,
          year
        );
        if (response.data) {
          setKomoditasData(response.data);
        }
      } catch (error) {
        console.error("Failed to fetch komoditas data:", error);
      }
    }
  };

  // Effect for setting initial values and fetching data
  useEffect(() => {
    const availableYears = getAvailableYears();
    if (availableYears.length > 0) {
      setSelectedYear(availableYears[0]);
    }

    // Set default sector and sub-sector when tab changes to komoditas
    if (activeTab === "komoditas" && sectors.length > 0) {
      const firstSector = sectors[0];
      setSelectedSektor(firstSector.id_sektor.toString());
      
      if (firstSector.sub_sektor && firstSector.sub_sektor.length > 0) {
        const firstSubSector = firstSector.sub_sektor[0];
        setSelectedSubSektor(firstSubSector.id_sub_sektor.toString());
        
        // Fetch data with default values
        handleFetchKomoditas(
          firstSubSector.id_sub_sektor.toString(),
          availableYears[0]
        );
      }
    } else {
      setKomoditasData([]);
    }
  }, [activeTab, id]);

  // Effect for handling year changes
  useEffect(() => {
    if (activeTab === "komoditas" && selectedSubSektor) {
      handleFetchKomoditas(selectedSubSektor, selectedYear);
    }
  }, [selectedYear, activeTab]);

  // Effect for handling sub-sector changes
  useEffect(() => {
    if (activeTab === "komoditas" && selectedSubSektor && selectedYear) {
      handleFetchKomoditas(selectedSubSektor, selectedYear);
    }
  }, [selectedSubSektor]);

  // Get available sub-sectors based on selected sector
  const availableSubSectors = selectedSektor
    ? sectors.find((sector) => sector.id_sektor.toString() === selectedSektor)
        ?.sub_sektor || []
    : [];

  // Handler for sector changes
  const handleSectorChange = (value) => {
    setSelectedSektor(value);
    const newSector = sectors.find((sector) => sector.id_sektor.toString() === value);
    if (newSector?.sub_sektor?.length > 0) {
      setSelectedSubSektor(newSector.sub_sektor[0].id_sub_sektor.toString());
    } else {
      setSelectedSubSektor("");
    }
  };

  return (
    <div className="pt-4 pb-10 2xl:px-60 xl:px-20 md:px-20">
      <p className="p-3 text-xl shadow-md font-bold font-montserrat text-white bg-[#4CA85F] rounded-md mb-4">
        {t.daerahDetil}
      </p>
      <div className="flex flex-col items-center justify-center max-w-8xl rounded-xl">
        <div className="flex flex-wrap justify-center space-x-4">
          {tabs.map((tab) => (
            <Button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-6 py-4 text-xs font-montserrat ${
                activeTab === tab
                  ? "bg-[#2D3B96] text-white"
                  : "bg-white text-black border border-gray-200"
              }`}
            >
              {t.tabs[tab].toUpperCase()}
            </Button>
          ))}
        </div>

        {(() => {
          if (activeTab === "komoditas") {
            return (
              <div className="w-full px-6 py-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <Select
                    value={selectedSektor}
                    onValueChange={handleSectorChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih Sektor" />
                    </SelectTrigger>
                    <SelectContent>
                      {sectors.map((sector) => (
                        <SelectItem
                          key={sector.id_sektor}
                          value={sector.id_sektor.toString()}
                        >
                          {sector.nama_sektor}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

              <Select
                value={selectedSubSektor}
                onValueChange={setSelectedSubSektor}
                disabled={!selectedSektor}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih Sub-Sektor" />
                </SelectTrigger>
                <SelectContent>
                  {availableSubSectors.map((subSector) => (
                    <SelectItem
                      key={subSector.id_sub_sektor}
                      value={subSector.id_sub_sektor.toString()}
                    >
                      {subSector.nama_sub_sektor}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={selectedYear}
                onValueChange={setSelectedYear}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih Tahun" />
                </SelectTrigger>
                <SelectContent>
                  {years.map((year) => (
                    <SelectItem key={year} value={year}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

                <Barchart
                  data={komoditasData}
                  type="komoditas"
                  selectedYear={selectedYear}
                />
              </div>
            );
          } else if (activeTab === "pdrb") {
            return (
              <div className="w-full px-6 py-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <Select
                    value={selectedYear2}
                    onValueChange={setSelectedYear2}
                  >
                    <SelectTrigger className="h-8 w-[140px] ml-10 mt-4">
                      <SelectValue placeholder="Pilih Tahun" />
                    </SelectTrigger>
                    <SelectContent>
                      {years.map((year) => (
                        <SelectItem key={year} value={year}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <Barchart
                  data={daerahDetil?.daerahDetil}
                  type={activeTab}
                  selectedYear={selectedYear2}
                />
              </div>
            );
          } else if (activeTab === "investasi") {
            console.log('daerah detail',daerahDetil?.daerahDetil?.investasi);
            
            return (
              <div className="w-full px-6 py-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <Select
                    value={selectedYear2}
                    onValueChange={setSelectedYear2}
                  >
                    <SelectTrigger className="h-8 w-[140px] ml-10 mt-4">
                      <SelectValue placeholder="Pilih Tahun" />
                    </SelectTrigger>
                    <SelectContent>
                      {years.map((year) => (
                        <SelectItem key={year} value={year}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <Barchart
                  data={daerahDetil?.daerahDetil}
                  type={activeTab}
                  selectedYear={selectedYear2}
                />
              </div>
            );
          } else {
            return (
              <div className="w-full px-6 py-4">
                <Barchart
                  data={daerahDetil?.daerahDetil}
                  type={activeTab}
                  selectedYear={selectedYear2}
                />
              </div>
            );

          }
        })()}
      </div>
    </div>
  );
};

export default DaerahDetil;