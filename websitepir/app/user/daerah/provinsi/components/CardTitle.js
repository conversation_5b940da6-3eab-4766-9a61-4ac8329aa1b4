"use client";
import React, { useEffect, useState } from "react";
import { getReferensiService } from "@/services/AllService";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import Image from "next/image";
import { useRouter } from "next/navigation"; // Import useRouter from next/navigation
import useNavStore from "@/store/NavStore";

const CardTitle = ({ data, id }) => {
  // const [filteredKabkot, setFilteredKabkot] = useState([]);
  const [selectedKabupaten, setSelectedKabupaten] = useState("");
  const router = useRouter(); // Initialize router
  const { setCurrentPage } = useNavStore();

  const handleKabupatenChange = (value) => {
    console.log('value', value);

    setSelectedKabupaten(value);
    if (value) {
      // Redirect to the specific route when a kabupaten is selected
      router.push(`/daerah/kabupaten/${value}`);
    }
  };


  return (
    <div className="flex flex-col items-center justify-center ml-24">
      <div className="pt-12 text-center rounded-lg">
        <h1 className="font-bold text-[40px] text-white pt-4">
          {data?.nama_provinsi}
        </h1>

        <div className="relative w-full h-[240px]">
          <Image
            src={data?.icon}
            alt="image"
            layout="fill"
            className="object-contain rounded-xl"
          />
        
        </div>

        {/* <div className="mt-6">
          <span className="px-6 py-2 font-bold font-montserrat text-blue-800 bg-white rounded-lg">
            {data?.luas}
          </span>
        </div> */}
      </div>
    </div>
  );
};

export default CardTitle;
