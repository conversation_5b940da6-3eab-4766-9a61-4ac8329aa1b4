import FormUnduhDokumen from "@/app/user/daerah/kawasan/components/FormUnduhDokumen";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import React, { useState } from "react";

const InfografisCard = ({ data }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [imgSrc, setImgSrc] = useState(data?.nama || "/no-data-image.png");

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  // Prevent right click
  const preventDefault = (e) => {
    e.preventDefault();
  };
  
  // Handle image error
  const handleImageError = () => {
    setImgSrc("/no-data-image.png");
  };

  return (
    <div className="flex flex-col justify-between h-[500px] border rounded-lg w-[300px]">
      <div
        className="relative p-2 select-none group" // Add group here
        onContextMenu={preventDefault}
        onDragStart={preventDefault}
      >
        <Image
          src={imgSrc}
          width={300}
          height={100}
          className="object-cover w-full h-[480px] rounded-lg"
          onContextMenu={preventDefault}
          draggable="false"
          style={{
            WebkitTouchCallout: "none",
            WebkitUserSelect: "none",
            KhtmlUserSelect: "none",
            MozUserSelect: "none",
            msUserSelect: "none",
            userSelect: "none",
          }}
          alt="Protected image"
          onError={handleImageError}
        />
        {/* Overlay div - hidden by default, shown on hover */}
        <div className="absolute inset-0 flex items-center justify-center transition-opacity duration-300 bg-black rounded-lg opacity-0 bg-opacity-70 group-hover:opacity-100">
          {/* <div className="flex items-center justify-center">
            <FormUnduhDokumen onClose={closeModal} />
          </div> */}
        </div>
      </div>
    </div>

  );
};

export default InfografisCard;
