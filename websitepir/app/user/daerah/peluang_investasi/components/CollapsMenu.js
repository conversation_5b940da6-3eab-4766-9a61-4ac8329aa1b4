import InfografisCard from "./InfografisCard";
import ClientFormUnduhDokumen from './ClientFormUnduhDokumen';
import FormUnduhDokumen from "@/app/user/daerah/kawasan/components/FormUnduhDokumen";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Image from "next/image";
import React, { useState, useEffect } from "react";
import useLanguageStore from "@/app/user/stores/languageStore";

const CollapsMenu = ({ data }) => {
  const translations = {
    id: {
      info: "Infografis & Infomemo",
      galeri: "Galeri"
    },
    en: {
      info: "Infographic & Infomemo",
      galeri: "Gallery"
    },
  };

  const { language } = useLanguageStore();
  const t = translations[language];
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentImage, setCurrentImage] = useState(null);
  const [galleryImages, setGalleryImages] = useState([]);

  // Initialize gallery images with fallback handling
  useEffect(() => {
    if (data?.galeri && Array.isArray(data.galeri)) {
      const images = data.galeri.map(item => ({
        ...item,
        imgSrc: item.image || "/no-data-image.png"
      }));
      setGalleryImages(images);
    } else {
      setGalleryImages([]);
    }
  }, [data?.galeri]);

  const openModal = (image) => {
    setCurrentImage(image);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setCurrentImage(null);
  };

  // Handle image error
  const handleImageError = (index) => {
    setGalleryImages(prev => {
      const updated = [...prev];
      if (updated[index]) {
        updated[index] = {
          ...updated[index],
          imgSrc: "/no-data-image.png"
        };
      }
      return updated;
    });
  };

  return (
    <div className="flex justify-center pt-10 pb-10 2xl:px-60 xl:px-20 md:px-20">
      <div className="flex flex-col items-center justify-center border max-w-8xl rounded-xl">
        <div className="w-full max-w-7xl ">
          {/* <Accordion
            type="single"
            defaultValue="item-1"
            collapsible
            className="w-full"
          >
            <AccordionItem value="item-1" className="">
              <AccordionTrigger className="flex justify-between w-full px-6 py-4 text-left border-b-[1px] font-montserrat font-bold text-[#2D3B96]">
                <span className="text-lg">Insentif</span>
              </AccordionTrigger>
              <AccordionContent className="px-6 py-4 text-black font-montserrat">
                {data.insentif && data.insentif.length > 0 ? (
                  data.insentif.map((item, index) => (
                    <div
                      key={index}
                      className="flex flex-col justify-center gap-4"
                    >
                      <span className="text-lg font-bold">
                        {item.nama.replace(/(<([^>]+)>)/gi, "") ||
                          "Data Tidak Tersedia"}
                      </span>
                      <span className="mb-3 ml-4 text-lg">
                        {item.keterangan.replace(/(<([^>]+)>)/gi, "") ||
                          "Data Tidak Tersedia"}
                      </span>
                    </div>
                  ))
                ) : (
                  <div className="flex flex-col justify-center gap-4">
                    <span className="text-lg font-bold">
                      Data Tidak Tersedia
                    </span>
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>
          </Accordion> */}
        </div>

        <div className="w-full max-w-7xl ">
          <Accordion
            type="single"
            defaultValue="item-1"
            collapsible
            className="w-full"
          >
            <AccordionItem value="item-1" className="">
              <AccordionTrigger className="flex justify-between w-full px-6 py-4 text-left border-b-[1px] font-montserrat font-bold text-[#2D3B96]">
                <span className="flex items-center justify-center gap-5">
                  {t.info} <div><ClientFormUnduhDokumen jenisKonten="peluang_daerah" /></div>
                </span>
              </AccordionTrigger>
              <AccordionContent className="gap-2 px-6 pt-4">
                <div className="flex flex-wrap gap-2">
                  {data.infografis && data.infografis.length > 0 ? (
                    data.infografis.map((item, index) => (
                      <InfografisCard key={index} data={item} />
                    ))
                  ) : (
                    <div className="flex flex-col justify-center gap-4">
                      <span className="text-lg font-bold">
                        {language === "id" ? "Data Tidak Tersedia" : "Data Not Available"}
                      </span>
                    </div>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>

        <div className="w-full max-w-7xl">
          <Accordion
            type="single"
            defaultValue="item-1"
            collapsible
            className="w-full"
          >
            <AccordionItem value="item-1" className="">
              <AccordionTrigger className="flex justify-between w-full px-6 py-4 text-left border-b-[1px] font-montserrat font-bold text-[#2D3B96]">
                <span>{t.galeri}</span>
              </AccordionTrigger>
              <AccordionContent className="px-6 py-4">
                <div className="flex justify-center">
                  <Carousel
                    opts={{
                      align: "start",
                      infinite: true,
                    }}
                    className="w-full max-w-5xl mx-auto"
                  >
                    <CarouselContent className="-ml-2 md:-ml-4">
                      {galleryImages.map((item, index) => (
                        <CarouselItem
                          key={index}
                          className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/4"
                        >
                          <div
                            className="pt-6 cursor-pointer"
                            onClick={() => openModal(item.imgSrc)}
                          >
                            <Image
                              src={item.imgSrc}
                              className="w-[200px] h-[150px] !rounded-none"
                              alt={`Gallery image ${index + 1}`}
                              width={1200}
                              height={675}
                              style={{ borderRadius: 0 }}
                              onError={() => handleImageError(index)}
                            />
                           
                          </div>
                        </CarouselItem>
                      ))}
                    </CarouselContent>
                    <CarouselPrevious />
                    <CarouselNext />
                  </Carousel>

                  {isModalOpen && (
                    <div
                      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
                      onClick={closeModal}
                    >
                      <div className="max-w-screen-xl max-h-screen p-4">
                        <Image
                          src={currentImage || "/jokowi_ikn.png"}
                          alt="Full screen image"
                          layout="responsive"
                          width={16}
                          height={9}
                          objectFit="contain"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
    </div>
  );
};

export default CollapsMenu;
