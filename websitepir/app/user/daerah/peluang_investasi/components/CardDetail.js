import LikeButton from "./LikeButton";
import useLanguageStore from "@/app/user/stores/languageStore";
import { But<PERSON> } from "@/components/ui/button";
import {
  postPIDLike,
  getPIDLikeService,
  getIsPIDLikeService,
} from "@/services/LikeService";
import { Eye, Heart, MapPin } from "lucide-react";
import Image from "next/image";
import React, { useEffect, useState } from "react";

const CardDetail = ({ data, id }) => {
  const [likes, setLikes] = useState(0);
  const [isLiked, setIsLiked] = useState(false);
  const [imgSrc, setImgSrc] = useState(data?.detail?.image || "/no-data-image.png");

  // Handle image error
  const handleImageError = () => {
    setImgSrc("/no-data-image.png");
  };

  useEffect(() => {
    const fetchLikesAndStatus = async () => {
      try {
        // Fetch jumlah likes
        const likesResponse = await getPIDLikeService(id);
        setLikes(likesResponse.data.jumlah);

        // Fetch status is_like
        const isLikedResponse = await getIsPIDLikeService(id);
        setIsLiked(isLikedResponse.data.is_like);
      } catch (error) {
        console.error("Error fetching likes or like status:", error);
      }
    };

    fetchLikesAndStatus();
  }, [id]);

  // Update image source when data changes
  useEffect(() => {
    setImgSrc(data?.detail?.image || "/no-data-image.png");
  }, [data?.detail?.image]);

  return (
    <div className="flex items-center justify-center gap-32 pt-20 mx-10 max-w-8xl">
      <div className="flex flex-col">
        <span className="text-[#4CA85F] font-montserrat font-bold text-4xl max-w-md">
          {data?.detail?.judul || ""}
        </span>
        <span className="text-[#828282] font-montserrat text-2xl mt-5 max-w-md">
          {data?.detail?.lokasi_kawasan || ""}
        </span>
        <div className="flex text-[#2D3A96] font-bold font-montserrat mt-4">
          <MapPin />
          <span className="ml-2">{data?.detail?.nama_provinsi || ""}</span>
        </div>
        <div className="flex items-center gap-4 mt-4">
          <LikeButton id_peluang_daerah={id} />

          {/* Tampilan total_pengunjung mirip dengan LikeButton */}
          <div className="flex items-center gap-2 px-4 py-2 mt-4 rounded-lg bg-[#4CA85F] hover:bg-[#3B884C] text-white font-montserrat text-md">
            <Eye size={20} color="#fff" /> {/* Icon mata */}
            <span>{data.detail.total_pengunjung}</span>
          </div>
        </div>
      </div>
      <div className="relative pb-10 max-w-content">
        <Image
          src={imgSrc}
          width={800}
          height={400}
          className="rounded-xl w-[700px] h-[430px] object-cover"
          alt={data?.detail?.judul || "image"}
          onError={handleImageError}
        />

        <div className="absolute top-4 right-4 font-bold bg-white border rounded-lg font-montserrat text-xl text-[#2D3A96] px-7 py-2 max-w-xl">
          {data.detail.nama_sektor}
        </div>
      </div>
    </div>
  );
};

export default CardDetail;

// const LikeButton = ({ id_peluang_daerah, setLikes, isLiked, setIsLiked }) => {
//   const handleLike = async () => {
//     try {
//       const response = await postPIDLike({ id_peluang_daerah });
//       console.log("Response:", response.data);
//       alert("Proyek berhasil disukai!");

//       // Perbarui jumlah like dan status isLiked
//       const updatedLikes = await getPIDLikeService(id_peluang_daerah);
//       setLikes(updatedLikes.data.jumlah);
//       setIsLiked(true);
//     } catch (error) {
//       console.error("Error liking the project:", error);
//       alert("Terjadi kesalahan saat menyukai proyek.");
//     }
//   };

//   return (
//     <Button
//       className={`justify-start font-montserrat w-48 mt-4 text-white text-md ${
//         isLiked ? "bg-gray-400 cursor-not-allowed" : "bg-[#4CA85F] hover:bg-[#2D3A96]"
//       }`}
//       variant="primary"
//       onClick={isLiked ? null : handleLike}
//       disabled={isLiked}
//     >
//       <Heart size={32} fill={isLiked ? "#4CA85F" : "none"} color="#4CA85F" />
//       {isLiked ? "Proyek Disukai" : "Sukai Proyek Ini"}
//     </Button>
//   );
// };
