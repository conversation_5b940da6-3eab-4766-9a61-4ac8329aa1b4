"use client";

import useLanguageStore from "@/app/user/stores/languageStore";
import { MapPin, ThumbsUp, Calendar, ChartColumnBig } from "lucide-react";
import Image from "next/image";
import React, { useState } from "react";

const CardPeluangInvestasi = ({
  title,
  location,
  image,
  nama_sektor,
  IRR,
  nilai_investasi,
  NPV,
  status,
  status_proyek,
}) => {
  const { language } = useLanguageStore();
  const [imgSrc, setImgSrc] = useState(image != null ? image : "/peluang_investasi/industri.png");
  
  // Handle image error
  const handleImageError = () => {
    setImgSrc("/no-data-image.png");
  };
  
  return (
    <div className="w-[550px] max-h-[350px] mt-5 cursor-pointer">
      <div className="overflow-hidden transition-all duration-300 bg-white rounded-lg shadow-lg hover:shadow-xl dark:bg-gray-950 h-[310px]">
        <div className="relative">
          <Image
            src={imgSrc}
            alt="Product Image"
            width={400}
            height={150}
            className="object-cover w-full h-40"
            onError={handleImageError}
          />
          <div className="absolute inset-0 bg-black opacity-40"></div>
          {status_proyek && (
            <div className="absolute top-0 left-0 w-full">
              <div
                className={`absolute ${
                  status_proyek === "READY TO OFFER"
                    ? "top-8 -left-9"
                    : "top-6 -left-7"
                } transform -rotate-45 bg-red-600 text-white font-bold text-xs py-1 px-8 z-20 shadow-md`}
              >
                {status_proyek.toUpperCase()}
              </div>
            </div>
          )}
          <div className="absolute inset-0 z-20 p-3 flex items-start justify-end text-[#4CA85F]">
            <span className="px-2 py-1 bg-white rounded-lg font-montserrat">
              {nama_sektor}
            </span>
          </div>
        </div>

        <div className="p-4 space-y-2">
          <div className="flex items-center justify-center justify-between">
            <div>
              <h3 className="text-[16px] font-semibold font-montserrat text-[#2D3A96]">
                {title}
              </h3>
              <div className="flex text-[#2D3A96] font-montserrat text-sm">
                <MapPin color="#2D3A96" size={20} className="mr-1" />
                {location}
              </div>
            </div>
            {/* <ThumbsUp color="#2D3A96" size={20} className="mr-1" /> */}
          </div>
          <div className="flex items-center justify-between">
            <div className="rounded-full bg-[#FFFFFF] p-2 w-10 h-10 flex items-center justify-center drop-shadow-lg">
              <ChartColumnBig color="black" size={20} />
            </div>
            {/* <Image src={'/icons/PeluangBiru.svg'} alt="Product Image" width={50} height={50} className="object-cover w-10 h-10" /> */}
            <div className="font-montserrat text-[#2D3A96] ml-2 mr-4">
              <h1 className="">{language === "id" ? "Nilai Investasi" : "Investment Value"}</h1>
              <p>{nilai_investasi}</p>
            </div>
            <div className="rounded-full bg-[#27AE60] p-2 w-10 h-10 flex items-center justify-center drop-shadow-lg">
              <ChartColumnBig color="#FFFFFF" size={20} />
            </div>
            <div className="font-montserrat text-[#2D3A96] ml-2 mr-4">
              <h1 className="">IRR</h1>
              <p>{IRR}</p>
            </div>
            <div className="rounded-full bg-[#2D3A96] p-2 w-10 h-10 flex items-center justify-center drop-shadow-lg">
              <ChartColumnBig color="#FFFFFF" size={20} />
            </div>
            <div className="flex bg-[#FF9500] px-2 py-1 text-white rounded-lg content-center justify-center items-center">
              <p className="text-sm font-medium font-montserrat">{status}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardPeluangInvestasi;
