"use client";

import useLanguageStore from "@/app/user/stores/languageStore";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import axios from "axios";
import { Info } from "lucide-react";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useState, useEffect } from "react";

const Hero = ({ title, link }) => {
  const router = useRouter();
  const [data, setData] = useState([]);
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(searchParams.get("q") || "");
  const [currentIndex, setCurrentIndex] = useState(0);
  const translations = {
    id: {
      pid: "Peluang Investasi Daerah",
      home: "Beranda",
      region: "Daerah",
    },
    en: {
      pid: "Regional Investment Opportunities",
      home: "Home",
      region: "Regional",
    },
  };

  const { language } = useLanguageStore();
  const t = translations[language];

  return (
    <div>
      <section className="relative flex flex-col items-center justify-start min-h-44">
        <div className="absolute z-10 flex items-center justify-between bg-[#2D3B96] w-full h-[200px]">
          <div className="ml-[150px]">
            <h1 className="text-white font-bold font-montserrat 2xl:text-[40px] xl:text-[35px] md:text-[30px] sm:text-[30px]">
              {" "}
              {t.pid}
            </h1>
          </div>
          <div className="text-xl text-white font-montserrat">
            <Breadcrumb className="mr-[150px]">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/" className="hover:text-white">
                  {t.home}
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink
                    href={`/daerah`}
                    className="hover:text-white"
                  >
                    {t.region}
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink
                    onClick={() => router.refresh()}
                    className=" whitespace-nowrap cursor-pointer hover:text-white w-96"
                  >
                    {link}
                  </BreadcrumbLink>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Hero;
