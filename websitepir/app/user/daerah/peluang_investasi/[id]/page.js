"use client";

import ChatBox from "../../../components/ChatBox";
import Footer from "../../../components/Footer";
import Navbar from "../../../components/Navbar";
import CardDetail from "../components/CardDetail";
import CollapsMenu from "../components/CollapsMenu";
import Description from "../components/Description";
import Hero from "../components/Hero";
import Kontak from "../components/Kontak";
import MapIKN from "../components/Map";
import ProyekTerkait from "../components/ProyekTerkait";
import { getPeluangInvestasiDaerahDetailByID } from "@/services/getHomeServices";
import useNavStore from "@/store/NavStore";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import usePeluangInvestasiDetails from "@/store/PeluangInvestasiDetail";
import { postCountViewerService } from "@/services/LikeService";
import useLanguageStore from "@/app/user/stores/languageStore";
import LoadingComponent from "../../../components/Loading";

const Loading = () => {
  return (
    <div className="fixed top-0 left-0 z-50 flex items-center justify-center w-full h-full bg-white">
      <div className="w-32 h-32 border-b-2 border-gray-900 rounded-full animate-spin"></div>
    </div>
  );
};

export default function DetailPeluangInvestasi() {
  const { id } = useParams();
  const { language } = useLanguageStore();
  const [loading, setLoading] = useState(true);

  const [data, setData] = useState({
    detail: {
      judul: "",
      longitude: 106.8456,
      latitude: -6.2088,
      video: ""
    },
    deskripsi: "",
    aspek_teknis: "",
    aspek_pasar: "",
    galeri: [
      { image1: "something.jpg" },
      { image1: "something.jpg" },
      { image1: "something.jpg" },
    ],
  });

  const { setCurrentPage } = useNavStore();

  // useEffect(() => {
  //   // Fetch data from service on mount
  //   const fetchData = async () => {
  //     const response = await getPeluangInvestasiDaerahDetailByID(id, language);
  //     setData(response);
  //   };

  //   const postCountViewer = async () => {
  //     try {
  //       await postCountViewerService({
  //         id_halaman_pengunjung: 8,
  //         id_peluang: id,
  //       });
  //     } catch (error) {
  //       console.error("Error posting count viewer:", error);
  //     }
  //   };

  //   fetchData();
  //   postCountViewer();
  //   setCurrentPage("daerah");
  // }, [id, language]);

    useEffect(() => {
      const fetchData = async () => {
        try {
          const response = await getPeluangInvestasiDaerahDetailByID(id, language);
          setData(response);
  
          await postCountViewerService({
            id_halaman_pengunjung: 8,
            id_konten: id,
          });
        } catch (error) {
          console.error("Error fetching data:", error);
        } finally {
          setLoading(false);
        }
      };
  
      fetchData();
      setCurrentPage("daerah");
    }, [id, language]);

    if (loading) {
      return <></>
      return <Loading />;
    }
    
  return (
    <>
      <ChatBox />
      <Navbar />
      <LoadingComponent/>
      <Hero
        title="Peluang Investasi Daerah"
        backgroundImage={"/no-data-image.png"}
        link={data?.detail?.judul || ""}
      />
      <CardDetail data={data} id={id} />
      <MapIKN
        data={data}
        lon={data?.detail?.longitude || 0}
        lat={data?.detail?.latitude || 0}
      />
      <Description data={data} />
      <CollapsMenu data={data} />
      <Kontak data={data} />
      <ProyekTerkait data={data} />
      <Footer />
    </>
  );
}
