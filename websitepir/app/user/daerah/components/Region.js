"use client";

import CardDaerah from "./CardDaerah";
import Map3 from "./Map3";
import SidebarMap from "./SidebarMap";
import { getBagianDaerahService } from "@/services/DaerahService";
import { getProvByZonaWaktuService } from "@/services/SidebarMapService";
import useMapStore from "@/store/UseMapStore";
import { useRouter } from "next/navigation";
import React, { useState, useEffect } from "react";
import MapLayer from "../../peluang_investasi/components/MapLayer";
import useLanguageStore from "../../stores/languageStore";

const Region = () => {
  const translations = {
    id: {
      zoneTitle: "Wilayah Terpilih",
      provinceSelected: "Provinsi Terpilih",
      wib: "Wilayah Indonesia Bagian Barat",
      wita: "Wilayah Indonesia Bagian Tengah",
      wit: "Wilayah Indonesia Bagian Timur",
      navigateText: "Navigasi ke",
    },
    en: {
      zoneTitle: "Selected Region",
      provinceSelected: "Selected Province",
      wib: "Western Indonesia Region",
      wita: "Central Indonesia Region",
      wit: "Eastern Indonesia Region",
      navigateText: "Navigate to",
    },
  };

  const { language } = useLanguageStore();
  const t = translations[language];
  const [dataWIB, setDataWIB] = useState([]);
  const [dataWITA, setDataWITA] = useState([]);
  const [dataWIT, setDataWIT] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [zoneData, setZoneData] = useState([]);
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  // Get selected values from store
  const { selectedZona, selectedProvinsi } = useMapStore();
  const resetStore = useMapStore(state => state.fetchZonaData);

  // Reset selections when component mounts or language changes
  useEffect(() => {
    resetStore(null);
  }, [language, resetStore]);

  // Ambil data awal
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const response = await getBagianDaerahService(language);
        
        // Ekstrak data berdasarkan zona waktu
        // const wib = response.data[0]["waktu indonesia barat (gmt +7)"] || [];
        // const wita = response.data[2]["waktu indonesia tengah (gmt + 8)"] || [];
        // const wit = response.data[1]["waktu indonesia timur (gmt +9)"] || [];
        // console.log('ini respon get all prov bawah',response.data);
        const wib = response.data[0] ? Object.values(response.data[0])[0] || [] : [];
        const wita = response.data[2] ? Object.values(response.data[1])[0] || [] : [];
        const wit = response.data[1] ? Object.values(response.data[2])[0] || [] : [];
        
        setDataWIB(wib);
        setDataWITA(wita);
        setDataWIT(wit);
        // console.log('ini respon get all prov bawah wib',wib );
        // console.log('ini respon get all prov bawah wita',wita);
        // console.log('ini respon get all prov bawah wit',wit);
        
        setIsLoading(false);
      } catch (error) {
        // console.error("Gagal mengambil data:", error);
        setIsLoading(false);
      }
    };

    fetchData();
  }, [language]);

  // Pencarian data berdasarkan zona waktu
  useEffect(() => {
    const fetchZonaData = async () => {
      if (!selectedZona) {
        setFilteredData([]);
        setZoneData([]);
        return;
      }

      try {
        setIsLoading(true);
        const response = await getProvByZonaWaktuService(selectedZona, language);
        const zonaProvIds = response.data.map((prov) => prov.id_adm_provinsi);

        setZoneData(response.data);

        const filtered = [...dataWIB, ...dataWITA, ...dataWIT].filter((item) =>
          zonaProvIds.includes(item.id_adm_provinsi)
        );

        setFilteredData(filtered);
        setIsLoading(false);
      } catch (error) {
        console.error("Gagal mengambil data zona:", error);
        setIsLoading(false);
      }
    };

    fetchZonaData();
  }, [selectedZona, dataWIB, dataWITA, dataWIT, language]);
  
  // Fungsi navigasi ke halaman detail provinsi
  const handleProvinceClick = (province) => {
    if (province) {
      router.push(`/daerah/provinsi/${province.id_adm_provinsi}`);
    }
  };

  // Render data zona sesuai kondisi
  const renderZoneSection = (title, data) => {
    if (!data || data.length === 0) return null;
    
    return (
      <div className="space-y-10">
        <p className="pt-8 text-2xl font-bold font-montserrat text-[#2D3B96]">
          {title}
        </p>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-4">
          {data.map((item) => (
            <CardDaerah
              key={item.id_adm_provinsi}
              iconDaerah={item.image}
              data={{
                id: item.id_adm_provinsi,
                nama: item.nama,
                luasWilayah: item.luas_wilayah,
                peluang: item.peluang,
                realisasi: item.realisasi,
              }}
              onClick={() => handleProvinceClick(item)}
            />
          ))}
        </div>
      </div>
    );
  };

  // Menentukan nama zona berdasarkan selectedZona
  const getZonaName = (zona) => {
    switch (zona) {
      case 1:
        return t.wib;
      case 2:
        return t.wita;
      case 3:
        return t.wit;
      default:
        return t.zoneTitle;
    }
  };

  // Filter untuk menampilkan hanya provinsi yang dipilih jika ada
  const renderFilteredData = () => {
    if (isLoading) {
      return <div>Loading...</div>;
    }

    // Jika hanya provinsi yang dipilih tanpa zona, tampilkan provinsi tersebut langsung
    if (selectedProvinsi && !selectedZona) {
      const selectedProvince = [...dataWIB, ...dataWITA, ...dataWIT].find(
        (item) => item.id_adm_provinsi === selectedProvinsi
      );
      return selectedProvince
        ? renderZoneSection("Provinsi Terpilih", [selectedProvince])
        : null;
    }

    // Jika zona dipilih, tampilkan sesuai zona
    if (selectedZona) {
      if (selectedProvinsi) {
        // Jika provinsi juga dipilih, tampilkan hanya provinsi tersebut
        const selectedProvince = filteredData.find(
          (item) => item.id_adm_provinsi === selectedProvinsi
        );
        return selectedProvince
          ? renderZoneSection(
            language === "id" ? "Provinsi Terpilih" : "Selected Province",
            [selectedProvince]
          )
          : null;
      }

      // Jika tidak ada provinsi yang dipilih, tampilkan provinsi berdasarkan zona
      const zonaName = getZonaName(selectedZona);
      return renderZoneSection(
        language === "id"
          ? `Provinsi Berdasarkan ${zonaName}`
          : `Province Based on ${zonaName}`,
        filteredData
      );
    }

    // Default: tampilkan semua zona waktu
    return (
      <>
        {dataWIB && dataWIB.length > 0 && renderZoneSection(t.wib, dataWIB)}
        {dataWITA && dataWITA.length > 0 && renderZoneSection(t.wita, dataWITA)}
        {dataWIT && dataWIT.length > 0 && renderZoneSection(t.wit, dataWIT)}
      </>
    );
  };

  return (
    <>
      <div className="flex mt-10 space-x-4 px-[100px]">
        <SidebarMap />
        <div className="flex-1">
          <Map3 />
          <div className="flex justify-center items-center pt-2 font-montserrat">
            <div className="h-6 w-6 mx-2 bg-[#CBB21A]"></div>
            <p className="mr-4">{t.wib}</p>
            <div className="h-6 w-6 mx-2 bg-[#AA34D0]"></div>
            <p className="mr-4">{t.wita}</p>
            <div className="h-6 w-6 mx-2 bg-[#6D702F]"></div>
            <p>{t.wit}</p>
          </div>
        </div>
      </div>

      <div className="pb-10 pt-2 2xl:px-60 xl:px-20 md:px-20">
        {renderFilteredData()}
      </div>
    </>
  );
};

export default Region;
