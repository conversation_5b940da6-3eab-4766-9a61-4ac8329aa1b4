"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandInput,
  CommandList,
  CommandItem,
  CommandEmpty,
} from "@/components/ui/command";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import useMapStore from "@/store/UseMapStore";
import { ChevronDown } from "lucide-react";
import React, { useEffect } from "react";
import useLanguageStore from "../../stores/languageStore";

// Configuration for dropdown menus
const DROPDOWN_CONFIG = (language) => [
  {
    key: "zona",
    label: { id: "Wilayah", en: "Region" },
    placeholder: { id: "Cari wilayah", en: "Search Region" },
    showAllLabel: { id: "Semua wilayah", en: "All Region" },
  },
    {
      key: "prov",
      label: { id: "Provinsi", en: "Province" },
      placeholder: { id: "Cari provinsi", en: "Search province" },
      showAllLabel: { id: "Semua Provinsi", en: "All Provinces" },
    },
    {
      key: "ppi",
      label: { id: "Peluang Investasi", en: "Investment Opportunities" },
      placeholder: { id: "Cari PPI", en: "Search PPI" },
      showAllLabel: { id: "Semua Peluang Investasi", en: "All Investment Opportunities" },
    },
    {
      key: "pid",
      label: { id: "Peluang Daerah", en: "Regional Opportunities" },
      placeholder: { id: "Cari PID", en: "Search PID" },
      showAllLabel: { id: "Semua Peluang Daerah", en: "All Regional Opportunities" },
    },
    {
      key: "ipro",
      label: { id: "Investment Project Ready to Offer", en: "Investment Project Ready to Offer" },
      placeholder: { id: "Cari IPRO", en: "Search IPRO" },
      showAllLabel: { id: "Semua IPRO", en: "All IPRO" },
    },
    {
      key: "kawasan",
      label: { id: "Kawasan Industri", en: "Industrial Zones" },
      placeholder: { id: "Cari kawasan", en: "Search industrial zones" },
      showAllLabel: { id: "Semua Kawasan Industri", en: "All Industrial Zones" },
    },
    {
      key: "kek",
      label: { id: "Kawasan Ekonomi Khusus", en: "Special Economic Zones" },
      placeholder: { id: "Cari KEK", en: "Search Special Economic Zones" },
      showAllLabel: { id: "Semua KEK", en: "All Special Economic Zones" },
    },
    {
      key: "bandara",
      label: { id: "Bandara", en: "Airport" },
      placeholder: { id: "Cari bandara", en: "Search airports" },
      showAllLabel: { id: "Semua Bandara", en: "All Airports" },
    },
    {
      key: "hotel",
      label: { id: "Hotel", en: "Hotel" },
      placeholder: { id: "Cari hotel", en: "Search hotels" },
      showAllLabel: { id: "Semua Hotel", en: "All Hotels" },
    },
    {
      key: "pelabuhan",
      label: { id: "Pelabuhan", en: "Port" },
      placeholder: { id: "Cari pelabuhan", en: "Search ports" },
      showAllLabel: { id: "Semua Pelabuhan", en: "All Ports" },
    },
    {
      key: "pendidikan",
      label: { id: "Pendidikan", en: "Education" },
      placeholder: { id: "Cari pendidikan", en: "Search education" },
      showAllLabel: { id: "Semua Pendidikan", en: "All Education" },
    },
    {
      key: "rumahSakit",
      label: { id: "Rumah Sakit", en: "Hospital" },
      placeholder: { id: "Cari rumah sakit", en: "Search hospitals" },
      showAllLabel: { id: "Semua Rumah Sakit", en: "All Hospitals" },
    },
    {
      key: "kabkot",
      label: { id: "Kabupaten/Kota", en: "Regency/City" },
      placeholder: { id: "Cari Kabupaten/Kota", en: "Search Regency/City" },
      showAllLabel: { id: "Semua Kabupaten/Kota", en: "All Regencies/Cities" },
    },
  ];
  

const FilterDropdown = ({
  config,
  isOpen,
  onOpenChange,
  items = [],
  selectedItem,
  onSelect,
  showAllOption = false,
}) => {
  const { language } = useLanguageStore();

  // Terjemahan label dan placeholder berdasarkan bahasa
  const getLabel = (label) => (language === 'id' ? label.id : label.en);
  const getPlaceholder = (placeholder) =>
    language === 'id' ? placeholder.id : placeholder.en;

  return (
    <div>
      <h2 className="text-sm font-montserrat font-medium px-4 pb-1 text-[#2D3A96]">{getLabel(config.label)}</h2>
      <Popover open={isOpen} onOpenChange={onOpenChange}>
        <PopoverTrigger asChild>
          <Button className="w-full font-montserrat justify-between bg-[#F1F1F1] text-[#2D3A96] hover:bg-[#D1D1D1]">
            {(selectedItem?.nama?.length > 30
              ? `${selectedItem.nama.slice(0, 30)}...`
              : selectedItem?.nama) || getLabel(config.showAllLabel)}
            <ChevronDown className="w-4 h-4 ml-2 text-[#2D3A96]" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full min-w-full p-0" align="start">
          <Command>
            <CommandInput placeholder={getPlaceholder(config.placeholder)} className="font-montserrat"/>
            <CommandList>
              <CommandEmpty>{language === 'id' ? 'Tidak ditemukan.' : 'Not found.'}</CommandEmpty>
              {showAllOption && (
                <CommandItem onSelect={() => onSelect(null, config.key)} className="font-montserrat">
                   {`${getLabel(config.showAllLabel)}`}
                </CommandItem>
              )}
              {Array.isArray(items) && items.map((item) => (
                <CommandItem
                  key={item.id || item.id_zona_waktu || item.id_adm_provinsi}
                  onSelect={() => {
                    onSelect(item, config.key);
                    onOpenChange(false);
                  }}
                  className="font-montserrat"
                >
                  {item.nama}
                </CommandItem>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
};

const SidebarMap = () => {
  const { language } = useLanguageStore();
  const dropdownConfig = DROPDOWN_CONFIG(language);
  const {
    zonaWaktu,
    provinsi,
    kabkot,
    selectedZona,
    selectedProvinsi,
    lists,
    popovers,
    selectedItems,
    initializeData,
    fetchZonaData,
    fetchProvinsiData,
    setPopoverState,
    handleListItemSelect,
  } = useMapStore();

  useEffect(() => {
    // Panggil initializeData setiap kali bahasa berubah
    initializeData();
  }, [language]); // Dependensi pada language


  const handleSelectZona = (zona) => {
    fetchZonaData(zona?.id_zona_waktu || null);
  };

  const handleSelectProvinsi = (prov) => {
    handleListItemSelect(prov, "prov");
  };

  const getSelectedZona = () =>
    zonaWaktu.find((z) => z.id_zona_waktu === selectedZona);

  const getSelectedProvinsi = () => {
    const { provinsi, selectedProvinsi } = useMapStore.getState();
    return (
      provinsi.find((item) => item.id_adm_provinsi === selectedProvinsi) || null
    );
  };
  const getSelectedKabko = () => {
    const { kabkot, selectedKabkot } = useMapStore.getState();
    console.log("kabkot: ", kabkot);
    console.log("selected kabkot: ", selectedKabkot);
    if (selectedKabkot === null) return null;
      return Array.isArray(kabkot) ? kabkot.find((item) => item.id_adm_kabkot === selectedKabkot) || null : null;
    };
  console.log("zona waktu sidebarmap",zonaWaktu);
  return (
    <div className="flex flex-col bg-white p-4 rounded-lg shadow-md w-80 flex-shrink-0 space-y-4">
      <FilterDropdown
        config={dropdownConfig[0]}
        isOpen={popovers.zona}
        onOpenChange={(isOpen) => setPopoverState("zona", isOpen)}
        items={zonaWaktu}
        selectedItem={getSelectedZona()}
        onSelect={handleSelectZona}
        showAllOption={true}
      />

      <FilterDropdown
        config={dropdownConfig[1]}
        isOpen={popovers.prov}
        onOpenChange={(isOpen) => setPopoverState("prov", isOpen)}
        items={provinsi}
        selectedItem={getSelectedProvinsi()}
        onSelect={handleSelectProvinsi}
        showAllOption={true}
      />

      <FilterDropdown
        config={dropdownConfig[12]}
        isOpen={popovers.kabkot}
        onOpenChange={(isOpen) => setPopoverState("kabkot", isOpen)}
        items={kabkot}
        selectedItem={getSelectedKabko()}
        onSelect={(item) => handleListItemSelect(item, "kabkot")}
        showAllOption={true}
        disabled={!selectedProvinsi}
      />

      {dropdownConfig.slice(2, 7).map((config) => {
        const key = config.key;
        return (
          <FilterDropdown
            key={key}
            config={config}
            isOpen={popovers[key]}
            onOpenChange={(isOpen) => setPopoverState(key, isOpen)}
            items={lists[key]}
            selectedItem={selectedItems[key]}
            onSelect={(item) => handleListItemSelect(item, key)}
            showAllOption={true}
          />
        );
      })}
    </div>
  );
};

export default SidebarMap;
