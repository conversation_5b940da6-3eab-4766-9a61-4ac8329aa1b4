import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'

const CardWithBackground = ({ title, description, image, onClick, kategori_sektor }) => {
  const [imageUrl, setImageUrl] = useState(image || '/no-data-image.png');

  // Fungsi untuk memeriksa apakah URL gambar valid
  const checkImageValidity = (url) => {
    if (!url) return false;
    
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
      img.src = url;
    });
  };

  useEffect(() => {
    // Jika image berubah, periksa validitasnya
    const validateImage = async () => {
      if (image) {
        const isValid = await checkImageValidity(image);
        if (!isValid) {
          setImageUrl('/no-data-image.png');
        } else {
          setImageUrl(image);
        }
      } else {
        setImageUrl('/no-data-image.png');
      }
    };

    validateImage();
  }, [image]);

  return (
    <motion.div 
      className="relative w-full h-64 overflow-hidden rounded-lg shadow-lg cursor-pointer group"
      initial={{ y: 0 }}
      whileHover={{ 
        y: [0, -15, 0],
        transition: { 
          y: {
            repeat: Infinity,
            duration: 1,
            ease: "easeInOut"
          }
        }
      }}
      animate={{ y: 0 }}
      transition={{
        type: "spring",
        stiffness: 500,
        damping: 30
      }}
      onClick={onClick}  
    >
      <div 
        className="absolute inset-0 bg-center bg-cover"
        style={{ backgroundImage: `url(${imageUrl})` }}
      ></div>
      <div className="absolute inset-0 transition-opacity duration-300 bg-black bg-opacity-40 group-hover:bg-opacity-50"></div>
      <div className="relative z-10 flex flex-col justify-between h-full p-6 text-white">
        <div className="h-1/3"></div>
        <div>
          <h3 className="text-xl font-semibold">{title || 'Untitled'}</h3>
          <p className="mt-2">{description || ''}</p>
        </div>
        <div className='absolute inset-0 z-20 p-3 flex items-start justify-end text-[#4CA85F]'>
            <span className='px-2 py-1 bg-white rounded-lg font-montserrat'>
              {kategori_sektor}
            </span>
        </div>
      </div>
    </motion.div>
  )
}

export default CardWithBackground
