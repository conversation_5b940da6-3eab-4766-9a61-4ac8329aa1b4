"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandInput,
  CommandList,
  CommandItem,
  CommandEmpty,
} from "@/components/ui/command";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import usePeluangInvestasiStore from "@/store/ListPeluangStore";
import usePeluangInvestasiIPROStore from "@/store/PeluangInvestasiIPROStore";
import usePeluangInvestasiMapStore from "@/store/PeluangInvestasiMapStore";
import { ChevronDown } from "lucide-react";
import React, { useEffect, useState } from "react";
import useLanguageStore from "../../stores/languageStore";

// Configuration for dropdown menus
const DROPDOWN_CONFIG2 = (language) => [
  {
    key: "zona",
    label: { id: "Wilayah", en: "Region" },
    placeholder: { id: "Cari Wilayah", en: "Search Region" },
    showAllLabel: { id: "Semua Wilayah", en: "All Region" },
  },
  {
    key: "prov",
    label: { id: "Provinsi", en: "Province" },
    placeholder: { id: "Cari provinsi", en: "Search province" },
    showAllLabel: { id: "Semua Provinsi", en: "All Provinces" },
  },
  {
    key: "kabkot",
    label: { id: "Kabupaten/Kota", en: "Regency/City" },
    placeholder: { id: "Cari Kabupaten/Kota", en: "Search Regency/City" },
    showAllLabel: { id: "Semua Kabupaten/Kota", en: "All Regencies/Cities" },
  },
  {
    key: "ppi",
    label: { id: "Peta Peluang Investasi", en: "Investment Opportunity Map" },
    placeholder: { id: "Cari PPI", en: "Search PPI" },
    showAllLabel: { id: "Semua PPI", en: "All Investment Opportunity Maps" },
  },
  {
    key: "ipro",
    label: {
      id: "Investment Project Ready to Offer",
      en: "Investment Project Ready to Offer",
    },
    placeholder: { id: "Cari IPRO", en: "Search IPRO" },
    showAllLabel: { id: "Semua IPRO", en: "All IPRO" },
  },
  // {
  //   key: "sektor",
  //   label: { id: "Sektor", en: "Sector" },
  //   placeholder: { id: "Cari Sektor", en: "Search Sector" },
  //   showAllLabel: { id: "Semua Sektor", en: "All Sectors" },
  //   items: [
  //     { id: 1, nama: { id: "Primer", en: "Primary" } },
  //     { id: 2, nama: { id: "Sekunder", en: "Secondary" } },
  //     { id: 3, nama: { id: "Tersier", en: "Tertiary" } },
  //   ],
  // },
  {
    key: "ksektor",
    label: { id: "Kategori Sektor", en: "Sector Category" },
    placeholder: { id: "Cari Kategori Sektor", en: "Search Category Sectors" },
    showAllLabel: { id: "Semua Kategori Sektor", en: "All Sectors Category" },
    items: [
      { id: 1, nama: { id: "Primer", en: "Primary" } },
      { id: 2, nama: { id: "Sekunder", en: "Secondary" } },
      { id: 3, nama: { id: "Tersier", en: "Tertiary" } },
    ],
  },
  {
    key: "tahun",
    label: { id: "Tahun", en: "Year" },
    placeholder: { id: "Cari Tahun", en: "Search Year" },
    showAllLabel: { id: "Semua Tahun", en: "All Years" },
  },
  {
    key: "pid",
    label: { id: "Peluang Investasi Daerah", en: "Investment Opportunity Regional" },
    placeholder: { id: "Cari PID", en: "Search Investment" },
    showAllLabel: { id: "Semua PID", en: " Investment Opportunity" },
  },
];

const FilterDropdown = ({
  config,
  isOpen,
  onOpenChange,
  items = [],
  selectedItem,
  onSelect,
  showAllOption = false,
  disabled = false,
}) => {
  const { language } = useLanguageStore();
  const getLabel = (label) => (language === 'id' ? label.id : label.en);
  const getPlaceholder = (placeholder) => language === 'id' ? placeholder.id : placeholder.en;
   // Helper function to get the display name for an item
   const getDisplayName = (item) => {
    // Handle items with translated names (from sektor)
    if (item?.nama?.id && item?.nama?.en) {
      return getLabel(item.nama);
    }
    // Handle regular items with simple nama property
    return item?.nama || '';
  };

  // Get the current display text for the button
  const getButtonText = () => {
    if (!selectedItem) {
      return getLabel(config.showAllLabel);
    }
    const displayName = getDisplayName(selectedItem);
    return displayName.length > 30 ? `${displayName.slice(0, 30)}...` : displayName;
  };

  const handleSelection = (item, type) => {
    if (item === null) {
      // Reset all filters
      usePeluangInvestasiMapStore.getState().resetSelections();
      onSelect(null, type);
    } else {
      onSelect(item, type);
    }
    onOpenChange(false);
  };

  return (
    <div className="font-montserrat">
      <h2 className="text-sm font-medium px-4 pb-1 text-[#2D3A96] font-montserrat">
        {getLabel(config.label)}
      </h2>
      <Popover open={isOpen} onOpenChange={disabled ? undefined : onOpenChange}>
        <PopoverTrigger asChild>
          <Button
            className={`w-full justify-between bg-[#F1F1F1] text-[#2D3A96] hover:bg-[#D1D1D1] font-montserrat ${
              disabled ? "opacity-50 cursor-not-allowed" : ""
            }`}
            disabled={disabled}
          >
            {getButtonText()}
            <ChevronDown className="w-4 h-4 ml-2 text-[#2D3A96]" />
          </Button>
        </PopoverTrigger>
        {!disabled && (
          <PopoverContent
            className="w-full min-w-full p-0 font-montserrat"
            align="start"
          >
            <Command>
              <CommandInput placeholder={getPlaceholder(config.placeholder)} className="font-montserrat"/>
              <CommandList>
                <CommandEmpty>{language === 'id' ? 'Tidak ditemukan.' : 'Not found.'}</CommandEmpty>
                {showAllOption && (
                  <CommandItem
                    onSelect={() => handleSelection(null, config.key)}
                  >
                    {/* {`Semua ${config.label}`} */}
                    {language === "id" ? `Semua ${config.label.id}` : `All ${config.label.en}`}
                  </CommandItem>
                )}
                {items.map((item) => (
                  <CommandItem
                    key={item.id || item.id_zona_waktu || item.id_adm_provinsi}
                    onSelect={() => handleSelection(item, config.key)}
                  >
                    {getDisplayName(item)}
                  </CommandItem>
                ))}
              </CommandList>
            </Command>
          </PopoverContent>
        )}
      </Popover>
    </div>
  );
};

const SidebarMap = () => {
  const { language } = useLanguageStore();
  const DROPDOWN_CONFIG = DROPDOWN_CONFIG2(language);
  const {
    zonaWaktu,
    provinsi,
    kabkot,
    tahun,
    selectedZona,
    selectedProvinsi,
    selectedKabkot,
    lists,
    popovers,
    initializeData,
    fetchZonaData,
    selectedItems,
    fetchProvinsiData,
    setPopoverState,
    handleListItemSelect,
    setSelectedZona,
    setSelectedProvinsi,
    setSelectedKabkot,
  } = usePeluangInvestasiMapStore();

  const [localSearchTerm, setLocalSearchTerm] = useState("");
  const [selectedSektor, setSelectedSektor] = useState("");
  const [selectedKatSektor, setSelectedKatSektor] = useState("");
  const [selectedProvinsiFromDropdown, setSelectedProvinsiFromDropdown] =
    useState("");
  const [selectedKabkoFromDropdown, setSelectedKabkoFromDropdown] =
    useState("");

  useEffect(() => {
    // Make the function available globally
    window.clearSearchInput = () => {
      setLocalSearchTerm("");
      setSearchTerm("");
    };
    
    // Clean up when component unmounts
    return () => {
      window.clearSearchInput = undefined;
    };
  }, []);

  const { fetchData, setCurrentPage, setSearchTerm, searchTerm } =
    usePeluangInvestasiStore();

  const { fetchDataIPRO, setCurrentPageIPRO, setSearchTermIPRO } =
    usePeluangInvestasiIPROStore();

  const handleSearch = (e) => {
    e.preventDefault();
    const filters = {
      id_kategori_sektor: selectedKatSektor || "",
      id_sektor: selectedSektor || "",
      id_zona_waktu: selectedZona || "",
      id_adm_provinsi: selectedProvinsi || "",
      id_adm_kabkot: selectedKabkot || "",
    };

    console.log("search filters: ", filters);

    setSearchTerm(localSearchTerm);
    setCurrentPage(1);
    fetchData(1, localSearchTerm, filters);
    setSearchTermIPRO(localSearchTerm);
    setCurrentPageIPRO(1);
    fetchDataIPRO(1, localSearchTerm, filters);
  };

  const handleSelectionAndSearch = async (item, type) => {
    console.log("ini handle selection and search", item);
    
    // First, call the original handleListItemSelect from the service
    await handleListItemSelect(item, type);
    // If item is null (Show All selected), reset all filters
    if (item === null) {
      const resetFilters = {
        id_kategori_sektor: "",
        id_sektor: "",
        id_zona_waktu: "",
        id_adm_provinsi: "",
        id_adm_kabkot: "",
      };

      // Reset all selections in the store
      usePeluangInvestasiMapStore.getState().resetSelections(); // You'll need to add this to your store
      
      setCurrentPage(1);
      fetchData(1, localSearchTerm, resetFilters);
      return;
    }
    
    if (type === "zona") {
      console.log('ini log zona change');
      
      await handleSelectZona(item);
      const currentState = usePeluangInvestasiMapStore.getState(); // Get current state
      const filters = {
        id_kategori_sektor: "",
        id_sektor: "",
        id_zona_waktu: currentState.selectedZona || "",
        id_adm_provinsi: "",
        id_adm_kabkot: "",
      };

      setCurrentPage(1);
      fetchData(1, localSearchTerm, filters);
    }

    if (type === "prov") {
      const currentState = usePeluangInvestasiMapStore.getState(); // Get current state
      const filters = {
        id_kategori_sektor: "",
        id_sektor: "",
        id_zona_waktu: currentState.selectedZona || "",
        id_adm_provinsi: item.id_adm_provinsi || "", // Use item directly
        id_adm_kabkot: "", // Reset kabkot filter
      };
      console.log('ini state prov',currentState);
      

      setCurrentPage(1);
      fetchData(1, localSearchTerm, filters);
    }

    if (type === "kabkot") {
      const currentState = usePeluangInvestasiMapStore.getState(); // Get current state
      const filters = {
        id_kategori_sektor: "",
        id_sektor: "",
        id_zona_waktu: currentState.selectedZona || "",
        id_adm_provinsi: currentState.selectedProvinsi || "",
        id_adm_kabkot: item.id_adm_kabkot || "", // Use item directly
      };

      setCurrentPage(1);
      fetchData(1, localSearchTerm, filters);
      setCurrentPageIPRO(1);
      fetchDataIPRO(1, localSearchTerm, filters);
    }

    if (type === "ksektor") {
      const currentState = usePeluangInvestasiMapStore.getState(); // Get current state
      const filters = {
        id_kategori_sektor: item.id || "",
        id_sektor: "", // Use item directly
        id_zona_waktu: currentState.selectedZona || "",
        id_adm_provinsi: currentState.selectedProvinsi || "",
        id_adm_kabkot: currentState.selectedKabkot || "",
      };

      setCurrentPage(1);
      fetchData(1, localSearchTerm, filters);
      //setCurrentPageIPRO(1);
      //fetchDataIPRO(1, localSearchTerm, filters);
    }

    if (type === "sektor") {
      const currentState = usePeluangInvestasiMapStore.getState(); // Get current state
      const filters = {
        id_kategori_sektor: "",
        id_sektor: item.id || "", // Use item directly
        id_zona_waktu: currentState.selectedZona || "",
        id_adm_provinsi: currentState.selectedProvinsi || "",
        id_adm_kabkot: currentState.selectedKabkot || "",
      };

      setCurrentPage(1);
      fetchData(1, localSearchTerm, filters);
      //setCurrentPageIPRO(1);
      //fetchDataIPRO(1, localSearchTerm, filters);
    }
  };

  useEffect(() => {
    initializeData();
  }, [language]);

  const handleSelectZona = (zona) => {
    console.log('ini hanle select zone');
    
    fetchZonaData(zona?.id_zona_waktu || null);
  };

  const handleSelectProvinsi = async (prov) => {
    // If no province is selected (showing all), reset kabkotlist
    if (!prov) {
      set({
        kabkot: [],
        selectedProvinsi: null,
        selectedKabkot: null,
      });
      return;
    }

    try {
      // Fetch kabkotdata for selected province
      const kabkoResponse = await getKabkotByProvinsiService(
        prov.id_adm_provinsi
      );

      // Update state with new kabkotlist and selected province
      set({
        kabko: kabkoResponse.data,
        selectedProvinsi: prov.id_adm_provinsi,
        selectedKabkot: null, // Reset selected kabkotwhen province changes
      });

      // Call fetchProvinsiData to update other related data
      fetchProvinsiData(prov.id_adm_provinsi);
    } catch (error) {
      console.error("Error fetching kabkotdata:", error);
    }
  };

  const getSelectedZona = () =>
    zonaWaktu.find((z) => z.id_zona_waktu === selectedZona);

  const getSelectedProvinsi = () => {
    const { provinsi, selectedProvinsi } =
      usePeluangInvestasiMapStore.getState();
    return (
      provinsi.find((item) => item.id_adm_provinsi === selectedProvinsi) || null
    );
  };

  const getSelectedKabko = () => {
    const { kabkot, selectedKabkot } = usePeluangInvestasiMapStore.getState();
    console.log("kabkot: ", kabkot);
    console.log("selected kabkot: ", selectedKabkot);
    return kabkot.find((item) => item.id_adm_kabkot === selectedKabkot) || null;
  };

  const getSelectedSektor = () => {
    const { selectedSektor } = usePeluangInvestasiMapStore.getState();
    console.log("selectedSektor: ", selectedSektor);
    return (
      DROPDOWN_CONFIG[5].items.find((item) => item.id === selectedSektor) ||
      null
    );
  };

  const getSelectedKatSektor = () => {
    const { selectedKatSektor } = usePeluangInvestasiMapStore.getState();
    console.log("selectedKatSektor: ", selectedKatSektor);
    return (
      DROPDOWN_CONFIG[5].items.find((item) => item.id === selectedKatSektor) ||
      null
    );
  };

  const getSelectedTahun = () => {
    const { selectedYear } = usePeluangInvestasiMapStore.getState();
    return tahun.find((item) => item.id === selectedYear) || null;
  };

  return (
    <div className="flex flex-col flex-shrink-0 p-4 space-y-6 bg-white border rounded-lg shadow-md w-80 font-montserrat">
      <div className="relative w-full py-[10px] mx-auto border rounded-xl">
        <form onSubmit={handleSearch}>
          <input
            type="text"
            placeholder={language === "id" ? "Masukkan Kata Kunci" : "Enter Keyword"}
            className="w-full pl-2 pr-10 text-gray-900 focus:outline-none"
            value={localSearchTerm}
            onChange={(e) => setLocalSearchTerm(e.target.value)}
          />
          <button className="absolute transform -translate-y-1/2 right-2 top-1/2 focus:outline-none">
            <svg
              className="w-6 h-6 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </button>
        </form>
      </div>
      <FilterDropdown
        config={DROPDOWN_CONFIG[0]}
        isOpen={popovers.zona}
        onOpenChange={(isOpen) => setPopoverState("zona", isOpen)}
        items={zonaWaktu}
        selectedItem={getSelectedZona()}
        onSelect={(item) => handleSelectionAndSearch(item, "zona")}
        showAllOption={true}
      />

      <FilterDropdown
        config={DROPDOWN_CONFIG[1]}
        isOpen={popovers.prov}
        onOpenChange={(isOpen) => setPopoverState("prov", isOpen)}
        items={provinsi}
        selectedItem={getSelectedProvinsi()}
        onSelect={(item) => handleSelectionAndSearch(item, "prov")}
        showAllOption={true}
      />

      <FilterDropdown
        config={DROPDOWN_CONFIG[2]}
        isOpen={popovers.kabkot}
        onOpenChange={(isOpen) => setPopoverState("kabkot", isOpen)}
        items={kabkot}
        selectedItem={getSelectedKabko()}
        onSelect={(item) => handleSelectionAndSearch(item, "kabkot")}
        showAllOption={true}
        disabled={!selectedProvinsi}
      />

      {DROPDOWN_CONFIG.slice(3, 5).map((config) => {
        const key = config.key;
        return (
          <FilterDropdown
            key={key}
            config={config}
            isOpen={popovers[key]}
            onOpenChange={(isOpen) => setPopoverState(key, isOpen)}
            items={lists[key]}
            selectedItem={selectedItems[key]}
            onSelect={(item) => handleListItemSelect(item, key)}
            showAllOption={true}
          />
        );
      })}

    {DROPDOWN_CONFIG.slice(7).map((config) => {
        const key = config.key;
        return (
          <FilterDropdown
            key={key}
            config={config}
            isOpen={popovers[key]}
            onOpenChange={(isOpen) => setPopoverState(key, isOpen)}
            items={lists[key]}
            selectedItem={selectedItems[key]}
            onSelect={(item) => handleListItemSelect(item, key)}
            showAllOption={true}
          />
        );
      })} 

      <FilterDropdown
        config={DROPDOWN_CONFIG[5]}
        isOpen={popovers.ksektor}
        onOpenChange={(isOpen) => setPopoverState("ksektor", isOpen)}
        items={DROPDOWN_CONFIG[5].items}
        selectedItem={getSelectedKatSektor()}
        onSelect={(item) => handleSelectionAndSearch(item, "ksektor")}
        showAllOption={true}
        // disabled={!selectedKabkot}
      />

      <FilterDropdown
        config={DROPDOWN_CONFIG[6]}
        isOpen={popovers.tahun}
        onOpenChange={(isOpen) => setPopoverState("tahun", isOpen)}
        items={tahun}
        selectedItem={getSelectedTahun()}
        onSelect={handleListItemSelect}
        showAllOption={true}
      />
    </div>
  );
};

export default SidebarMap;
