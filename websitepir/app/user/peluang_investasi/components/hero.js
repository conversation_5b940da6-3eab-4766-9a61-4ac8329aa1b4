"use client";

import {
  B<PERSON><PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import useNavStore from "@/store/NavStore";
import axios from "axios";
import { motion } from "framer-motion";
import Image from "next/image";
import React, { useState, useEffect } from "react";
import useLanguageStore from "../../stores/languageStore";

const Hero = ({ title, link, backgroundImage }) => {
  const [data, setData] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const { currentPage, setCurrentPage } = useNavStore();
  const translations = {
    id: {
      pi: "Peluang Investasi",
      home: "Beranda",
      region: "Daerah",
    },
    en: {
      pi: "Investment Opportunities",
      home: "Home",
      region: "Regional",
    },
  };

  const { language } = useLanguageStore();
  const t = translations[language];

  const fetchData = async () => {
    const langParam = language === 'en' ? '?en=true' : '';
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/peluang/slider/${langParam}`
      );
      if (response.data.success) {
        setData(response.data.data.data);
      } else {
        console.error("Gagal memuat data");
      }
    } catch (error) {
      console.error("Gagal memuat data:", error.message);
    }
  };

  useEffect(() => {
    fetchData();
    setCurrentPage("peluang_investasi");
  }, [language]);

  useEffect(() => {
    const intervalId = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % data.length);
    }, 5000);

    return () => clearInterval(intervalId);
  }, [data.length]);

  return (
    <div>
      <section className="relative flex flex-col items-center justify-start w-full min-h-screen">
        {data.map((item, index) => (
          <motion.div
            key={item.id_app_slider}
            initial={{ opacity: 0 }}
            animate={{ opacity: index === currentIndex ? 1 : 0 }}
            transition={{ duration: 1 }}
            className="absolute w-full h-full"
          >
            <Image
              src={item.nama_file_image}
              alt={item.judul}
              fill={true}
              style={{ objectFit: "cover" }}
              layout="fill"
              className="object-cover w-full h-full"
            />
          </motion.div>
        ))}
        <div className="absolute z-10 flex items-center justify-between bg-opacity-50 bg-white w-full h-[200px]">
          <div className="ml-[150px]">
            <h1 className="font-bold font-montserrat 2xl:text-[40px] xl:text-[35px] md:text-[30px] sm:text-[30px]">
              {t.pi}
            </h1>
          </div>
          <div className="text-xl font-montserrat">
            <Breadcrumb className="mr-[150px]">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">{t.home}</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/peluang_investasi">
                    {t.pi}
                  </BreadcrumbLink>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </div>
        <div className="relative mt-36 z-10 justify-start px-4 pb-24 text-center text-white pt-[250px]">
          <h1 className="mb-4 text-3xl font-bold md:text-5xl font-montserrat">
            <span className="bg-[#2D3A96] rounded-full px-5">
              {data.length > 0 ? data[currentIndex].judul : "Sumber Daya Alam"}
            </span>
          </h1>
          <br />
          {/* <p className="flex mb-8 text-[16px] font-bold md:text-xl md:w-full lg:w-[1200px] sm:w-full font-montserrat leading-6 items-center justify-center">
            <span className="py-2 bg-gray-400 bg-opacity-80">
              {data.length > 0
                ? data[currentIndex].deskripsi
                : "Tiga proyek terintegrasi Peternakan Ayam Broiler dan Ayam Petelur, Hortikultura Bawang Merah dan Sambal Cabai Merah, dan Budidaya Udang dengan fokus pada pembibitan, dan pengolahan ikan/penyimpanan dingin. Secara kolektif, inisiatif-inisiatif ini menekankan pendekatan pertanian yang komprehensif dan berkelanjutan."}
            </span>
          </p> */}
        </div>
      </section>
    </div>
  );
};

export default Hero;
