"use client";

import Card<PERSON>ithBackground from "./cardwithbackground";
import usePeluangInvestasiStore from "@/store/ListPeluangStore";
import PeluangInvestasiStore from "@/store/PeluangInvestasiStore";
import { ChevronRight, ChevronLeft } from "lucide-react";
import React, { useEffect } from "react";
import Slider from "react-slick";
import useLanguageStore from "../../stores/languageStore";
import useCSStore from "@/store/CardSliderStore";
import { Button } from "@/components/ui/button";

const CardSlider = () => {
  const {
    peluangInvestasiCard,
    setSelectedPeluangInvestasiCardByName,
    setSelectedPeluangInvestasiCardDetail,
    setPeluangInvestasiCard 
  } = PeluangInvestasiStore();
  const { fetchData, setCurrentPage } = usePeluangInvestasiStore();
  const { language } = useLanguageStore();

  const { setSelectedSektorId } = useCSStore();

  useEffect(() => {
    setPeluangInvestasiCard(language);
  }, [language]);

  const handleCardClick = async (id_sektor) => {
    console.log("Clicked sector:", id_sektor);
    if (window.clearSearchInput && typeof window.clearSearchInput === 'function') {
      window.clearSearchInput();
    }
    setCurrentPage(1);
    setSelectedSektorId(id_sektor);
    const filters = {
      id_sektor: id_sektor,
    };

    await fetchData(1, "", filters);

    const listSection = document.getElementById("list-peluang");
    if (listSection) {
      listSection.scrollIntoView({ behavior: "smooth" });
    }
  };
  console.log("data peluang card", peluangInvestasiCard)
  // Make sure data exists before creating settings
  const cardArray = peluangInvestasiCard?.data
    ? Object.entries(peluangInvestasiCard.data).map(([key, value]) => ({
        key,
        ...value,
      }))
    : [];

  // Only render if we have data
  if (!cardArray.length) {
    return <div>Loading...</div>;
  }
  const SamplePrevArrow = (props) => {
      const { className, style, onClick } = props;
      return (
        <div
          className="slick-arrow"
          style={{
            ...style,
            position: "absolute",
            left: "-40px",
            top: "50%",
            transform: "translateY(-50%)",
            cursor: "pointer",
            display: "block",
          }}
          onClick={onClick}
        >
          <ChevronLeft style={{ color: "black" }} />
        </div>
      );
    };
  
    function SampleNextArrow(props) {
      const { className, style, onClick } = props;
      return (
        <div
          className="slick-arrow"
          style={{
            ...style,
            display: "block",
            position: "absolute",
            right: "-40px",
            top: "50%",
            transform: "translateY(-50%)",
            cursor: "pointer",
          }}
          onClick={onClick}
        >
          <ChevronRight style={{ color: "black" }} />
        </div>
      );
    }

  const settings = {
    dots: false,
    infinite: cardArray.length > 4, // Only make infinite if enough slides
    speed: 500,
    slidesToShow: Math.min(4, cardArray.length), // Don't show more slides than we have items
    slidesToScroll: 1,
    nextArrow: <SampleNextArrow />,
    prevArrow: <SamplePrevArrow />,
    appendDots: (dots) => (
      <div>
        <ul className="custom-dots"> {dots} </ul>
      </div>
    ),
    responsive: [
      {
        breakpoint: 1500,
        settings: {
          slidesToShow: Math.min(3, cardArray.length),
          slidesToScroll: 1,
          infinite: cardArray.length > 3,
          dots: true,
        },
      },
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: Math.min(2, cardArray.length),
          slidesToScroll: 1,
          infinite: cardArray.length > 2,
          dots: true,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          infinite: true,
          dots: true,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          infinite: true,
          dots: true,
        },
      },
    ],
    centerMode: cardArray.length > 4, // Only use centerMode if enough slides
  };

  return (
    <div className="mx-20 mb-10">
      <Slider {...settings}>
        {cardArray.map((item, index) => (
          <div key={`slider-item-${item.id_sektor || index}`} className="px-2">
            <CardWithBackground
              title={item.nama || ""}
              description={`${item.jumlah_proyek || 0}`}
              image={item.image || "/no-data-image.png"}
              onClick={() => handleCardClick(item.id_sektor)}
              kategori_sektor={item.kategori_sektor || ""}
            />
          </div>
        ))}
      </Slider>
      <Button
        className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
        onClick={handleCardClick}
      >
        {language === "id" ? "Semua Sektor" : "All Sector"}    
      </Button>
    </div>
  );
};

export default CardSlider;
