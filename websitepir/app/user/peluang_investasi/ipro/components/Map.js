"use client";

import maplibregl from "maplibre-gl";
import React, { useState, useEffect, useRef, useCallback } from "react";
import "maplibre-gl/dist/maplibre-gl.css";
import useLanguageStore from "@/app/user/stores/languageStore";
import {
  getInfoGeoportal,
  getTokenGeoportal,
} from "@/services/GetInfoGeoportal";
import useIKNStore from "@/store/IKNStore";
import ApiGeo from "@/utils/ApiGeo";
import { decryptToken } from "@/utils/decryptToken";
import {
  Scan,
  ChartColumnBig,
  Calendar,
  HandCoins,
  Navigation,
} from "lucide-react";
import dynamic from "next/dynamic";
import Image from "next/image";
import Map, { Marker, Popup } from "react-map-gl";

const ReactPlayer = dynamic(() => import("react-player"), { ssr: false });

class LayerControl {
  constructor(options) {
    const defaultOptions = {
      layers: [],
    };
    this._options = !options ? defaultOptions : options;
    this._layers = this._options.layers;
    this._isOpen = false; // Defaultkan legend tertutup
  }

  onAdd(map) {
    this._map = map;

    // Create container
    this._container = document.createElement("div");
    Object.assign(this._container.style, {
      backgroundColor: "white",
      padding: "10px",
      borderRadius: "4px",
      boxShadow: "0 0 10px rgba(0,0,0,0.1)",
      position: "absolute",
      top: "10px",
      right: "10px",
      zIndex: 1000, // Ensure it's above other map controls
      minWidth: "200px",
    });

    // Create header
    const header = document.createElement("div");
    Object.assign(header.style, {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: "10px",
    });

    // Create title
    const title = document.createElement("span");
    title.textContent = "Legend";
    Object.assign(title.style, {
      fontWeight: "bold",
      fontSize: "14px",
    });

    // Create toggle button
    this._btn = document.createElement("button");
    this._btn.type = "button";
    this._btn.innerHTML = "▼"; // Initial state, button to open
    Object.assign(this._btn.style, {
      background: "transparent",
      border: "none",
      cursor: "pointer",
      padding: "2px 6px",
      fontSize: "12px",
      zIndex: 1001, // Ensure it's clickable and above other content
      pointerEvents: "auto", // Ensure the button is clickable
    });

    header.appendChild(title);
    header.appendChild(this._btn);

    // Create icon list container
    this._iconList = document.createElement("div");
    Object.assign(this._iconList.style, {
      display: "none", // Make sure the legend is initially closed
      marginTop: "5px",
      transition: "all 0.3s ease",
      pointerEvents: "auto", // Allow interactions
      zIndex: 999,
    });

    // Add icons to legend list
    this._layers.forEach((layer) => {
      const layerItem = document.createElement("div");
      Object.assign(layerItem.style, {
        margin: "8px 0",
        display: "flex",
        alignItems: "center",
        gap: "8px",
        cursor: "default", // Disable the click interaction on items
      });

      const iconContainer = document.createElement("div");
      Object.assign(iconContainer.style, {
        width: "20px",
        height: "24px",
        backgroundImage: `url('/pinPeluangSektor/${layer.iconmap}')`,
        backgroundSize: "contain",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center",
      });

      const iconLabel = document.createElement("span");
      const readableName =
        layer.name ||
        layer.iconmap
          .replace(/\.[^/.]+$/, "")
          .replace(/[_-]/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase());

      iconLabel.textContent = readableName;
      Object.assign(iconLabel.style, {
        fontSize: "14px",
        color: "#333",
        flex: "1",
      });

      layerItem.appendChild(iconContainer);
      layerItem.appendChild(iconLabel);
      this._iconList.appendChild(layerItem);

      // Remove the click event listener from items (so they can't be clicked)
      layerItem.addEventListener("click", (e) => {
        e.preventDefault(); // Prevent the default behavior of clicking
      });
    });

    // Append header and icon list to container
    this._container.appendChild(header);
    this._container.appendChild(this._iconList);

    // Toggle visibility of icon list on button click
    this._btn.addEventListener("click", () => {
      this._isOpen = !this._isOpen;
      this._iconList.style.display = this._isOpen ? "block" : "none";
      this._btn.innerHTML = this._isOpen ? "▼" : "▶";
    });

    return this._container;
  }

  onRemove() {
    this._container.parentNode.removeChild(this._container);
    this._map = undefined;
  }
}

const MapIKN = ({ data, lon, lat }) => {
  const mapRef = useRef(null);
  const [mapInstance, setMapInstance] = useState(null);
  const [geoAccessToken, setGeoAccessToken] = useState(null);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [popupInfo, setPopupInfo] = useState(null);
  const [popupCoordinates, setPopupCoordinates] = useState(null);
  const [popupContent, setPopupContent] = useState(null);
  const [markerPopupVisible, setMarkerPopupVisible] = useState(false);

  const mapConfig = {
    longitude: lon,
    latitude: lat,
    zoom: 6,
    pitch: 0,
    bearing: 0,
  };

  const onLoadedData = () => {
    setIsVideoLoaded(true);
  };

  const fetchGeoToken = async () => {
    // Validate environment variables
    const username = process.env.NEXT_PUBLIC_GEO_USERNAME;
    const password = process.env.NEXT_PUBLIC_GEO_PASSWORD;
    
    if (!username || !password) {
      console.error('Missing geo credentials in environment variables');
      return;
    }

    try {
      // const response = await getTokenGeoportal();
      // const decryptedToken = await decryptToken(response.token);
      // return decryptedToken;
      const response = await ApiGeo.post("/iam/login", {
        username: username,
        password: password,
      });
      // console.log("Login response:", response);
      return response.accessToken;
    } catch (error) {
      console.error("Failed to login:", error);
      return null;
    }
  };

  const addLayerToMap = async (mapInstance, layerUid, token) => {
    if (!token) return;

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/cms/layer/${layerUid}/info`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );


      if (!response.ok) throw new Error(`Failed to fetch layer: ${layerUid}`);

      const layerInfo = await response.json();
      const layerName = layerInfo.data[0].layerName;

      // const response = await getInfoGeoportal(layerUid);
      // if (!response?.data)
      //   throw new Error(`Failed to fetch layer: ${layerUid}`);
      // const layerName = response.data[0].layerName;
      // const layerBbox = response.data[0].layerBbox;

      const sourceId = `geoserverSource-${layerUid}`;
      const layerId = `geoserver-layer-${layerUid}`;

      if (mapInstance.getLayer(layerId)) {
        mapInstance.removeLayer(layerId);
      }
      if (mapInstance.getSource(sourceId)) {
        mapInstance.removeSource(sourceId);
      }

      mapInstance.addSource(sourceId, {
        type: "raster",
        tiles: [
          `${process.env.NEXT_PUBLIC_BASE_URL2}/geoserver/wms?service=WMS&version=1.1.0&request=GetMap&layers=${layerName}&bbox={bbox-epsg-3857}&width=512&height=512&srs=EPSG:3857&styles=&format=image%2Fpng&transparent=true&token=${token}`,
        ],
        tileSize: 256,
      });

      mapInstance.addLayer({
        id: layerId,
        type: "raster",
        source: sourceId,
        minzoom: 0,
        maxzoom: 19,
        paint: { "raster-opacity": 0.8 },
      });
    } catch (error) {
      console.error(`Error adding layer ${layerUid}:`, error);
    }
  };

  const handleMapClick = async (e) => {
    if (
      !mapInstance ||
      !geoAccessToken ||
      !data.layer ||
      data.layer.length === 0
    )
      return;

    try {
      // Reset popup state
      setPopupInfo(null);
      setPopupCoordinates(null);
      setPopupContent(null);

      const layerUids = data.layer.map((layer) => layer.layeruid);
      const coordinates = [e.lngLat.lng, e.lngLat.lat];
      const zoomLevel = Math.round(mapInstance.getZoom());


      // Make the API request for each layer
      for (const layerUid of layerUids) {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL_GEO}/cms/map/${layerUid}/object_info`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${geoAccessToken}`,
            },
            body: JSON.stringify({
              layer_uid: [layerUid],
              coord: coordinates,
              zoomLevel: zoomLevel,
            }),
          }
        );

        if (!response.ok) {
          console.error(`Failed to fetch object info for layer ${layerUid}`);
          continue;
        }

        const responseData = await response.json();

        if (
          responseData.status === "success" &&
          responseData.data &&
          responseData.data.length > 0
        ) {
          // Store popup data in state
          setPopupInfo(responseData.data[0]);
          setPopupCoordinates(coordinates);
          setPopupContent(responseData.data[0].layerData[0].properties);
          break; // Show popup for the first layer with data
        }
      }
    } catch (error) {
      console.error("Error fetching object info:", error);
    }
  };

  const handleMarkerClick = useCallback((e) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }
    console.log("Marker clicked!", e);
    console.log("Current markerPopupVisible state:", markerPopupVisible);
    
    // Log data yang tersedia untuk popup
    console.log("Data untuk popup:", data);
    console.log("Detail data:", data?.detail);
    
    setMarkerPopupVisible(prev => !prev);
    console.log("New markerPopupVisible state will be:", !markerPopupVisible);
  }, [markerPopupVisible, data]);

  useEffect(() => {
    console.log("markerPopupVisible changed to:", markerPopupVisible);
  }, [markerPopupVisible]);

  useEffect(() => {
    const initializeLayers = async () => {
      if (mapInstance && data.layer) {
        const token = await fetchGeoToken();
        setGeoAccessToken(token);

        if (token) {
          for (const layer of data.layer) {
            await addLayerToMap(mapInstance, layer.layeruid, token);
          }
          const iconlayers = [
            {
              name: `${data?.detail?.nama_sektor}`,
              iconmap: `${
                data?.detail?.nama_sektor
                  ? data.detail.nama_sektor.toLowerCase()
                  : ""
              }.svg`,
            },
          ];

          const layerControl = new LayerControl({ layers: iconlayers });
          mapInstance.addControl(layerControl);
        }
      }
    };

    initializeLayers();
  }, [mapInstance, data.layer]);

  useEffect(() => {
    // Add click event listener when mapInstance is available
    if (mapInstance) {
      mapInstance.on("click", handleMapClick);

      // Clean up the event listener when component unmounts
      return () => {
        mapInstance.off("click", handleMapClick);
        if (popupInfo) {
          popupInfo.remove();
        }
      };
    }
  }, [mapInstance, geoAccessToken, data.layer]);

  useEffect(() => {
    if (mapInstance) {
      //flyto
      mapInstance.flyTo({
        center: [lon, lat],
        zoom: 12,
        essential: true,
      });
    }
  }, [mapInstance]);

  const onMapLoad = (event) => {
    setMapInstance(event.target);
  };

  const MarkerComponent = () => {
    console.log("Rendering MarkerComponent");
    return (
      <div 
        onClick={handleMarkerClick}
        style={{ cursor: 'pointer' }}
      >
        <Image
          src={`/pinPeluangSektor/${
            data?.detail?.nama_sektor?.toLowerCase() || 'other'
          }.svg`}
          alt="marker"
          width={30}
          height={36}
        />
      </div>
    );
  };

  const { language } = useLanguageStore();
  return (
    <div className="flex items-center justify-center w-full pt-4">
      <div className="bg-[#2D3A96] rounded-lg border shadow-lg mr-4 text-white font-montserrat w-[370px] h-[500px] flex flex-col justify-start">
        <div className="w-[360px] h-[200px] pl-2 pt-2 rounded-md">
          {data.detail.vidio ? (
            <ReactPlayer
              url={data.detail.vidio}
              playing={true}
              controls={true}
              loop={true}
              muted={true}
              playsinline={true}
              width="100%"
              height="100%"
              style={{ borderRadius: "0.75rem" }}
              onReady={onLoadedData}
            />
          ) : (
            <div className="flex items-center justify-center w-full h-full">
              <span className="text-xl">
                {language === "id"
                  ? "Video Tidak Tersedia"
                  : "Video Not Available"}
              </span>
            </div>
          )}
        </div>
        <div className="pt-2 mb-2 ml-4 font-bold">
          {language === "id" ? "Informasi" : "Information"}
        </div>
        <div className="grid grid-cols-[auto,1fr] gap-x-4 ml-4 mr-10 gap-1 text-sm">
          <span className="flex items-center justify-start">
            <Scan
              className="p-1 mr-2 bg-white rounded-full"
              color="#2D3A96"
              size={20}
            />
            {language === "id" ? "Kode KBLI" : "KBLI Code"}
          </span>
          <span className="ml-2 text-start">
            :{" "}
            {data.detail.kode_kbli
              ? data.detail.kode_kbli
              : language === "id"
              ? "Data Tidak Tersedia"
              : "Data Not Available"}
          </span>

          <span className="flex items-center justify-start">
            <ChartColumnBig
              className="p-1 mr-2 bg-white rounded-full"
              color="#2D3A96"
              size={20}
            />
            {language === "id" ? "Nilai Investasi" : "Investment Value"}
          </span>
          <span className="ml-2 text-start">
            :{" "}
            {data.detail.nilai_investasi
              ? data.detail.nilai_investasi
              : language === "id"
              ? "Data Tidak Tersedia"
              : "Data Not Available"}
          </span>

          <span className="flex items-center justify-start">
            <Calendar
              className="p-1 mr-2 bg-white rounded-full"
              color="#2D3A96"
              size={20}
            />
            {language === "id" ? "Tahun" : "Year"}
          </span>
          <span className="ml-2 text-start">
            : {data.detail.tahun ? data.detail.tahun : language === "id" ? "Data Tidak Tersedia" : "Data Not Available"}
          </span>

          <span className="flex items-center justify-start">
            <ChartColumnBig
              className="p-1 mr-2 bg-white rounded-full"
              color="#2D3A96"
              size={20}
            />
            IRR
          </span>
          <span className="ml-2 text-start">
            :{" "}
            {data.detail.nilai_irr
              ? data.detail.nilai_irr
              : language === "id"
              ? "Data Tidak Tersedia"
              : "Data Not Available"}
          </span>

          <span className="flex items-center justify-start">
            <ChartColumnBig
              className="p-1 mr-2 bg-white rounded-full"
              color="#2D3A96"
              size={20}
            />
            NPV
          </span>
          <span className="ml-2 text-start">
            :{" "}
            {data.detail.nilai_npv
              ? data.detail.nilai_npv
              : language === "id"
              ? "Data Tidak Tersedia"
              : "Data Not Available"}
          </span>

          <span className="flex items-center justify-start">
            <HandCoins
              className="p-1 mr-2 bg-white rounded-full"
              color="#2D3A96"
              size={20}
            />
            Payback Period
          </span>
          <span className="ml-2 text-start">
            :{" "}
            {data.detail.payback_period
              ? data.detail.payback_period
              : language === "id"
              ? "Data Tidak Tersedia"
              : "Data Not Available"}
          </span>

          <span className="flex items-center justify-start">
            <Navigation
              className="p-1 mr-2 bg-white rounded-full"
              color="#2D3A96"
              size={20}
            />
            Longitude
          </span>
          <span className="ml-2 text-start">
            :{" "}
            {data.detail.longitude
              ? data.detail.longitude
              : language === "id"
              ? "Data Tidak Tersedia"
              : "Data Not Available"}
          </span>

          <span className="flex items-center justify-start">
            <Navigation
              className="p-1 mr-2 bg-white rounded-full"
              color="#2D3A96"
              size={20}
            />
            Latitude
          </span>
          <span className="ml-2 text-start">
            :{" "}
            {data.detail.latitude
              ? data.detail.latitude
              : language === "id"
              ? "Data Tidak Tersedia"
              : "Data Not Available"}
          </span>
        </div>
      </div>
      <div className="w-full max-w-4xl overflow-hidden border rounded-lg shadow-lg h-[500px]">
        <Map
          ref={mapRef}
          mapLib={maplibregl}
          initialViewState={mapConfig}
          style={{ width: "100%", height: "100%" }}
          mapStyle="https://api.maptiler.com/maps/streets/style.json?key=Qidd2LwQWN3Wl1snG5sI"
          className="rounded-xl"
          onLoad={onMapLoad}
          onClick={handleMapClick}
        >
          <Marker 
            longitude={lon} 
            latitude={lat} 
            anchor="bottom"
            style={{ zIndex: 1 }}
          >
            <MarkerComponent />
          </Marker>

          {/* Popup point marker */}
          {console.log("Rendering Map, markerPopupVisible:", markerPopupVisible)}
          {markerPopupVisible && (
              <Popup
                longitude={lon}
                latitude={lat}
                anchor="bottom"
                onClose={() => {
                  console.log("Popup onClose triggered");
                  setMarkerPopupVisible(false);
                }}
                className="custom-popup"
                maxWidth="350px"
                closeOnClick={true}
                closeButton={true}
              >
                <div className="map-popup">
                  <h3 className="popup-title">
                    {data?.detail?.nama_peluang || (language === "id" ? "Detail Peluang Investasi" : "Investment Opportunity Detail")}
                  </h3>
                  <div className="popup-content-container">
                    <table className="map-popup-table">
                      <tbody>
                        <tr>
                          <td className="property-name">{language === "id" ? "Judul" : "Title"}</td>
                          <td className="property-value">{data?.detail?.judul || "-"}</td>
                        </tr>
                        <tr>
                          <td className="property-name">{language === "id" ? "Provinsi" : "Province"}</td>
                          <td className="property-value">{data?.detail?.nama_provinsi || "-"}</td>
                        </tr>
                        <tr>
                          <td className="property-name">{language === "id" ? "Kabupaten/Kota" : "City"}</td>
                          <td className="property-value">{data?.detail?.nama_kabkot || "-"}</td>
                        </tr>
                        <tr>
                          <td className="property-name">{language === "id" ? "Sektor" : "Sector"}</td>
                          <td className="property-value">{data?.detail?.nama_sektor || "-"}</td>
                        </tr>
                        <tr>
                          <td className="property-name">{language === "id" ? "Komoditi" : "Commodity"}</td>
                          <td className="property-value">{data?.detail?.komoditi || "-"}</td>
                        </tr>
                        <tr>
                          <td className="property-name">{language === "id" ? "Lokasi Kawasan" : "Location Area"}</td>
                          <td className="property-value">{data?.detail?.lokasi_kawasan || "-"}</td>
                        </tr>
                        <tr>
                          <td className="property-name">{language === "id" ? "Nilai Investasi" : "Investment Value"}</td>
                          <td className="property-value">{data?.detail?.nilai_investasi || "-"}</td>
                        </tr>
                        <tr>
                          <td className="property-name">{language === "id" ? "Nilai IRR" : "IRR Value"}</td>
                          <td className="property-value">{data?.detail?.irr || "-"}</td>
                        </tr>
                        <tr>
                          <td className="property-name">{language === "id" ? "Nilai NPV" : "NPV Value"}</td>
                          <td className="property-value">{data?.detail?.nilai_npv || "-"}</td>
                        </tr>
                        <tr>
                          <td className="property-name">{language === "id" ? "Payback Period" : "Payback Period"}</td>
                          <td className="property-value">{data?.detail?.payback_period || "-"}</td>
                        </tr>
                        <tr>
                          <td className="property-name">{language === "id" ? "Tahun" : "Year"}</td>
                          <td className="property-value">{data?.detail?.tahun || "-"}</td>
                        </tr>
                        <tr>
                          <td className="property-name">{language === "id" ? "Kode KBLI" : "KBLI Code"}</td>
                          <td className="property-value">{data?.detail?.kode_kbli || "-"}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </Popup>
          )}

          {/* Render the existing popup conditionally */}
          {popupCoordinates && popupContent && (
            <Popup
              longitude={popupCoordinates[0]}
              latitude={popupCoordinates[1]}
              anchor="bottom"
              onClose={() => {
                setPopupInfo(null);
                setPopupCoordinates(null);
                setPopupContent(null);
              }}
              className="custom-popup"
              maxWidth="350px"
            >
              <div className="map-popup">
                <h3 className="popup-title">
                  {language === "id"
                    ? "Informasi"
                    : "Information"}
                </h3>
                <div className="popup-content-container">
                  <table className="map-popup-table">
                    <thead>
                      <tr>
                        <th>{language === "id" ? "Properti" : "Property"}</th>
                        <th>{language === "id" ? "Nilai" : "Value"}</th>
                      </tr>
                    </thead>
                    <tbody>
                    {Object.entries(popupContent).map(([key, value]) => {
                        let k = key;
                        if(key == "wadmkk"){
                          k = language === "id" ?   "Kota" : "City";
                        }
                        if(key == "wadmpr"){
                          k = language === "id" ? "Provinsi" : "Province";
                        }
                        if(key == "kdpkab"){
                          k = language === "id" ? "Kode Kab/Kota" : "Regency/City Code";
                        }
                        if(key == "kdppum"){
                          k = language === "id" ? "Kode Provinsi" : "Province Code";
                        }

                        if(key == "ogc_fid" || key == "id_peluang" || key == 'count'){
                          return;
                        }
                        return (
                          <tr key={k}>
                            <td className="property-name">{k}</td>
                            <td className="property-value">{value}</td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </Popup>
          )}
        </Map>

        {/* Custom styles */}
        <style jsx global>{`
          /* Popup container styling */
          .maplibregl-popup-content {
            padding: 0 !important;
            border-radius: 8px !important;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15) !important;
            overflow: hidden !important;
            max-width: 350px !important;
            font-family: 'Montserrat', sans-serif;
          }

          /* Close button styling */
          .maplibregl-popup-close-button {
            font-size: 16px !important;
            color: #666 !important;
            padding: 5px 8px !important;
            background: transparent !important;
            right: 0 !important;
            top: 0 !important;
            z-index: 10;
          }

          .maplibregl-popup-close-button:hover {
            color: #333 !important;
            background-color: rgba(0, 0, 0, 0.05) !important;
          }

          /* Popup content styling */
          .map-popup {
            width: 100%;
          }

          .popup-title {
            margin: 0;
            padding: 12px 15px;
            font-size: 16px;
            font-weight: 600;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            color: #212529;
          }

          .popup-content-container {
            max-height: 300px;
            overflow-y: auto;
            padding: 0;
          }

          /* Table styling */
          .map-popup-table {
            width: 100%;
            border-collapse: collapse;
          }

          .map-popup-table th {
            padding: 10px;
            text-align: left;
            background-color: #f1f3f5;
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
            z-index: 1;
            border-bottom: 1px solid #dee2e6;
          }

          .map-popup-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
          }

          .map-popup-table tbody tr:hover {
            background-color: #e9ecef;
          }

          .map-popup-table td {
            padding: 8px 10px;
            border-bottom: 1px solid #dee2e6;
            color: #495057;
            word-break: break-word;
          }

          .property-name {
            font-weight: 500;
            width: 40%;
          }

          .property-value {
            font-family: 'Montserrat', sans-serif;
          }

          /* Custom scrollbar */
          .popup-content-container::-webkit-scrollbar {
            width: 8px;
          }

          .popup-content-container::-webkit-scrollbar-track {
            background: #f1f1f1;
          }

          .popup-content-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
          }

          .popup-content-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
          }
        `}</style>
      </div>
    </div>
  );
};

export default MapIKN;
