import LikeButton from "./LikeButton";
import { But<PERSON> } from "@/components/ui/button";
import { getPPILikeService, getIsPPILikeService } from "@/services/LikeService";
import { Eye, MapPin, ThumbsUp } from "lucide-react";
import Image from "next/image";
import React from "react";
import { useState, useEffect } from "react";

const CardDetail = ({ data, id }) => {
  const [likes, setLikes] = useState(0);
  const [isLiked, setIsLiked] = useState(false);

  useEffect(() => {
    const fetchLikesAndStatus = async () => {
      try {
        // Fetch jumlah likes
        const likesResponse = await getPPILikeService(id);
        setLikes(likesResponse.data.jumlah);

        // Fetch status is_like
        const isLikedResponse = await getIsPPILikeService(id);
        setIsLiked(isLikedResponse.data.is_like);
      } catch (error) {
        console.error("Error fetching likes or like status:", error);
      }
    };

    fetchLikesAndStatus();
  }, [id]);
  return (
    <div className="flex items-center justify-center gap-32 pt-20 mx-10 max-w-8xl">
      <div className="flex flex-col">
        <span className="text-[#4CA85F] font-montserrat font-bold text-4xl max-w-md">
          {data.detail.judul}
        </span>
        <span className="text-[#828282] font-montserrat text-2xl mt-5 max-w-md">
          {data.detail.nama_kabkot}
        </span>
        <div className="flex text-[#2D3A96] font-bold font-montserrat mt-4">
          <MapPin />
          <span className="ml-2">{data.detail.nama_provinsi}</span>
        </div>
        <div className="flex items-center gap-4 mt-4">
          <LikeButton id_peluang_kabkot={id} />
          {/* Tampilan total_pengunjung mirip dengan LikeButton */}
          <div className="flex items-center gap-2 px-4 py-2 mt-4 rounded-lg bg-[#4CA85F] hover:bg-[#3B884C] text-white font-montserrat text-md">
            <Eye size={20} color="#fff" /> {/* Icon mata */}
            <span>{data.detail.total_pengunjung}</span>
          </div>
        </div>
      </div>
      <div className="relative pb-10 max-w-content">
        <Image
          src={data.detail.image}
          width={800}
          height={400}
          className="rounded-xl w-[700px] h-[430px] object-cover"
          alt="image"
        />
        <div className="absolute top-4 right-4 font-bold bg-white border rounded-lg font-montserrat text-xl text-[#2D3A96] px-7 py-2 max-w-xl">
          {data.detail.nama_sektor}
        </div>
        {/* <div className="absolute p-2 bg-white rounded-full mb-9 bottom-4 right-4">
          <ThumbsUp className="w-6 h-6" fill="#4CA85F" color="#4CA85F" />
        </div> */}
      </div>
    </div>
  );
};

export default CardDetail;
