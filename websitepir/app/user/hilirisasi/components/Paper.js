"use client";

import useLanguageStore from "../../stores/languageStore";
import CardSlider from "./cardslider";
import { Button } from "@/components/ui/button";
import { useStandardFetchingData } from "@/hooks/useStandardFetchingData";
import { postCountViewerService } from "@/services/LikeService";
import useRoadmapStore from "@/store/RoadmapStore";
import { useRouter, useSearchParams } from "next/navigation";
import { useState, useEffect } from "react";

const handleDownload = async ({ fileUrl, name }) => {
  window.open(fileUrl, "_blank");
};

const scrollTo = (type) => {
  const section = document.getElementById(type);
  if (section) {
    section.scrollIntoView({ behavior: "smooth" });
  }
};

const Paper = () => {
  const route = useRouter();
  const searchParams = useSearchParams();
  const id_komoditi = searchParams.get("id");
  const [scrollPosition, setScrollPosition] = useState(0);
  const { language } = useLanguageStore();
  const {
    data: roadMapData,
    fetchDataRoadmap,
    selectedKomoditi,
    setSelectedKomoditi,
  } = useRoadmapStore();

  const { data: dataKajian } = useStandardFetchingData(
    `/fe/artikel/get_kajian_hilirisasi/${selectedKomoditi}${
      language === "en" ? "?en=true" : ""
    }`
  );

  useEffect(() => {
    const initializeData = async () => {
      await fetchDataRoadmap(1, "", {}, language);
    };

    initializeData();
  }, [language]);

  // useEffect(() => {
  //   if (roadMapData.length > 0) {
  //     if (
  //       !id_komoditi ||
  //       !roadMapData.some((item) => item.id_komoditi === Number(id_komoditi))
  //     ) {
  //       const firstId = roadMapData[0].id_komoditi;
  //       route.replace(`/user/hilirisasi?id=${firstId}`);
  //       setSelectedKomoditi(firstId);
  //     } else {
  //       setSelectedKomoditi(Number(id_komoditi));
  //     }
  //   }
  // }, [roadMapData, id_komoditi]);

  useEffect(() => {
  if (roadMapData.length > 0) {
    let resolvedId;
    if (
      !id_komoditi ||
      !roadMapData.some((item) => item.id_komoditi === Number(id_komoditi))
    ) {
      resolvedId = roadMapData[0].id_komoditi;
      route.replace(`/hilirisasi?id=${resolvedId}`);
    } else {
      resolvedId = Number(id_komoditi);
    }

    setSelectedKomoditi(resolvedId);

    // Trigger count viewer setelah id ditetapkan
    const countViewer = async () => {
      try {
        await postCountViewerService({id_halaman_pengunjung: 9, id_konten: resolvedId });
      } catch (error) {
        console.error("Error counting viewer:", error);
      }
    };

    countViewer();
  }
}, [roadMapData, id_komoditi]);


  const handleCardClick = (id) => (e) => {
    e.preventDefault();
    setSelectedKomoditi(id);
    route.push(`/hilirisasi?id=${id}`);
  };

  // Optional: Show loading state while redirecting
  if (!selectedKomoditi && roadMapData.length > 0) {
    return <div>Loading...</div>;
  }


  return (
    <div className="p-8">
      <div className="relative flex items-start justify-between gap-8 my-6">
        <div className="sticky top-0 right-0 w-2/12 p-4 bg-white border rounded-lg shadow-md">
        <div className="mb-6 bg-[#2D3B96] mt-[-10px] p-3 rounded-lg">
        <center><h1 className="text-2xl font-bold text-white font-montserrat">{language === "en" ? "Commodity" : "Komoditas"}</h1></center>
        </div>
          {roadMapData.map((item, index) => (
            <div key={index}>
              <Button
                onClick={handleCardClick(item.id_komoditi)}
                className={`flex justify-start w-full text-left font-montserrat transition-colors ${
                  selectedKomoditi === item.id_komoditi
                    ? "bg-[#00A551] text-white hover:bg-[#00A551]/90"
                    : "bg-white text-black hover:bg-[#00A551]/10"
                }`}
              >
                {item.nama_komoditi}
              </Button>
              <hr className="my-2" />
            </div>
          ))}
        </div>

        <div className="w-10/12">
          <div
            id="roadmap"
            className="w-full p-4 mb-8 bg-white rounded-lg shadow-md"
            style={{ background: "#00A551" }}
          >
            <p className="text-2xl font-bold text-white font-montserrat">
              {language === "en" ? "Roadmap" : "Roadmap"}
            </p>
          </div>

          <div className="w-full p-8 bg-white rounded-lg shadow-md">
            <div>
              {roadMapData.map((item) => {
                if (selectedKomoditi === item.id_komoditi) {
                  return (
                    <div key={item.id_komoditi}>
                      <h2 className="mb-4 text-lg font-semibold font-montserrat text-[#2D3B96] text-xl">
                        {item.nama_komoditi}
                      </h2>
                    </div>
                  );
                }
                return null;
              })}
            </div>

            <div className="mt-6">
              {roadMapData
                .find((item) => item.id_komoditi === selectedKomoditi)
                ?.roadmaps?.map((item) => {
                  const pdfFiles = item.image.filter((img) =>
                    img.endsWith(".pdf")
                  );

                  return (
                    <div key={item.id_roadmap} className="mb-6">
                      <h2 className="mb-4 text-lg font-semibold font-montserrat text-[#2D3B96]">
                        {item.judul}
                      </h2>
                      <p className="mb-4 text-sm font-montserrat">
                        {item.deskripsi}
                      </p>

                      <div className="flex flex-col gap-4">
                        {item.image
                          .filter((img) => !img.endsWith(".pdf"))
                          .map((img, index) => (
                            <img
                              key={index}
                              src={img}
                              alt={`Image ${index + 1}`}
                              className="w-full h-auto rounded-lg"
                            />
                          ))}
                      </div>

                      {pdfFiles.length > 0 && (
                        <div className="flex flex-col items-start mt-4 space-y-2">
                          {pdfFiles.map((pdf, index) => (
                            <a
                              key={index}
                              href={pdf}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center px-4 py-2 space-x-2 transition-colors rounded-md hover:bg-gray-100"
                            >
                              <img
                                src="/pdf-icons.png"
                                width={30}
                                height={30}
                                alt="PDF icon"
                                className="object-contain"
                              />
                              <span className="text-sm font-medium text-gray-600">
                                {pdfFiles.length > 1
                                  ? `Download PDF Part ${index + 1}`
                                  : "Download PDF"}
                              </span>
                            </a>
                          ))}
                        </div>
                      )}
                    </div>
                  );
                })}
            </div>
          </div>

          <div
            className="w-full p-4 my-8 bg-white rounded-lg shadow-md"
            style={{ background: "#00A551" }}
          >
            <p
              id="kajian"
              className="text-2xl font-bold text-white font-montserrat"
            >
              {language === "en" ? "Study" : "Kajian"}
            </p>
          </div>

          <div className="w-full p-8 bg-white rounded-lg shadow-md">
            {dataKajian?.data?.map((item) => (
              <div key={item.id_kebijakan}>
                <div className="flex flex-wrap items-center justify-between gap-4 py-4 border-gray-300">
                  <div className="flex gap-8">
                    <div
                      style={{
                        minWidth: 200,
                        maxWidth: 200,
                        minHeight: 200,
                        maxHeight: 200,
                      }}
                    >
                      <img
                        src={item.cover}
                        alt="PDF Icon"
                        className="object-cover w-full h-full"
                      />
                    </div>
                    <div>
                      <div className="block">
                        <p className="text-lg font-semibold font-montserrat">
                          {item.judul}
                        </p>
                        <p className="text-sm font-montserrat">
                          {item.deskripsi_singkat || "-"}
                        </p>
                      </div>
                      <div className="flex justify-end gap-4 mt-4">
                        <Button
                          variant={"secondary"}
                          onClick={() =>
                            handleDownload({
                              fileUrl: item.files[0].url,
                              name: item.files[0].nama,
                            })
                          }
                          className="px-4 py-2 font-semibold rounded-lg font-montserrat"
                        >
                          {language === "en" ? "Download" : "Unduh"}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
                <hr className="w-full mx-auto" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Paper;
