"use client";

import {
  <PERSON><PERSON><PERSON>rumb,
  <PERSON>readcrumb<PERSON><PERSON>,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import useNavStore from "@/store/NavStore";
import axios from "axios";
import { motion } from "framer-motion";
import Image from "next/image";
import React, { useState, useEffect } from "react";
import useLanguageStore from "../../stores/languageStore";

const Hero = ({ title, link, backgroundImage }) => {
  // const [data, setData] = useState([]);
  // const [currentIndex, setCurrentIndex] = useState(0);

  // const fetchData = async () => {
  //   try {
  //     const response = await axios.get(`${process.env.NEXT_PUBLIC_BASE_URL}/peluang/slider`);
  //     if (response.data.success) {
  //       setData(response.data.data.data);
  //     } else {
  //       console.error('Gagal memuat data');
  //     }
  //   } catch (error) {
  //     console.error('Gagal memuat data:', error.message);
  //   }
  // };

  // useEffect(() => {
  //   fetchData();
  // }, []);

  // useEffect(() => {
  //   const intervalId = setInterval(() => {
  //     setCurrentIndex((prevIndex) => (prevIndex + 1) % data.length);
  //   }, 5000);

  //   return () => clearInterval(intervalId);
  // }, [data.length]);

  const translations = {
    id : {
      title : "Kajian",
      artikel : "Kajian",
      home : "Beranda",
      klik : "Klik"
    },
    en : {
      title : "Study",
      artikel : "Study",
      home : "Home",
      klik : "Click"
    }
  }
  const { language } = useLanguageStore();
  const t = translations[language];

  useEffect(() => {
    // This empty effect will cause the component to re-evaluate 
    // when the language dependency changes
  }, [language]);
  const { currentPage, setCurrentPage } = useNavStore();

  useEffect(() => {
    setCurrentPage("informasi");
  }, [currentPage]);

  return (
    <div>
      <section className="relative flex flex-col items-center justify-start w-full text-white">
        <div
          className="z-10 flex items-center justify-between w-full h-[200px] bg-[#2D3B96]"
        >
          <div className="ml-[150px]">
            <h1 className="font-bold font-montserrat 2xl:text-[40px] xl:text-[35px] md:text-[30px] sm:text-[30px]">
              {t.title}
            </h1>
          </div>
          <div className="text-xl font-montserrat">
            <Breadcrumb className="mr-[150px]">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">{t.home}</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/artikel">{t.artikel}</BreadcrumbLink>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Hero;
