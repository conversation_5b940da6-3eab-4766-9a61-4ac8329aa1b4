"use client";

import CardWithImage from "./CardWithImage";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import useLanguageStore from "../stores/languageStore";
import { getSektorUnggulanWilayah } from "@/services/getMapService";
import useProvinceStore from "@/store/useProvinceStore";
import { Map } from "lucide-react";
import { useRouter } from "next/navigation";
import { set } from "react-hook-form";
import Slider from "react-slick";
import styles from "./SektorUnggulanWilayah.module.css";

const SektorUnggulanWilayah = () => {
  const router = useRouter();
  const { selectedProvinceId, selectedProvinceName, setAutoRotating } =
    useProvinceStore();
  const { language } = useLanguageStore();
  const [sektorUnggulanWilayah, setSektorUnggulanWilayah] = useState([
    {
      nama: "Peluang Investasi",
      tahun: "2024",
      value: "100",
    },
    {
      nama: "Peluang Investasi",
      tahun: "2024",
      value: "100",
    },
  ]);
  const [selectedSectorWilayah, setSelectedSectorWilayah] = useState([
    {
      nama_provinsi: "Nama Provinsi",
      tahun: "2024",
      value: "100",
    },
    {
      nama_provinsi: "Peluang Investasi",
      tahun: "2024",
      value: "100",
    },
  ]);
  const [selectedItem, setSelectedItem] = useState(0);
  const [slideCount, setSlideCount] = useState(2);
  const [sliderSettings, setSliderSettings] = useState({
    dots: false,
    infinite: false,
    speed: 500,
    slidesToShow: 2, // Menampilkan 2 item sesuai permintaan
    slidesToScroll: 1,
    arrows: true,
    adaptiveHeight: false,
    autoplay: false,
    speed: 1500,
    autoplaySpeed: 3000,
    pauseOnHover: true,
    centerMode: false,
    initialSlide: 0,
    swipeToSlide: true,
    cssEase: "linear",
    responsive: [
      {
        breakpoint: 1280,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
          infinite: false,
          centerMode: false,
          variableWidth: false,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          infinite: false,
          centerMode: false,
          variableWidth: false,
        },
      },
    ],
    variableWidth: false,
  });

  const handleClickedPeluangInvestasiCard = (id_adm_kabkot, status) => {
    if (status === "IPRO") {
      return () => {
        router.push(`/peluang_investasi/ipro/${id_adm_kabkot}`);
      };
    } else if (status === "PID") {
      return () => {
        router.push(`/daerah/peluang_investasi/${id_adm_kabkot}`);
      };
    }
    return () => {
      router.push(`/peluang_investasi/detailed/${id_adm_kabkot}`);
    };
  };

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setSliderSettings(prevSettings => ({
          ...prevSettings,
          slidesToShow: 1,
          centerMode: false,
          variableWidth: false,
          infinite: false,
          arrows: true,
          autoplay: false,
        }));
      } else if (window.innerWidth < 1280) {
        setSliderSettings(prevSettings => ({
          ...prevSettings,
          slidesToShow: 2, // Tetap menampilkan 2 item
          centerMode: false,
          variableWidth: false,
          infinite: false,
          arrows: true,
          autoplay: false,
        }));
      } else {
        setSliderSettings(prevSettings => ({
          ...prevSettings,
          slidesToShow: 2, // Tetap menampilkan 2 item
          centerMode: false,
          variableWidth: false,
          infinite: false,
          arrows: true,
          autoplay: false,
        }));
      }
    };

    handleResize(); // Call once on mount
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    setSliderSettings(prevSettings => ({
      ...prevSettings,
      infinite: false, // Ensure slider doesn't loop
      autoplay: false, // Disable autoplay to prevent looping
    }));
  }, [slideCount]);

  useEffect(() => {
    async function fetchData() {
      try {
        const response = await getSektorUnggulanWilayah(
          selectedProvinceId,
          language
        );
        
        // Tambahkan timestamp ke URL gambar untuk mencegah caching
        const timestamp = new Date().getTime();
        const peluangInvestasi = response.data.peluang_investasi.map(item => ({
          ...item,
          // Tambahkan parameter timestamp ke URL gambar jika ada
          image: item.image ? `${item.image}?t=${timestamp}` : `/no-data-image.png?t=${timestamp}`
        }));
        
        setSektorUnggulanWilayah(peluangInvestasi);
        
        // Untuk sektor unggulan, jangan tambahkan timestamp ke icon_path
        const sektorUnggulan = response.data.sektor_unggulan.map(item => ({
          ...item,
          // Gunakan icon_path asli tanpa timestamp
          icon_path: item.icon_path || "/no-data-image.png"
        }));
        
        setSelectedSectorWilayah(sektorUnggulan);
        setSlideCount(response.data.peluang_investasi.length);
      } catch (error) {
        console.error("Error in getSektorUnggulanWilayah:", error);
      }
    }
    fetchData();
  }, [selectedProvinceId, language]);

  return (
    <div className="pt-16 w-full">
      <div className="grid grid-cols-8 text-[#2D3A96] font-montserrat font-bold"></div>
      <div className="flex justify-center w-full">
        <div className="flex flex-col w-full min-h-[700px] max-w-[1400px] mx-auto px-5 sm:px-10 bg-[#2D3A96] rounded-xl mb-16 overflow-hidden">
          {/* Sektor Unggulan Wilayah Section */}
          <div className="w-full pt-10 pb-5 px-4 sm:px-6 md:px-10">
            <div className="flex flex-col text-white font-montserrat">
              <span className="text-3xl font-bold">
                {language === "en"
                  ? "Regional Leading Sector"
                  : "Sektor Unggulan Wilayah"}
              </span>
              <span className="pt-2 text-2xl font-semibold">
                {selectedProvinceName}
              </span>
            </div>
            <div className="flex flex-wrap justify-start gap-5 pt-6 pb-2 text-white font-montserrat text-[12px]">
              {selectedSectorWilayah.map((item, index) => (
                <button
                  key={index}
                  className={`flex flex-col items-center p-2 rounded-lg hover:shadow-lg hover:border ${
                    selectedItem === item.index
                      ? "bg-blue-500 border-white"
                      : ""
                  }`}
                  onClick={() => {
                    setSelectedItem(index);
                    // setAutoRotating(false);
                  }}
                >
                  <Image
                    src={item.icon_path || "/no-data-image.png"}
                    width={48}
                    height={48}
                    alt="icon"
                  />
                  {item.nama_sektor}
                </button>
              ))}
            </div>
            <div className="font-montserrat font-[18px] pt-3 text-justify text-white">
              {selectedSectorWilayah && selectedSectorWilayah[selectedItem]
                ? selectedSectorWilayah[selectedItem].deskripsi
                : "Data tidak tersedia"}
            </div>
          </div>
          
          {/* Peluang Investasi Section with Slider */}
          <div className="w-full px-4 sm:px-6 md:px-10 pb-10">
            <div className="flex justify-start">
              {/* <div className="w-16 bg-white h-[4px] mt-2"></div> */}
              <h1 className="pl-2 text-3xl font-bold text-white font-montserrat">
                {language === "en"
                  ? "Investment Opportunities"
                  : "Peluang Investasi"}
              </h1>
            </div>
            <div className="w-full mx-auto mt-5 mb-5 overflow-hidden">
              {sektorUnggulanWilayah.length > 0 ? (
                <div className="w-full max-w-full">
                  <Slider 
                    {...sliderSettings} 
                    className={styles.sektorUnggulanSlider}
                    key={`slider-${sektorUnggulanWilayah.length}-${JSON.stringify(sektorUnggulanWilayah)}`} // Force re-render when data changes
                  >
                    {Array.isArray(sektorUnggulanWilayah) ? (
                      sektorUnggulanWilayah.length > 0 ? (
                        sektorUnggulanWilayah.map((sektor, index) => (
                          <div
                          key={`sektor-${sektor.id_peluang}-${index}-${new Date().getTime()}`}
                          className="cursor-pointer px-2"
                          onClick={handleClickedPeluangInvestasiCard(
                            sektor.id_peluang,
                            sektor.status
                          )}
                        >
                          <CardWithImage
                            sourceImage={sektor.image || `/no-data-image.png?t=${new Date().getTime()}`}
                            title={sektor.nama || ""}
                            alamat={
                              `${sektor.nama_kabkot || ""} ${sektor.nama_provinsi || ""}` || ""
                            }
                            value={
                              sektor.nilai_investasi || ""
                            }
                            tahun={sektor.tahun || ""}
                            status={sektor.status || ""}
                          />
                        </div>
                      ))
                    ) : (
                      <span className="text-white text-2xl font-bold font-montserrat">
                        {language === "en" ? "No Data Available" : "Data Tidak Tersedia"}
                      </span>
                      // <div className="px-2">
                      //   <CardWithImage
                      //     sourceImage="/no-data-image.png"
                      //     title="No Sectors Available"
                      //     alamat="N/A"
                      //     value="N/A"
                      //     tahun="N/A"
                      //   />
                      // </div>
                    )
                  ) : (
                    <div className="px-2">
                      Error: Invalid data format for sektorUnggulanWilayah
                    </div>
                  )}
                </Slider>   
              </div>
            ) : (
              <div className="flex justify-center items-center w-full">
                <span className="text-white text-2xl font-bold font-montserrat">
                  {language === "en" ? "No Data Available" : "Data Tidak Tersedia"}
                </span>
              </div>
            )} 
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SektorUnggulanWilayah;
