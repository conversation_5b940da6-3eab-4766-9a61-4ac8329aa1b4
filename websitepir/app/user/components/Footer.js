import { FOOTER_LINKS } from "../constants/constant";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Phone, House, Copyright, LogIn } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import useLanguageStore from "../stores/languageStore";

// const Footer = () => {
//   return (
//     <div>
//       <footer className='flex flex-row justify-between bg-[#2D3A96] text-white w-full h-full'>
//         <div className='flex flex-col w-[800px] h-[322px] p-10 justify-center'>
//           <h1 className='font-bold font-montserrat text-[20px]'>Kementerian Investasi/BKPM</h1><br/>
//           <div className='flex flex-cols'>
//             <House size={30}/>
//             <h3 className='font-montserrat text-[16px] ml-2'>
//               Kantor Pusat: Jl. Jend. Gatot Subroto No. 44, Jakarta 12190 P.O. Box 3186, Indonesia
//             </h3>
//           </div>
//           <div>
//             <h3 className='flex font-montserrat text-[16px]'><Phone size={30} className='mr-2'/>+6221 525 2008</h3>
//           </div>
//         </div>

//         {/* <div className='flex items-center justify-end'>
//           <div className='flex flex-wrap gap-[30px] pr-10 sm:justify-end md:flex-1'>
//             {FOOTER_LINKS.map((columns, index) => (
//               <FooterColumn key={index} title={columns.title}>
//                 <ul className="flex flex-col gap-2 regular-14 text-gray-30">
//                   {columns.links.map((link, linkIndex) => (
//                     <Link href="/" key={`${columns.title}-${linkIndex}`}>
//                       {link}
//                     </Link>
//                   ))}
//                 </ul>
//               </FooterColumn>
//             ))}
//           </div>
//         </div> */}
//       </footer>
//       <div className='h-[1px] bg-white'></div>
//       <div className='w-full font-montserrat text-[13px] h-[70px] bg-[#2D3A96] text-white p-5'>
//         Hak Cipta 2024 Kementrian Investasi/BKPM All Rights Reserved
//       </div>
//     </div>
//   )
// }

const FooterColumn = ({ title, children }) => {
  return (
    <div className="flex flex-col gap-5">
      <div className="bold-18 whitespace-nowrap font-montserrat text-[14px] font-bold">
        {title}
      </div>
      <div className="font-montserrat text-[13px]">{children}</div>
    </div>
  );
};

const Footer = () => {
  const NEXT_PUBLIC_API_BASE_URL_GEO_LOGIN = process.env.NEXT_PUBLIC_API_BASE_URL_GEO_LOGIN || "/geoportal";
  
  return (
    <div className="relative w-full h-[20vh] sm:h-[25vh] md:h-[30vh] lg:h-[35vh] xl:h-[40vh]">
      <Image
        src="/footer_bkpm.png"
        alt="footer"
        fill
        className="object-cover"
        priority
      />
      <div className="relative z-10 w-full h-full">
        <div className="flex justify-between h-full">
          <div className="flex flex-col justify-center ml-10">
            <div>
              <Image
                src="/footer_logo.png"
                alt="logo"
                width={400}
                height={52}
                className="mb-2"
              />
              <p className="text-white text-md font-montserrat">
                Kantor Pusat: JL. Jend. Gatot Subroto No.44,
              </p>
              <p className="text-white text-md font-montserrat">
                Jakarta 12190 P.O. Box 3186, Indonesia
              </p>
              <p className="mt-10 text-white text-md font-montserrat">
                +6221 525 2008
              </p>
            </div>
          </div>
          <div className="flex flex-col items-end justify-center mr-10 space-y-4">
            <div className="flex space-x-3">
              <Link href="/admin">
                <Button className="bg-white text-[#2D3A96] hover:bg-gray-200 font-montserrat flex items-center gap-2 px-4 py-2 rounded-md transition-all">
                  <LogIn size={16} />
                  <span>Admin PIR</span>
                </Button>
              </Link>
              <Link href={NEXT_PUBLIC_API_BASE_URL_GEO_LOGIN}>
                <Button className="bg-[#2D3A96] text-white border border-white hover:bg-[#1e2a7a] font-montserrat flex items-center gap-2 px-4 py-2 rounded-md transition-all">
                  <LogIn size={16} />
                  <span>Geoportal</span>
                </Button>
              </Link>
            </div>
            <div className="flex items-center">
              <Copyright color="white" className="mr-2" />
              <p className="text-white text-md font-montserrat">
                {new Date().getFullYear()} Potensi Investasi Regional -
                Kementerian Investasi dan Hilirisasi/BKPM
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Footer;
