"use client";

import LanguageSwitcher from "@/app/user/components/LanguageSwitcher";
import { NAV_LINKS } from "@/app/user/constants/constant";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import useNavStore from "@/store/NavStore";
import useInsentifStore from "@/store/useInsentifStore";
import { AnimatePresence, motion } from "framer-motion";
import { ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React, { useState, useEffect } from "react";
import useLanguageStore from "../stores/languageStore";

const Navbar = () => {
  const [navLinks, setNavLinks] = useState(NAV_LINKS);
  const { data, fetchDataInsentif } = useInsentifStore();
  const { language, setLanguage } = useLanguageStore();
  const { currentPage, setCurrentPage } = useNavStore();
  const [open, setOpen] = useState(false);
  
  const getLabel = (link) => (language === "en" ? link.label_en : link.label); // Tentukan label berdasarkan bahasa

  const FlyoutLink = ({ children, href, FlyoutContent, data }) => {
    const [open, setOpen] = useState(false);

    const showFlyout = FlyoutContent && open;

    return (
      <div
        onMouseEnter={() => setOpen(true)}
        onMouseLeave={() => setOpen(false)}
        className={`relative w-fit h-fit rounded-xl hover:bg-[#4CA85F] text-[#033783] hover:text-white hover:rounded-md hover:p-1 ${
          currentPage === data
            ? "border-b-[2px] border-[#033783] rounded-b-none"
            : ""
        }`}
      >
        <a href={href} className="relative text-sm font-medium font-montserrat">
          {children}
          {/* <span
            style={{
              transform: showFlyout ? "scaleX(1)" : "scaleX(0)",
            }}
            className="absolute h-1 transition-transform duration-300 ease-out origin-left scale-x-0 bg-[#033783] rounded-full -bottom-2 -left-2 -right-2"
          /> */}
        </a>
        <AnimatePresence>
          {showFlyout && (
            <motion.div
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 15 }}
              style={{ translateX: "-50%", radius: "0.5rem" }}
              transition={{ duration: 0.3, ease: "easeOut" }}
              className="absolute z-20 text-black bg-white left-1/2 top-12 roundex-xl"
            >
              <div className="absolute left-0 right-0 h-6 bg-transparent -top-6" />
              <div className="absolute top-0 w-4 h-4 rotate-45 -translate-x-1/2 -translate-y-1/2 bg-[#4CA85F] left-1/2" />
              <FlyoutContent />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  const PricingContent = ({ data }) => {
    const translations = {
      id: {
        kebijakan: "Kebijakan",
        insentif: "Insentif",
        artikel: "Kajian",
      },
      en: {
        kebijakan: "Policy",
        insentif: "Incentive",
        artikel: "Study",
      },
    };

    const t = translations[language];

    return (
      <div
        className={`w-48 h-48 p-6 bg-[#4CA85F] shadow-xl font-bold text-white font-montserrat `}
      >
        <div className="mb-6 space-y-3">
          <a href="/kebijakan" className="block text-sm hover:underline">
            {t.kebijakan}
          </a>
          <div className="h-[1px] bg-white w-full"></div>
          <a href="/insentif" className="block text-sm hover:underline">
            {t.insentif}
          </a>
          <div className="h-[1px] bg-white w-full"></div>
          <a href="/artikel" className="block text-sm hover:underline">
            {t.artikel}
          </a>
          <div className="h-[1px] bg-white w-full"></div>
        </div>
      </div>
    );
  };

  useEffect(() => {}, [currentPage]);

  return (
    <nav className="flex py-[16px] justify-between items-center max-container bg-white">
      <div className="pl-12">
        <Link href="/" className="flex-shrink-0">
          <Image unoptimized src="/navbar_logo.png" alt="logo" width={284} height={32} />
        </Link>
      </div>
      <ul className="flex items-center justify-center flex-1 gap-4 max-lg:hidden">
        {navLinks.map((link) => (
          <li key={link.key}>
            {link.dropdown ? (
              <div
                className={`flex justify-center px-3 font-montserrat text-[#033783] mb-[3px] rounded-xl `}
              >
                <FlyoutLink
                  href="#"
                  FlyoutContent={PricingContent}
                  data={link.key}
                >
                   {getLabel(link)}
                </FlyoutLink>
              </div>
            ) : (
           
              <Link
                href={link.href}
                className={`text-[#033783] inline-flex items-center px-1 pt-1 text-sm font-medium font-montserrat cursor-pointer pb-1.5 transition-all hover:bg-[#4CA85F] hover:text-white hover:rounded-lg hover:p-2 ${
                  currentPage === link.key
                    ? "border-b-[2px] border-[#033783]"
                    : ""
                } p-2 transition-all hover:bg-[#4CA85F] hover:text-white hover:rounded-lg`}
              >
                {getLabel(link)}
              </Link>
            )}
          </li>
        ))}
      </ul>
      <div className="flex items-center justify-center gap-3 pr-6">
        <LanguageSwitcher />
      </div>
      <div className="hidden mr-10 max-lg:block">
        <Image  unoptimized src="/hamburger.svg" width={24} height={16} alt="menu" />
      </div>
    </nav>
  );
};

export default Navbar;
