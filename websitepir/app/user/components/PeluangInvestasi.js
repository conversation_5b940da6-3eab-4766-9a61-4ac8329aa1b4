"use client";

import { mediumCard } from "../constants/constant";
import { peluangInvestasi } from "../constants/constant";
import useLanguageStore from "../stores/languageStore";
import CardPeluangInvestasi from "./CardPeluangInvestasi";
import CircleMenuInvestment from "./CircleMenu";
import MediumCard from "./MediumCard";
import MiniCard from "./MiniCard";
import MiniMediumCard from "./MiniMediumCard";
import StretchMenu from "./StretchMenu";
import WheelComponent from "./WheelNav";
import { Button } from "@/components/ui/button";
import { getPeluangInvestasiDetails } from "@/services/getHomeServices";
import PeluangInvestasiStore from "@/store/PeluangInvestasiStore";
import { ChevronRight, ChevronLeft } from "lucide-react";
import Image from "next/image";
import React, { useState, useEffect, useRef } from "react";
import Slider from "react-slick";
import { getRunningTextService } from "@/services/getHomeServices";

// import {wheelnav} from 'wheelnav'
// import R from 'raphael'

const PeluangInvestasi = () => {
  const { language } = useLanguageStore();
    const [runningText, setRunningText] = useState("");
  
  var {
    selectedPeluangInvestasiName,
    selectedPeluangInvestasiId,
    setSelectedPeluangInvestasiId,
    selectedPeluangInvestasiLogo,
  } = PeluangInvestasiStore();
  const [slideCount, setSlideCount] = useState(2);
  const [selectedPeluangInvestasiDetails, setSelectedPeluangInvestasiDetails] =
    useState([
      {
        nama: "Peluang Investasi Detail",
        tahun: "2024",
        deskripsi:
          "Peluang Investasi berkelanjutan sangat lah penting bagi masyarakat Indonesia, karena satu dan lain hal itu akan menjadi penting untuk kita sama sama menjaganya",
        lokasi_kawasan: "Jawa barat",
        nilai_irr: "10%",
        npv: "Rp 100 M",
        nilai_pp: "3 Tahun",
        nilai_investasi: "Rp 50 M",
      },
      {
        nama: "Peluang Investasi Detail",
        tahun: "2024",
        deskripsi:
          "Peluang Investasi berkelanjutan sangat lah penting bagi masyarakat Indonesia, karena satu dan lain hal itu akan menjadi penting untuk kita sama sama menjaganya",
        lokasi_kawasan: "Jawa Timur",
        nilai_irr: "10%",
        npv: "Rp 100 M",
        nilai_pp: "3 Tahun",
        nilai_investasi: "Rp 50 M",
      },
      {
        nama: "Peluang Investasi Detail",
        tahun: "2024",
        deskripsi:
          "Peluang Investasi berkelanjutan sangat lah penting bagi masyarakat Indonesia, karena satu dan lain hal itu akan menjadi penting untuk kita sama sama menjaganya",
        lokasi_kawasan: "Jawa Timur",
        nilai_irr: "10%",
        npv: "Rp 100 M",
        nilai_pp: "3 Tahun",
        nilai_investasi: "Rp 50 M",
      },
    ]);
  const wheelContainerRef = useRef();

  const SamplePrevArrow = (props) => {
    const { className, style, onClick } = props;
    return (
      <div
        className="slick-arrow"
        style={{
          ...style,
          position: "absolute",
          left: "-23px",
          top: "50%",
          transform: "translateY(-50%)",
          cursor: "pointer",
          display: "block",
        }}
        onClick={onClick}
      >
        <ChevronLeft style={{ color: "white" }} />
      </div>
    );
  };

  function SampleNextArrow(props) {
    const { className, style, onClick } = props;
    return (
      <div
        className="slick-arrow"
        style={{
          ...style,
          display: "block",
          position: "absolute",
          right: "-23px",
          top: "50%",
          transform: "translateY(-50%)",
          cursor: "pointer",
        }}
        onClick={onClick}
      >
        <ChevronRight style={{ color: "white" }} />
      </div>
    );
  }
  const settings = {
    dots: false,
    infinite: slideCount > 2,
    speed: 1000,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: true,
    nextArrow: <SampleNextArrow />,
    prevArrow: <SamplePrevArrow />,
    autoplay: true,
    autoplaySpeed: 3000,
  };

  useEffect(() => {
    async function fetchData() {
      try {
        // console.log('selectedPeluangInvestasiId adalah: ', selectedPeluangInvestasiId)
        const data = await getPeluangInvestasiDetails(
          selectedPeluangInvestasiId, language
        );
        // console.log('data detail: ', data)
        setSlideCount(selectedPeluangInvestasiDetails.length);
        setSelectedPeluangInvestasiId(selectedPeluangInvestasiId);
        setSelectedPeluangInvestasiDetails(data);
      } catch (error) {
        console.error("Error in getPeluangInvestasiDetails:", error);
      }
    }
    fetchData();
  }, [selectedPeluangInvestasiId, language]);

   useEffect(() => {
        // Fetch running text on component mount
        const fetchRunningText = async () => {
          try {
            const response = await getRunningTextService(language);
            if (response?.data?.length) {
              const activeTexts = response.data
                .map((item) => item.keterangan)
                .join(" | "); // Separator sementara untuk memisahkan item
              setRunningText(activeTexts);
            }
            
          } catch (error) {
            console.error("Error fetching running text:", error);
          }
        };
    
        fetchRunningText();
      }, [language]);
  // 2xl:gap-[30px] xl:gap-none
  return (
    <div>
      {runningText && (
        <div className="w-full bg-[#2D3A96] mb-10 text-white py-2 overflow-hidden font-montserrat">
          <marquee>
            {runningText.split("|").map((text, index, array) => (
              <React.Fragment key={index}>
                <span className="inline-block text-2xl font-bold">{text}</span>
                {index !== array.length - 1 && ( // Tambahkan separator kecuali untuk elemen terakhir
                  <span className="mx-5 text-white">•</span>
                )}
              </React.Fragment>
            ))}
          </marquee>
        </div>
      )}
      <div className="flex mt-10 flex-row bg-[#4CA85F] gap-[10px] rounded-xl shadow-2xl mb-10 pb-10 mx-10 2xl:mx-[100px] h-max justify-center">
        {/* <div className="flex flex-row bg-[#4CA85F] gap-[10px] shadow-2xl h-max justify-center "> */}
     

        <div className="flex pt-10 pb-10 font-montserrat">
          {/* <StretchMenu/> */}
          {/* <CircleMenuInvestment/> */}
          <WheelComponent />
        </div>
        <div className="flex flex-col">
          <h4 className="p-10 text-[30px] text-white font-montserrat font-bold w-fit">
          {language === "en" ? "INVESTMENT OPPORTUNITIES" : "PELUANG INVESTASI"}
          </h4>
          <div className="flex text-[36px] font-montserrat font-bold text-white pl-10 pb-5">
            <Image
              src={
                selectedPeluangInvestasiLogo || "icons2/Pariwisata 56 putih.svg"
              }
              alt=""
              width={54}
              height={54}
              className="mr-2 drop-shadow-xl"
            />
            <div className="flex items-center pl-2">
              {selectedPeluangInvestasiName}
            </div>
          </div>
          <div className="mx-6 xl:max-w-4xl 2xl:max-w-4xl lg:max-w-4xl max-h-max">
            <Slider {...settings}>
              {Array.isArray(selectedPeluangInvestasiDetails) ? (
                selectedPeluangInvestasiDetails.length > 0 ? (
                  selectedPeluangInvestasiDetails.map((item, index) => (
                    <div key={index}>
                      <CardPeluangInvestasi
                        id={item.id_peluang_kabkot}
                        title={item.nama || ""}
                        description={item.deskripsi || ""}
                        lokasi={item.lokasi_kawasan || ""}
                        tahun={item.tahun || ""}
                        stats={[
                          {
                            label: "Internal Rate of Return",
                            value: item.nilai_irr || "",
                          },
                          {
                            label: "Net Present Value",
                            value: item.nilai_npv || "",
                          },
                          {
                            label: "Payback Period",
                            value: item.nilai_pp || "",
                          },
                          {
                            label: "Investment Value",
                            value: item.nilai_investasi || "",
                          },
                        ]}
                      />
                    </div>
                  ))
                ) : (
                  <CardPeluangInvestasi
                    id="read-more"
                    title="No Data Available"
                    description="There are currently no investment opportunities to display."
                    lokasi=""
                    tahun=""
                    stats={[
                      { label: "Internal Rate of Return", value: "" },
                      { label: "Net Present Value", value: "" },
                      { label: "Payback Period", value: "" },
                      { label: "Investment Value", value: "" },
                    ]}
                  />
                )
              ) : (
                <div>Error: Invalid data format</div>
              )}
            </Slider>
          </div>
          {/* <div className='flex justify-end 2xl:pr-32 md:p-0'> 
                <Button className='bg-[#2D3A96] text-white font-montserrat font-bold text-[16px] w-[170px] h-[50px] hover:bg-white hover:text-[#2D3A96] rounded-lg p-2'>Selengkapnya</Button>
            </div> */}
          {/* <div className='flex flex-row gap-10 pt-[20px] pl-10 mb-10'>
                { mediumCard.map((card, index) => 
                    <MediumCard key={index} icon={card.icon} title={card.title} perusahaan={card.perusahaan} alamat={card.alamat} email={card.email} portofolio={card.portofolio}/>)
                }
            </div> */}
        </div>
        <div></div>
      </div>
    </div>
  );
};

export default PeluangInvestasi;
