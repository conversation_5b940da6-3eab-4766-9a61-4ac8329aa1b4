"use client";

import { center } from "@turf/turf";
import axios from "axios";
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useState, useEffect, useRef } from "react";
import Slider from "react-slick";
import { ChevronRight, ChevronLeft } from "lucide-react";

const Logos = () => {
  const [data, setData] = useState([]);
  const [isHovering, setIsHovering] = useState(false);
  const scrollRef = useRef(null);
  const [hoveredIndex, setHoveredIndex] = useState(null);
  const router = useRouter();

  const routeLinkTerkait = {
    14: `/eksternal_link/bkpm`,
    18: `/eksternal_link/oss`,
    19: `/eksternal_link/iipc`,
    38: `/eksternal_link/jdih`,
    58: `/eksternal_link/jdih`,
  };

  useEffect(() => {
    const fetchAndAnimate = async () => {
      try {
        const response = await axios.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}/home/<USER>
        );
        setData(response.data.data);
      } catch (error) {
        console.error("Gagal memuat data:", error.message);
      }
    };

    fetchAndAnimate();
  }, []);

  useEffect(() => {
    // Remove the sliding animation logic completely
    // No need for scrollElement or animation
  }, [data, isHovering]);

  const SamplePrevArrow = (props) => {
    const { className, style, onClick } = props;
    return (
      <div
        className="slick-arrow"
        style={{
          ...style,
          position: "absolute",
          left: "-40px",
          top: "50%",
          transform: "translateY(-50%)",
          cursor: "pointer",
          display: "block",
        }}
        onClick={onClick}
      >
        <ChevronLeft style={{ color: "black" }} />
      </div>
    );
  };

  function SampleNextArrow(props) {
    const { className, style, onClick } = props;
    return (
      <div
        className="slick-arrow"
        style={{
          ...style,
          display: "block",
          position: "absolute",
          right: "-40px",
          top: "50%",
          transform: "translateY(-50%)",
          cursor: "pointer",
        }}
        onClick={onClick}
      >
        <ChevronRight style={{ color: "black" }} />
      </div>
    );
  }

  const settings = {
    dots: false,
    infinite: true,
    centerMode: true,
    speed: 500,
    slidesToShow: 4, // Adjust based on your needs
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 1500,
    nextArrow: <SampleNextArrow />,
    prevArrow: <SamplePrevArrow />,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  return (
    <div className="flex flex-col items-center justify-center w-full">
      <div className="w-full py-20 max-w-7xl">
        <Slider {...settings}>
          {data.map((item, index) => (
            <Link
              key={item.id_link_terkait}
              href={`/eksternal_link/${item.id_link_terkait}`}
              onClick={(e) => {
                e.preventDefault();
                router.push(`/eksternal_link/${item.id_link_terkait}`);
              }}
            >
              <div
                className="relative flex-shrink-0 px-2" // Added px-2 for spacing
                style={{
                  width: "180px",
                  height: "90px",
                }}
                onMouseEnter={() => setHoveredIndex(index)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <Image
                  src={`${item.file_logo}`}
                  alt={item.nama}
                  fill
                  sizes="180px"
                  className={`object-contain transition-transform duration-300 ${
                    hoveredIndex === index ? "scale-110" : "scale-100"
                  }`}
                  priority
                />
              </div>
            </Link>
          ))}
        </Slider>
      </div>
    </div>
  );
};

export default Logos;
