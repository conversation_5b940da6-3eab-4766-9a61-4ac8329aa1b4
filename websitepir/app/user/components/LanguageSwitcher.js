'use client';

import useLanguageStore from '@/app/user/stores/languageStore';
import Image from 'next/image';
import { usePathname, useRouter, useParams } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';

export default function LanguageSwitcher() {
  const router = useRouter();
  const { i18n } = useTranslation();
  const pathname = usePathname();
  const routeParams = useParams();
  const { language, setLanguage } = useLanguageStore();
  const [isMounted, setIsMounted] = useState(false);
  
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const changeLanguage = (lang) => {
    setLanguage(lang);
    
    // Only run on the client side
    if (isMounted) {
      // Get current URL search parameters
      const currentUrl = window.location.href;
      const url = new URL(currentUrl);
      const searchParams = url.search;
      
      // Preserve query parameters when changing language
      const targetPath = searchParams ? `${pathname}${searchParams}` : pathname;
      
      router.push(targetPath);
    }
  };

  return (
    <div className="flex items-center justify-center gap-3 pr-6">
      <button
        onClick={() => changeLanguage('id')}
        className={`transition-opacity duration-300 ${language === 'id' ? 'opacity-100' : 'opacity-50 hover:opacity-75'
          }`}
      >
        <Image unoptimized src="/indo.svg" width={36} height={36} alt="Indonesian" />
      </button>
      <button
        onClick={() => changeLanguage('en')}
        className={`transition-opacity duration-300 ${language === 'en' ? 'opacity-100' : 'opacity-50 hover:opacity-75'
          }`}
      >
        <Image unoptimized src="/en-flag.png" width={36} height={36} alt="English" />
      </button>
    </div>
  );
}
