"use client";

import { But<PERSON> } from "@/components/ui/button";
import { MapPin, ChartColumnBig, Calendar } from "lucide-react";
import React, { useState, useEffect } from "react";

const CardWithImage = ({ title, alamat, value, tahun, sourceImage, status }) => {
  // Generate a unique timestamp for this render
  const timestamp = React.useMemo(() => new Date().getTime(), [sourceImage]);
  
  // Add timestamp to image source to prevent caching
  const imageWithTimestamp = React.useMemo(() => {
    const baseImage = sourceImage || "/no-data-image.png";
    // Check if the URL already has query parameters
    const hasParams = baseImage.includes('?');
    return `${baseImage}${hasParams ? '&' : '?'}t=${timestamp}`;
  }, [sourceImage, timestamp]);
  
  const [imgSrc, setImgSrc] = useState(imageWithTimestamp);
  const defaultImage = `/no-data-image.png?t=${timestamp}`; // Using existing no-data-image with timestamp

  // Update imgSrc when sourceImage prop changes
  useEffect(() => {
    setImgSrc(imageWithTimestamp);
  }, [imageWithTimestamp]);

  // Handle image load error
  const handleImageError = () => {
    setImgSrc(defaultImage);
  };

  return (
    <div className="w-full max-w-[470px] ml-2 mt-5 h-[300px]">
      <div className="overflow-hidden transition-all duration-300 bg-white rounded-lg shadow-lg hover:shadow-xl dark:bg-gray-950 h-[300px]">
        <img
          key={`img-${timestamp}`}
          src={imgSrc}
          alt="Product Image"
          width={470}
          height={150}
          className="object-cover w-full h-40"
          style={{ aspectRatio: "470/150", objectFit: "cover" }}
          onError={handleImageError}
        />
        <div className="pt-2 pl-2 pr-2 space-y-2 ">
          <div className="min-h-[48px]">
            <h3 className="text-[16px] font-semibold font-montserrat text-[#2D3A96]">
              {title}
            </h3>
          </div>
          <div className="flex">
            <MapPin />
            <p className="text-gray-500 dark:text-gray-400 font-montserrat">
              {alamat}
            </p>
          </div>
          <div className="flex">
            <ChartColumnBig />
            <p className="text-gray-500 dark:text-gray-400 font-montserrat">
              {value}
            </p>
            <Calendar className="ml-4" />
            <p className="ml-2 text-gray-500 dark:text-gray-400 font-montserrat">
              {tahun}
            </p>
            <div className="flex ml-3 bg-[#FF9500] px-2 py-1 text-white rounded-lg content-center justify-center items-center">
              <p className="text-sm font-medium font-montserrat">{status}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardWithImage;
