.sliderContainer {
  width: 100%;
  position: relative;
  margin: 0 auto;
  overflow: hidden;
  padding: 0 60px; /* Significantly increased padding */
  max-width: 100%; /* Ensure it doesn't exceed parent width */
}

.sektorUnggulanSlider {
  width: 100% !important;
  margin: 0 auto !important;
  padding: 0 !important;
  max-width: 100%; /* Ensure it doesn't exceed parent width */
}

.sektorUnggulanSlider :global(.slick-track) {
  display: flex !important;
  gap: 20px; /* Increased gap between slides */
  margin-left: 0 !important; /* Align to left */
  margin-right: auto !important;
  flex-direction: row !important; /* Force horizontal layout */
  width: 100% !important; /* Ensure track takes full width of list */
  max-width: 100%; /* Ensure it doesn't exceed parent width */
}

.sektorUnggulanSlider :global(.slick-slide) {
  height: inherit;
  display: flex !important;
  justify-content: center;
  width: 450px !important; /* Significantly increased width for larger items */
  float: left !important; /* Ensure horizontal layout */
  max-width: 100%; /* Ensure it doesn't exceed parent width */
}

.sektorUnggulanSlider :global(.slick-slide > div) {
  width: 450px; /* Significantly increased width for larger items */
  margin: 0 auto;
  max-width: 100%; /* Ensure it doesn't exceed parent width */
}

/* Make all cards the same size */
.sektorUnggulanSlider :global(.slick-slide .card-container) {
  width: 450px; /* Significantly increased width for larger items */
  height: 300px; /* Increased height */
  max-width: 100%; /* Ensure it doesn't exceed parent width */
}

.sektorUnggulanSlider :global(.slick-prev),
.sektorUnggulanSlider :global(.slick-next) {
  z-index: 10;
  width: 40px;
  height: 40px;
}

.sektorUnggulanSlider :global(.slick-prev) {
  left: 15px !important; /* Move left arrow inside the container */
}

.sektorUnggulanSlider :global(.slick-next) {
  right: 15px !important; /* Move right arrow inside the container */
}

.sektorUnggulanSlider :global(.slick-prev:before),
.sektorUnggulanSlider :global(.slick-next:before) {
  font-size: 24px;
  color: white;
}

/* Center single slide */
.sektorUnggulanSlider :global(.slick-slide.slick-active.slick-center) {
  display: flex !important;
  justify-content: center;
}

.sektorUnggulanSlider :global(.slick-list) {
  margin: 0 auto !important;
  overflow: visible !important; /* Ensure slides are visible */
  width: 100% !important; /* Force width to be 100% of parent container */
  padding: 0 40px !important; /* Further increased padding to create margin effect */
  max-width: 100%; /* Ensure it doesn't exceed parent width */
  padding-right: 0 !important;
  margin-right: 0 !important;
}

/* Force center alignment for all slides */
.sektorUnggulanSlider :global(.slick-track) {
  display: flex !important;
  justify-content: center !important;
  transform-style: preserve-3d !important;
  width: 100% !important; /* Ensure track takes full width of list */
  max-width: 100%; /* Ensure it doesn't exceed parent width */
}

/* Fix for initial load issue */
.sektorUnggulanSlider :global(.slick-initialized .slick-slide) {
  display: flex !important;
}

/* Style for disabled next arrow when at the end */
.sektorUnggulanSlider :global(.slick-disabled) {
  opacity: 0.25 !important;
  cursor: not-allowed !important;
}
