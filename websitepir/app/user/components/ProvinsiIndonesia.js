"use client";

import useLanguageStore from "../stores/languageStore";
import useStore from "../stores/useStore";
import { scrollToSection } from "../utils/scroll";
import ButtonWithIcon from "./ButtonWithIcon";
import ChartMap from "./ChartMap";
import MiniCard from "./MiniCard";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectText,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Indonesia from "@/public/indo_38_topo.json";
import { getMapsService } from "@/services/getMapService";
import useProvinceStore from "@/store/useProvinceStore";
import axios from "axios";
import { config } from "dotenv";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import highchartsMap from "highcharts/modules/map";
import { Map } from "lucide-react";
import React, { useState, useEffect } from "react";

// Initialize highchartsMap
if (typeof Highcharts === "object") {
  highchartsMap(Highcharts);
}

const ProvinsiIndonesia = () => {
  const [mapData, setMapData] = useState(null);
  const [provinces, setProvinces] = useState([]);
  const { language } = useLanguageStore();
  // const [selectedProvinceId, setSelectedProvinceId] = useState(31);
  const {
    selectedProvinceId,
    selectedProvinceDetails,
    setSelectedProvinceDetails,
    setSelectedProvinceIdAndUpdateMap,
    isAutoRotating,
    setAutoRotating,
  } = useProvinceStore();
  // const [isLoading, setIsLoading] = useState(true);
  const [maps, setMaps] = useState({});

  const fetchProvincesRef = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/home/<USER>/${
          language === "en" ? "en" : "id"
        }`
      );
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      const data = await response.json();
      console.log("Data fetched:", data.data);
      setProvinces(data.data.data);
    } catch (error) {
      console.error("Error fetching provinces:", error);
    }
  };

  const fetchProvincesDetails = async (id) => {
    if (!id) return; // Don't fetch if id is null or undefined
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/home/<USER>/${
          language === "en" ? "en" : "id"
        }/${id}`
      );
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      const data = await response.json();
      console.log("setSelectedProvinceDetails:", data.data);
      setSelectedProvinceDetails(data);
    } catch (error) {
      console.error("Error fetching provinces:", error);
    }
  };

  useEffect(() => {
    fetchProvincesRef();

    // Fetch your topojson/geojson data
    fetch("https://code.highcharts.com/mapdata/countries/id/id-all.topo.json")
      .then((response) => response.json())
      .then((data) => setMapData(data));
  }, [language]);

  useEffect(() => {
    console.log("useEffect triggered. selectedProvinceId:", selectedProvinceId);
    if (selectedProvinceId) {
      console.log("Calling fetchProvincesDetails with ID:", selectedProvinceId);
      fetchProvincesDetails(selectedProvinceId);
    } else {
      console.log("selectedProvinceId is falsy, not fetching details");
    }
  }, [selectedProvinceId, language]);

  useEffect(() => {
    async function fetchData() {
      // setIsLoading(true);
      try {
        const response = await getMapsService(selectedProvinceId, language);
        setMaps(response.data);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        // setIsLoading(false);
      }
    }

    fetchData();
  }, [selectedProvinceId, language]);

  const data_provinsi = [
    {
      title: "Peluang",
      icon: "/icons/PeluangBiru.svg",
      value: "1 Peluang",
    },
    {
      title: "Umr",
      icon: "/icons/UMR 2024 biru.svg",
      value: "1 Peluang",
    },
    {
      title: "Jumlah Penduduk",
      icon: "/icons/Jumlah Penduduk 56 biru.svg",
      value: "1 Peluang",
    },
    {
      title: "PDRB",
      icon: "/icons/PDRB 56 biru.svg",
      value: "1 Peluang",
    },
    {
      title: "Realisasi Investasi 2023",
      icon: "/icons/Realisasi Investasi 2023 56 biru.svg",
      value: "1 Peluang",
    },
    {
      title: "Kawasan Industri",
      icon: "/icons/Kawasan Industri 56 biru.svg",
      value: "1 Peluang",
    },
  ];

  // if (isLoading) {
  //     return <div className='flex items-center justify-center'>Loading...</div>;
  // }

  useEffect(() => {
    let intervalId;

    if (isAutoRotating) {
      intervalId = setInterval(
        () => {
          // Get random province from dataHcKey
          const dataHcKey = useProvinceStore.getState().dataHcKey;
          const randomIndex = Math.floor(Math.random() * dataHcKey.length);
          const randomProvinceId = dataHcKey[randomIndex][1]; // Get the ID (second element)

          setSelectedProvinceIdAndUpdateMap(randomProvinceId);
        },
        //set default to 10 seconds
        process.env.NEXT_PUBLIC_GLOBE_AUTORUN_TIME || 20000
      );
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isAutoRotating, selectedProvinceId, provinces]);

  return (
    <div className="flex flex-col pt-10">
      <div className="flex flex-col lg:flex-row gap-[35px] justify-center items-center">
        {/* Sidebar */}
        <div className="relative h-full pb-10 border shadow-2xl rounded-xl w-full lg:w-auto max-w-[400px] mx-4 lg:mx-0">
          <Map
            className="absolute z-10 -top-2 -left-6 border-4 border-white drop-shadow-md bg-[#2D3A96] rounded-full p-2"
            size={56}
            color="white"
          />
          <div className="flex justify-center items-center bg-[#2D3A96] h-24 w-full rounded-t-xl">
            <Select
              className="pt-10 text-center bg-[#2D3A96]"
              onValueChange={(value) => {
                setSelectedProvinceIdAndUpdateMap(parseInt(value));
              }}
              value={selectedProvinceId}
            >
              <SelectTrigger className="bg-[#2D3A96] border-[#2D3A96] text-2xl font-montserrat border-0 text-white w-[280px]">
                <SelectValue placeholder="Pilih Provinsi" />
              </SelectTrigger>
              <SelectContent className="text-xl font-bold font-montserrat">
                {provinces.map((province) => (
                  <SelectItem
                    key={province.id_adm_provinsi}
                    value={province.id_adm_provinsi}
                  >
                    {province.nama}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center justify-center mx-10">
            <div className="flex flex-col items-start pt-[20px]">
              <MiniCard
                icon={data_provinsi[0].icon}
                cardName={language === "en" ? "Opportunity" : "Peluang"}
                description={maps.peluang || ""}
                className="border-0"
              />
              <MiniCard
                icon={data_provinsi[1].icon}
                cardName={language === "en" ? "Minimum Wage" : "UMR"}
                tahun={maps.umr?.tahun}
                description={
                  maps.umr?.nilai ? maps.umr.nilai : ""
                }
                className="border-0"
              />
              <MiniCard
                icon={data_provinsi[2].icon}
                cardName={
                  language === "en" ? "Total population" : "Jumlah Penduduk"
                }
                description={
                  maps.jumlah_penduduk?.nilai
                    ? maps.jumlah_penduduk.nilai
                    : language === "en"
                    ? ""
                    : ""
                }
                className="border-0"
              />
              <MiniCard
                icon={data_provinsi[3].icon}
                cardName={language === "en" ? "PDRB" : "PDRB"}
                tahun={maps.prdb?.tahun}
                description={
                  maps.prdb?.nilai ? maps.prdb.nilai : ""
                }
                className="border-0"
              />
              <MiniCard
                icon={data_provinsi[4].icon}
                cardName={
                  language === "en"
                    ? "Investment Realization"
                    : "Realisasi Investasi"
                }
                description={
                  maps &&
                  maps.realisasi_investasi &&
                  maps.realisasi_investasi[0]
                    ? maps.realisasi_investasi[0].nilai
                    : language === "en"
                    ? ""
                    : ""
                }
                description2={
                  maps &&
                  maps.realisasi_investasi &&
                  maps.realisasi_investasi[1]
                    ? maps.realisasi_investasi[1].nilai
                    : language === "en"
                    ? ""
                    : ""
                }
                className="border-0"
              />

              <MiniCard
                icon={data_provinsi[5].icon}
                cardName={
                  language === "en" ? "Industrial area" : "Kawasan Industri"
                }
                description={
                  maps.kawasan
                    ? maps.kawasan
                    : language === "en"
                    ? ""
                    : ""
                }
                className="border-0"
              />
            </div>
          </div>
        </div>

        {/* Chart Map */}
        <div
          className="flex w-full lg:w-[900px] h-[400px] lg:h-[600px] border-black justify-center items-center pt-[30px] mt-4 lg:mt-0"
          style={{
            width: "700px",
            height: "700px",
            backgroundImage: 'url("/peta.svg")',
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
          }}
        >
          <ChartMap provinceId={selectedProvinceId} />
        </div>
      </div>
    </div>
  );
};

export default ProvinsiIndonesia;
