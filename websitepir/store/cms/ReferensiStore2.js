import { create } from "zustand";
import { getReferensiService } from "@/services/AllService";
import useAuthStore from "@/store/AuthStore";

// Helper function to get user data
const getUserData = async () => {
  const { user, roleId, getMe } = useAuthStore.getState();
  if (!user) {
    await getMe();
    return useAuthStore.getState();
  }
  return { user, roleId };
};

const useReferensiStore2 = create((set, get) => ({
  // State untuk menyimpan data referensi
  sumberDataOptions: [],
  provinsiOptions: [],
  kabkotOptions: [],
  peluangSektorOptions: [],
  peluangStatusOptions: [],
  sektorNasionalInsentifOptions: [],
  sektorNasionalRefOptions: [],
  subSektorNasionalRefOptions: [],
  sumberDataInstansiOptions: [],
  kategoriInsentifOptions: [],
  komoditiOptions: [],
  jenisInsentifOptions: [],
  peluangKabkotOptions: [],
  newsKategoriOptions: [],
  investasiJenisOptions: [],
  investasiJenisDataOptions: [],
  kawasanIndustriOptions:[],
  demografiKategoriOptions:[],
  investasiSektorOptions:[],
  kategoriSektorOptions:[],
  peluangPrioritasOptions:[],
  userInternalJabatanOptions:[],
  userInternalProvinsiOptions:[],
  userInternalKawasanOptions:[],
  isLoading: {
    sumberData: false,
    provinsi: false,
    kabkot: false,
    peluangSektor: false,
    peluangStatus: false,
    sektorNasionalInsentif: false,
    sektorNasionalRef: false,
    subSektorNasionalRef: false,
    sumberDataInstansi: false,
    kategoriInsentif: false,
    komoditi: false,
    jenisInsentif: false,
    peluangKabkot: false,
    newsKategori: false,
    investasiJenis: false,
    investasiJenisData: false,
    kawasanIndustri: false,
    demografiKategori: false,
    investasiSektor: false,
    kategoriSektor: false,
    peluangPrioritas: false,
    userInternalJabatan: false,
    userInternalProvinsi: false,
    userInternalKawasan: false,
  },

  // Fungsi untuk memuat data referensi secara modular
  fetchSumberData: async () => {
    if (get().sumberDataOptions.length === 0) { // Hanya fetch jika data belum ada
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, sumberData: true },
        }));
        const response = await getReferensiService("vw_sumber_data_id");
        set({
          sumberDataOptions: response.data,
          isLoading: { ...get().isLoading, sumberData: false },
        });
      } catch (error) {
        console.error("Error fetching sumber data:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, sumberData: false },
        }));
      }
    }
  },

  fetchProvinsi: async () => {
    try {
      set((state) => ({
        isLoading: { ...state.isLoading, provinsi: true },
      }));
      const response = await getReferensiService("tb_adm_provinsi");
      
      // Get user data using helper function
      const { user, roleId } = await getUserData();
      let provinsiData = response.data;
      
      if (roleId === 14 && user?.pic_prov_arr?.length > 0) {
        provinsiData = provinsiData.filter(item => user.pic_prov_arr.includes(item.id_adm_provinsi));
      }
      if (roleId === 3) {
        provinsiData = provinsiData.filter(item => item.id_adm_provinsi === user.id_adm_provinsi);
      }
      if (roleId === 4) {
        provinsiData = provinsiData.filter(item => item.id_adm_provinsi === user.id_adm_provinsi.slice(0, 2));
      }
      
      console.log("serData1",user);
      console.log("serData",provinsiData);
      
      set({
        provinsiOptions: provinsiData,
        isLoading: { ...get().isLoading, provinsi: false },
      });
    } catch (error) {
      console.error("Error fetching provinsi:", error);
      set((state) => ({
        isLoading: { ...state.isLoading, provinsi: false },
      }));
    }
  },

  fetchKabkot: async () => {
    if (get().kabkotOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, kabkot: true },
        }));
        const response = await getReferensiService("tb_adm_kabkot");
        set({
          kabkotOptions: response.data,
          isLoading: { ...get().isLoading, kabkot: false },
        });
      } catch (error) {
        console.error("Error fetching kabkot:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, kabkot: false },
        }));
      }
    }
  },

  fetchPeluangSektor: async () => {
    if (get().peluangSektorOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, peluangSektor: true },
        }));
        const response = await getReferensiService("tb_peluang_sektor");
        set({
          peluangSektorOptions: response.data,
          isLoading: { ...get().isLoading, peluangSektor: false },
        });
      } catch (error) {
        console.error("Error fetching peluang sektor:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, peluangSektor: false },
        }));
      }
    }
  },

  fetchPeluangStatus: async () => {
    if (get().peluangStatusOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, peluangStatus: true },
        }));
        const response = await getReferensiService("tb_peluang_status");
        set({
          peluangStatusOptions: response.data,
          isLoading: { ...get().isLoading, peluangStatus: false },
        });
      } catch (error) {
        console.error("Error fetching peluang status:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, peluangStatus: false },
        }));
      }
    }
  },

  fetchSektorNasionalInsentif: async () => {
    if (get().sektorNasionalInsentifOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, sektorNasionalInsentif: true },
        }));
        const response = await getReferensiService("tb_sektor_nasional_insentif");
        set({
          sektorNasionalInsentifOptions: response.data,
          isLoading: { ...get().isLoading, sektorNasionalInsentif: false },
        });
      } catch (error) {
        console.error("Error fetching sektor nasional insentif:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, sektorNasionalInsentif: false },
        }));
      }
    }
  },

  fetchSektorNasionalRef: async () => {
    if (get().sektorNasionalRefOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, sektorNasionalRef: true },
        }));
        const response = await getReferensiService("tb_sektor_nasional_ref");
        set({
          sektorNasionalRefOptions: response.data,
          isLoading: { ...get().isLoading, sektorNasionalRef: false },
        });
      } catch (error) {
        console.error("Error fetching sektor nasional ref:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, sektorNasionalRef: false },
        }));
      }
    }
  },

  fetchSubSektorNasionalRef: async () => {
    if (get().subSektorNasionalRefOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, subSektorNasionalRef: true },
        }));
        const response = await getReferensiService("tb_sub_sektor_nasional_ref");
        set({
          subSektorNasionalRefOptions: response.data,
          isLoading: { ...get().isLoading, subSektorNasionalRef: false },
        });
      } catch (error) {
        console.error("Error fetching sub sektor nasional ref:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, subSektorNasionalRef: false },
        }));
      }
    }
  },

  fetchSumberDataInstansi: async () => {
    if (get().sumberDataInstansiOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, sumberDataInstansi: true },
        }));
        const response = await getReferensiService("tb_sumber_data_instansi");
        set({
          sumberDataInstansiOptions: response.data,
          isLoading: { ...get().isLoading, sumberDataInstansi: false },
        });
      } catch (error) {
        console.error("Error fetching sumber data instansi:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, sumberDataInstansi: false },
        }));
      }
    }
  },

  fetchKategoriInsentif: async () => {
    if (get().kategoriInsentifOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, kategoriInsentif: true },
        }));
        const response = await getReferensiService("tb_kategori_insentif");
        set({
          kategoriInsentifOptions: response.data,
          isLoading: { ...get().isLoading, kategoriInsentif: false },
        });
      } catch (error) {
        console.error("Error fetching kategori insentif:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, kategoriInsentif: false },
        }));
      }
    }
  },

  fetchKomoditi: async () => {
    if (get().komoditiOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, komoditi: true },
        }));
        const response = await getReferensiService("tb_komoditi_nasional_ref ");
        set({
          komoditiOptions: response.data,
          isLoading: { ...get().isLoading, komoditi: false },
        });
      } catch (error) {
        console.error("Error fetching komoditi:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, komoditi: false },
        }));
      }
    }
  },

  fetchJenisInsentif: async () => {
    if (get().jenisInsentifOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, jenisInsentif: true },
        }));
        const response = await getReferensiService("tb_jenis_insentif");
        set({
          jenisInsentifOptions: response.data,
          isLoading: { ...get().isLoading, jenisInsentif: false },
        });
      } catch (error) {
        console.error("Error fetching jenis insentif:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, jenisInsentif: false },
        }));
      }
    }
  },

  fetchPeluangKabkot: async () => {
    if (get().peluangKabkotOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, peluangKabkot: true },
        }));
        const response = await getReferensiService("tb_peluang_kabkot");
        set({
          peluangKabkotOptions: response.data,
          isLoading: { ...get().isLoading, peluangKabkot: false },
        });
      } catch (error) {
        console.error("Error fetching peluang kabkot:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, peluangKabkot: false },
        }));
      }
    }
  },

  fetchNewsKategori: async () => {
    if (get().newsKategoriOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, newsKategori: true },
        }));
        const response = await getReferensiService("tb_news_kategori");
        set({
          newsKategoriOptions: response.data,
          isLoading: { ...get().isLoading, newsKategori: false },
        });
      } catch (error) {
        console.error("Error fetching news kategori:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, newsKategori: false },
        }));
      }
    }
  },

  fetchInvestasiJenis: async () => {
    if (get().investasiJenisOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, investasiJenis: true },
        }));
        const response = await getReferensiService("tb_investasi_jenis");
        set({
          investasiJenisOptions: response.data,
          isLoading: { ...get().isLoading, investasiJenis: false },
        });
      } catch (error) {
        console.error("Error fetching tb_investasi_jenis:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, investasiJenis: false },
        }));
      }
    }
  },

  fetchInvestasiJenisData: async () => {
    if (get().investasiJenisDataOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, investasiJenisData: true },
        }));
        const response = await getReferensiService("tb_investasi_jenis_data");
        set({
          investasiJenisDataOptions: response.data,
          isLoading: { ...get().isLoading, investasiJenisData: false },
        });
      } catch (error) {
        console.error("Error fetching tb_investasi_jenis:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, investasiJenisData: false },
        }));
      }
    }
  },

  fetchKawasanIndustri: async () => {
    if (get().kawasanIndustriOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, kawasanIndustri: true },
        }));
        const response = await getReferensiService("tb_kawasan_industri");
        set({
          kawasanIndustriOptions: response.data,
          isLoading: { ...get().isLoading, kawasanIndustri: false },
        });
      } catch (error) {
        console.error("Error fetching tb_investasi_jenis:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, kawasanIndustri: false },
        }));
      }
    }
  },
 
  fetchDemografiKategori: async () => {
    if (get().demografiKategoriOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, demografiKategori: true },
        }));
        const response = await getReferensiService("tb_demografi_kategori");
        set({
          demografiKategoriOptions: response.data,
          isLoading: { ...get().isLoading, demografiKategori: false },
        });
      } catch (error) {
        console.error("Error fetching tb_demografi_kategori:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, demografiKategori: false },
        }));
      }
    }
  },
 
  fetchInvestasiSektor: async () => {
    if (get().investasiSektorOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, investasiSektor: true },
        }));
        const response = await getReferensiService("tb_investasi_sektor");
        set({
          investasiSektorOptions: response.data,
          isLoading: { ...get().isLoading, investasiSektor: false },
        });
      } catch (error) {
        console.error("Error fetching tb_investasi_sektor:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, investasiSektor: false },
        }));
      }
    }
  },
  
  fetchKategoriSektor: async () => {
    if (get().kategoriSektorOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, kategoriSektor: true },
        }));
        const response = await getReferensiService("tb_kategori_sektor");
        set({
          kategoriSektorOptions: response.data,
          isLoading: { ...get().isLoading, kategoriSektor: false },
        });
      } catch (error) {
        console.error("Error fetching tb_kategori_sektor:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, kategoriSektor: false },
        }));
      }
    }
  },
  
  fetchPeluangPrioritas: async () => {
    if (get().peluangPrioritasOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, peluangPrioritas: true },
        }));
        const response = await getReferensiService("tb_peluang_prioritas");
        set({
          peluangPrioritasOptions: response.data,
          isLoading: { ...get().isLoading, peluangPrioritas: false },
        });
      } catch (error) {
        console.error("Error fetching tb_peluang_prioritas:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, peluangPrioritas: false },
        }));
      }
    }
  },
  fetchUserInternalJabatan: async () => {
    if (get().userInternalJabatanOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, userInternalJabatan: true },
        }));
        const response = await getReferensiService("tb_ref_user_jabatan");
        set({
          userInternalJabatanOptions: response.data,
          isLoading: { ...get().isLoading, userInternalJabatan: false },
        });
      } catch (error) {
        console.error("Error fetching tb_user_internal_jabatan:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, userInternalJabatan: false },
        }));
      }
    }
  },
  fetchUserInternalProvinsi: async () => {
    if (get().userInternalProvinsiOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, userInternalProvinsi: true },
        }));
        const response = await getReferensiService("tb_user_internal_provinsi");
        set({
          userInternalProvinsiOptions: response.data,
          isLoading: { ...get().isLoading, userInternalProvinsi: false },
        });
      } catch (error) {
        console.error("Error fetching tb_user_internal_provinsi:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, userInternalProvinsi: false },
        }));
      }
    }
  },
  fetchUserInternalKawasan: async () => {
    if (get().userInternalKawasanOptions.length === 0) {
      try {
        set((state) => ({
          isLoading: { ...state.isLoading, userInternalKawasan: true },
        }));
        const response = await getReferensiService("tb_user_internal_kawasan_industri");
        set({
          userInternalKawasanOptions: response.data,
          isLoading: { ...get().isLoading, userInternalKawasan: false },
        });
      } catch (error) {
        console.error("Error fetching tb_user_internal_kawasan_industri:", error);
        set((state) => ({
          isLoading: { ...state.isLoading, userInternalKawasan: false },
        }));
      }
    }
  },

}));

export default useReferensiStore2;
