import useLanguageStore from "@/app/user/stores/languageStore";
import {
  getProvByZonaWaktuService,
  getZonaWaktuService,
  getPPIByZonaWaktuService,
  getPPIByProvinsiService,
  getIPROByZonaWaktuService,
  getIPROByProvinsiService,
  getKawasanByZonaWaktuService,
  getKawasanByProvinsiService,
  getKEKByZonaWaktuService,
  getKEKByProvinsiService,
  getBandaraByZonaWaktuService,
  getBandaraByProvinsiService,
  getHotelByZonaWaktuService,
  getHotelByProvinsiService,
  getPelabuhanByZonaWaktuService,
  getPelabuhanByProvinsiService,
  getPendidikanByZonaWaktuService,
  getPendidikanByProvinsiService,
  getRumahSakitByZonaWaktuService,
  getRumahSakitByProvinsiService,
  getPPIByKabkotService,
  getIPROByKabkotService,
  getKawasanByKabkotService,
  getKEKByKabkotService,
  getBandaraByKabkotService,
  getHotelByKabkotService,
  getPelabuhanByKabkotService,
  getPendidikanByKabkotService,
  getRumahSakitByKabkotService,
  getPIDByZonaWaktuService,
  getPIDByProvinsiService,
  getPIDByKabkotService,
  getRTRWByProvinsiService,
  getRDTRByProvinsiService,
  getRTRWByKabkotService,
  getRDTRByKabkotService,
  getKomoditiByProvinsiService,
  getKomoditiByKabkotService,
  getUtilitasByProvinsiService,
  getUtilitasByKabkotService,
  getProvByZonaWaktuLangService,
  getKabkotRefService
} from "@/services/SidebarMapService";
import { queryFeatures } from "@esri/arcgis-rest-feature-layer";
import { request } from "@esri/arcgis-rest-request";
import * as turf from "@turf/turf";
import { create } from "zustand";

const useMapStore = create((set, get) => ({
  // Data states
  zonaWaktu: [],
  provinsi: [],
  kabkot: [],
  allProvinsi: [],
  selectedZona: null,
  selectedProvinsi: null,
  selectedKabkot: null,

  // Selected items state
  selectedItems: {
    ppi: null,
    ipro: null,
    pid: null,
    kawasan: null,
    kek: null,
    bandara: null,
    hotel: null,
    pelabuhan: null,
    pendidikan: null,
    rumahSakit: null,
    rtrw: null,
    rdtr: null,
    komoditi: null,
    utilitas: null,
  },

  // Map states
  mapCenter: [118.0149, -2.5489],
  mapZoom: 4,

  // Lists states
  lists: {
    ppi: [],
    ipro: [],
    pid: [],
    kawasan: [],
    kek: [],
    bandara: [],
    hotel: [],
    pelabuhan: [],
    pendidikan: [],
    rumahSakit: [],
    rtrw: [],
    rdtr: [],
    komoditi: [],
    utilitas: [],
  },

  // Popover states
  popovers: {
    zona: false,
    prov: false,
    kabko: false,
    ppi: false,
    ipro: false,
    pid: false,
    kawasan: false,
    kek: false,
    bandara: false,
    hotel: false,
    pelabuhan: false,
    pendidikan: false,
    rumahSakit: false,
    rtrw: false,
    rdtr: false,
    komoditi: false,
    utilitas: false,
  },

  // Actions
  setMapPosition: (center, zoom) => {
    set({
      mapCenter: center,
      mapZoom: zoom,
    });
  },

  setPopoverState: (popover, isOpen) => {
    set((state) => ({
      popovers: {
        ...state.popovers,
        [popover]: isOpen,
      },
    }));
  },

  // handleListItemSelect: (item, type) => {
  //   if (!item) {
  //     get().setMapPosition([118.0149, -2.5489], 4);
  //     return;
  //   }

  //    // Update selected item
  //    set(state => ({
  //     selectedItems: {
  //       ...state.selectedItems,
  //       [type]: item
  //     }
  //   }));

  //   if (item.lon && item.lat) {
  //     get().setMapPosition([parseFloat(item.lon), parseFloat(item.lat)], 12);
  //   }

  //   // Handle zona and provinsi selection
  //   switch(type) {
  //     case 'zona':
  //       get().fetchZonaData(item?.id_zona_waktu || null);
  //       break;
  //     case 'prov':
  //       get().fetchProvinsiData(item?.id_adm_provinsi || null);
  //       break;
  //     default:
  //       break;
  //   }
  // },

  handleListItemSelect: async (item, type) => {
    if (!item) {
      get().setMapPosition([118.0149, -2.5489], 4);
      return;
    }

    // // Update selected item
    // set((state) => ({
    //   selectedItems: {
    //     ...state.selectedItems,
    //     [type]: item,
    //   },
    // }));

    // Reset semua selectedItems kecuali item yang dipilih
    set((state) => ({
      selectedItems: Object.keys(state.selectedItems).reduce((acc, key) => {
        acc[key] = key === type ? item : null; // Set null untuk semua kecuali item yang dipilih
        return acc;
      }, {}),
    }));

    if (type === "rtrw" || type === "rdtr") {
      try {
        const proxyUrl =
          "https://gistaru.atrbpn.go.id/proxy_bkpmsipd/run.ashx?"; // Ganti dengan proxy URL yang sesuai
        const metadata = await request(`${proxyUrl}${item.url}?f=json`);
        const featureCollection = await queryFeatures({
          url: `${proxyUrl}${item.url}`,
          where: "1=1",
          outFields: ["*"],
          returnGeometry: true,
          f: "geojson",
        });

        // Hitung bounds berdasarkan featureCollection
        const bounds = turf.bbox(featureCollection);

        // Fit map ke bounds
        const mapInstance = get().mapInstance;
        mapInstance.fitBounds(bounds, { padding: 20 });
      } catch (error) {
        console.error("Error fitting bounds for RTRW/RDTR:", error);
      }
    } else if (item.lon && item.lat) {
      get().setMapPosition([parseFloat(item.lon), parseFloat(item.lat)], 17);
    }

    // Handle zona and provinsi selection
    switch (type) {
      case "zona":
        get().fetchZonaData(item?.id_zona_waktu || null);
        break;
      case "prov":
        console.log("Provinsi item: ", item);
        // Jika provinsi memiliki lon dan lat, zoom ke lokasi tersebut
        if (item.lon && item.lat) {
          get().setMapPosition([parseFloat(item.lon), parseFloat(item.lat)], 7);
        }
        get().fetchProvinsiData(item?.id_adm_provinsi || null);
        break;
      case "kabkot":
        console.log("Kabupaten/Kota item: ", item);
        // Simpan ID kabkot yang dipilih
        set({ selectedKabkot: item?.id_adm_kabkot || null });
        // Jika kabkot memiliki lon dan lat, zoom ke lokasi tersebut
        if (item.lon && item.lat) {
          get().setMapPosition([parseFloat(item.lon), parseFloat(item.lat)], 10);
        }
        // Ambil data berdasarkan kabupaten/kota yang dipilih
        get().fetchKabkotData(item?.id_adm_kabkot || null);
        break;
      default:
        break;
    }
  },

  // Initialize data
initializeData: async () => {
  try {
    const { language } = useLanguageStore.getState(); // Ambil bahasa langsung dari state
    const [zonaResponse, provResponse] = await Promise.all([
      getZonaWaktuService(language), // Kirim parameter language ke API
      getProvByZonaWaktuLangService(language),
    ]);
console.log("zona repso",zonaResponse);
    set({
      zonaWaktu: zonaResponse.data,
      allProvinsi: provResponse.data,
      provinsi: provResponse.data,
    });
  } catch (error) {
    console.error("Failed to initialize data:", error);
  }
},


  // Reset lists
  resetLists: () => {
    set({
      lists: {
        ppi: [],
        ipro: [],
        pid: [],
        kawasan: [],
        kek: [],
        bandara: [],
        hotel: [],
        pelabuhan: [],
        pendidikan: [],
        rumahSakit: [],
        rtrw: [],
        rdtr: [],
        komoditi: [],
        utilitas: [],
      },
      selectedItems: {
        ppi: null,
        ipro: null,
        pid: null,
        kawasan: null,
        kek: null,
        bandara: null,
        hotel: null,
        pelabuhan: null,
        pendidikan: null,
        rumahSakit: null,
        rtrw: false,
        rdtr: false,
        komoditi: false,
        utilitas: false,
      },
    });
  },

  // Fetch data based on zona waktu
  fetchZonaData: async (id) => {
    const { language } = useLanguageStore.getState();
    if (!id) {
      set((state) => ({
        provinsi: state.allProvinsi,
        selectedZona: null,
        selectedProvinsi: null,
      }));
      get().resetLists();
      get().setMapPosition([118.0149, -2.5489], 4);
      return;
    }

    try {
      const [
        provResponse,
        ppiResponse,
        iproResponse,
        pidResponse,
        kawasanResponse,
        kekResponse,
      ] = await Promise.all([
        
        getProvByZonaWaktuService(id, language),
        getPPIByZonaWaktuService(id, language),
        getIPROByZonaWaktuService(id, language),
        getPIDByZonaWaktuService(id, language),
        getKawasanByZonaWaktuService(id, language),
        getKEKByZonaWaktuService(id, language),
        // getBandaraByZonaWaktuService(id, language),
        // getHotelByZonaWaktuService(id, language),
        // getPelabuhanByZonaWaktuService(id, language),
        // getPendidikanByZonaWaktuService(id, language),
        // getRumahSakitByZonaWaktuService(id, language),
      ]);

      set({
        provinsi: provResponse.data,
        selectedZona: id,
        selectedProvinsi: null,
        lists: {
          ppi: ppiResponse.data,
          ipro: iproResponse.data,
          pid: pidResponse.data,
          kawasan: kawasanResponse.data,
          kek: kekResponse.data,
          bandara: [],
          hotel: [],
          pelabuhan: [],
          pendidikan: [],
          rumahSakit: [],
        },
        selectedItems: {
          ppi: null,
          ipro: null,
          pid: null,
          kawasan: null,
          kek: null,
          bandara: null,
          hotel: null,
          pelabuhan: null,
          pendidikan: null,
          rumahSakit: null,
        },
      });
    } catch (error) {
      console.error("Failed to fetch zona data:", error);
    }
  },

  // Fetch data based on provinsi
  fetchProvinsiData: async (id) => {
    const { language } = useLanguageStore.getState();
    if (!id) {
      get().resetLists();
      set({ selectedProvinsi: null });
      return;
    }

    try {
      const [
        kabkotResponse,
        ppiResponse,
        iproResponse,
        pidResponse,
        kawasanResponse,
        kekResponse,
        bandaraResponse,
        hotelResponse,
        pelabuhanResponse,
        pendidikanResponse,
        rumahSakitResponse,
        rtrwResponse,
        rdtrResponse,
        komoditiResponse,
        utilitasResponse,
      ] = await Promise.all([
        getKabkotRefService(id, language),  // Remove language parameter as it's not used in the service
        getPPIByProvinsiService(id, language),
        getIPROByProvinsiService(id, language),
        getPIDByProvinsiService(id, language),
        getKawasanByProvinsiService(id, language),
        getKEKByProvinsiService(id, language),
        getBandaraByProvinsiService(id, language),
        getHotelByProvinsiService(id, language),
        getPelabuhanByProvinsiService(id, language),
        getPendidikanByProvinsiService(id, language),
        getRumahSakitByProvinsiService(id, language),
        getRTRWByProvinsiService(id, language),
        getRDTRByProvinsiService(id, language),
        getKomoditiByProvinsiService(id, language),
        getUtilitasByProvinsiService(id, language),
      ]);

      console.log("Kabkot response:", kabkotResponse);  // Add debugging to check response
      const kabkotData = kabkotResponse.data && kabkotResponse.data.data ? kabkotResponse.data.data : [];

      set({
        selectedProvinsi: id,
        kabkot: kabkotData, // Set kabkot data from the first response
        selectedKabkot: null, // Reset selected kabkot when province changes
        lists: {
          ppi: ppiResponse.data,
          ipro: iproResponse.data,
          pid: pidResponse.data,
          kawasan: kawasanResponse.data,
          kek: kekResponse.data,
          bandara: bandaraResponse.data,
          hotel: hotelResponse.data,
          pelabuhan: pelabuhanResponse.data,
          pendidikan: pendidikanResponse.data,
          rumahSakit: rumahSakitResponse.data,
          rtrw: rtrwResponse.data,
          rdtr: rdtrResponse.data,
          komoditi: komoditiResponse.data,
          utilitas: utilitasResponse.data,
        },
        selectedItems: {
          ppi: null,
          ipro: null,
          pid: null,
          kawasan: null,
          kek: null,
          bandara: null,
          hotel: null,
          pelabuhan: null,
          pendidikan: null,
          rumahSakit: null,
          rtrw: null,
          rdtr: null,
          komoditi: null,
          utilitas: null,
        },
      });
    } catch (error) {
      console.error("Failed to fetch provinsi data:", error);
    }
  },

  // Fetch data based on kabupaten/kota
  fetchKabkotData: async (id) => {
    const { language } = useLanguageStore.getState();
    
    if (!id) {
      // Reset data jika tidak ada kabupaten/kota yang dipilih
      set({
        selectedKabkot: null,
        lists: {
          ...get().lists,
          ppi: [],
          ipro: [],
          pid: [],
          kawasan: [],
          kek: [],
          bandara: [],
          hotel: [],
          pelabuhan: [],
          pendidikan: [],
          rumahSakit: [],
          rtrw: [],
          rdtr: [],
          komoditi: [],
          utilitas: [],
        },
      });
      return;
    }

    try {
      const [
        ppiResponse,
        iproResponse,
        pidResponse,
        kawasanResponse,
        kekResponse,
        bandaraResponse,
        hotelResponse,
        pelabuhanResponse,
        pendidikanResponse,
        rumahSakitResponse,
        rtrwResponse,
        rdtrResponse,
        komoditiResponse,
        utilitasResponse,
      ] = await Promise.all([
        getPPIByKabkotService(id, language),
        getIPROByKabkotService(id, language),
        getPIDByKabkotService(id, language),
        getKawasanByKabkotService(id, language),
        getKEKByKabkotService(id, language),
        getBandaraByKabkotService(id, language),
        getHotelByKabkotService(id, language),
        getPelabuhanByKabkotService(id, language),
        getPendidikanByKabkotService(id, language),
        getRumahSakitByKabkotService(id, language),
        getRTRWByKabkotService(id, language),
        getRDTRByKabkotService(id, language),
        getKomoditiByKabkotService(id, language),
        getUtilitasByKabkotService(id, language),
      ]);

      set({
        selectedKabkot: id,
        lists: {
          ...get().lists,
          ppi: ppiResponse.data,
          ipro: iproResponse.data,
          pid: pidResponse.data,
          kawasan: kawasanResponse.data,
          kek: kekResponse.data,
          bandara: bandaraResponse.data,
          hotel: hotelResponse.data,
          pelabuhan: pelabuhanResponse.data,
          pendidikan: pendidikanResponse.data,
          rumahSakit: rumahSakitResponse.data,
          rtrw: rtrwResponse.data,
          rdtr: rdtrResponse.data,
          komoditi: komoditiResponse.data,
          utilitas: utilitasResponse.data,
        },
      });
    } catch (error) {
      console.error("Failed to fetch kabkot data:", error);
    }
  },
}));

export default useMapStore;
