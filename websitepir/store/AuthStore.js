import { getMeService } from "@/services/AuthServices";
import {
    setSecureToken,
    getAuthToken,
    clearSecureToken,
    isAuthenticated
} from "@/utils/SecureTokenManager";

// Legacy token manager for backward compatibility
const { setTokenUserInLocalStorage, removeTokenUserFromLocalStorage, setExpiresTokenInLocalStorage, removeExpiresTokenFromLocalStorage, getTokenUserFromLocalStorage, setGeoTokenInLocalStorage,
    setGeoExpiresTokenInLocalStorage,
    removeGeoTokenFromLocalStorage,
    removeGeoExpiresTokenFromLocalStorage,
    getGeoTokenFromLocalStorage} = require("@/utils/TokenManager");
const { create } = require("zustand");

const useAuthStore = create((set) => ({
    user: null,
    token: null,
    geoToken: null,
    roleId: null,
    id: null,   
    id_adm_kabkot: null,
    id_adm_provinsi: null,
    id_kawasan: null,
    isLoggedIn: false,
    isLoading: true,
    setUser: (user, token, expires, geoToken, geoExpires) => {
        set({ user, token, expires, geoToken, geoExpires, isLoggedIn: !!token});

        // Use secure token storage
        if (token && expires) {
            setSecureToken(token, expires);
        }

        // Legacy storage for backward compatibility
        setTokenUserInLocalStorage(token);
        setExpiresTokenInLocalStorage(expires);
        setGeoTokenInLocalStorage(geoToken);
        setGeoExpiresTokenInLocalStorage(geoExpires);
    },
    logout: () => {
        set({ user: null, token: null, roleId: null, id: null, id_adm_kabkot: null, id_adm_provinsi: null, id_kawasan: null, expires: null, geoToken: null, geoExpires: null, isLoggedIn: false, isLoading: false });

        // Clear secure token storage
        clearSecureToken();

        // Legacy cleanup for backward compatibility
        removeTokenUserFromLocalStorage();
        removeExpiresTokenFromLocalStorage();
        removeGeoTokenFromLocalStorage();
        removeGeoExpiresTokenFromLocalStorage();
    },
    // getMe: async () => {
    //     try {
    //         const token = getTokenUserFromLocalStorage();
    //         const response = await getMeService(token);

    //         if (response && response.data) {
    //             set({ user: response.data, roleId: response.data?.roleId });
    //         }
    //     } catch (error) {
    //         console.error("Fetch user error:", error);
    //         throw error;
    //     }
    // }
    getMe: async () => {
        try {
            const token = getTokenUserFromLocalStorage();
            if (!token) {
                // Jika token tidak ada, keluar dari fungsi tanpa memanggil getMeService
                // console.warn("No token found, skipping getMe");
                return;
            }

            const response = await getMeService();

            if (response && response.data) {
                set({
                    user: response.data,
                    roleId: response.data?.roleId,
                    id: response.data?.id || null,
                    id_adm_kabkot: response.data?.id_adm_kabkot || null,
                    id_adm_provinsi: response.data?.id_adm_provinsi || null,
                    id_kawasan: response.data?.id_kawasan || null,
                    isLoading: false
                });
            }
        } catch (error) {
            console.error("Fetch user error:", error);
            throw error;
        }
    }
}));

export default useAuthStore;