const app2Url = process.env.APP2_URL;
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  // Configure routes that should not be statically generated
  excludeDefaultMomentLocales: true,
  reactStrictMode: true,
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  poweredByHeader: false,
  images: {
    domains: ["0.0.0.0", "localhost", "pirdev.webgis.app"],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "pirdev.webgis.app",
        port: "",
        pathname: "/be/uploads/icon-web/**",
      },
    ],
  },
  env: {
    API_URL: process.env.API_URL,
  },
  // Prevent static generation of API routes that use cookies
  experimental: {
    // Use outputFileTracingExcludes to skip problematic routes during build
    outputFileTracingExcludes: {
      '/api/layers/get-layer-id': ['**/app/api/layers/get-layer-id/route.js'],
      '/api/maps/get-map-id': ['**/app/api/maps/get-map-id/route.js']
    },
  },
  serverRuntimeConfig: {
    mySecret: process.env.MY_SECRET, // Hanya tersedia di sisi server
    httpAgentOptions: {
      keepAlive: false, // Disable keep-alive to prevent request smuggling
      maxHeaderSize: 8192, // 8KB max header size
      maxCachedSessions: 100,
      maxFreeSockets: 256,
      timeout: 60000
    }
  },
  publicRuntimeConfig: {
    staticFolder: "/static", // Dapat digunakan di server dan client
  },
  async rewrites() {
    return [
      {
        source: '/peluang_investasi',
        destination: '/user/peluang_investasi',
      },
      {
        source: '/peluang_investasi/:path*',
        destination: '/user/peluang_investasi/:path*',
      },
      {
        source: '/daerah',
        destination: '/user/daerah',
      },
      {
        source: '/daerah/:path*',
        destination: '/user/daerah/:path*',
      },
      {
        source: '/hilirisasi',
        destination: '/user/hilirisasi',
      },
      {
        source: '/hilirisasi/:path*',
        destination: '/user/hilirisasi/:path*',
      },
      {
        source: '/kebijakan',
        destination: '/user/kebijakan',
      },
      {
        source: '/kebijakan/:path*',
        destination: '/user/kebijakan/:path*',
      },
      {
        source: '/insentif',
        destination: '/user/insentif',
      },
      {
        source: '/insentif/:path*',
        destination: '/user/insentif/:path*',
      },
      {
        source: '/artikel',
        destination: '/user/artikel',
      },
      {
        source: '/artikel/:path*',
        destination: '/user/artikel/:path*',
      },
      {
        source: '/sektor_unggulan_nasional',
        destination: '/user/sektor_unggulan_nasional',
      },
      {
        source: '/sektor_unggulan_nasional/:path*',
        destination: '/user/sektor_unggulan_nasional/:path*',
      },
      {
        source: '/faq',
        destination: '/user/faq',
      },
      {
        source: '/faq/:path*',
        destination: '/user/faq/:path*',
      },
      {
        source: '/search',
        has: [
          {
            type: 'query',
            key: 'q'
          }
        ],
        destination: '/user/search?q=:q',
      },
      {
        source: '/search',
        has: [
          {
            type: 'query',
            key: 'q'
          },
          {
            type: 'query',
            key: 'page'
          }
        ],
        destination: '/user/search?q=:q&page=:page',
      },
      {
        source: '/eksternal_link',
        destination: '/user/eksternal_link',
      },
      {
        source: '/eksternal_link/:path*',
        destination: '/user/eksternal_link/:path*',
      },
      {
        source: '/ikn',
        destination: '/user/ikn',
      },
      {
        source: '/ikn/:path*',
        destination: '/user/ikn/:path*',
      },
    ];
  },
  async headers() {
    const cspHeader = `
      default-src 'self' https:;
      script-src 'self' 'unsafe-eval' 'unsafe-inline' https://www.googletagmanager.com https://www.google-analytics.com https://www.youtube.com;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
      img-src 'self' blob: data: https: ${process.env.NEXT_PUBLIC_BASE_URL || ''};
      font-src 'self' https://fonts.gstatic.com;
      object-src 'none';
      base-uri 'self';
      form-action 'self';
      frame-ancestors 'none';
      upgrade-insecure-requests;
      connect-src 'self' https: wss: ${process.env.NEXT_PUBLIC_API_BASE_URL_GEO || ''} ${process.env.NEXT_PUBLIC_BASE_URL || ''};
      media-src 'self' blob: data: https:;
      child-src 'none';
      frame-src 'self' https:;
      worker-src 'self' blob:;
    `.replace(/\s{2,}/g, ' ').trim();

    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: cspHeader
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=63072000; includeSubDomains; preload'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), interest-cohort=(), bluetooth=(), midi=(), speaker-selection=()'
          },
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'unsafe-none'
          },
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'same-origin'
          },
          {
            key: 'Cross-Origin-Resource-Policy',
            value: 'same-origin'
          },
          // Remove potentially dangerous headers
          {
            key: 'Server',
            value: ''
          },
          {
            key: 'X-Powered-By',
            value: ''
          }
        ],
      },
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: "default-src 'none'; frame-ancestors 'none';"
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate'
          },
          {
            key: 'Pragma',
            value: 'no-cache'
          },
          {
            key: 'Expires',
            value: '0'
          },
          // Anti-smuggling headers
          {
            key: 'Connection',
            value: 'close'
          },
          {
            key: 'Keep-Alive',
            value: 'timeout=5, max=1'
          }
        ],
      },
      {
        source: '/admin/:path*',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN'
          },
          // Less restrictive Cache-Control to allow proper loading
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, must-revalidate'
          }
          // Removed Connection: close and other restrictive headers
        ]
      }
    ]
  },
};

module.exports = nextConfig;

