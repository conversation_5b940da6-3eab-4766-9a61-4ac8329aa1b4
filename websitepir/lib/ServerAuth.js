/**
 * Server-side Authentication and Credential Management
 * Provides secure credential handling for server-side operations
 */

/**
 * Get secure credentials from server-side environment variables
 * This function should only be called on the server side
 */
export const getSecureCredentials = () => {
  // Ensure this only runs on server side
  if (typeof window !== 'undefined') {
    throw new Error('getSecureCredentials should only be called on the server side');
  }
  
  const credentials = {
    geoUsername: process.env.NEXT_PUBLIC_GEO_USERNAME,
    geoPassword: process.env.NEXT_PUBLIC_GEO_PASSWORD,
    geoportalUsername: process.env.NEXT_PUBLIC_UNAME_GEOPORTAL,
    geoportalPassword: process.env.NEXT_PUBLIC_PASS_GEOPORTAL,
    apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL_GEO,
    baseUrl: process.env.NEXT_PUBLIC_BASE_URL,
    encryptionKey: process.env.NEXT_PUBLIC_ENCRYPTION_KEY,
    csrfSecret: process.env.CSRF_SECRET,
    sessionSecret: process.env.SESSION_SECRET
  };
  
  // Validate that required credentials exist
  const requiredFields = ['geoUsername', 'geoPassword'];
  const missingFields = requiredFields.filter(field => !credentials[field]);
  
  if (missingFields.length > 0) {
    throw new Error(`Missing required credentials: ${missingFields.join(', ')}`);
  }
  
  return credentials;
};

/**
 * Validate credential format and strength
 */
export const validateCredentials = (credentials) => {
  const { geoUsername, geoPassword } = credentials;
  
  // Username validation
  if (!geoUsername || typeof geoUsername !== 'string') {
    return { valid: false, message: 'Invalid username format' };
  }
  
  if (geoUsername.length < 3 || geoUsername.length > 50) {
    return { valid: false, message: 'Username must be between 3 and 50 characters' };
  }
  
  // Password validation
  if (!geoPassword || typeof geoPassword !== 'string') {
    return { valid: false, message: 'Invalid password format' };
  }
  
  if (geoPassword.length < 8) {
    return { valid: false, message: 'Password must be at least 8 characters' };
  }
  
  // Check for common weak passwords
  const weakPasswords = ['password', '12345678', 'admin', 'qwerty'];
  if (weakPasswords.some(weak => geoPassword.toLowerCase().includes(weak))) {
    return { valid: false, message: 'Password contains common weak patterns' };
  }
  
  return { valid: true, message: 'Credentials are valid' };
};

/**
 * Create secure authentication headers for API requests
 */
export const createSecureAuthHeaders = (credentials, additionalHeaders = {}) => {
  const validation = validateCredentials(credentials);
  if (!validation.valid) {
    throw new Error(`Invalid credentials: ${validation.message}`);
  }
  
  const { geoUsername, geoPassword } = credentials;
  
  // Create basic auth header
  const basicAuth = Buffer.from(`${geoUsername}:${geoPassword}`).toString('base64');
  
  return {
    'Authorization': `Basic ${basicAuth}`,
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': 'PIR-Server/1.0',
    'X-Requested-With': 'XMLHttpRequest',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache',
    ...additionalHeaders
  };
};

/**
 * Secure API request function for server-side operations
 */
export const makeSecureApiRequest = async (url, options = {}) => {
  try {
    const credentials = getSecureCredentials();
    const headers = createSecureAuthHeaders(credentials, options.headers);
    
    const requestOptions = {
      method: options.method || 'GET',
      headers,
      ...options,
      // Override headers to ensure security
      headers
    };
    
    // Add timeout for security
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), options.timeout || 10000);
    
    requestOptions.signal = controller.signal;
    
    const response = await fetch(url, requestOptions);
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Secure API request failed:', error);
    throw error;
  }
};

/**
 * Validate API URL to prevent SSRF attacks
 */
export const validateApiUrl = (url) => {
  try {
    const parsedUrl = new URL(url);
    
    // Only allow specific protocols
    if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
      return { valid: false, message: 'Invalid protocol' };
    }
    
    // Get allowed hosts from environment
    const allowedHosts = [
      process.env.NEXT_PUBLIC_API_BASE_URL_GEO?.replace(/^https?:\/\//, ''),
      'localhost',
      '127.0.0.1',
      'pirdev.webgis.app'
    ].filter(Boolean);
    
    // Check if hostname is allowed
    const isAllowedHost = allowedHosts.some(host => {
      return parsedUrl.hostname === host || parsedUrl.hostname.endsWith(`.${host}`);
    });
    
    if (!isAllowedHost) {
      return { valid: false, message: 'Host not allowed' };
    }
    
    // Prevent access to private IP ranges
    const privateIpPatterns = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      /^192\.168\./,
      /^127\./,
      /^169\.254\./,
      /^::1$/,
      /^fc00:/,
      /^fe80:/
    ];
    
    const isPrivateIp = privateIpPatterns.some(pattern => pattern.test(parsedUrl.hostname));
    if (isPrivateIp && !['localhost', '127.0.0.1'].includes(parsedUrl.hostname)) {
      return { valid: false, message: 'Private IP access not allowed' };
    }
    
    return { valid: true, message: 'URL is valid' };
  } catch (error) {
    return { valid: false, message: 'Invalid URL format' };
  }
};

/**
 * Create secure session token
 */
export const createSecureSessionToken = () => {
  if (typeof window !== 'undefined') {
    throw new Error('createSecureSessionToken should only be called on the server side');
  }
  
  const crypto = require('crypto');
  const timestamp = Date.now().toString();
  const randomBytes = crypto.randomBytes(32).toString('hex');
  const sessionSecret = process.env.SESSION_SECRET || 'default-session-secret';
  
  const payload = `${timestamp}:${randomBytes}`;
  const signature = crypto.createHmac('sha256', sessionSecret).update(payload).digest('hex');
  
  return `${payload}:${signature}`;
};

/**
 * Validate secure session token
 */
export const validateSecureSessionToken = (token, maxAge = 24 * 60 * 60 * 1000) => {
  if (typeof window !== 'undefined') {
    throw new Error('validateSecureSessionToken should only be called on the server side');
  }
  
  if (!token || typeof token !== 'string') {
    return { valid: false, message: 'Invalid token format' };
  }
  
  try {
    const crypto = require('crypto');
    const parts = token.split(':');
    
    if (parts.length !== 3) {
      return { valid: false, message: 'Invalid token structure' };
    }
    
    const [timestamp, randomBytes, signature] = parts;
    const tokenTime = parseInt(timestamp);
    
    // Check token age
    if (Date.now() - tokenTime > maxAge) {
      return { valid: false, message: 'Token expired' };
    }
    
    // Verify signature
    const payload = `${timestamp}:${randomBytes}`;
    const sessionSecret = process.env.SESSION_SECRET || 'default-session-secret';
    const expectedSignature = crypto.createHmac('sha256', sessionSecret).update(payload).digest('hex');
    
    const isValidSignature = crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
    
    if (!isValidSignature) {
      return { valid: false, message: 'Invalid signature' };
    }
    
    return { valid: true, tokenAge: Date.now() - tokenTime };
  } catch (error) {
    return { valid: false, message: 'Token validation error' };
  }
};

/**
 * Sanitize environment variables for client-side exposure
 */
export const getSafeClientConfig = () => {
  return {
    apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL_GEO,
    baseUrl: process.env.NEXT_PUBLIC_BASE_URL,
    recaptchaSiteKey: process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY,
    googleAnalytics: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS,
    // Never expose sensitive credentials
    hasCredentials: !!(process.env.NEXT_PUBLIC_GEO_USERNAME && process.env.NEXT_PUBLIC_GEO_PASSWORD)
  };
};

export default {
  getSecureCredentials,
  validateCredentials,
  createSecureAuthHeaders,
  makeSecureApiRequest,
  validateApiUrl,
  createSecureSessionToken,
  validateSecureSessionToken,
  getSafeClientConfig
};
