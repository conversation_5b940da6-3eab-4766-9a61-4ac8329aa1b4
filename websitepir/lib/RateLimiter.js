/**
 * Rate Limiter Module
 * Provides protection against brute force attacks and API abuse
 */

// In-memory store for rate limiting
// In production, consider using Redis or another distributed store
const rateLimitStore = new Map();
const MAX_ENTRIES = 10000; // Prevent memory leaks
const CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour

// Rate limit configurations for different endpoints
const RATE_LIMITS = {
  // Authentication endpoints - increased for high traffic
  auth: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 30, // 30 requests per window
    blockDuration: 15 * 60 * 1000 // 15 minutes block
  },
  // API endpoints - optimized for high concurrency
  api: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 200, // 200 requests per minute
    blockDuration: 2 * 60 * 1000 // 2 minutes block
  },
  // Default for all other endpoints - adjusted for production load
  default: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 300, // 300 requests per minute
    blockDuration: 1 * 60 * 1000 // 1 minute block
  }
};

// Helper function to get rate limit config based on path
const getRateLimitConfig = (path) => {
  if (path.includes('/auth') || path.includes('/login') || path.includes('/register')) {
    return RATE_LIMITS.auth;
  } else if (path.includes('/api/') || path.includes('/be/')) {
    return RATE_LIMITS.api;
  }
  return RATE_LIMITS.default;
};

// Clean up old entries to prevent memory leaks
const cleanupRateLimitStore = () => {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (now > entry.expiresAt) {
      rateLimitStore.delete(key);
    }
  }
  
  // If still too many entries, remove oldest
  if (rateLimitStore.size > MAX_ENTRIES) {
    const entries = [...rateLimitStore.entries()].sort((a, b) => a[1].lastRequest - b[1].lastRequest);
    const toDelete = entries.slice(0, entries.length - MAX_ENTRIES);
    toDelete.forEach(([key]) => rateLimitStore.delete(key));
  }
};

// Schedule regular cleanup
setInterval(cleanupRateLimitStore, CLEANUP_INTERVAL);

/**
 * Check if a request is rate limited
 * @param {string} ip - IP address
 * @param {string} path - Request path
 * @returns {Object} - Rate limit result
 */
export const checkRateLimit = (ip, path) => {
  // Create a key based on IP and path category
  const pathCategory = path.includes('/auth') ? 'auth' : 
                      (path.includes('/api/') || path.includes('/be/')) ? 'api' : 
                      'default';
  const key = `${ip}:${pathCategory}`;
  
  const config = getRateLimitConfig(path);
  const now = Date.now();
  
  // Get or create rate limit entry
  let entry = rateLimitStore.get(key);
  if (!entry) {
    entry = {
      count: 0,
      firstRequest: now,
      lastRequest: now,
      blocked: false,
      blockedUntil: 0,
      expiresAt: now + config.windowMs
    };
  }
  
  // Check if currently blocked
  if (entry.blocked && now < entry.blockedUntil) {
    const remainingBlockTime = Math.ceil((entry.blockedUntil - now) / 1000);
    return {
      limited: true,
      remaining: 0,
      resetTime: entry.blockedUntil,
      blockedFor: remainingBlockTime,
      message: `Rate limit exceeded. Try again in ${remainingBlockTime} seconds.`
    };
  }
  
  // Reset if window expired
  if (now > entry.expiresAt) {
    entry.count = 0;
    entry.firstRequest = now;
    entry.expiresAt = now + config.windowMs;
    entry.blocked = false;
    entry.blockedUntil = 0;
  }
  
  // Increment counter
  entry.count++;
  entry.lastRequest = now;
  
  // Check if limit exceeded
  if (entry.count > config.maxRequests) {
    entry.blocked = true;
    entry.blockedUntil = now + config.blockDuration;
    
    rateLimitStore.set(key, entry);
    
    const remainingBlockTime = Math.ceil(config.blockDuration / 1000);
    return {
      limited: true,
      remaining: 0,
      resetTime: entry.blockedUntil,
      blockedFor: remainingBlockTime,
      message: `Rate limit exceeded. Try again in ${remainingBlockTime} seconds.`
    };
  }
  
  // Update store
  rateLimitStore.set(key, entry);
  
  // Return limit info
  return {
    limited: false,
    remaining: config.maxRequests - entry.count,
    resetTime: entry.expiresAt,
    message: null
  };
};

/**
 * Log rate limit events
 * @param {Object} result - Rate limit check result
 * @param {string} ip - IP address
 * @param {string} path - Request path
 * @param {string} userAgent - User agent
 */
export const logRateLimitEvent = (result, ip, path, userAgent) => {
  if (result.limited) {
    console.warn(`[RATE LIMIT] Blocked request from ${ip} to ${path}. ${result.message}`, {
      timestamp: new Date().toISOString(),
      ip,
      path,
      userAgent,
      blockedFor: result.blockedFor,
      resetTime: new Date(result.resetTime).toISOString()
    });
  }
};

/**
 * Reset rate limit for an IP
 * @param {string} ip - IP address to reset
 */
export const resetRateLimit = (ip) => {
  for (const key of rateLimitStore.keys()) {
    if (key.startsWith(`${ip}:`)) {
      rateLimitStore.delete(key);
    }
  }
};

export default {
  checkRateLimit,
  logRateLimitEvent,
  resetRateLimit
};
