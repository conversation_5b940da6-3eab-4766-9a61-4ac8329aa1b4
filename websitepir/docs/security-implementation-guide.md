# Security Implementation Guide

This document provides detailed technical guidance for implementing and maintaining the security measures in the Website PIR application.

## Table of Contents
1. [HTTP Request Smuggling Prevention](#http-request-smuggling-prevention)
2. [SSRF Protection Implementation](#ssrf-protection-implementation)
3. [Credential Security Management](#credential-security-management)
4. [Security Headers Configuration](#security-headers-configuration)
5. [URL Sanitization System](#url-sanitization-system)
6. [Testing and Validation](#testing-and-validation)
7. [Monitoring and Alerting](#monitoring-and-alerting)

## HTTP Request Smuggling Prevention

### Overview
HTTP Request Smuggling occurs when front-end and back-end servers disagree about where one request ends and the next begins. Our implementation prevents CL.TE, TE.CL, and TE.TE attacks.

### Implementation Details

#### 1. Middleware Enhancement (`middleware.js`)

```javascript
// Enhanced function to check for request smuggling attempts
const checkForRequestSmuggling = (request) => {
  const headers = request.headers;
  const contentLength = headers.get('content-length');
  const transferEncoding = headers.get('transfer-encoding');
  const connection = headers.get('connection');
  
  // Check for conflicting headers (CL.TE vulnerability)
  if (contentLength && transferEncoding) {
    return true; // Potential smuggling attempt
  }
  
  // Check for suspicious Transfer-Encoding values
  if (transferEncoding && !transferEncoding.toLowerCase().includes('chunked')) {
    return true;
  }
  
  // Check for suspicious Connection headers
  if (connection && connection.toLowerCase().includes('upgrade')) {
    return true;
  }
  
  // Check for multiple Content-Length headers
  const clHeaders = headers.get('content-length');
  if (clHeaders && clHeaders.includes(',')) {
    return true;
  }
  
  return false;
};
```

#### 2. Request Body Pattern Detection

```javascript
const checkForMaliciousPatterns = async (request) => {
  try {
    // Only read body for POST/PUT/PATCH requests
    if (!['POST', 'PUT', 'PATCH'].includes(request.method)) {
      return false;
    }
    
    const body = await request.text();
    
    // Check for HTTP request smuggling patterns
    const smugglingPatterns = [
      /HTTP\/1\.[01]/i,                                    // HTTP version headers
      /GET\s+\/.*HTTP\/1\.[01]/i,                         // GET requests in body
      /POST\s+\/.*HTTP\/1\.[01]/i,                        // POST requests in body
      /PUT\s+\/.*HTTP\/1\.[01]/i,                         // PUT requests in body
      /DELETE\s+\/.*HTTP\/1\.[01]/i,                      // DELETE requests in body
      /\r\n\r\n.*HTTP\//i,                               // Double CRLF followed by HTTP
      /Transfer-Encoding:\s*chunked.*Content-Length:/i,   // TE + CL combination
      /Content-Length:.*Transfer-Encoding:\s*chunked/i    // CL + TE combination
    ];
    
    return smugglingPatterns.some(pattern => pattern.test(body));
  } catch (error) {
    console.error('Error reading request body:', error);
    return false;
  }
};
```

#### 3. Security Headers for Anti-Smuggling

```javascript
// Set strict anti-smuggling headers
response.headers.set('Connection', 'close');           // Prevent keep-alive abuse
response.headers.set('Keep-Alive', 'timeout=5, max=1'); // Limit connection reuse
response.headers.delete('Transfer-Encoding');          // Remove dangerous headers
response.headers.delete('TE');                         // Remove TE header
```

### Testing Request Smuggling Protection

```bash
# Test CL.TE vulnerability
curl -X POST \
  -H "Content-Length: 10" \
  -H "Transfer-Encoding: chunked" \
  -d "0\r\n\r\nGET /admin HTTP/1.1\r\nHost: target.com\r\n\r\n" \
  https://your-app.com/api/test

# Expected: 400 Bad Request: Request Smuggling Detected

# Test TE.CL vulnerability  
curl -X POST \
  -H "Transfer-Encoding: chunked" \
  -H "Content-Length: 4" \
  -d "5c\r\nGET /admin HTTP/1.1\r\nHost: target.com\r\n\r\n0\r\n\r\n" \
  https://your-app.com/api/test

# Expected: 400 Bad Request: Request Smuggling Detected
```

## SSRF Protection Implementation

### Overview
Server-Side Request Forgery (SSRF) allows attackers to make requests to internal services. Our implementation uses URL validation and whitelisting.

### Implementation Details

#### 1. Input Validation Functions

```javascript
// Validate API parameters
const validateLayerUid = (layerUid) => {
  if (!layerUid) return false;
  // Only allow alphanumeric characters, hyphens, and underscores
  const validPattern = /^[a-zA-Z0-9_-]+$/;
  return validPattern.test(layerUid) && layerUid.length <= 100;
};

const validateMapUid = (mapUid) => {
  if (!mapUid) return false;
  const validPattern = /^[a-zA-Z0-9_-]+$/;
  return validPattern.test(mapUid) && mapUid.length <= 100;
};
```

#### 2. URL Validation for SSRF Prevention

```javascript
const validateApiUrl = (url) => {
  try {
    const parsedUrl = new URL(url);
    const allowedHosts = [
      process.env.NEXT_PUBLIC_API_BASE_URL_GEO?.replace(/^https?:\/\//, ''),
      'localhost',    // Only for development
      '127.0.0.1'     // Only for development
    ].filter(Boolean);
    
    return allowedHosts.some(host => parsedUrl.hostname === host);
  } catch {
    return false;
  }
};
```

#### 3. Enhanced Fetch with Security Options

```javascript
const res = await fetch(apiUrl, {
  method: "GET",
  headers: {
    "Content-Type": "application/json",
    "Authorization": `Bearer ${geoAccessToken}`,
    "User-Agent": "NextJS-API-Client/1.0",
  },
  // Security options
  signal: AbortSignal.timeout(10000), // 10 second timeout
});
```

### URL Sanitizer Utility (`utils/UrlSanitizer.js`)

#### Private IP Detection

```javascript
const isPrivateIP = (ip) => {
  const privateRanges = [
    /^10\./,                          // 10.0.0.0/8
    /^172\.(1[6-9]|2[0-9]|3[01])\./,  // **********/12
    /^192\.168\./,                    // ***********/16
    /^127\./,                         // *********/8 (localhost)
    /^169\.254\./,                    // ***********/16 (link-local)
    /^::1$/,                          // IPv6 localhost
    /^fc00:/,                         // IPv6 unique local
    /^fe80:/,                         // IPv6 link-local
  ];
  
  return privateRanges.some(range => range.test(ip));
};
```

#### Blocked Hosts List

```javascript
const BLOCKED_HOSTS = [
  'localhost',
  '127.0.0.1',
  '0.0.0.0',
  '::1',
  '***************',        // AWS metadata service
  '10.0.0.0/8',
  '**********/12',
  '***********/16',
  'metadata.google.internal', // Google Cloud metadata
  'metadata',
];
```

### Testing SSRF Protection

```bash
# Test internal service access
curl "https://your-app.com/api/layers/get-layer-id?layerUid=../../../etc/passwd"
# Expected: 400 Bad Request: Invalid layerUid parameter

# Test metadata service access
curl "https://your-app.com/api/layers/get-layer-id?layerUid=http://***************/"
# Expected: 400 Bad Request: Invalid layerUid parameter

# Test private network scanning
curl "https://your-app.com/api/layers/get-layer-id?layerUid=http://***********/"
# Expected: 400 Bad Request: Invalid layerUid parameter
```

## Credential Security Management

### Environment Variable Implementation

#### 1. Secure Credential Loading

```javascript
// hooks/useMapApi.js
const fetchGeoToken = async () => {
  try {
    // Use environment variables instead of hardcoded credentials
    const username = process.env.NEXT_PUBLIC_GEO_USERNAME;
    const password = process.env.NEXT_PUBLIC_GEO_PASSWORD;
    
    if (!username || !password) {
      throw new Error("Missing geo credentials in environment variables");
    }
    
    const response = await ApiGeo.post("/iam/login", {
      username: username,
      password: password,
    });
    
    setGeoAccessToken(response.accessToken);
  } catch (error) {
    setError("Failed to get access token");
    console.error(error);
  }
};
```

#### 2. Environment Variable Validation

```javascript
// Add validation in components that use credentials
const validateCredentials = () => {
  const requiredVars = [
    'NEXT_PUBLIC_GEO_USERNAME',
    'NEXT_PUBLIC_GEO_PASSWORD',
    'NEXT_PUBLIC_API_BASE_URL_GEO'
  ];
  
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
};
```

### Required Environment Variables

```bash
# Geographic API Credentials
NEXT_PUBLIC_GEO_USERNAME=your_NEXT_PUBLIC_GEO_USERNAME
NEXT_PUBLIC_GEO_PASSWORD=your_secure_NEXT_PUBLIC_GEO_PASSWORD

# API URLs
NEXT_PUBLIC_API_BASE_URL_GEO=https://your-geo-api.com
NEXT_PUBLIC_BASE_URL=https://your-api.com
NEXT_PUBLIC_BASE_URL2=https://your-frontend.com

# Encryption Key (32 character hex string)
NEXT_PUBLIC_ENCRYPTION_KEY=your_32_character_hex_encryption_key
```

### Credential Rotation Process

1. **Generate New Credentials**
   ```bash
   # Generate secure password
   openssl rand -base64 32
   
   # Generate encryption key
   node -e "console.log(require('crypto').randomBytes(16).toString('hex'))"
   ```

2. **Update Environment Variables**
   - Update `.env.local` for development
   - Update production environment configuration
   - Update CI/CD pipeline secrets

3. **Verify Application Functionality**
   - Test API connections
   - Verify authentication flows
   - Monitor error logs

## Security Headers Configuration

### Content Security Policy (CSP)

```javascript
// next.config.js
const cspHeader = `
  default-src 'self';
  script-src 'self' 'unsafe-eval' 'unsafe-inline' 
    https://www.googletagmanager.com 
    https://www.google-analytics.com;
  style-src 'self' 'unsafe-inline' 
    https://fonts.googleapis.com;
  img-src 'self' blob: data: https: 
    ${process.env.NEXT_PUBLIC_BASE_URL || ''};
  font-src 'self' 
    https://fonts.gstatic.com;
  object-src 'none';
  base-uri 'self';
  form-action 'self';
  frame-ancestors 'none';
  upgrade-insecure-requests;
  connect-src 'self' https: wss: 
    ${process.env.NEXT_PUBLIC_API_BASE_URL_GEO || ''} 
    ${process.env.NEXT_PUBLIC_BASE_URL || ''};
  media-src 'self' blob: data: https:;
  child-src 'none';
  frame-src 'self' https:;
  worker-src 'self' blob:;
`.replace(/\s{2,}/g, ' ').trim();
```

### Comprehensive Security Headers

```javascript
// Applied to all routes
{
  key: 'Strict-Transport-Security',
  value: 'max-age=63072000; includeSubDomains; preload'
},
{
  key: 'X-Content-Type-Options',
  value: 'nosniff'
},
{
  key: 'X-Frame-Options',
  value: 'DENY'
},
{
  key: 'X-XSS-Protection',
  value: '1; mode=block'
},
{
  key: 'Referrer-Policy',
  value: 'strict-origin-when-cross-origin'
},
{
  key: 'Permissions-Policy',
  value: 'camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()'
}
```

### API-Specific Headers

```javascript
// Applied to /api/* routes
{
  key: 'Cache-Control',
  value: 'no-cache, no-store, must-revalidate'
},
{
  key: 'Pragma',
  value: 'no-cache'
},
{
  key: 'Expires',
  value: '0'
},
{
  key: 'Connection',
  value: 'close'
},
{
  key: 'Keep-Alive',
  value: 'timeout=5, max=1'
}
```

## URL Sanitization System

### Usage Examples

```javascript
import { sanitizeUrl, validateApiUrl, sanitizeQueryParams } from '@/utils/UrlSanitizer';

// Sanitize external URLs
try {
  const safeUrl = sanitizeUrl(userInput, ['allowed-domain.com']);
  console.log('Safe URL:', safeUrl);
} catch (error) {
  console.error('URL sanitization failed:', error.message);
}

// Validate API endpoints
try {
  const apiUrl = validateApiUrl(endpoint, ['api.example.com']);
  console.log('Valid API URL:', apiUrl);
} catch (error) {
  console.error('API URL validation failed:', error.message);
}

// Sanitize query parameters
const cleanParams = sanitizeQueryParams({
  search: '<script>alert("xss")</script>',
  page: '1',
  malicious: 'javascript:alert(1)'
});
// Result: { search: 'scriptalert(xss)/script', page: '1' }
```

### Custom Validation Rules

```javascript
// Add custom validation for specific use cases
const validateGeoApiUrl = (url) => {
  const allowedPaths = ['/cms/layer/', '/cms/map/', '/iam/login'];
  const parsedUrl = new URL(url);
  
  return allowedPaths.some(path => parsedUrl.pathname.startsWith(path));
};
```

## Testing and Validation

### Automated Security Tests

Create test files to validate security measures:

```javascript
// tests/security/request-smuggling.test.js
describe('Request Smuggling Protection', () => {
  test('should block CL.TE attacks', async () => {
    const response = await fetch('/api/test', {
      method: 'POST',
      headers: {
        'Content-Length': '10',
        'Transfer-Encoding': 'chunked'
      },
      body: '0\r\n\r\nGET /admin HTTP/1.1\r\n\r\n'
    });
    
    expect(response.status).toBe(400);
    expect(response.statusText).toContain('Request Smuggling Detected');
  });
});

// tests/security/ssrf.test.js
describe('SSRF Protection', () => {
  test('should block internal service access', async () => {
    const response = await fetch('/api/layers/get-layer-id?layerUid=../../../etc/passwd');
    
    expect(response.status).toBe(400);
    expect(response.statusText).toContain('Invalid layerUid parameter');
  });
});
```

### Manual Testing Checklist

- [ ] Request smuggling protection
- [ ] SSRF prevention
- [ ] CSP violation detection
- [ ] Security headers presence
- [ ] Credential validation
- [ ] URL sanitization
- [ ] Input validation

### Security Monitoring

#### Log Analysis

```javascript
// Add security event logging
const logSecurityEvent = (event, details) => {
  console.log(JSON.stringify({
    timestamp: new Date().toISOString(),
    event: event,
    details: details,
    userAgent: request.headers.get('user-agent'),
    ip: request.headers.get('x-forwarded-for') || 'unknown'
  }));
};

// Usage in middleware
if (checkForRequestSmuggling(request)) {
  logSecurityEvent('REQUEST_SMUGGLING_ATTEMPT', {
    path: pathname,
    headers: Object.fromEntries(request.headers.entries())
  });
  
  return new NextResponse(null, {
    status: 400,
    statusText: 'Bad Request: Request Smuggling Detected'
  });
}
```

#### Alerting Setup

```javascript
// Simple alerting function
const sendSecurityAlert = async (event, details) => {
  if (process.env.NODE_ENV === 'production') {
    // Send to monitoring service
    await fetch(process.env.SECURITY_WEBHOOK_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        alert: 'Security Event',
        event: event,
        details: details,
        timestamp: new Date().toISOString()
      })
    });
  }
};
```

## Monitoring and Alerting

### Key Metrics to Monitor

1. **Security Events**
   - Request smuggling attempts
   - SSRF attempts
   - Invalid credential usage
   - CSP violations

2. **Performance Impact**
   - Middleware processing time
   - Request validation overhead
   - Failed authentication rates

3. **Error Rates**
   - 400 Bad Request responses
   - 401 Unauthorized responses
   - 500 Internal Server Errors

### Recommended Tools

- **Application Monitoring**: New Relic, DataDog, or similar
- **Log Analysis**: ELK Stack, Splunk, or CloudWatch
- **Security Scanning**: OWASP ZAP, Burp Suite
- **Dependency Scanning**: Snyk, npm audit

### Maintenance Schedule

- **Daily**: Monitor security logs and alerts
- **Weekly**: Review failed authentication attempts
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Comprehensive security audit
- **Annually**: Penetration testing

---

This implementation guide provides the technical foundation for maintaining robust security in the Website PIR application. Regular updates and monitoring are essential for continued protection against evolving threats. 