import useAuthStore from "@/store/AuthStore";
import Api from "@/utils/Api"
import ApiForm from "@/utils/ApiForm";

// export const getDataTablePDRBSektorDaerahService = async (page, pageSize, search) => {
//     const { id_adm_provinsi, id_adm_kabkot } = useAuthStore.getState();
//     let url = `/pdrb_sektor_daerah/datatable/${page}/${pageSize}/${search}`;

//     if (id_adm_provinsi) {
//         url += `?id_adm_provinsi=${id_adm_provinsi}`;
//       } else if (id_adm_kabkot) {
//         url += `?id_adm_kabkot=${id_adm_kabkot}`;
//       }
//         hasQueryParam = true;
//     } else if (id_adm_kabkot) {
//         url += `?id_adm_kabkot=${id_adm_kabkot}`;
//         hasQueryParam = true;
//     }
    
//     // Menambahkan parameter sorting
//     if (order && by) {
//         // Gunakan ? jika belum ada parameter query, & jika sudah ada
//         if (hasQueryParam) {
//             url += `&order=${order}&by=${by}`;
//         } else {
//             url += `?order=${order}&by=${by}`;
//         }
//     }
export const getDataTablePDRBSektorDaerahService = async (page, pageSize, search, order = null, by = null, status = null) => {
    const { id_adm_provinsi, id_adm_kabkot, roleId, id } = useAuthStore.getState();
    let url = `/pdrb_sektor_daerah/datatable/${page}/${pageSize}/${search}`;
    
    // Menambahkan parameter yang sudah ada (provinsi atau kabkot)
    let hasQueryParam = false;
    console.log('roleID',roleId)
    console.log('roleIDid',id)
    // Add pic parameter for role 14 users
    if (roleId === 14) {
      url += `?pic=${id}`;
      hasQueryParam = true;
    } else if (id_adm_provinsi) {
      url += `?id_adm_provinsi=${id_adm_provinsi}`;
      hasQueryParam = true;
    } else if (id_adm_kabkot) {
      url += `?id_adm_kabkot=${id_adm_kabkot}`;
      hasQueryParam = true;
    }
    
    // Menambahkan parameter sorting
    if (order && by) {
      // Gunakan ? jika belum ada parameter query, & jika sudah ada
      if (hasQueryParam) {
        url += `&order=${order}&by=${by}`;
      } else {
        url += `?order=${order}&by=${by}`;
      }
    }
  
    // Menambahkan parameter status jika ada
    if (status !== null) {
      // Gunakan ? jika belum ada parameter query, & jika sudah ada
      if (hasQueryParam || order || by) {
        url += `&status=${status}`;
      } else {
        url += `?status=${status}`;
      }
    }
    
    const response = await Api.get(url);
    return response;
  };
  

export const getPDRBSektorDaerahService = async () => {
    return await Api.get(`/pdrb_sektor_daerah/`);
}

export const getByIdPDRBSektorDaerahService = async (id) => {
    return await Api.get(`/pdrb_sektor_daerah/${id}`);
}

export const createPDRBSektorDaerahService = async (body) => {
    return await ApiForm.post(`/pdrb_sektor_daerah`, body);
}

export const editPDRBSektorDaerahService = async (body, id) => {
    return await ApiForm.put(`/pdrb_sektor_daerah/${id}`, body)
}

export const deletePDRBSektorDaerahService = async (id) => {
    return await Api.delete(`/pdrb_sektor_daerah/${id}`);
}

export const editStatusPDRBSektorDaerahService = async (body, id) => {
    // Retrieve the token from where you are storing it (example: localStorage)
    // const token = localStorage.getItem('token'); // or from cookies or Zustand
    
    // Make the request with the Authorization header
    return await ApiForm.put(`/pdrb_sektor_daerah/change_status/${id}`, body
    //     , {
    //   headers: {
    //     Authorization: `Bearer ${token}`, // Include the token in the Authorization header
    //   },
    // }
);
  };

// export const editStatusPDBSektorNasionalService = async (body, id) => {
//     // Retrieve the token from where you are storing it (example: localStorage)
//     const token = localStorage.getItem('token'); // or from cookies or Zustand
    
//     // Make the request with the Authorization header
//     return await ApiForm.put(`/pdb_sektor_nasional/aprove/${id}`, body, {
//       headers: {
//         Authorization: `Bearer ${token}`, // Include the token in the Authorization header
//       },
//     });
//   };
