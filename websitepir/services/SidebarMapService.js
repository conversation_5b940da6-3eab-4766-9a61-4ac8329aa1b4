import Api from "@/utils/Api"
import ApiForm from "@/utils/ApiForm";


//zona waktu
export const getZonaWaktuService = async (lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_zona_waktu/${langParam}`);
}

export const getProvByZonaWaktuLangService = async (lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_prov/${langParam}`);
};

export const getProvByZonaWaktuService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_prov/${id}${langParam}`);
}

export const getPPIByZonaWaktuService = async (id,lang) => {
     const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_ppi_by_zonawaktu/${id}${langParam}`);
}

export const getIPROByZonaWaktuService = async (id,lang) => {
     const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_ipro_by_zonawaktu/${id}${langParam}`);
}

export const getPIDByZonaWaktuService = async (id,lang) => {
     const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_pid_by_zonawaktu/${id}${langParam}`);
}

export const getKawasanByZonaWaktuService = async (id,lang) => {
     const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_kawasan_by_zonawaktu/${id}${langParam}`);
}

export const getKEKByZonaWaktuService = async (id,lang) => {
     const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_kek_by_zonawaktu/${id}${langParam}`);
}

export const getBandaraByZonaWaktuService = async (id,lang) => {
     const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_bandara_by_zonawaktu/${id}${langParam}`);
}

export const getPelabuhanByZonaWaktuService = async (id,lang) => {
     const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_pelabuhan_by_zonawaktu/${id}${langParam}`);
}

export const getHotelByZonaWaktuService = async (id,lang) => {
     const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_hotel_by_zonawaktu/${id}${langParam}`);
}

export const getPendidikanByZonaWaktuService = async (id,lang) => {
     const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_pendidikan_by_zonawaktu/${id}${langParam}`);
}

export const getRumahSakitByZonaWaktuService = async (id,lang) => {
     const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_rumah_sakit_by_zonawaktu/${id}${langParam}`);
}

//byprovinsi
export const getPPIByProvinsiService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_ppi_by_prov/${id}${langParam}`);
}

export const getIPROByProvinsiService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_ipro_by_prov/${id}${langParam}`);
}

export const getPIDByProvinsiService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_pid_by_prov/${id}${langParam}`);
}

export const getKawasanByProvinsiService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_kawasan_by_prov/${id}${langParam}`);
}

export const getKEKByProvinsiService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_kek_by_prov/${id}${langParam}`);
}

export const getBandaraByProvinsiService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_bandara_by_prov/${id}${langParam}`);
}

export const getPelabuhanByProvinsiService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_pelabuhan_by_prov/${id}${langParam}`);
}

export const getHotelByProvinsiService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_hotel_by_prov/${id}${langParam}`);
}

export const getPendidikanByProvinsiService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_pendidikan_by_prov/${id}${langParam}`);
}

export const getRumahSakitByProvinsiService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_rumah_sakit_by_prov/${id}${langParam}`);
}

export const getRTRWByProvinsiService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_rtrw_by_prov/${id}${langParam}`);
}

export const getRDTRByProvinsiService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_rdtr_by_prov/${id}${langParam}`);
}

export const getKomoditiByProvinsiService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_komoditi_by_prov/${id}${langParam}`);
}

export const getUtilitasByProvinsiService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_utilitas_by_prov/${id}${langParam}`);
}

//by kabkot
export const getPPIByKabkotService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_ppi_by_kabkot/${id}${langParam}`);
}
export const getIPROByKabkotService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_ipro_by_kabkot/${id}${langParam}`);
}

export const getPIDByKabkotService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_pid_by_kabkot/${id}${langParam}`);
}
export const getKawasanByKabkotService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_kawasan_by_kabkot/${id}${langParam}`);
}

export const getKEKByKabkotService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_kek_by_kabkot/${id}${langParam}`);
}

export const getBandaraByKabkotService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_bandara_by_kabkot/${id}${langParam}`);
}

export const getPelabuhanByKabkotService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_pelabuhan_by_kabkot/${id}${langParam}`);
}

export const getHotelByKabkotService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_hotel_by_kabkot/${id}${langParam}`);
}

export const getPendidikanByKabkotService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_pendidikan_by_kabkot/${id}${langParam}`);
}

export const getRumahSakitByKabkotService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_rumah_sakit_by_kabkot/${id}${langParam}`);
}

export const getRTRWByKabkotService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_rtrw_by_kabkot/${id}${langParam}`);
}

export const getRDTRByKabkotService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_rdtr_by_kabkot/${id}${langParam}`);
}

export const getKomoditiByKabkotService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_komoditi_by_kabkot/${id}${langParam}`);
}

export const getUtilitasByKabkotService = async (id,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/daerah/get_utilitas_by_kabkot/${id}${langParam}`);
}

export const getKabkotRefService = async (id_prov,lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
    return await Api.get(`/peluang/kabkot_ref/${id_prov}${langParam}`);
}

export const getBandaraIKN = async (lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
   return await Api.get(`/ikn/get_bandara/${langParam}`);
}

export const getPelabuhanIKN = async (lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
   return await Api.get(`/ikn/get_pelabuhan/${langParam}`);
}

export const getHotelIKN = async (lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
   return await Api.get(`/ikn/get_hotel/${langParam}`);
}

export const getPendidikanIKN = async (lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
   return await Api.get(`/ikn/get_pendidikan/${langParam}`);
}

export const getRumahSakitIKN = async (lang) => {
    const langParam = lang === 'en' ? '?en=true' : '';
   return await Api.get(`/ikn/get_rumah_sakit/${langParam}`);
}