import useAuthStore from "@/store/AuthStore";
import Api, { APICORS } from "@/utils/Api"
import ApiForm from "@/utils/ApiForm";
import { getTokenUserFromLocalStorage } from "@/utils/TokenManager";
import { getCookie } from "cookies-next";


//umr
// export const getDataTableUMRProvinsiService = async (page, pageSize, search) => {
//     const response = await Api.get(`/umr_provinsi/datatable/${page}/${pageSize}/${search}`);
//     return response;
// }
// export const getDataTableUMRProvinsiService = async (page, pageSize, search) => {
//     const { id_adm_provinsi } = useAuthStore.getState(); // Ambil id_adm_provinsi dari store

//     let url = `/umr_provinsi/datatable/${page}/${pageSize}/${search}`;

//     // Jika id_adm_provinsi ada, tambahkan sebagai query parameter
//     if (id_adm_provinsi) {
//         url += `?id_adm_provinsi=${id_adm_provinsi}`;
//     }

//     const response = await Api.get(url);
//     return response;
// };
// export const getDataTableUMRProvinsiService = async (page, pageSize, search) => {
//     const { user, isLoading: userLoading } = useAuthStore.getState();

//     // Pastikan tidak melakukan request jika state masih loading atau user belum tersedia
//     if (userLoading || !user) {
//         return null;
//     }

//     const baseURL = `/umr_provinsi/datatable/${page}/${pageSize}/${search}`;

//     const queryParams = [];

//     if (user.id_adm_provinsi) {
//         queryParams.push(`id_adm_provinsi=${user.id_adm_provinsi}`);
//     }

//     const url = queryParams.length ? `${baseURL}?${queryParams.join('&')}` : baseURL;

//     const response = await Api.get(url);
//     return response;
// };
const waitForUserState = async (maxAttempts = 5, interval = 200) => {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
        const { user, isLoading } = useAuthStore.getState();

        if (!isLoading && user) {
            return { user, success: true };
        }

        await new Promise(resolve => setTimeout(resolve, interval));
    }

    return { user: null, success: false };
};

// export const getDataTableUMRProvinsiService = async (page, pageSize, search) => {
//     try {
//         // Tunggu sampai user state tersedia
//         const { user, success } = await waitForUserState();

//         if (!success) {
//             throw new Error('Tidak dapat memuat data user setelah beberapa percobaan');
//         }

//         // Validasi user state
//         if (!user) {
//             throw new Error('User data tidak tersedia');
//         }

//         // Buat URL dengan query parameters
//         const baseURL = `/umr_provinsi/datatable/${page}/${pageSize}/${search}`;
//         const queryParams = new URLSearchParams();

//         if (user.id_adm_provinsi) {
//             queryParams.set('id_adm_provinsi', user.id_adm_provinsi);
//         }

//         // Gabungkan URL dengan query parameters
//         const url = queryParams.toString()
//             ? `${baseURL}?${queryParams.toString()}`
//             : baseURL;

//         const response = await Api.get(url);
//         return response;

//     } catch (error) {
//         console.error('Error in getDataTableUMRProvinsiService:', error);
//         throw error;
//     }
// };
export const getDataTableUMRProvinsiService = async (page, pageSize, search, sort = null, by = null, status = null) => {
    try {
      // Tunggu sampai user state tersedia
      const { user, success } = await waitForUserState();
  
      if (!success) {
        throw new Error('Tidak dapat memuat data user setelah beberapa percobaan');
      }
  
      // Validasi user state
      if (!user) {
        throw new Error('User data tidak tersedia');
      }
  
      // Base URL
      const baseURL = `/umr_provinsi/datatable/${page}/${pageSize}/${search}`;
      const queryParams = new URLSearchParams();
      
      // Tambahkan filter provinsi jika ada
      if (user.id_adm_provinsi) {
        queryParams.set('id_adm_provinsi', user.id_adm_provinsi);
      }

      // Tambahkan pic parameter untuk role 14
      if (user.roleId === 14) {
        queryParams.set('pic', user.id);
      }

      if (status !== null && status !== undefined) {
        queryParams.set('status', status);
      }
      
      // Tambahkan sorting jika ada
      if (sort && by) {
        queryParams.set('order', sort); // contoh: order = "tahun"
        queryParams.set('by', by);      // contoh: by = "desc"
      }
  
      // Gabungkan URL dengan query parameter
      const url = queryParams.toString() ? `${baseURL}?${queryParams.toString()}` : baseURL;
  
      const response = await Api.get(url);
      return response;
  
    } catch (error) {
      console.error('Error in getDataTableUMRProvinsiService:', error);
      throw error;
    }
  };
  


export const getUMRProvinsiService = async () => {
    return await Api.get(`/umr_provinsi/`);
}

export const getByIdUMRProvinsiService = async (id) => {
    return await Api.get(`/umr_provinsi/${id}`);
}

export const createUMRProvinsiService = async (body) => {
    return await ApiForm.post(`/umr_provinsi`, body);
}

export const editUMRProvinsiService = async (body, id) => {
    return await ApiForm.put(`/umr_provinsi/${id}`, body)
}

export const deleteUMRProvinsiService = async (id) => {
    return await Api.delete(`/umr_provinsi/${id}`);
}


export const editStatusUMRProvinsiService = async (body, id) => {
    // Retrieve the token from where you are storing it (example: localStorage)
    // const token = localStorage.getItem('token'); // or from cookies or Zustand

    // Make the request with the Authorization header
    return await ApiForm.put(`/umr_provinsi/change_status/${id}`, body
        //     , {
        //   headers: {
        //     Authorization: `Bearer ${token}`, // Include the token in the Authorization header
        //   },
        // }
    );
};




//kantor
// export const getDataTableKantorProvinsiService = async () => {
//     const response = await Api.get(`/admin/provinsi_kantor/lists`);
//     return response;
// }
export const getKantorProvinsiService = async () => {
    return await Api.get(`/admin/provinsi_kantor/lists`);
}

export const getByIdKantorProvinsiService = async (id) => {
    return await Api.get(`/admin/provinsi_kantor/lists/${id}`);
}

export const createKantorProvinsiService = async (body) => {
    return await ApiForm.post(`/admin/provinsi_kantor/create`, body);
}

export const editKantorProvinsiService = async (body, id) => {
    return await ApiForm.patch(`/admin/provinsi_kantor/update/${id}`, body)
}

export const deleteKantorProvinsiService = async (id) => {
    return await Api.delete(`/admin/provinsi_kantor/delete/${id}`);
}

export const statusKantorProvinsiService = async (body, id) => {
    return await APICORS.patch(`/admin/provinsi_kantor/toggle_status/${id}`, body);
}
//eskpor import
// export const getDataTableKantorProvinsiService = async () => {
//     const response = await Api.get(`/admin/provinsi_kantor/lists`);
//     return response;
// }
export const getEksporImporProvinsiService = async () => {
    return await Api.get(`/admin/provinsi_ekspor_impor/lists`);
}

export const getByIdEksporImporProvinsiService = async (id) => {
    return await Api.get(`/admin/provinsi_ekspor_impor/lists/${id}`);
}

export const createEksporImporProvinsiService = async (body) => {
    return await ApiForm.post(`/admin/provinsi_ekspor_impor/create`, body);
}

export const editEksporImporProvinsiService = async (body, id) => {
    return await ApiForm.patch(`/admin/provinsi_ekspor_impor/update/${id}`, body)
}

export const deleteEksporImporProvinsiService = async (id) => {
    return await Api.delete(`/admin/provinsi_ekspor_impor/delete/${id}`);
}


export const statusEksporImporProvinsiService = async (body, id) => {
    // const token = localStorage.getItem('token');
    const token = getTokenUserFromLocalStorage();
    return await APICORS.patch(`/admin/provinsi_ekspor_impor/toggle_status/${id}`, body
        , {
            headers: {
                Authorization: `Bearer ${token}`, // Include the token in the Authorization header
            },
        }
    );
}

//Utilitas

export const getUtilitasProvinsiService = async () => {
    return await Api.get(`/admin/provinsi_utilitas/lists`);
}

export const getByIdUtilitasProvinsiService = async (id) => {
    return await Api.get(`/admin/provinsi_utilitas/lists/${id}`);
}

export const createUtilitasProvinsiService = async (body) => {
    return await ApiForm.post(`/admin/provinsi_utilitas/create`, body);
}

export const editUtilitasProvinsiService = async (body, id) => {
    return await ApiForm.patch(`/admin/provinsi_utilitas/update/${id}`, body)
}

export const deleteUtilitasProvinsiService = async (id) => {
    return await Api.delete(`/admin/provinsi_utilitas/delete/${id}`);
}

export const statusUtilitasProvinsiService = async (body, id) => {
    return await APICORS.patch(`/admin/provinsi_utilitas/toggle_status/${id}`, body);
}
//Demografi

export const getDemografiProvinsiService = async () => {
    return await Api.get(`/admin/provinsi_demografi/lists`);
}

export const getByIdDemografiProvinsiService = async (id) => {
    return await Api.get(`/admin/provinsi_demografi/lists/${id}`);
}

export const createDemografiProvinsiService = async (body) => {
    return await ApiForm.post(`/admin/provinsi_demografi/create`, body);
}

export const editDemografiProvinsiService = async (body, id) => {
    return await ApiForm.patch(`/admin/provinsi_demografi/update/${id}`, body)
}

export const deleteDemografiProvinsiService = async (id) => {
    return await Api.delete(`/admin/provinsi_demografi/delete/${id}`);
}

export const statusDemografiProvinsiService = async (body, id) => {
    return await APICORS.patch(`/admin/provinsi_demografi/toggle_status/${id}`, body);
}

//Investasi

export const getInvestasiProvinsiService = async () => {
    return await Api.get(`/admin/provinsi_investasi/lists`);
}

export const getByIdInvestasiProvinsiService = async (id) => {
    return await Api.get(`/admin/provinsi_investasi/lists/${id}`);
}

export const createInvestasiProvinsiService = async (body) => {
    return await ApiForm.post(`/admin/provinsi_investasi/create`, body);
}

export const editInvestasiProvinsiService = async (body, id) => {
    return await ApiForm.patch(`/admin/provinsi_investasi/update/${id}`, body)
}

export const deleteInvestasiProvinsiService = async (id) => {
    return await Api.delete(`/admin/provinsi_investasi/delete/${id}`);
}


export const statusInvestasiProvinsiService = async (body, id) => {
    return await APICORS.patch(`/admin/provinsi_investasi/toggle_status/${id}`, body);
}

//Profil

export const getProfilProvinsiService = async () => {
    return await Api.get(`/admin/provinsi_profil/lists`);
}

export const getByIdProfilProvinsiService = async (id) => {
    return await Api.get(`/admin/provinsi_profil/lists/${id}`);
}

export const createProfilProvinsiService = async (body) => {
    return await ApiForm.post(`/admin/provinsi_profil/create`, body);
}

export const editProfilProvinsiService = async (body, id) => {
    return await ApiForm.patch(`/admin/provinsi_profil/update/${id}`, body)
}

export const deleteProfilProvinsiService = async (id) => {
    return await Api.delete(`/admin/provinsi_profil/delete/${id}`);
}
