# Security Remediation Plan - SISMONP3TGAI Application

## Overview
This document provides a comprehensive remediation plan for addressing the security vulnerabilities identified in the SISMONP3TGAI application. The plan is prioritized by risk level and includes specific implementation steps.

## Phase 1: Critical Vulnerabilities (Immediate - Week 1-2)

### 1.1 SQL Injection Fixes

#### Fix Direct SQL Query Construction
**File**: `application/modules/user_managementt/controllers/User_managementt.php`

**Current Code (Line 74):**
```php
$query = $this->db->query("select $col from $tbl where $w='$id' and tahun=$yearnow");
```

**Recommended Fix:**
```php
// Use Query Builder with proper escaping
$this->db->select($this->db->escape_str($col));
$this->db->from($this->db->escape_str($tbl));
$this->db->where($this->db->escape_str($w), $id);
$this->db->where('tahun', $yearnow);
$query = $this->db->get();
```

#### Fix Dynamic WHERE Clause Construction
**File**: `application/modules/database_perlokasi/controllers/Database_perlokasi.php`

**Current Code (Lines 139-152):**
```php
if($aa !=''){
    $a=" kd_satker='$aa' and ";
}
```

**Recommended Fix:**
```php
$where_conditions = array();
if($aa != ''){
    $where_conditions['kd_satker'] = $aa;
}
if($bb != ''){
    $where_conditions['kd_prov'] = $bb;
}
// Use CodeIgniter's where() method
$this->db->where($where_conditions);
```

### 1.2 Authentication Security Fixes

#### Remove Universal Fallback Password
**File**: `application/modules/login/controllers/Login.php`

**Current Code (Line 318):**
```php
if (password_verify($password,$ruser[0]['password']) || password_verify($password,$sapujagat[0]['password'])) {
```

**Recommended Fix:**
```php
if (password_verify($password, $ruser[0]['password'])) {
    // Remove the universal fallback completely
```

#### Remove Hardcoded Credentials
**File**: `application/core/MY_Controller.php`

**Current Code:**
```php
$header[] = 'client-id:webgis';
$header[] = 'client-pass:webgisindonesia';
```

**Recommended Fix:**
```php
$header[] = 'client-id:' . $this->config->item('api_client_id');
$header[] = 'client-pass:' . $this->config->item('api_client_pass');
```

### 1.3 Encryption Key Configuration

#### Set Proper Encryption Key
**File**: `application/config/config.php`

**Current Code (Line 343):**
```php
$config['encryption_key'] = '';
```

**Recommended Fix:**
```php
$config['encryption_key'] = 'your-32-character-encryption-key-here';
// Generate using: bin2hex(random_bytes(16))
```

## Phase 2: High Priority Vulnerabilities (Week 2-3)

### 2.1 File Upload Security

#### Secure File Upload Configuration
**Files**: Multiple upload controllers

**Current Code:**
```php
$config['allowed_types'] = 'jpg|jpeg|png|gif|pdf|docx|doc|xls|xlsx|rar|zip|ppt|pptx';
$config['upload_path'] = FCPATH . 'uploads/';
```

**Recommended Fix:**
```php
// Create secure upload directory outside web root
$secure_upload_path = APPPATH . '../secure_uploads/';
if (!is_dir($secure_upload_path)) {
    mkdir($secure_upload_path, 0755, true);
}

$config['upload_path'] = $secure_upload_path;
$config['allowed_types'] = 'jpg|jpeg|png|gif|pdf'; // Remove dangerous types
$config['max_size'] = 5120; // 5MB limit
$config['encrypt_name'] = TRUE; // Encrypt file names
$config['remove_spaces'] = TRUE;

// Add MIME type validation
$config['detect_mime'] = TRUE;
$allowed_mimes = array(
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'pdf' => 'application/pdf'
);
```

#### File Content Validation
```php
public function validate_file_content($file_path, $file_type) {
    // Validate file signature/magic bytes
    $file_content = file_get_contents($file_path, false, null, 0, 20);
    
    $signatures = array(
        'pdf' => '%PDF',
        'jpg' => "\xFF\xD8\xFF",
        'png' => "\x89PNG\r\n\x1a\n"
    );
    
    if (isset($signatures[$file_type])) {
        return strpos($file_content, $signatures[$file_type]) === 0;
    }
    
    return false;
}
```

### 2.2 CSRF Protection Enhancement

#### Consistent CSRF Implementation
**File**: `application/config/config.php`

**Current Code:**
```php
$config['csrf_regenerate'] = FALSE;
```

**Recommended Fix:**
```php
$config['csrf_regenerate'] = TRUE;
$config['csrf_exclude_uris'] = array('api/*'); // Only exclude API endpoints if needed
```

#### AJAX CSRF Token Implementation
**Files**: All JavaScript files with AJAX requests

**Add to all AJAX requests:**
```javascript
// Get CSRF token
var csrf_name = '<?php echo $this->security->get_csrf_token_name(); ?>';
var csrf_hash = '<?php echo $this->security->get_csrf_hash(); ?>';

// Include in AJAX data
var data = {
    // ... other data
};
data[csrf_name] = csrf_hash;

$.post(url, data, function(response) {
    // Update CSRF token for next request
    csrf_hash = response.csrf_hash;
});
```

### 2.3 Session Security Configuration

#### Secure Session Settings
**File**: `application/config/config.php`

**Current Code:**
```php
$config['sess_save_path'] = FCPATH . '/tmp';
$config['sess_match_ip'] = FALSE;
$config['cookie_secure'] = FALSE;
$config['cookie_httponly'] = FALSE;
```

**Recommended Fix:**
```php
// Create secure session directory outside web root
$secure_session_path = APPPATH . '../secure_sessions/';
if (!is_dir($secure_session_path)) {
    mkdir($secure_session_path, 0700, true);
}

$config['sess_save_path'] = $secure_session_path;
$config['sess_match_ip'] = TRUE;
$config['sess_time_to_update'] = 300;
$config['sess_regenerate_destroy'] = TRUE;
$config['cookie_secure'] = TRUE; // Enable for HTTPS
$config['cookie_httponly'] = TRUE;
$config['cookie_samesite'] = 'Strict';
```

## Phase 3: Medium Priority Vulnerabilities (Week 3-4)

### 3.1 XSS Prevention

#### Output Encoding Implementation
**Create helper function in**: `application/helpers/security_helper.php`

```php
<?php
if (!function_exists('html_escape_output')) {
    function html_escape_output($data) {
        if (is_array($data)) {
            return array_map('html_escape_output', $data);
        }
        return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('js_escape_output')) {
    function js_escape_output($data) {
        return json_encode($data, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
    }
}
?>
```

#### Input Validation Enhancement
```php
// Add to MY_Controller.php
protected function validate_input($data, $rules) {
    $this->load->library('form_validation');
    $this->form_validation->set_data($data);
    $this->form_validation->set_rules($rules);
    
    if (!$this->form_validation->run()) {
        return false;
    }
    
    // Additional sanitization
    foreach ($data as $key => $value) {
        $data[$key] = $this->security->xss_clean($value);
    }
    
    return $data;
}
```

### 3.2 Security Headers Implementation

#### Add Security Headers
**File**: `application/core/MY_Controller.php` (in constructor)

```php
public function __construct() {
    parent::__construct();
    
    // Security headers
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
    header('Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\'; style-src \'self\' \'unsafe-inline\';');
    
    // Remove server information
    header_remove('X-Powered-By');
    header_remove('Server');
}
```

## Phase 4: Additional Security Measures (Week 4-6)

### 4.1 Environment Configuration

#### Create Environment Configuration File
**File**: `application/config/environment.php`

```php
<?php
// Environment-specific configuration
switch (ENVIRONMENT) {
    case 'development':
        $config['api_client_id'] = 'dev_client_id';
        $config['api_client_pass'] = 'dev_client_pass';
        break;
    case 'production':
        $config['api_client_id'] = getenv('API_CLIENT_ID');
        $config['api_client_pass'] = getenv('API_CLIENT_PASS');
        break;
}
?>
```

### 4.2 Logging and Monitoring

#### Security Event Logging
```php
// Add to MY_Controller.php
protected function log_security_event($event_type, $details = array()) {
    $log_data = array(
        'timestamp' => date('Y-m-d H:i:s'),
        'ip_address' => $this->input->ip_address(),
        'user_agent' => $this->input->user_agent(),
        'user_id' => $this->session->userdata('user_id'),
        'event_type' => $event_type,
        'details' => json_encode($details)
    );
    
    // Log to database or file
    log_message('security', json_encode($log_data));
}
```

## Implementation Timeline

| Phase | Duration | Priority | Tasks |
|-------|----------|----------|-------|
| Phase 1 | Week 1-2 | Critical | SQL Injection, Authentication, Encryption |
| Phase 2 | Week 2-3 | High | File Upload, CSRF, Sessions |
| Phase 3 | Week 3-4 | Medium | XSS, Security Headers |
| Phase 4 | Week 4-6 | Low | Environment, Logging |

## Testing and Validation

### Security Testing Checklist
- [ ] SQL injection testing with automated tools
- [ ] File upload testing with malicious files
- [ ] CSRF token validation testing
- [ ] XSS payload testing
- [ ] Session security testing
- [ ] Authentication bypass testing
- [ ] Authorization testing

### Recommended Tools
- **OWASP ZAP** - Web application security scanner
- **SQLMap** - SQL injection testing
- **Burp Suite** - Comprehensive web security testing
- **Nikto** - Web server scanner

---

*This remediation plan should be implemented in phases with proper testing at each stage.*
